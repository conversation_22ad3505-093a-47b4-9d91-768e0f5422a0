#!/usr/bin/env python3
"""
测试所有动画效果 - 验证fade、slide、zoom等所有动画
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_all_animations():
    """测试所有动画效果"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试所有动画效果...")
    logger.info("包括：fade、slide、zoom等所有动画类型")
    
    # 全面的动画测试配置
    test_configs = [
        # Fade动画
        {
            'name': '淡入效果',
            'animation': 'fade_in',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_fade_in.mp4'
        },
        {
            'name': '淡出效果',
            'animation': 'fade_out',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_fade_out.mp4'
        },
        {
            'name': '淡入淡出效果',
            'animation': 'fade_in_out',
            'duration': 8.0,
            'animation_duration': 2.0,
            'output': 'all_fade_in_out.mp4'
        },
        
        # Slide动画
        {
            'name': '从左滑入',
            'animation': 'slide_in_left',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_in_left.mp4'
        },
        {
            'name': '从右滑入',
            'animation': 'slide_in_right',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_in_right.mp4'
        },
        {
            'name': '从上滑入',
            'animation': 'slide_in_top',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_in_top.mp4'
        },
        {
            'name': '从下滑入',
            'animation': 'slide_in_bottom',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_in_bottom.mp4'
        },
        {
            'name': '向左滑出',
            'animation': 'slide_out_left',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_out_left.mp4'
        },
        {
            'name': '向右滑出',
            'animation': 'slide_out_right',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_out_right.mp4'
        },
        {
            'name': '向上滑出',
            'animation': 'slide_out_top',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_out_top.mp4'
        },
        {
            'name': '向下滑出',
            'animation': 'slide_out_bottom',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_slide_out_bottom.mp4'
        },
        
        # Zoom动画
        {
            'name': '缩放进入',
            'animation': 'zoom_in',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_zoom_in.mp4'
        },
        {
            'name': '缩放退出',
            'animation': 'zoom_out',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'all_zoom_out.mp4'
        },
        {
            'name': '缩放进入退出',
            'animation': 'zoom_in_out',
            'duration': 8.0,
            'animation_duration': 2.0,
            'output': 'all_zoom_in_out.mp4'
        },
        
        # 无动画对比
        {
            'name': '无动画',
            'animation': 'none',
            'duration': 5.0,
            'animation_duration': 0.0,
            'output': 'all_no_animation.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.6)  # 60%宽度，适中大小
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        total_time = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}/{len(test_configs)}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"all_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    start_time = time.time()
                    
                    # 应用封面叠加效果
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    total_duration = config['duration'] + 1  # 多1秒缓冲
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=total_duration
                    ).overwrite_output()
                    
                    # 执行，设置超时
                    import subprocess
                    
                    cmd = ffmpeg.compile(out)
                    
                    # 使用subprocess执行，设置超时
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    try:
                        # 等待最多60秒
                        stdout, stderr = process.communicate(timeout=60)
                        
                        if process.returncode == 0:
                            end_time = time.time()
                            processing_time = end_time - start_time
                            total_time += processing_time
                            
                            # 检查结果
                            if Path(config['output']).exists():
                                file_size = Path(config['output']).stat().st_size
                                logger.info(f"✅ {config['name']} 测试成功!")
                                logger.info(f"   文件大小: {file_size} bytes")
                                logger.info(f"   处理时间: {processing_time:.2f}秒")
                                success_count += 1
                            else:
                                logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        else:
                            logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                            
                    except subprocess.TimeoutExpired:
                        logger.error(f"❌ {config['name']} 测试超时（60秒）")
                        process.kill()
                        process.communicate()
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
        
        logger.info(f"\n=== 所有动画效果测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        logger.info(f"总处理时间: {total_time:.2f}秒")
        logger.info(f"平均处理时间: {total_time/len(test_configs):.2f}秒/个")
        
        if success_count > 0:
            logger.info("\n📋 生成的动画测试文件:")
            
            logger.info("\n🎭 Fade动画:")
            logger.info("- all_fade_in.mp4 (淡入效果)")
            logger.info("- all_fade_out.mp4 (淡出效果)")
            logger.info("- all_fade_in_out.mp4 (淡入淡出效果)")
            
            logger.info("\n🏃 Slide动画:")
            logger.info("- all_slide_in_left.mp4 (从左滑入)")
            logger.info("- all_slide_in_right.mp4 (从右滑入)")
            logger.info("- all_slide_in_top.mp4 (从上滑入)")
            logger.info("- all_slide_in_bottom.mp4 (从下滑入)")
            logger.info("- all_slide_out_left.mp4 (向左滑出)")
            logger.info("- all_slide_out_right.mp4 (向右滑出)")
            logger.info("- all_slide_out_top.mp4 (向上滑出)")
            logger.info("- all_slide_out_bottom.mp4 (向下滑出)")
            
            logger.info("\n🔍 Zoom动画:")
            logger.info("- all_zoom_in.mp4 (缩放进入)")
            logger.info("- all_zoom_out.mp4 (缩放退出)")
            logger.info("- all_zoom_in_out.mp4 (缩放进入退出)")
            
            logger.info("\n📝 对比:")
            logger.info("- all_no_animation.mp4 (无动画)")
            
            logger.info("\n🎬 验证方法:")
            logger.info("播放all_*.mp4文件，应该能看到:")
            logger.info("- 各种不同的动画效果")
            logger.info("- 封面在整个duration期间都可见")
            logger.info("- 处理速度合理（不会卡死）")
            
            logger.info("\n如果所有动画都正常工作，说明集成完全成功！")
            
            return True
        else:
            logger.error("❌ 所有测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("🚀 开始所有动画效果测试")
    logger.info("测试范围：fade、slide、zoom等所有动画类型")
    
    success = test_all_animations()
    
    if success:
        logger.info("\n🎉 所有动画效果测试完成!")
        logger.info("如果所有动画都正常工作，说明集成完全成功！")
        logger.info("现在你可以使用任何动画效果生成视频了。")
    else:
        logger.error("\n❌ 动画效果测试失败")
        logger.error("需要进一步调试问题")
        sys.exit(1)
