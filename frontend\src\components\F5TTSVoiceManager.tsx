import React, { useState, useEffect } from 'react'
import { f5TtsVoicesApi, TTSVoice } from '../services/apiService'

interface F5TTSVoiceManagerProps {
  onVoiceSelect?: (voice: TTSVoice) => void
  className?: string
}

export default function F5TTSVoiceManager({ onVoiceSelect, className = '' }: F5TTSVoiceManagerProps) {
  const [voices, setVoices] = useState<TTSVoice[]>([])
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [loading, setLoading] = useState(false)
  const [testingVoice, setTestingVoice] = useState<string | null>(null)
  
  // 新音色表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    language: 'zh-CN',
    gender: 'female',
    ref_text: '',
    ref_audio: null as File | null,
    remove_silence: false,
    cross_fade_duration: 0.15,
    nfe_value: 32,
    randomize_seed: true
  })

  useEffect(() => {
    loadVoices()
  }, [])

  const loadVoices = async () => {
    try {
      const response = await f5TtsVoicesApi.getVoices()
      if (response.data) {
        setVoices(response.data)
      }
    } catch (error) {
      console.error('加载音色列表失败:', error)
    }
  }

  const handleCreateVoice = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.ref_audio || !formData.name.trim() || !formData.ref_text.trim()) {
      alert('请填写必填字段并上传参考音频文件')
      return
    }

    setLoading(true)
    try {
      const data = new FormData()
      data.append('name', formData.name)
      data.append('description', formData.description)
      data.append('language', formData.language)
      data.append('gender', formData.gender)
      data.append('ref_text', formData.ref_text)
      data.append('ref_audio', formData.ref_audio)
      data.append('remove_silence', formData.remove_silence.toString())
      data.append('cross_fade_duration', formData.cross_fade_duration.toString())
      data.append('nfe_value', formData.nfe_value.toString())
      data.append('randomize_seed', formData.randomize_seed.toString())

      const response = await f5TtsVoicesApi.createVoice(data)
      
      if (response.data) {
        // 重新加载音色列表
        await loadVoices()
        
        // 重置表单
        setFormData({
          name: '',
          description: '',
          language: 'zh-CN',
          gender: 'female',
          ref_text: '',
          ref_audio: null,
          remove_silence: false,
          cross_fade_duration: 0.15,
          nfe_value: 32,
          randomize_seed: true
        })
        setShowCreateForm(false)
        alert('音色创建成功！')
      } else {
        alert(`创建音色失败: ${response.error || '未知错误'}`)
      }
      
    } catch (error) {
      console.error('创建音色失败:', error)
      alert('创建音色失败')
    } finally {
      setLoading(false)
    }
  }

  const handleTestVoice = async (voiceId: string) => {
    setTestingVoice(voiceId)
    try {
      const response = await f5TtsVoicesApi.testVoice(voiceId, "这是一个音色测试。")
      if (response.data) {
        alert('音色测试成功！')
      } else {
        alert(`音色测试失败: ${response.error || '未知错误'}`)
      }
    } catch (error) {
      console.error('音色测试失败:', error)
      alert('音色测试失败')
    } finally {
      setTestingVoice(null)
    }
  }

  const handleDeleteVoice = async (voiceId: string, voiceName: string) => {
    if (!confirm(`确定要删除音色"${voiceName}"吗？此操作不可撤销。`)) {
      return
    }

    try {
      const response = await f5TtsVoicesApi.deleteVoice(voiceId)
      if (response.data !== null) {
        await loadVoices()
        alert('音色删除成功！')
      } else {
        alert(`删除音色失败: ${response.error || '未知错误'}`)
      }
    } catch (error) {
      console.error('删除音色失败:', error)
      alert('删除音色失败')
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('audio/')) {
        alert('请选择音频文件')
        return
      }
      setFormData({ ...formData, ref_audio: file })
    }
  }

  return (
    <div className={`f5-tts-voice-manager ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">F5-TTS 音色管理</h3>
        <button 
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          onClick={() => setShowCreateForm(true)}
        >
          添加新音色
        </button>
      </div>

      {/* 音色列表 */}
      <div className="space-y-3">
        {voices.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无F5-TTS音色，请添加新音色
          </div>
        ) : (
          voices.map(voice => (
            <div key={voice.id} className="border border-gray-200 rounded-lg p-4 bg-white">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{voice.name}</h4>
                  {voice.description && (
                    <p className="text-sm text-gray-600 mt-1">{voice.description}</p>
                  )}
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                    <span>{voice.language}</span>
                    <span>{voice.gender === 'male' ? '男性' : '女性'}</span>
                    {voice.usage_count !== undefined && (
                      <span>使用次数: {voice.usage_count}</span>
                    )}
                  </div>
                </div>
                <div className="flex gap-2 ml-4">
                  <button 
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors disabled:opacity-50"
                    onClick={() => handleTestVoice(voice.id)}
                    disabled={testingVoice === voice.id}
                  >
                    {testingVoice === voice.id ? '测试中...' : '测试'}
                  </button>
                  {onVoiceSelect && (
                    <button 
                      className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                      onClick={() => onVoiceSelect(voice)}
                    >
                      选择
                    </button>
                  )}
                  {!voice.is_built_in && (
                    <button 
                      className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                      onClick={() => handleDeleteVoice(voice.id, voice.name)}
                    >
                      删除
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* 创建音色表单模态框 */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">创建新音色</h3>
            <form onSubmit={handleCreateVoice} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    音色名称 *
                  </label>
                  <input 
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    语言
                  </label>
                  <select 
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.language}
                    onChange={(e) => setFormData({...formData, language: e.target.value})}
                  >
                    <option value="zh-CN">中文</option>
                    <option value="en-US">英文</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    性别
                  </label>
                  <select 
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.gender}
                    onChange={(e) => setFormData({...formData, gender: e.target.value})}
                  >
                    <option value="female">女性</option>
                    <option value="male">男性</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    交叉淡化时长
                  </label>
                  <input 
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.cross_fade_duration}
                    onChange={(e) => setFormData({...formData, cross_fade_duration: parseFloat(e.target.value)})}
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea 
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={2}
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  参考音频文件 *
                </label>
                <input 
                  type="file"
                  accept="audio/*"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  onChange={handleFileChange}
                  required
                />
                {formData.ref_audio && (
                  <p className="text-sm text-gray-600 mt-1">
                    已选择: {formData.ref_audio.name}
                  </p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  参考文本 *
                </label>
                <textarea 
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  value={formData.ref_text}
                  onChange={(e) => setFormData({...formData, ref_text: e.target.value})}
                  placeholder="请输入参考音频文件中说话的内容..."
                  required
                />
              </div>
              
              <div className="flex justify-end gap-3 pt-4">
                <button 
                  type="button" 
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  onClick={() => setShowCreateForm(false)}
                >
                  取消
                </button>
                <button 
                  type="submit" 
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
                  disabled={loading}
                >
                  {loading ? '创建中...' : '创建'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
