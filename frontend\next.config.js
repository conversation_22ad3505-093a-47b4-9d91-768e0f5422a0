/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  skipTrailingSlashRedirect: true,
  
  // 生产环境跳过检查
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production' || process.env.SKIP_TYPE_CHECK === 'true',
  },
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'production' || process.env.SKIP_ESLINT === 'true',
  },
  
  // 生产环境优化配置
  ...(process.env.NODE_ENV === 'production' && {
    compress: true, // 启用gzip压缩
    poweredByHeader: false, // 移除X-Powered-By头部
    generateEtags: true, // 生成ETags用于缓存
    // 代码分割和优化
    experimental: {
      optimizePackageImports: ['lucide-react', '@heroicons/react'],
    },
  }),
  
  // 开发环境或静态导出配置
  ...(process.env.BUILD_MODE === 'export' && {
    output: 'export',
    trailingSlash: true,
    images: {
      unoptimized: true,
      domains: [],
    },
  }),
  
  // 通用图片配置
  images: {
    unoptimized: process.env.BUILD_MODE === 'export',
    domains: [],
    // 生产环境图片优化
    ...(process.env.NODE_ENV === 'production' && process.env.BUILD_MODE !== 'export' && {
      formats: ['image/webp', 'image/avif'],
      minimumCacheTTL: 60 * 60 * 24 * 30, // 30天缓存
    }),
  },
  
  // 编译器优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? { exclude: ['error'] } : false,
  },
  
  // Webpack 配置优化
  webpack: (config, { dev, isServer }) => {
    // 生产环境优化
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        minimize: true,
        usedExports: true,
        sideEffects: false,
      }
    }
    return config
  },
  
  // API 代理已移除，所有 API 调用直接使用 DirectHttpClient 和 NEXT_PUBLIC_API_BASE_URL
  // 不再需要 Next.js 代理，统一使用环境变量配置后端地址
  
  // 增加服务器配置以提高超时限制
  serverRuntimeConfig: {
    // 增加服务器端的超时时间
    maxDuration: 300, // 5分钟
  },
  
  // 公共运行时配置
  publicRuntimeConfig: {
    apiUrl: process.env.NEXT_PUBLIC_API_URL,
  },
}

module.exports = nextConfig
