/**
 * 全局通知组件
 * 显示成功/失败/警告/信息弹窗
 */

'use client'

import React, { useEffect } from 'react'
import { X, CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react'
import { useNotificationStore } from '../store/notificationStore'
import { NotificationMessage } from '../types/store'

const NotificationIcon: React.FC<{ type: NotificationMessage['type'] }> = ({ type }) => {
  const iconProps = { className: "w-5 h-5" }
  
  switch (type) {
    case 'success':
      return <CheckCircle {...iconProps} className="w-5 h-5 text-green-500" />
    case 'error':
      return <XCircle {...iconProps} className="w-5 h-5 text-red-500" />
    case 'warning':
      return <AlertTriangle {...iconProps} className="w-5 h-5 text-yellow-500" />
    case 'info':
      return <Info {...iconProps} className="w-5 h-5 text-blue-500" />
    default:
      return <Info {...iconProps} className="w-5 h-5 text-gray-500" />
  }
}

const NotificationItem: React.FC<{ notification: NotificationMessage }> = ({ notification }) => {
  const { removeNotification } = useNotificationStore()

  const getBgColor = (type: NotificationMessage['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'info':
        return 'bg-blue-50 border-blue-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <div
      className={`${getBgColor(notification.type)} border rounded-lg p-4 shadow-lg transform transition-all duration-300 ease-in-out max-w-sm`}
    >
      <div className="flex items-start space-x-3">
        <NotificationIcon type={notification.type} />
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900">{notification.title}</h4>
          <p className="mt-1 text-sm text-gray-600">{notification.message}</p>
        </div>
        <button
          onClick={() => removeNotification(notification.id)}
          className="flex-shrink-0 ml-3 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

export const NotificationContainer: React.FC = () => {
  const { notifications } = useNotificationStore()

  if (notifications.length === 0) return null

  return (
    <div className="fixed right-4 space-y-2" style={{ top: '80px', zIndex: 9999 }}>
      {notifications.map((notification) => (
        <NotificationItem key={notification.id} notification={notification} />
      ))}
    </div>
  )
}

export default NotificationContainer
