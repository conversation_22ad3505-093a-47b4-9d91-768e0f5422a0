# 后端API开发实施计划

## 🎯 目标

基于前端Zustand状态管理结构，逐步实现后端API接口，确保与前端功能完全对应。

## 📋 开发阶段

### 阶段1：项目基础架构 (1天)

#### 1.1 FastAPI项目初始化
- [ ] 创建FastAPI应用结构
- [ ] 配置路由模块
- [ ] 设置CORS和中间件
- [ ] 配置环境变量

#### 1.2 数据库模型设计
- [ ] SQLAlchemy模型定义
- [ ] 数据库迁移脚本
- [ ] 种子数据初始化

#### 1.3 统一响应系统
- [ ] 响应格式标准化
- [ ] 错误处理中间件
- [ ] 请求ID生成器

**文件清单：**
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── core/
│   │   ├── config.py          # 配置管理
│   │   ├── database.py        # 数据库连接
│   │   └── responses.py       # 统一响应格式
│   ├── models/
│   │   ├── __init__.py
│   │   ├── settings.py        # 设置模型
│   │   ├── resources.py       # 资源模型
│   │   └── tasks.py           # 任务模型
│   ├── api/
│   │   ├── __init__.py
│   │   └── v1/
│   │       ├── __init__.py
│   │       └── router.py      # 主路由
│   └── middleware/
│       ├── __init__.py
│       ├── cors.py
│       └── error_handler.py
```

### 阶段2：设置管理API (1天)

#### 2.1 设置CRUD操作
- [ ] GET `/api/v1/settings` - 获取所有设置
- [ ] PUT `/api/v1/settings/tts` - 更新TTS设置
- [ ] PUT `/api/v1/settings/llm` - 更新LLM设置
- [ ] PUT `/api/v1/settings/general` - 更新通用设置

#### 2.2 配置验证和测试
- [ ] POST `/api/v1/settings/tts/test` - 测试TTS配置
- [ ] POST `/api/v1/settings/llm/test` - 测试LLM配置
- [ ] POST `/api/v1/settings/reset` - 重置设置

#### 2.3 与前端状态对接
- [ ] 验证数据结构一致性
- [ ] 测试设置页面集成

**对应前端Store：**
- `frontend/src/store/settingsStore.ts`
- `frontend/src/app/settings/page.tsx`

### 阶段3：文件上传系统 (1天)

#### 3.1 基础文件上传
- [ ] POST `/api/v1/upload` - 单文件上传
- [ ] POST `/api/v1/upload/batch` - 批量上传
- [ ] GET `/api/v1/files/{fileId}` - 获取文件
- [ ] DELETE `/api/v1/files/{fileId}` - 删除文件

#### 3.2 文件处理能力
- [ ] 音频文件元数据提取
- [ ] 视频文件缩略图生成
- [ ] 文件格式验证
- [ ] 文件大小限制

#### 3.3 存储管理
- [ ] 本地存储适配器
- [ ] 文件路径管理
- [ ] 临时文件清理

### 阶段4：资源管理API (2天)

#### 4.1 背景音乐管理
- [ ] GET `/api/v1/resources/music` - 获取音乐列表
- [ ] POST `/api/v1/resources/music` - 上传音乐
- [ ] PUT `/api/v1/resources/music/{id}` - 更新音乐信息
- [ ] DELETE `/api/v1/resources/music/{id}` - 删除音乐

#### 4.2 视频素材管理
- [ ] GET `/api/v1/resources/videos` - 获取视频列表
- [ ] POST `/api/v1/resources/videos` - 上传视频
- [ ] POST `/api/v1/resources/videos/batch` - 批量上传

#### 4.3 提示词管理
- [ ] GET `/api/v1/resources/prompts` - 获取提示词列表
- [ ] POST `/api/v1/resources/prompts` - 创建提示词
- [ ] PUT `/api/v1/resources/prompts/{id}` - 更新提示词
- [ ] POST `/api/v1/resources/prompts/{id}/test` - 测试提示词

#### 4.4 账号管理
- [ ] GET `/api/v1/resources/accounts` - 获取账号列表
- [ ] POST `/api/v1/resources/accounts` - 创建账号
- [ ] PUT `/api/v1/resources/accounts/{id}` - 更新账号
- [ ] POST `/api/v1/resources/accounts/batch` - 批量操作

#### 4.5 封面模板管理
- [ ] GET `/api/v1/resources/cover-templates` - 获取模板列表
- [ ] POST `/api/v1/resources/cover-templates` - 创建模板
- [ ] POST `/api/v1/resources/cover-templates/{id}/preview` - 预览模板

**对应前端Store：**
- `frontend/src/store/musicStore.ts`
- `frontend/src/store/videoMaterialStore.ts`
- `frontend/src/store/promptStore.ts`
- `frontend/src/store/accountStore.ts`
- `frontend/src/store/coverTemplateStore.ts`

### 阶段5：任务队列系统 (2天)

#### 5.1 任务管理API
- [ ] POST `/api/v1/generation/tasks` - 创建生成任务
- [ ] GET `/api/v1/generation/tasks` - 获取任务列表
- [ ] GET `/api/v1/generation/tasks/{id}` - 获取任务详情
- [ ] POST `/api/v1/generation/tasks/{id}/cancel` - 取消任务
- [ ] POST `/api/v1/generation/tasks/{id}/retry` - 重试任务

#### 5.2 任务队列处理
- [ ] 异步任务队列实现
- [ ] 任务状态管理
- [ ] 进度跟踪机制
- [ ] 错误处理和重试

#### 5.3 后台处理服务
- [ ] Reddit故事抓取
- [ ] LLM故事改写
- [ ] TTS音频生成
- [ ] 视频合成处理

**对应前端Store：**
- `frontend/src/store/generationStore.ts`
- `frontend/src/app/generate/page.tsx`
- `frontend/src/app/tasks/page.tsx`

### 阶段6：WebSocket实时通信 (1天)

#### 6.1 WebSocket连接管理
- [ ] WebSocket路由设置
- [ ] 连接管理器
- [ ] 消息广播系统

#### 6.2 实时状态推送
- [ ] 任务进度更新
- [ ] 系统状态监控
- [ ] 错误通知

#### 6.3 前端集成测试
- [ ] WebSocket连接测试
- [ ] 实时数据同步验证

**对应前端Hook：**
- `frontend/src/hooks/useApi.ts` (WebSocket hooks)

### 阶段7：系统管理API (1天)

#### 7.1 系统监控
- [ ] GET `/api/v1/system/health` - 健康检查
- [ ] GET `/api/v1/system/stats` - 系统统计
- [ ] GET `/api/v1/system/logs` - 系统日志

#### 7.2 系统维护
- [ ] POST `/api/v1/system/cleanup` - 清理临时文件
- [ ] POST `/api/v1/system/backup` - 数据备份

## 📁 项目结构

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI应用入口
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py             # 配置管理
│   │   ├── database.py           # 数据库连接
│   │   ├── security.py           # 安全相关
│   │   └── responses.py          # 统一响应格式
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base.py               # 基础模型
│   │   ├── settings.py           # 设置模型
│   │   ├── resources.py          # 资源模型
│   │   └── tasks.py              # 任务模型
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── settings.py           # 设置Schema
│   │   ├── resources.py          # 资源Schema
│   │   └── tasks.py              # 任务Schema
│   ├── api/
│   │   ├── __init__.py
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── router.py         # 主路由
│   │       ├── settings.py       # 设置路由
│   │       ├── resources.py      # 资源路由
│   │       ├── generation.py     # 生成任务路由
│   │       ├── upload.py         # 文件上传路由
│   │       ├── system.py         # 系统管理路由
│   │       └── websocket.py      # WebSocket路由
│   ├── services/
│   │   ├── __init__.py
│   │   ├── settings_service.py   # 设置服务
│   │   ├── resource_service.py   # 资源服务
│   │   ├── task_service.py       # 任务服务
│   │   ├── file_service.py       # 文件服务
│   │   └── generation_service.py # 生成服务
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── file_utils.py         # 文件工具
│   │   ├── media_utils.py        # 媒体处理工具
│   │   └── validation.py        # 验证工具
│   ├── middleware/
│   │   ├── __init__.py
│   │   ├── cors.py              # CORS中间件
│   │   ├── error_handler.py     # 错误处理中间件
│   │   └── logging.py           # 日志中间件
│   └── worker/
│       ├── __init__.py
│       ├── task_worker.py       # 任务工作器
│       ├── reddit_scraper.py    # Reddit抓取器
│       ├── ai_service.py        # AI服务调用
│       └── video_processor.py   # 视频处理器
├── migrations/
│   └── versions/                # 数据库迁移文件
├── tests/
│   ├── __init__.py
│   ├── test_api/
│   ├── test_services/
│   └── test_utils/
├── requirements.txt
├── alembic.ini                  # 数据库迁移配置
├── .env.example
└── README.md
```

## 🔧 技术栈

### 核心框架
- **FastAPI** - 现代Python Web框架
- **SQLAlchemy** - ORM数据库操作
- **Alembic** - 数据库迁移
- **Pydantic** - 数据验证和序列化

### 异步处理
- **Celery** - 分布式任务队列
- **Redis** - 消息代理和缓存
- **asyncio** - 异步编程

### AI服务集成
- **OpenAI API** - GPT和TTS服务
- **aiohttp** - 异步HTTP客户端

### 文件处理
- **Pillow** - 图像处理
- **FFmpeg-python** - 视频音频处理
- **mutagen** - 音频元数据提取

### WebSocket
- **python-socketio** - WebSocket支持
- **asyncio** - 异步事件处理

## 📊 数据库设计

### 核心表结构

```sql
-- 设置表
CREATE TABLE settings (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36),
    tts_config JSON,
    llm_config JSON,
    general_config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 资源表
CREATE TABLE resources (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type ENUM('music', 'video', 'prompt', 'account', 'template') NOT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    category VARCHAR(100),
    tags JSON,
    is_built_in BOOLEAN DEFAULT FALSE,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 生成任务表
CREATE TABLE generation_tasks (
    id VARCHAR(36) PRIMARY KEY,
    config JSON NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    progress INT DEFAULT 0,
    current_step VARCHAR(255),
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    result JSON,
    error TEXT,
    logs JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🧪 测试计划

### 单元测试
- [ ] 模型测试
- [ ] 服务层测试
- [ ] 工具函数测试

### 集成测试
- [ ] API端点测试
- [ ] 数据库集成测试
- [ ] 文件上传测试

### 端到端测试
- [ ] 完整任务流程测试
- [ ] 前后端集成测试
- [ ] WebSocket通信测试

## 📝 开发规范

### 代码风格
- 使用 `black` 代码格式化
- 使用 `flake8` 代码检查
- 使用 `mypy` 类型检查

### 命名规范
- 文件名：snake_case
- 类名：PascalCase
- 函数名：snake_case
- 变量名：snake_case

### 注释规范
- 使用 docstring 记录函数和类
- 重要业务逻辑添加注释
- API 端点使用 FastAPI 的自动文档

## 🚀 部署准备

### 开发环境
```bash
# 安装依赖
pip install -r requirements.txt

# 数据库迁移
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 生产环境
- Docker 容器化
- Nginx 反向代理
- 数据库连接池优化
- Redis 缓存配置

## 📋 检查清单

### 阶段1完成检查
- [ ] FastAPI 应用启动正常
- [ ] 数据库连接成功
- [ ] 统一响应格式生效
- [ ] 错误处理正常

### 阶段2完成检查
- [ ] 设置API全部实现
- [ ] 前端设置页面正常对接
- [ ] TTS/LLM配置测试通过

### 阶段3完成检查
- [ ] 文件上传功能正常
- [ ] 文件元数据提取正确
- [ ] 文件存储和访问正常

### 最终完成检查
- [ ] 所有API端点实现
- [ ] 前后端完全集成
- [ ] WebSocket实时通信正常
- [ ] 任务队列处理正常
- [ ] 文档完整

这个实施计划确保了与前端Zustand状态管理的完美对接，每个阶段都有明确的目标和检查点。
