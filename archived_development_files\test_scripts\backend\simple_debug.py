#!/usr/bin/env python
"""
简化调试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic():
    print("🔍 基础模块测试...")
    
    # 测试1: 导入ApiResponse
    try:
        from src.core.responses import ApiResponse
        print("✅ ApiResponse导入成功")
    except Exception as e:
        print(f"❌ ApiResponse导入失败: {e}")
        return
    
    # 测试2: 导入schemas
    try:
        from src.schemas.settings import SettingsResponse, TTSConfig, LLMConfig, GeneralSettings
        print("✅ schemas导入成功")
    except Exception as e:
        print(f"❌ schemas导入失败: {e}")
        return
    
    # 测试3: 导入Settings模型
    try:
        from src.models.settings import Settings
        print("✅ Settings模型导入成功")
    except Exception as e:
        print(f"❌ Settings模型导入失败: {e}")
        return
    
    # 测试4: 创建Settings对象
    try:
        settings = Settings()
        print("✅ Settings对象创建成功")
    except Exception as e:
        print(f"❌ Settings对象创建失败: {e}")
        return
    
    # 测试5: 测试to_frontend_format方法
    try:
        frontend_data = settings.to_frontend_format()
        print(f"✅ frontend_data创建成功")
        print(f"   数据结构: {list(frontend_data.keys())}")
    except Exception as e:
        print(f"❌ frontend_data创建失败: {e}")
        return
    
    print("\n🎉 基础测试通过!")

if __name__ == "__main__":
    test_basic()
