"""
改进的Python字节码编译工具
生成更简洁的分发包，直接执行main.pyc
"""

import os
import sys
import shutil
import py_compile
from pathlib import Path
import subprocess

def compile_directory_to_pyc(src_dir, dest_dir):
    """编译目录中的Python文件为pyc"""
    print(f"编译: {src_dir} -> {dest_dir}")
    
    os.makedirs(dest_dir, exist_ok=True)
    
    for py_file in src_dir.rglob("*.py"):
        rel_path = py_file.relative_to(src_dir)
        dest_pyc = dest_dir / str(rel_path).replace('.py', '.pyc')
        dest_pyc.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            py_compile.compile(str(py_file), str(dest_pyc), doraise=True)
            print(f"  ✓ {rel_path}")
        except Exception as e:
            print(f"  ✗ 编译失败 {rel_path}: {e}")
    
    # 复制非Python文件
    for non_py_file in src_dir.rglob("*"):
        if non_py_file.is_file() and not non_py_file.name.endswith('.py'):
            rel_path = non_py_file.relative_to(src_dir)
            dest_file = dest_dir / rel_path
            dest_file.parent.mkdir(parents=True, exist_ok=True)
            try:
                shutil.copy2(non_py_file, dest_file)
                print(f"  📁 {rel_path}")
            except Exception as e:
                print(f"  ✗ 复制失败 {rel_path}: {e}")

def _ensure_six_module(lib_dir):
    """确保six.py模块存在，如果缺失则自动添加"""
    lib_path = Path(lib_dir)
    six_py_path = lib_path / "six.py"

    # 检查six.py是否存在
    if six_py_path.exists():
        print("  ✓ six.py 已存在")
        return True

    # 检查是否有six的dist-info目录（说明six已安装但缺少源文件）
    six_dist_info = None
    for item in lib_path.glob("six-*.dist-info"):
        if item.is_dir():
            six_dist_info = item
            break

    if not six_dist_info:
        print("  ℹ️ 未发现six模块，跳过")
        return False

    print("  ⚠️ 发现six模块但缺少six.py源文件，正在修复...")

    try:
        # 尝试从系统Python环境中找到six.py
        import six
        six_source_path = Path(six.__file__)

        if six_source_path.exists() and six_source_path.name == "six.py":
            # 复制six.py到lib目录
            shutil.copy2(six_source_path, six_py_path)
            print(f"  ✓ 已复制six.py: {six_source_path} -> {six_py_path}")
            return True
        else:
            print(f"  ⚠️ 找到的six模块不是源文件: {six_source_path}")

    except ImportError:
        print("  ⚠️ 系统中未安装six模块，尝试安装...")
        try:
            # 安装six模块
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "six",
                "--target", str(lib_path),
                "--no-user",
                "--no-warn-script-location",
                "--disable-pip-version-check"
            ], capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                print("  ✓ six模块安装成功")
                return True
            else:
                print(f"  ✗ six模块安装失败: {result.stderr}")

        except Exception as e:
            print(f"  ✗ 安装six模块时出错: {e}")

    except Exception as e:
        print(f"  ✗ 处理six模块时出错: {e}")

    return False

def install_all_packages(lib_dir):
    """安装所有依赖包 - 优先从现有venv复制，否则重新安装"""
    
    # 获取完整的依赖列表
    def get_all_packages():
        """从requirements.txt读取所有依赖包"""
        backend_dir = Path(__file__).parent
        req_file = backend_dir / "requirements.txt"
        packages = []
        
        if req_file.exists():
            with open(req_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过注释和空行
                    if line and not line.startswith('#'):
                        packages.append(line)
        
        return packages
    
    all_packages = get_all_packages()
    os.makedirs(lib_dir, exist_ok=True)
    
    # 检查是否存在venv目录
    backend_dir = Path(__file__).parent
    venv_dir = backend_dir / "venv"
    venv_site_packages = None
    
    # 查找venv中的site-packages目录
    if venv_dir.exists():
        possible_paths = [
            venv_dir / "Lib" / "site-packages",  # Windows
            venv_dir / "lib" / "python3.8" / "site-packages",  # Linux/Mac
            venv_dir / "lib" / "python3.9" / "site-packages",
            venv_dir / "lib" / "python3.10" / "site-packages",
            venv_dir / "lib" / "python3.11" / "site-packages",
            venv_dir / "lib" / "python3.12" / "site-packages"
        ]
        
        for path in possible_paths:
            if path.exists():
                venv_site_packages = path
                break
    
    # 方案1：如果找到venv，直接复制所有包
    if venv_site_packages:
        print(f"发现现有venv: {venv_site_packages}")
        print("正在复制所有依赖包...")
        
        try:
            # 复制整个site-packages目录
            for item in venv_site_packages.iterdir():
                if item.is_dir():
                    dest = lib_dir / item.name
                    if not dest.exists():
                        shutil.copytree(item, dest)
                        print(f"  ✓ 复制目录: {item.name}")
                elif item.is_file() and item.suffix == '.pth':
                    # 复制.pth文件
                    shutil.copy2(item, lib_dir / item.name)
                    print(f"  ✓ 复制文件: {item.name}")
            
            print("✅ 所有依赖包复制完成")

            # 检查并添加six.py（如果缺失）
            _ensure_six_module(lib_dir)

            return True

        except Exception as e:
            print(f"❌ 复制依赖包失败: {e}")
            print("切换到重新安装模式...")
    
    # 方案2：重新安装所有依赖
    print("正在安装所有依赖包...")
    success_count = 0
    
    for package in all_packages:
        print(f"安装: {package}")
        try:
            cmd = [
                sys.executable, "-m", "pip", "install",
                package,
                "--target", str(lib_dir),
                "--no-user",
                "--no-warn-script-location",
                "--disable-pip-version-check",
                "--timeout", "120"
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=180,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                print(f"  ✓ {package}")
                success_count += 1
            else:
                print(f"  ✗ {package} 安装失败")
                if result.stderr:
                    print(f"    错误: {result.stderr[:200]}...")
                
        except Exception as e:
            print(f"  ✗ {package} 安装出错: {e}")
    
    print(f"依赖包安装完成: {success_count}/{len(all_packages)}")

    # 检查并添加six.py（如果缺失）
    _ensure_six_module(lib_dir)

    return success_count > len(all_packages) // 2

def create_simple_launcher(dist_dir):
    """创建简洁的main.pyc启动器"""
    
    # 创建launcher.py启动器脚本
    launcher_code = '''#!/usr/bin/env python3
"""
Reddit Story Video Generator Launcher
Directly execute compiled main.pyc
"""

import sys
import os
from pathlib import Path
import runpy

def main():
    # Get script directory
    script_dir = Path(__file__).parent.absolute()
    lib_dir = script_dir / "lib"
    app_dir = script_dir / "app"
    
    # Add lib directory to Python path
    if lib_dir.exists():
        sys.path.insert(0, str(lib_dir))
    
    # Add app directory to Python path and change working directory
    if app_dir.exists():
        sys.path.insert(0, str(app_dir))
        os.chdir(str(app_dir))
    
    # Check if main.pyc exists
    main_pyc = app_dir / "main.pyc"
    if not main_pyc.exists():
        print(f"Error: main.pyc not found: {main_pyc}")
        return 1
    
    try:
        # Directly run main.pyc as main module
        # This will trigger the if __name__ == "__main__" code block in main.pyc
        runpy.run_path(str(main_pyc), run_name="__main__")
        return 0
        
    except KeyboardInterrupt:
        print("\\nApplication stopped")
        return 0
    except Exception as e:
        print(f"Startup failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    # 写入launcher.py启动器
    launcher_file = dist_dir / "launcher.py"
    with open(launcher_file, 'w', encoding='utf-8') as f:
        f.write(launcher_code)
    
    # 创建Windows批处理文件
    batch_code = '''@echo off
chcp 65001 > nul
title Reddit Story Video Generator
cls
echo.
echo ====================================
echo Reddit Story Video Generator
echo ====================================
echo.
echo Starting application...
echo.
python launcher.py
'''
    
    batch_file = dist_dir / "start.bat"
    with open(batch_file, 'w', encoding='utf-8') as f:
        f.write(batch_code)
    
    print(f"✓ 创建启动器: {launcher_file}")
    print(f"✓ 创建批处理: {batch_file}")

def create_readme(dist_dir):
    """创建说明文档"""
    readme_content = f'''# Reddit Story Video Generator - Compiled Distribution

## Features
- ✅ Source code compiled to Python bytecode (.pyc) for protection
- ✅ Clean launcher that directly executes main.pyc
- ✅ All necessary dependencies included

## How to Run

### Command Line (Recommended)
```bash
cd dist
python launcher.py
```

### Windows Double-click
Double-click `start.bat` file

## Directory Structure
```
dist/
├── launcher.py      # Clean launcher (directly executes main.pyc)
├── start.bat        # Windows batch startup script
├── app/             # Compiled application
│   ├── main.pyc     # Main program (bytecode)
│   ├── src/         # Source code (compiled to pyc)
│   ├── templates/   # Template files
│   ├── frontend_dist/ # Frontend resources
│   └── *.db         # Database files
├── lib/             # Python dependencies
├── requirements.txt # Dependencies list backup
└── README.md        # This file
```

## Technical Details
- Launcher uses `runpy.run_path()` to directly execute main.pyc
- Automatically sets Python paths and working directory
- Maintains all original functionality

## System Requirements
- Python 3.8+
- Windows/Linux/macOS

Compiled: {__import__('time').strftime('%Y-%m-%d %H:%M:%S')}
Python Version: {sys.version}
'''
    
    readme_file = dist_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ 创建说明: {readme_file}")

def main():
    """主函数"""
    print("=" * 60)
    print("Reddit Story Video Generator")
    print("改进的字节码编译工具")
    print("=" * 60)
    
    project_root_dir = Path(__file__).parent.parent
    backend_dir = Path(__file__).parent
    dist_dir = backend_dir / "dist"
    
    # 清理现有目录
    if dist_dir.exists():
        print(f"清理现有目录: {dist_dir}")
        shutil.rmtree(dist_dir)
    
    app_dir = dist_dir / "app"
    lib_dir = dist_dir / "lib"
    
    print(f"输出目录: {dist_dir}")
    
    try:
        # 1. 编译main.py
        print("\\n1. 编译主程序...")
        main_file = backend_dir / "main.py"
        if main_file.exists():
            app_dir.mkdir(parents=True, exist_ok=True)
            main_pyc = app_dir / "main.pyc"
            py_compile.compile(str(main_file), str(main_pyc))
            print("✓ main.py -> main.pyc")
        
        # 2. 编译src目录
        print("\\n2. 编译源码...")
        src_dir = backend_dir / "src"
        if src_dir.exists():
            compile_directory_to_pyc(src_dir, app_dir / "src")
        
        # 3. 复制资源文件
        print("\\n3. 复制资源...")
        
        # 数据库（不复制）
        # for db_file in backend_dir.glob("*.db"):
        #     shutil.copy2(db_file, app_dir / db_file.name)
        #     print(f"✓ {db_file.name}")
        
        # 复制默认封面模板
        reddit_template_dir = project_root_dir / "reddit-template"
        if reddit_template_dir.exists():
            shutil.copytree(reddit_template_dir, dist_dir / "reddit-template")
            print("✓ frontend_dist/")

        # 创建前端模板目录
        (app_dir / "templates").mkdir(exist_ok=True)
        print("✓ templates/")
        
        # 前端资源
        frontend_dist = backend_dir / "frontend_dist"
        if frontend_dist.exists():
            shutil.copytree(frontend_dist, app_dir / "frontend_dist")
            print("✓ frontend_dist/")
        
        # 创建uploads目录
        (app_dir / "uploads").mkdir(exist_ok=True)
        print("✓ uploads/")
        
        # 4. 安装所有依赖
        print("\\n4. 安装所有依赖...")
        install_all_packages(lib_dir)
        
        # 5. 创建启动器
        print("\\n5. 创建启动器...")
        create_simple_launcher(dist_dir)
        
        # 6. 复制requirements.txt备用
        req_src = backend_dir / "requirements.txt"
        if req_src.exists():
            shutil.copy2(req_src, dist_dir / "requirements.txt")
            print("✓ requirements.txt")
        
        # 7. 创建说明文档
        print("\\n6. 创建文档...")
        create_readme(dist_dir)
        
        # 8. 统计信息
        print("\\n" + "=" * 60)
        print("🎉 编译完成!")
        print(f"📁 输出目录: {dist_dir}")
        
        # 计算大小
        total_size = sum(f.stat().st_size for f in dist_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        print(f"📊 总大小: {size_mb:.1f} MB")
        
        print("\\n🚀 使用方法:")
        print(f"1. cd {dist_dir.name}")
        print("2. python launcher.py")
        print("3. 或双击 start.bat")
        
        print("\\n✨ 特点:")
        print("- 简洁的启动器，直接执行main.pyc")
        print("- 无多余的源码文件")
        print("- 完整的字节码保护")
        print("=" * 60)
        
    except Exception as e:
        print(f"\\n❌ 编译失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
