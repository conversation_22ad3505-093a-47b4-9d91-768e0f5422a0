#!/usr/bin/env python3
"""
检查后端API真实响应格式
"""
import requests
import json

def test_api(endpoint):
    """测试API端点"""
    try:
        resp = requests.get(f'http://localhost:8000/api{endpoint}', timeout=5)
        print(f"\n🔗 {endpoint}")
        print(f"Status: {resp.status_code}")
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response structure:")
            print(json.dumps(data, indent=2, ensure_ascii=False)[:1000])
            
            # 分析数据结构
            if isinstance(data, dict):
                if 'success' in data and 'data' in data:
                    print(f"Backend format: success={data.get('success')}, data type={type(data.get('data'))}")
                    if isinstance(data['data'], list) and len(data['data']) > 0:
                        print(f"First item: {json.dumps(data['data'][0], indent=2, ensure_ascii=False)[:200]}")
        else:
            print(f"Error: {resp.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

def main():
    print("🚀 检查后端API真实响应格式")
    print("=" * 50)
    
    endpoints = [
        '/video-categories',
        '/video-materials', 
        '/prompts',
        '/accounts',
        '/cover-templates',
        '/settings'
    ]
    
    for endpoint in endpoints:
        test_api(endpoint)

if __name__ == "__main__":
    main()
