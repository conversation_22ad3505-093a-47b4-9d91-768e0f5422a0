#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
封面模板圆角修复测试脚本
"""

import os
import sys
from pathlib import Path

def test_template_border_radius():
    """测试封面模板圆角修复"""
    print("🔧 封面模板圆角问题修复测试")
    print("=" * 50)
    
    template_path = Path("backend/templates/1b88fddc-fb2c-4e8d-96e4-c1e2a74f7227.html")
    
    if not template_path.exists():
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ 读取模板文件成功")
        
        # 检查关键的CSS样式
        checks = [
            ("#reddit-cover", "border-radius"),
            ("overflow: hidden", "overflow"),
            (".post-header", "border-radius"),
            ("border-radius: 12px", "圆角值")
        ]
        
        for check, desc in checks:
            if check in content:
                print(f"✅ 找到 {desc}: {check}")
            else:
                print(f"❌ 未找到 {desc}: {check}")
        
        # 检查截图目标元素的圆角设置
        if 'id="reddit-cover"' in content and 'border-radius' in content:
            lines = content.split('\n')
            reddit_cover_found = False
            for i, line in enumerate(lines):
                if 'id="reddit-cover"' in line:
                    reddit_cover_found = True
                    print(f"✅ 截图目标元素位置 (第{i+1}行): {line.strip()}")
                    break
            
            if reddit_cover_found:
                print("📝 建议: 需要确保#reddit-cover元素具有圆角样式")
                print("📝 当前模板的圆角在.post-container上，但截图的是#reddit-cover")
                
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_first_sentence_extraction():
    """测试第一句话提取逻辑"""
    print("\n🔧 第一句话提取逻辑测试")
    print("=" * 50)
    
    test_cases = [
        "这是第一句话。这是第二句话。这是第三句话。",
        "第一句话！第二句话？第三句话。",
        "简单测试。更多内容在这里。",
        "没有标点的文本继续很长很长很长很长",
        "中文标点测试。这里是第二句！",
        "English sentence. Another sentence here.",
    ]
    
    def extract_first_sentence(story_text: str) -> str:
        """模拟提取第一句话的逻辑"""
        if not story_text:
            return ""
        
        # 定义句末标点符号（包括中英文）
        sentence_enders = ('.', '?', '!', '。', '？', '！')
        
        # 找到第一个句末标点的位置
        first_sentence_end_pos = -1
        found_ender = None
        
        for ender in sentence_enders:
            pos = story_text.find(ender)
            if pos != -1:
                if first_sentence_end_pos == -1 or pos < first_sentence_end_pos:
                    first_sentence_end_pos = pos
                    found_ender = ender
        
        if first_sentence_end_pos == -1:
            # 如果没有找到标点，返回前100个字符
            first_sentence = story_text[:100].strip()
            return first_sentence
        
        # 提取第一句话（包含标点）
        first_sentence = story_text[:first_sentence_end_pos + 1].strip()
        return first_sentence
    
    for i, test_case in enumerate(test_cases, 1):
        result = extract_first_sentence(test_case)
        print(f"测试 {i}:")
        print(f"  原文: {test_case}")
        print(f"  第一句话: '{result}'")
        print()
    
    return True

def main():
    """主函数"""
    print("🧪 封面问题诊断测试")
    print("=" * 60)
    
    success1 = test_template_border_radius()
    success2 = test_first_sentence_extraction()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ 诊断测试完成")
        print("📋 问题总结:")
        print("1. 圆角问题: 需要为#reddit-cover元素添加border-radius样式")
        print("2. 第一句话提取: 逻辑正常，但需要添加日志来跟踪实际使用情况")
    else:
        print("❌ 诊断测试存在问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
