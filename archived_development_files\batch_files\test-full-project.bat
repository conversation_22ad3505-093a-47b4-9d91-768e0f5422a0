@echo off
chcp 65001 >nul
echo ================================================================
echo Reddit Story Video Generator - 完整项目测试
echo 前端 Hydration 修复 + 后端模块修复
echo ================================================================
echo.

set "PROJECT_ROOT=%~dp0"

echo 📋 测试计划:
echo   1. 后端启动测试 (简化版本)
echo   2. 前端启动测试 (Hydration 修复版本)
echo   3. 前后端连通性测试
echo.

echo ================================
echo [阶段 1] 后端测试
echo ================================
echo.

call fix-and-test-backend.bat
if %errorlevel% neq 0 (
    echo ❌ 后端启动失败
    goto :frontend_only
)

echo.
echo ✅ 后端启动成功！
echo.
echo 🔄 等待 5 秒后启动前端...
timeout /t 5 /nobreak >nul

:frontend_only
echo.
echo ================================
echo [阶段 2] 前端测试
echo ================================
echo.

start "Backend Server" cmd /k "cd /d %PROJECT_ROOT%backend && call venv\Scripts\activate.bat && python simple_main.py"

timeout /t 3 /nobreak >nul

call test-frontend.bat

echo.
echo ================================
echo [阶段 3] 测试完成
echo ================================
echo.
echo 📍 如果一切正常，你应该能访问:
echo   • 前端: http://localhost:3000
echo   • 后端: http://localhost:8000/docs
echo.
echo 🧪 测试页面:
echo   • 主页面: http://localhost:3000
echo   • 扩展测试: http://localhost:3000/extension-test
echo   • 后端健康检查: http://localhost:8000/api/health
echo.
pause
