"""
简单的后端测试脚本
"""
import sys
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def test_imports():
    """测试各个模块的导入"""
    try:
        from src.core.config import get_settings
        print("✓ Config module imported successfully")
        
        settings = get_settings()
        print(f"✓ Settings loaded: environment={settings.environment}")
        
        from src.core.database import get_db, init_db
        print("✓ Database module imported successfully")
        
        from src.models.settings import Settings
        print("✓ Settings model imported successfully")
        
        from src.schemas.settings import SettingsResponse, TTSConfig, LLMConfig, GeneralSettings
        print("✓ Settings schemas imported successfully")
        
        from src.core.responses import ApiResponse
        print("✓ Response classes imported successfully")
        
        from src.api.settings import router
        print("✓ Settings router imported successfully")
        
        from src.api.routes import api_router
        print("✓ API router imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting backend tests...\n")
    
    success = test_imports()
    
    if success:
        print("\n✅ All tests passed! Backend setup is working correctly.")
        print("\n📝 Next steps:")
        print("1. Run: uvicorn main:app --reload --host 0.0.0.0 --port 8000")
        print("2. Open: http://localhost:8000/docs")
        print("3. Test API: http://localhost:8000/api/v1/settings")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
