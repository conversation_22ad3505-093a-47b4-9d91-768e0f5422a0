'use client';

import { useState } from 'react';

// API基础URL配置
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

export default function TestApiPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testFetch = async () => {
    setLoading(true);
    console.log('=== 开始测试API调用 ===');
    
    try {
      // 测试1: 使用 DirectHttpClient 调用 API
      console.log('测试1: 使用 DirectHttpClient 调用 /cover-templates/');
      const response1 = await fetch(`${API_BASE}/cover-templates/`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });
      console.log('DirectHttpClient响应:', response1.status, response1.statusText);
      
      if (response1.ok) {
        const data1 = await response1.json();
        console.log('DirectHttpClient数据:', data1);
        setResult({ type: 'relative', data: data1 });
      } else {
        console.log('相对路径失败');
        setResult({ type: 'relative', error: `${response1.status} ${response1.statusText}` });
      }

    } catch (error) {
      console.error('相对路径异常:', error);
      setResult({ type: 'relative', error: String(error) });
    }

    try {
      // 测试2: 直接fetch绝对路径
      console.log(`测试2: 绝对路径 ${API_BASE}/api/cover-templates/`);
      const response2 = await fetch(`${API_BASE}/api/cover-templates/`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });
      console.log('绝对路径响应:', response2.status, response2.statusText);
      
      if (response2.ok) {
        const data2 = await response2.json();
        console.log('绝对路径数据:', data2);
        setResult({ type: 'absolute', data: data2 });
      } else {
        console.log('绝对路径失败');
        setResult({ type: 'absolute', error: `${response2.status} ${response2.statusText}` });
      }
      
    } catch (error) {
      console.error('绝对路径异常:', error);
      setResult({ type: 'absolute', error: String(error) });
    }

    setLoading(false);
    console.log('=== API测试完成 ===');
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">API测试页面</h1>
      
      <button
        onClick={testFetch}
        disabled={loading}
        className="px-4 py-2 bg-blue-600 text-white rounded disabled:opacity-50"
      >
        {loading ? '测试中...' : '测试API调用'}
      </button>

      {result && (
        <div className="mt-4 p-4 bg-gray-100 rounded">
          <h2 className="font-bold">测试结果:</h2>
          <pre className="mt-2 text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-4 p-4 bg-yellow-100 rounded">
        <h3 className="font-bold">检查清单:</h3>
        <ul className="list-disc list-inside mt-2">
          <li>检查浏览器开发者工具的Network标签</li>
          <li>查看Console标签的日志输出</li>
          <li>验证请求是否发出及响应状态</li>
        </ul>
      </div>
    </div>
  );
}
