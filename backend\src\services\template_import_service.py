"""
模板导入服务
支持HTML文件导入为封面模板，变量绑定和初始化
"""

import os
import json
import re
import shutil
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from uuid import uuid4
from datetime import datetime
import logging
from urllib.parse import urlparse, unquote

from ..models.resources import CoverTemplate
from ..core.database import get_db_session
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

class TemplateImportService:
    """模板导入服务类"""
    
    def __init__(self):
        # 设置模板目录为backend/templates
        backend_dir = Path(__file__).parent.parent.parent
        self.templates_dir = backend_dir / "templates"
        self.templates_dir.mkdir(exist_ok=True)
        
    def extract_variables_from_html(self, html_content: str) -> List[str]:
        """从HTML内容中提取变量"""
        # 匹配 {{variable_name}} 格式的变量
        pattern = r'\{\{(\w+)\}\}'
        variables = re.findall(pattern, html_content)
        return list(set(variables))  # 去重
    
    def extract_image_paths_from_html(self, html_content: str) -> List[str]:
        """从HTML内容中提取图片路径（包括本地路径和远程URL）"""
        # 匹配 <img src="..." > 标签中的src路径
        pattern = r'<img[^>]+src\s*=\s*["\']([^"\']+)["\'][^>]*>'
        image_paths = re.findall(pattern, html_content, re.IGNORECASE)
        
        # 过滤掉data URI和模板变量，但保留HTTP/HTTPS URL和本地路径
        valid_images = []
        for path in image_paths:
            # 跳过data URI和模板变量
            if not path.startswith('data:') and not (path.startswith('{{') and path.endswith('}}')):
                valid_images.append(path)
        
        return valid_images
    
    def download_remote_image(self, url: str, target_dir: Path) -> Optional[str]:
        """下载远程图片到本地目录"""
        try:
            # 解析URL获取文件名
            parsed_url = urlparse(url)
            filename = unquote(parsed_url.path.split('/')[-1])
            
            # 如果没有文件扩展名，尝试从Content-Type推断
            if '.' not in filename or filename == '':
                try:
                    response_head = requests.head(url, timeout=5)  # 减少HEAD请求超时
                    content_type = response_head.headers.get('content-type', '')
                except:
                    # 如果HEAD请求失败，使用默认文件名
                    content_type = ''
                    
                if 'image/png' in content_type:
                    filename = f"image_{hash(url) % 100000}.png"
                elif 'image/jpeg' in content_type or 'image/jpg' in content_type:
                    filename = f"image_{hash(url) % 100000}.jpg"
                elif 'image/gif' in content_type:
                    filename = f"image_{hash(url) % 100000}.gif"
                elif 'image/webp' in content_type:
                    filename = f"image_{hash(url) % 100000}.webp"
                else:
                    filename = f"image_{hash(url) % 100000}.png"  # 默认为PNG
            
            # 确保文件名安全
            filename = re.sub(r'[^\w\-_\.]', '_', filename)
            target_file = target_dir / filename
            
            # 创建目录
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 下载图片（优化超时和错误处理）
            try:
                response = requests.get(url, timeout=15, stream=True)  # 减少到15秒
                response.raise_for_status()
                
                with open(target_file, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.info(f"成功下载远程图片: {url} -> {target_file}")
                return filename
                
            except requests.exceptions.Timeout:
                logger.warning(f"下载图片超时: {url}")
                return None
            except requests.exceptions.RequestException as e:
                logger.warning(f"下载图片失败: {url}, 错误: {e}")
                return None
            
        except Exception as e:
            logger.error(f"下载远程图片失败 {url}: {e}")
            return None
    
    def copy_template_images(self, source_dir: str, template_id: str, image_paths: List[str]) -> Dict[str, str]:
        """复制模板图片资源到目标目录（支持本地文件和远程URL）"""
        template_images_dir = self.templates_dir / f"{template_id}_images"
        template_images_dir.mkdir(exist_ok=True)
        
        # 存储原始路径到新路径的映射
        path_mapping = {}
        
        for img_path in image_paths:
            try:
                new_path = None
                
                # 判断是远程URL还是本地路径
                if img_path.startswith(('http://', 'https://')):
                    # 处理远程URL
                    downloaded_filename = self.download_remote_image(img_path, template_images_dir / "remote")
                    if downloaded_filename:
                        new_path = f"{template_id}_images/remote/{downloaded_filename}"
                        path_mapping[img_path] = new_path
                        logger.info(f"远程图片已下载: {img_path} -> {new_path}")
                    else:
                        logger.warning(f"远程图片下载失败: {img_path}")
                
                elif img_path.startswith('//'):
                    # 处理协议相对URL（如 //example.com/image.png）
                    full_url = f"https:{img_path}"
                    downloaded_filename = self.download_remote_image(full_url, template_images_dir / "remote")
                    if downloaded_filename:
                        new_path = f"{template_id}_images/remote/{downloaded_filename}"
                        path_mapping[img_path] = new_path
                        logger.info(f"协议相对URL图片已下载: {img_path} -> {new_path}")
                    else:
                        logger.warning(f"协议相对URL图片下载失败: {img_path}")
                
                else:
                    # 处理本地路径
                    source_file = Path(source_dir) / img_path
                    
                    if source_file.exists():
                        # 保持目录结构
                        relative_path = Path(img_path)
                        target_file = template_images_dir / relative_path
                        
                        # 创建目标目录
                        target_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 复制文件
                        shutil.copy2(source_file, target_file)
                        
                        # 记录路径映射（相对于模板目录）
                        new_path = f"{template_id}_images/{img_path}"
                        path_mapping[img_path] = new_path
                        
                        logger.info(f"本地图片已复制: {source_file} -> {target_file}")
                    else:
                        logger.warning(f"本地图片文件不存在: {source_file}")
                    
            except Exception as e:
                logger.error(f"处理图片失败 {img_path}: {e}")
        
        return path_mapping
    
    def update_html_image_paths(self, html_content: str, path_mapping: Dict[str, str]) -> str:
        """更新HTML中的图片路径"""
        updated_html = html_content
        
        for old_path, new_path in path_mapping.items():
            # 替换src属性中的路径，保持img标签结构完整
            # 使用字符串替换，避免正则表达式的组引用问题
            old_pattern = f'src="{old_path}"'
            new_pattern = f'src="{new_path}"'
            updated_html = updated_html.replace(old_pattern, new_pattern)
            
            # 同时处理单引号情况
            old_pattern_single = f"src='{old_path}'"
            new_pattern_single = f"src='{new_path}'"
            updated_html = updated_html.replace(old_pattern_single, new_pattern_single)
        
        return updated_html
    
    def process_html_template(
        self, 
        html_file_path: str, 
        name: str,
        description: str = "",
        category: str = "custom"
    ) -> Dict[str, Any]:
        """处理HTML模板文件"""
        try:
            # 读取HTML文件
            with open(html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 生成模板ID
            template_id = str(uuid4())
            
            # 提取变量
            variables = self.extract_variables_from_html(html_content)
            
            # 提取图片路径
            image_paths = self.extract_image_paths_from_html(html_content)
            
            # 如果有图片，复制图片资源并更新路径
            if image_paths:
                source_dir = os.path.dirname(html_file_path)
                path_mapping = self.copy_template_images(source_dir, template_id, image_paths)
                html_content = self.update_html_image_paths(html_content, path_mapping)
                logger.info(f"处理了 {len(image_paths)} 个图片资源")
            
            # 生成模板数据
            template_data = {
                "id": template_id,
                "name": name,
                "description": description,
                "category": category,
                "html_content": html_content,
                "variables": variables,
                "width": 1920,  # 默认尺寸
                "height": 1080,
                "format": "png",
                "is_built_in": True,  # 系统默认模板
                "created_at": datetime.utcnow().isoformat()
            }
            
            return template_data
            
        except Exception as e:
            logger.error(f"处理HTML模板文件失败: {e}")
            raise
    
    def save_template_to_db(self, template_data: Dict[str, Any], db: Session) -> CoverTemplate:
        """保存模板到数据库"""
        try:
            # 创建数据库模板对象
            db_template = CoverTemplate(
                id=template_data["id"],
                name=template_data["name"],
                description=template_data["description"],
                category=template_data["category"],
                preview_path=f"templates/preview_{template_data['id']}.png",
                template_path=f"templates/{template_data['id']}.html",
                variables=template_data["variables"],
                is_built_in=template_data["is_built_in"],
                width=template_data["width"],
                height=template_data["height"],
                format=template_data["format"],
                elements=[],  # HTML模板不使用canvas元素
                background={},
                usage_count=0
            )
            
            # 保存HTML内容到文件
            template_file_path = self.templates_dir / f"{template_data['id']}.html"
            with open(template_file_path, 'w', encoding='utf-8') as f:
                f.write(template_data["html_content"])
            
            # 添加到数据库
            db.add(db_template)
            db.commit()
            db.refresh(db_template)
            
            logger.info(f"成功保存模板: {template_data['name']}")
            return db_template
            
        except Exception as e:
            db.rollback()
            logger.error(f"保存模板到数据库失败: {e}")
            raise
    
    def import_html_template(
        self,
        html_file_path: str,
        name: str,
        description: str = "",
        category: str = "custom",
        db: Optional[Session] = None
    ) -> CoverTemplate:
        """导入HTML模板"""
        should_close_db = False
        if db is None:
            db = get_db_session()
            should_close_db = True
        
        try:
            # 检查文件是否存在
            if not os.path.exists(html_file_path):
                raise FileNotFoundError(f"HTML文件不存在: {html_file_path}")
            
            # 检查是否已存在同名模板
            existing = db.query(CoverTemplate).filter(CoverTemplate.name == name).first()
            if existing:
                logger.warning(f"模板 '{name}' 已存在，跳过导入")
                return existing
            
            # 处理HTML模板
            template_data = self.process_html_template(
                html_file_path, name, description, category
            )
            
            # 保存到数据库
            db_template = self.save_template_to_db(template_data, db)
            
            return db_template
            
        finally:
            if should_close_db:
                db.close()
    
    def init_default_templates(self, db: Optional[Session] = None):
        """初始化默认模板"""
        should_close_db = False
        if db is None:
            db = get_db_session()
            should_close_db = True
        
        try:
            # 默认封面模板
            # 获取项目根目录下的reddit-template路径
            backend_dir = Path(__file__).parent.parent.parent
            project_root = backend_dir.parent
            social_post_template_path = project_root / "reddit-template" / "social_post_template.html"
            
            if social_post_template_path.exists():
                self.import_html_template(
                    html_file_path=str(social_post_template_path),
                    name="默认封面模板",
                    description="适用于各种社交媒体平台的帖子封面模板，支持头像、用户名、标题和描述变量",
                    category="社交媒体",
                    db=db
                )
                logger.info("成功导入默认封面模板")
            else:
                logger.warning(f"默认封面模板文件不存在: {social_post_template_path}")
            
            # 可以添加更多默认模板
            # self.import_html_template(...)
            
        except Exception as e:
            logger.error(f"初始化默认模板失败: {e}")
            raise
        finally:
            if should_close_db:
                db.close()
    
    def render_template(
        self, 
        template_id: str, 
        variables: Dict[str, str],
        db: Optional[Session] = None,
        base_url: str = "http://localhost:8000"
    ) -> str:
        """渲染模板"""
        should_close_db = False
        if db is None:
            db = get_db_session()
            should_close_db = True
        
        try:
            # 获取模板
            template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
            if not template:
                raise ValueError(f"模板不存在: {template_id}")
            
            # 读取HTML内容
            template_file_path = self.templates_dir / f"{template_id}.html"
            if not template_file_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_file_path}")
            
            with open(template_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 替换变量
            for var_name, var_value in variables.items():
                pattern = f'{{{{{var_name}}}}}'
                html_content = html_content.replace(pattern, var_value)
            
            # 转换图片路径为HTTP可访问的URL
            html_content = self.convert_image_paths_to_urls(html_content, template_id, base_url)
            
            return html_content
            
        finally:
            if should_close_db:
                db.close()
    
    def convert_image_paths_to_urls(self, html_content: str, template_id: str, base_url: str) -> str:
        """将模板中的本地图片路径转换为HTTP可访问的URL"""
        import re
        
        # 匹配模板专用图片路径
        pattern = rf'{template_id}_images/([^"\'\s]+)'
        
        def replace_path(match):
            relative_path = match.group(1)
            # 转换为HTTP URL
            # print(f"转换图片路径: {relative_path} -> {base_url}/templates/{template_id}_images/{relative_path}")
            return f"{base_url}/templates/{template_id}_images/{relative_path}"
        
        # 替换所有匹配的路径
        updated_html = re.sub(pattern, replace_path, html_content)
        
        return updated_html
    
    def list_template_variables(self, template_id: str, db: Optional[Session] = None) -> List[str]:
        """获取模板的变量列表"""
        should_close_db = False
        if db is None:
            db = get_db_session()
            should_close_db = True
        
        try:
            template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
            if not template:
                raise ValueError(f"模板不存在: {template_id}")
            
            # 确保返回类型正确
            variables = template.variables
            if isinstance(variables, list):
                return variables
            else:
                return []
            
        finally:
            if should_close_db:
                db.close()

# 创建全局实例
template_import_service = TemplateImportService()
