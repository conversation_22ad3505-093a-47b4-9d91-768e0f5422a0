@echo off
:: Reddit故事视频生成器 - 前后端完整联调验证脚本
echo ===============================================
echo   Reddit Story Video Generator 
echo   Full Integration Test
echo ===============================================
echo.

:: 设置工作目录
cd /d "%~dp0"

:: 检查环境
echo 🔍 Checking environment...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    pause
    exit /b 1
)

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    pause
    exit /b 1
)

echo ✅ Environment check passed
echo.

:: 1. 后端API集成测试
echo 📡 Step 1: Backend API Integration Test
echo ----------------------------------------
cd backend

echo 🔧 Installing backend dependencies...
pip install -r requirements.txt

echo 🧪 Running API integration tests...
python test_integration.py
if errorlevel 1 (
    echo ❌ Backend API tests failed
    echo Please check the test output above
    pause
    exit /b 1
)

echo 🔍 Running structure validation tests...
python test_structure.py
if errorlevel 1 (
    echo ❌ API structure validation failed
    echo Please check the test output above
    pause
    exit /b 1
)

echo ✅ Backend API tests passed
echo.

:: 2. 前端构建测试
echo 🎨 Step 2: Frontend Build Test
echo --------------------------------
cd ..\frontend

echo 📦 Installing frontend dependencies...
npm install

echo 🏗️ Building frontend...
npm run build
if errorlevel 1 (
    echo ❌ Frontend build failed
    pause
    exit /b 1
)

echo ✅ Frontend build successful
echo.

:: 3. 启动联调环境
echo 🚀 Step 3: Starting Integration Environment
echo ---------------------------------------------
cd ..

echo 🔥 Starting backend server...
start "Backend Server" cmd /k "cd backend && python start_server.py"

echo ⏳ Waiting for backend to start...
timeout /t 10 /nobreak

echo 🎯 Starting frontend development server...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo ⏳ Waiting for frontend to start...
timeout /t 10 /nobreak

:: 4. 运行联调测试
echo 🧪 Step 4: Running Integration Tests
echo ------------------------------------

echo 📋 Opening test checklist...
start "" "http://localhost:3000"
start "" "http://localhost:8000/docs"

echo.
echo ===============================================
echo   Integration Environment Ready!
echo ===============================================
echo 🎯 Frontend: http://localhost:3000
echo 📡 Backend:  http://localhost:8000
echo 📚 API Docs: http://localhost:8000/docs
echo.
echo 📋 Manual Test Checklist:
echo   1. Check if frontend loads without errors
echo   2. Test settings page functionality
echo   3. Test all resource management pages
echo   4. Verify data persistence
echo   5. Check console for API errors
echo.
echo Press any key to run automated integration tests...
pause

:: 5. 自动化联调测试
echo 🤖 Running automated integration tests...
cd backend
python -c "
import time
import requests
import sys

print('⏳ Waiting for servers to be ready...')
time.sleep(5)

# Test backend health
try:
    resp = requests.get('http://localhost:8000/health', timeout=5)
    if resp.status_code == 200:
        print('✅ Backend server is healthy')
    else:
        print('❌ Backend health check failed')
        sys.exit(1)
except Exception as e:
    print(f'❌ Backend connection failed: {e}')
    sys.exit(1)

# Test frontend
try:
    resp = requests.get('http://localhost:3000', timeout=5)
    if resp.status_code == 200:
        print('✅ Frontend server is accessible')
    else:
        print('❌ Frontend server check failed')
        sys.exit(1)
except Exception as e:
    print(f'❌ Frontend connection failed: {e}')
    sys.exit(1)

print('🎉 All servers are running and accessible!')
"

if errorlevel 1 (
    echo ❌ Automated integration tests failed
    pause
    exit /b 1
)

echo.
echo ===============================================
echo   Integration Test Complete!
echo ===============================================
echo 🎉 All tests passed successfully
echo 📝 Please verify manual test checklist
echo 🔄 Servers are still running for manual testing
echo.
echo Press any key to continue with manual testing...
pause

cd ..
