/**
 * 全局Loading组件
 * 显示在整个应用程序最顶层，阻止用户操作
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useGlobalLoadingStore } from '@/store/globalLoadingStore'

const GlobalLoadingOverlay: React.FC = () => {
  const { isGlobalLoading, getActiveTask, getTotalProgress } = useGlobalLoadingStore()
  const activeTask = getActiveTask()
  const totalProgress = getTotalProgress()
  const [elapsedTime, setElapsedTime] = useState(0)

  // 实时更新已执行时间
  useEffect(() => {
    if (!activeTask) return

    const timer = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - activeTask.startTime) / 1000))
    }, 1000)

    return () => clearInterval(timer)
  }, [activeTask])

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (!isGlobalLoading || !activeTask) {
    return null
  }

  return (
    <div
      className="fixed inset-0 z-[9999] bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300"
      style={{ 
        pointerEvents: 'all', // 确保阻止所有点击事件
        touchAction: 'none'   // 阻止触摸事件
      }}
    >
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 transform transition-all duration-300">
          {/* 任务图标 */}
          <div className="flex justify-center mb-6">
            <div className="relative">
              {/* 外圈旋转动画 */}
              <div className="w-16 h-16 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-spin" style={{ animationDuration: '2s' }}></div>
              {/* 内圈反向旋转动画 */}
              <div className="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-blue-500 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
              {/* 进度圈 */}
              <div className="absolute top-0 left-0 w-16 h-16">
                <svg className="transform -rotate-90 w-16 h-16">
                  <circle
                    cx="32"
                    cy="32"
                    r="28"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="transparent"
                    className="text-green-500"
                    strokeDasharray={`${2 * Math.PI * 28}`}
                    strokeDashoffset={`${2 * Math.PI * 28 * (1 - totalProgress / 100)}`}
                    style={{ transition: 'stroke-dashoffset 0.5s ease-in-out' }}
                  />
                </svg>
              </div>
              
              {/* 任务类型图标 */}
              <div className="absolute inset-0 flex items-center justify-center">
                {activeTask.type === 'upload' && (
                  <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                )}
                {activeTask.type === 'processing' && (
                  <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.78 0-2.678-2.153-1.415-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
                  </svg>
                )}
                {activeTask.type === 'general' && (
                  <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </div>
            </div>
          </div>

          {/* 任务标题 */}
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">
            {activeTask.title}
          </h3>

          {/* 任务描述 */}
          {activeTask.description && (
            <p className="text-gray-600 dark:text-gray-300 text-center mb-6 min-h-[1.5rem] transition-all duration-300">
              {activeTask.description}
            </p>
          )}

          {/* 进度信息 */}
          <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600 dark:text-gray-300">进度</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {totalProgress}%
              </span>
            </div>
            
            {/* 进度条 */}
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
              <div
                className="h-3 rounded-full relative overflow-hidden transition-all duration-500 ease-out bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700"
                style={{ width: `${totalProgress}%` }}
              >
                {/* 流动光效 */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-40 animate-shimmer"></div>
                {/* 跳动光点 */}
                <div className="absolute right-0 top-0 h-full w-1 bg-white opacity-80 animate-pulse"></div>
              </div>
            </div>

            {/* 详细信息 */}
            <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
              <span>已用时: {formatTime(elapsedTime)}</span>
              {activeTask.estimatedDuration && (
                <span>预估: {formatTime(activeTask.estimatedDuration)}</span>
              )}
            </div>
          </div>

          {/* 温馨提示 */}
          <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-100 dark:border-blue-800">
            <p className="text-sm text-blue-700 dark:text-blue-300 text-center">
              <svg className="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              请稍候，正在处理中，请勿关闭页面或进行其他操作
            </p>
          </div>

          {/* 装饰性动画点 */}
          <div className="flex justify-center mt-4 space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-loading-bounce" style={{ animationDelay: '0s' }}></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-loading-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-loading-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GlobalLoadingOverlay
