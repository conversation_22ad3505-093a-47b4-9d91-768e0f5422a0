#!/usr/bin/env python3
"""
初始化封面模板数据库数据
"""

import sqlite3
import json
import os
from datetime import datetime
import uuid

def init_cover_templates():
    print("=== 初始化封面模板数据 ===\n")
    
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), "backend", "reddit_story_generator.db")
    
    # 测试模板数据
    test_templates = [
        {
            'id': str(uuid.uuid4()),
            'name': '经典简约模板',
            'preview_path': 'covers/preview_classic.png',
            'template_path': 'covers/template_classic.json',
            'variables': json.dumps([
                {'id': 'title', 'name': '标题', 'type': 'text'},
                {'id': 'author', 'name': '作者', 'type': 'text'}
            ]),
            'is_built_in': True,
            'description': '简约风格的封面模板，适合大多数内容',
            'category': '经典',
            'tags': json.dumps(['简约', '通用', '经典']),
            'usage_count': 15,
            'width': 1920,
            'height': 1080,
            'format': 'png',
            'elements': json.dumps([
                {
                    'id': 'title-text',
                    'type': 'text',
                    'x': 100,
                    'y': 400,
                    'width': 800,
                    'height': 120,
                    'content': '视频标题',
                    'fontSize': 48,
                    'color': '#ffffff',
                    'textAlign': 'center',
                    'variableBinding': {
                        'enabled': True,
                        'variableName': 'title',
                        'propertyPath': 'content'
                    }
                },
                {
                    'id': 'author-text',
                    'type': 'text',
                    'x': 100,
                    'y': 600,
                    'width': 400,
                    'height': 60,
                    'content': '作者名称',
                    'fontSize': 24,
                    'color': '#cccccc',
                    'textAlign': 'left',
                    'variableBinding': {
                        'enabled': True,
                        'variableName': 'author',
                        'propertyPath': 'content'
                    }
                }
            ]),
            'background': json.dumps({
                'type': 'gradient',
                'value': {
                    'direction': 'to bottom right',
                    'colors': ['#667eea', '#764ba2']
                }
            })
        },
        {
            'id': str(uuid.uuid4()),
            'name': '现代科技模板',
            'preview_path': 'covers/preview_tech.png',
            'template_path': 'covers/template_tech.json',
            'variables': json.dumps([
                {'id': 'title', 'name': '标题', 'type': 'text'}
            ]),
            'is_built_in': True,
            'description': '现代科技风格，适合科技、编程类内容',
            'category': '科技',
            'tags': json.dumps(['科技', '现代', '编程']),
            'usage_count': 8,
            'width': 1920,
            'height': 1080,
            'format': 'png',
            'elements': json.dumps([
                {
                    'id': 'title-text',
                    'type': 'text',
                    'x': 200,
                    'y': 300,
                    'width': 1000,
                    'height': 150,
                    'content': '科技标题',
                    'fontSize': 56,
                    'color': '#00ff88',
                    'textAlign': 'left',
                    'variableBinding': {
                        'enabled': True,
                        'variableName': 'title',
                        'propertyPath': 'content'
                    }
                },
                {
                    'id': 'tech-shape',
                    'type': 'shape',
                    'x': 50,
                    'y': 50,
                    'width': 100,
                    'height': 100,
                    'shapeType': 'circle',
                    'backgroundColor': '#00ff88',
                    'borderWidth': 0
                }
            ]),
            'background': json.dumps({
                'type': 'gradient',
                'value': {
                    'direction': 'to bottom',
                    'colors': ['#0c0c0c', '#1a1a2e']
                }
            })
        },
        {
            'id': str(uuid.uuid4()),
            'name': '温暖日落模板',
            'preview_path': 'covers/preview_sunset.png',
            'template_path': 'covers/template_sunset.json',
            'variables': json.dumps([
                {'id': 'title', 'name': '标题', 'type': 'text'},
                {'id': 'subtitle', 'name': '副标题', 'type': 'text'}
            ]),
            'is_built_in': False,
            'description': '温暖的日落色彩，适合生活、旅行类内容',
            'category': '现代',
            'tags': json.dumps(['温暖', '日落', '生活']),
            'usage_count': 3,
            'width': 1920,
            'height': 1080,
            'format': 'png',
            'elements': json.dumps([
                {
                    'id': 'main-title',
                    'type': 'text',
                    'x': 150,
                    'y': 200,
                    'width': 900,
                    'height': 100,
                    'content': '主标题',
                    'fontSize': 42,
                    'color': '#ffffff',
                    'textAlign': 'center',
                    'variableBinding': {
                        'enabled': True,
                        'variableName': 'title',
                        'propertyPath': 'content'
                    }
                },
                {
                    'id': 'sub-title',
                    'type': 'text',
                    'x': 150,
                    'y': 320,
                    'width': 900,
                    'height': 60,
                    'content': '副标题',
                    'fontSize': 28,
                    'color': '#ffe4b3',
                    'textAlign': 'center',
                    'variableBinding': {
                        'enabled': True,
                        'variableName': 'subtitle',
                        'propertyPath': 'content'
                    }
                }
            ]),
            'background': json.dumps({
                'type': 'gradient',
                'value': {
                    'direction': 'to top',
                    'colors': ['#fa709a', '#fee140']
                }
            })
        }
    ]
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cover_templates';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("cover_templates 表不存在，创建表...")
            # 创建表的SQL（根据模型定义）
            create_table_sql = """
            CREATE TABLE cover_templates (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                preview_path TEXT NOT NULL,
                template_path TEXT NOT NULL,
                variables TEXT NOT NULL DEFAULT '[]',
                is_built_in BOOLEAN NOT NULL DEFAULT 0,
                description TEXT,
                category TEXT DEFAULT 'general',
                tags TEXT DEFAULT '[]',
                usage_count INTEGER DEFAULT 0,
                width INTEGER,
                height INTEGER,
                format TEXT,
                elements TEXT NOT NULL DEFAULT '[]',
                background TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            cursor.execute(create_table_sql)
            print("表创建成功")
        
        # 清空现有数据（如果有的话）
        cursor.execute("DELETE FROM cover_templates;")
        print("清空现有数据")
        
        # 插入测试数据
        insert_sql = """
        INSERT INTO cover_templates (
            id, name, preview_path, template_path, variables, is_built_in,
            description, category, tags, usage_count, width, height, format,
            elements, background
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        for i, template in enumerate(test_templates, 1):
            values = (
                template['id'],
                template['name'],
                template['preview_path'],
                template['template_path'],
                template['variables'],
                template['is_built_in'],
                template['description'],
                template['category'],
                template['tags'],
                template['usage_count'],
                template['width'],
                template['height'],
                template['format'],
                template['elements'],
                template['background']
            )
            cursor.execute(insert_sql, values)
            print(f"插入模板 {i}: {template['name']}")
        
        conn.commit()
        
        # 验证插入结果
        cursor.execute("SELECT COUNT(*) FROM cover_templates;")
        count = cursor.fetchone()[0]
        print(f"\n成功初始化 {count} 个模板")
        
        # 显示插入的模板信息
        cursor.execute("SELECT id, name, category, usage_count FROM cover_templates ORDER BY name;")
        templates = cursor.fetchall()
        
        print("\n已创建的模板:")
        for template in templates:
            print(f"  - ID: {template[0][:8]}... 名称: {template[1]} 分类: {template[2]} 使用次数: {template[3]}")
        
        print(f"\n数据库位置: {db_path}")
        print("初始化完成！现在可以测试前端API调用。")
        
    except Exception as e:
        print(f"初始化数据库时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    init_cover_templates()
