"""
数据库迁移脚本 - 添加视频分类表
"""

import sqlite3
import os
from pathlib import Path

def migrate_database():
    """执行数据库迁移，添加视频分类表"""
    
    # 数据库文件路径
    db_path = "D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"
    
    print(f"正在迁移数据库: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 检查视频分类表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='video_categories'")
        if cursor.fetchone():
            print("视频分类表已存在，跳过创建")
        else:
            # 创建视频分类表
            cursor.execute("""
                CREATE TABLE video_categories (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✓ 创建视频分类表成功")
            
            # 插入一些默认分类
            default_categories = [
                ('general', '通用分类', '默认的视频分类'),
                ('background', '背景视频', '用作背景的视频素材'),
                ('intro', '开场视频', '视频开场使用的素材'),
                ('outro', '结尾视频', '视频结尾使用的素材'),
                ('transition', '转场视频', '视频转场使用的素材')
            ]
            
            for name, desc, detail in default_categories:
                # 生成UUID
                import uuid
                category_id = str(uuid.uuid4())
                cursor.execute(
                    "INSERT INTO video_categories (id, name, description) VALUES (?, ?, ?)",
                    (category_id, name, desc)
                )
            
            print("✓ 插入默认分类成功")
        
        # 检查视频素材表是否存在分类字段
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='video_materials'")
        if cursor.fetchone():
            # 视频素材表存在，检查是否有分类字段
            cursor.execute("PRAGMA table_info(video_materials)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'category' not in columns:
                # 添加分类字段到视频素材表
                cursor.execute("ALTER TABLE video_materials ADD COLUMN category TEXT DEFAULT 'general'")
                print("✓ 视频素材表添加分类字段成功")
            else:
                print("视频素材表分类字段已存在")
        else:
            print("视频素材表不存在，将在后续创建时包含分类字段")
        
        # 提交更改
        conn.commit()
        print("✓ 数据库迁移完成")
        
    except Exception as e:
        print(f"✗ 数据库迁移失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
