*.db
frontend/.next
frontend/frontend-dist
frontend/node_modules
frontend/package-lock.json
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
backend/.env
backend/.env.local
backend/.env.development.local
backend/data/
debug_outputs/

# Python virtual environment
backend/venv/
venv/
backend/uploads/
backend/templates/
backend/test_outputs/

uploads/
temp/

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version
