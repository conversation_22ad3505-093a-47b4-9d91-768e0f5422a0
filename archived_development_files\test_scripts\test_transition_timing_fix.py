#!/usr/bin/env python3
"""
测试转场时间计算修复 - 验证不同时长的视频片段转场
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_variable_duration_videos():
    """创建不同时长的测试视频片段"""
    
    test_videos = []
    
    # 创建不同时长的视频片段（模拟真实场景）
    video_configs = [
        ('red', '#FF0000', 3.0),    # 3秒红色
        ('green', '#00FF00', 7.0),  # 7秒绿色  
        ('blue', '#0000FF', 4.0),   # 4秒蓝色
        ('yellow', '#FFFF00', 6.0)  # 6秒黄色
    ]
    
    durations = []
    
    for i, (name, color, duration) in enumerate(video_configs):
        output_path = f"test_var_{name}_{duration}s.mp4"
        
        if not Path(output_path).exists():
            logger.info(f"创建{duration}秒测试视频: {output_path}")
            
            # 创建指定时长的纯色视频
            (
                ffmpeg
                .input(f'color={color}:size=1080x1920:duration={duration}:rate=30', f='lavfi')
                .output(output_path, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
        
        test_videos.append(output_path)
        durations.append(duration)
    
    return test_videos, durations

def test_variable_duration_transitions():
    """测试不同时长视频片段的转场效果"""
    
    logger.info("🎬 开始测试不同时长视频片段的转场效果...")
    
    # 创建不同时长的测试视频
    test_videos, real_durations = create_variable_duration_videos()
    
    logger.info(f"测试视频片段: {test_videos}")
    logger.info(f"实际时长: {real_durations}")
    
    # 测试配置
    test_configs = [
        {
            'name': '修复后的淡入淡出转场',
            'transition_type': 'fade',
            'duration': 1.0,
            'output': 'fixed_fade_transition.mp4'
        },
        {
            'name': '修复后的溶解转场',
            'transition_type': 'dissolve',
            'duration': 1.5,
            'output': 'fixed_dissolve_transition.mp4'
        }
    ]
    
    success_count = 0
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
        logger.info(f"转场类型: {config['transition_type']}")
        logger.info(f"转场时长: {config['duration']}s")
        logger.info(f"视频片段时长: {real_durations}")
        logger.info(f"输出文件: {config['output']}")
        
        try:
            start_time = time.time()
            
            # 创建视频流
            streams = []
            for video_path in test_videos:
                stream = ffmpeg.input(video_path)
                streams.append(stream)
            
            logger.info(f"创建了 {len(streams)} 个视频流")
            
            # 应用修复后的转场效果
            final_stream = VideoCompositionService._create_video_with_transitions(
                streams, config['transition_type'], config['duration'], real_durations
            )
            
            # 输出视频
            out = ffmpeg.output(
                final_stream,
                config['output'],
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p'
            ).overwrite_output()
            
            # 执行FFmpeg命令
            import subprocess
            
            cmd = ffmpeg.compile(out)
            logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            try:
                # 等待最多90秒
                stdout, stderr = process.communicate(timeout=90)
                
                if process.returncode == 0:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功!")
                        logger.info(f"   文件大小: {file_size} bytes")
                        logger.info(f"   处理时间: {processing_time:.2f}秒")
                        
                        # 计算预期的总时长
                        total_duration = sum(real_durations)
                        transition_count = len(real_durations) - 1
                        expected_duration = total_duration - (transition_count * config['duration'])
                        
                        logger.info(f"   预期总时长: {expected_duration:.1f}秒 (原始{total_duration}s - {transition_count}个转场×{config['duration']}s)")
                        logger.info(f"   预期效果: 红(3s)→绿(7s)→蓝(4s)→黄(6s)，转场时重叠{config['duration']}s")
                        
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                else:
                    logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                    if stderr:
                        stderr_text = stderr.decode('utf-8', errors='ignore')
                        logger.error(f"stderr: {stderr_text[:500]}...")
                        
            except subprocess.TimeoutExpired:
                logger.error(f"❌ {config['name']} 测试超时（90秒）")
                process.kill()
                process.communicate()
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n=== 转场时间修复测试完成 ===")
    logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
    
    if success_count > 0:
        logger.info("\n📋 修复验证测试文件:")
        logger.info("- fixed_fade_transition.mp4 (修复后的淡入淡出)")
        logger.info("- fixed_dissolve_transition.mp4 (修复后的溶解转场)")
        
        logger.info("\n🔍 验证要点:")
        logger.info("1. 视频应该流畅播放，不会卡住")
        logger.info("2. 转场应该在正确的时间点发生")
        logger.info("3. 每个颜色段的时长应该符合预期")
        logger.info("4. 转场期间两个片段应该平滑过渡")
        
        logger.info("\n🎬 播放顺序:")
        logger.info("红色(3s) → [转场] → 绿色(7s) → [转场] → 蓝色(4s) → [转场] → 黄色(6s)")
        logger.info("如果播放流畅且转场自然，说明时间计算修复成功！")
        
        return True
    else:
        logger.error("❌ 所有转场时间修复测试都失败了")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_var_red_3.0s.mp4',
        'test_var_green_7.0s.mp4', 
        'test_var_blue_4.0s.mp4',
        'test_var_yellow_6.0s.mp4'
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            Path(file_path).unlink()
            logger.info(f"清理测试文件: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始转场时间计算修复验证")
    logger.info("测试不同时长的视频片段转场效果")
    
    try:
        success = test_variable_duration_transitions()
        
        if success:
            logger.info("\n🎉 转场时间修复验证完成!")
            logger.info("修复成功！现在转场应该在正确的时间点发生。")
        else:
            logger.error("\n❌ 转场时间修复验证失败")
            sys.exit(1)
            
    finally:
        # 清理测试文件
        cleanup_test_files()
