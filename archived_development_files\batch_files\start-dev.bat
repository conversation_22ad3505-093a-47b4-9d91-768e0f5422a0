@echo off
REM Reddit Story Video Generator - Development Startup Script for Windows

echo 🚀 Starting Reddit Story Video Generator Development Environment...

REM 检查 Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

REM 检查 Python
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.11+ first.
    pause
    exit /b 1
)

REM 设置环境变量
set ENVIRONMENT=development

echo 📦 Installing dependencies...

REM 安装前端依赖
echo Installing frontend dependencies...
cd frontend
if not exist "node_modules" (
    echo Running npm install...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install frontend dependencies
        pause
        exit /b 1
    )
) else (
    echo Frontend dependencies already installed
)
cd ..

REM 安装后端依赖
echo Installing backend dependencies...
cd backend
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

REM 激活虚拟环境并安装依赖
echo Activating virtual environment and installing packages...
call venv\Scripts\activate.bat
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo ✅ Dependencies installed successfully!

REM 启动服务
echo 🌟 Starting development servers...

REM 启动后端服务
echo Starting backend API server...
start "Backend API" cmd /k "cd /d "%~dp0backend" && call venv\Scripts\activate.bat && python main.py"

REM 等待后端启动
echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM 启动前端服务
echo Starting frontend dev server...
start "Frontend Dev" cmd /k "cd /d "%~dp0frontend" && npm run dev"

echo 🎉 Development environment started successfully!
echo Frontend: http://localhost:3000
echo Backend API: http://localhost:8000/docs
echo.
echo Press any key to continue or close this window to stop...
pause >nul
