我现在需要制作一个Reddit故事视频生成器。


# 技术架构要求

1. Next.js(TypeScript)  + Electron + sqlite3
2. 所有界面展示都由Next.js完成，并且要求可以打包成静态代码
3. 所有跟第三方API交互的，全部由Electron完成


# 大致的功能需求

## 系统设置

主要是：

1. 文字转语音的服务提供商相关验证信息，除去主流的厂家外，还可以支持本地部署的f5-tts api
2. 大模型的服务商和相关验证信息（OpenAI格式）



## 背景音乐管理

可以通过文件夹一次性导入一批背景音乐



## 视频素材管理

可以通过文件夹异性导入一批视频素材



## 提示词管理

可以管理提示词，提示词用途主要是给到大模型去生成故事脚本



## 账号名称管理

可以管理账号名称，可以批量创建、删除，用于后面的封面模板



## 封面模板管理

这里的封面模板指的是视频播放第一句话时长范围内，在视频中浮动显示的一张静态图片，如下图框选的部分，主要可定义的动态部分有账号头像、账号名称、故事简述。

![](https://fastly.jsdelivr.net/gh/bucketio/img5@main/2025/06/24/1750755051416-9aefbce1-2b87-475e-ba48-3c5b24af8387.png)



## 视频生成

固定9:16的画面比例，1080P。

### 输入条件

1. 语音服务
2. 大模型
3. 提示词
4. 语音倍速（可以从0.5x - 3x），默认1.5x
5. 视频素材（可多选，或者选“随机”自动编排）
6. 背景音乐（可选一个，或者选“随机”自动编排）
7. 封面模板（可选一个，或者选”随机“自动编排）
8. 字幕（可选一个，包括字体、大小、颜色[默认白色]）
9. 账号选择（可多选，可全选），以及每个账号生成视频个数
10. 输出目录（每个视频以这种格式命名：账号名_生日日期时间（yyyyMMddHHmmss）.mp4）

### 生成要求

根据账号数和每个账号视频数，自动创建执行队列，该队列自动保存到数据库，以及在生成前后自动更新进度和状态。对于每一个视频执行以下操作：

1. 根据提示词，调用大模型生成故事文案。
2. 调用语音服务将故事文案转成语音。
3. 根据语音长度（要结合语音倍速），随机选择视频素材（尽可能减少每个素材的重复度），如果视频素材不足，可以重复选择到满足时长为止
4. 根据语音，生成SRT字幕，需要足够精细，每一段只有最多3个单词或词语，标点符号可要可不要，但不要夸标点符号取词
5. 选择一个背景音乐
6. 生成封面，将账号名称和故事文案第一句话填充进去
7. 生成视频要求：故事文案第一句话，不需要再显示字幕，取而代之的是在对应时长范围内的视频画面添加静态封面浮层（垂直居中）；第二句话开始，根据SRT，结合字幕要求打印到画面垂直居中的位置；故事文案的语音作为主音轨，背景音乐作为辅音轨，最终生成一个完整视频。