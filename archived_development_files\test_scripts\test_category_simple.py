#!/usr/bin/env python3
"""
简单测试 - 检查后端是否正确接收category参数
"""

import requests
import os
import tempfile
from pathlib import Path

def create_test_video_file():
    """创建一个测试用的小文件"""
    # 创建一个临时文件模拟MP4
    temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
    temp_file.write(b'fake video content for testing')
    temp_file.close()
    return temp_file.name

def test_upload_with_category():
    """测试上传时category参数传递"""
    API_BASE = "http://localhost:8000"
    UPLOAD_URL = f"{API_BASE}/api/video-materials/upload"
    
    test_file_path = create_test_video_file()
    
    try:
        # 测试不同category值
        test_categories = ["general", "action", "comedy", "drama", "test-category"]
        
        for category in test_categories:
            print(f"\n测试分类: {category}")
            
            with open(test_file_path, "rb") as f:
                files = {"file": (f"test_{category}.mp4", f, "video/mp4")}
                data = {
                    "category": category,
                    "tags": "test,category-check"
                }
                
                try:
                    response = requests.post(UPLOAD_URL, files=files, data=data, timeout=10)
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"✓ 上传成功")
                        print(f"  服务器返回: {result}")
                    else:
                        print(f"✗ 上传失败: {response.status_code}")
                        print(f"  错误详情: {response.text}")
                        
                except requests.exceptions.RequestException as e:
                    print(f"✗ 请求失败: {e}")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)

def test_query_materials():
    """测试查询素材"""
    API_BASE = "http://localhost:8000"
    QUERY_URL = f"{API_BASE}/api/video-materials"
    
    print("\n查询所有素材:")
    try:
        response = requests.get(QUERY_URL, timeout=10)
        if response.status_code == 200:
            materials = response.json()
            print(f"✓ 查询成功，找到 {len(materials)} 个素材")
            
            for material in materials[:5]:  # 只显示前5个
                print(f"  - {material.get('name')} (分类: {material.get('category')})")
        else:
            print(f"✗ 查询失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 查询失败: {e}")

def check_server_status():
    """检查服务器状态"""
    API_BASE = "http://localhost:8000"
    
    try:
        response = requests.get(f"{API_BASE}/docs", timeout=5)
        if response.status_code == 200:
            print("✓ 后端服务器正在运行")
            return True
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("✗ 无法连接到后端服务器")
        print("请确保后端服务正在运行在 http://localhost:8000")
        return False

if __name__ == "__main__":
    print("开始检查分类上传功能...")
    
    if not check_server_status():
        print("\n请先启动后端服务器，然后重新运行此测试")
        exit(1)
    
    # 执行测试
    test_upload_with_category()
    
    # 查询结果
    test_query_materials()
    
    print("\n测试完成！")
