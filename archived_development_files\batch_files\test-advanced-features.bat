@echo off
echo ========================================
echo    封面模板管理高级功能测试套件
echo ========================================

echo.
echo 1. 检查依赖...
python -c "import playwright" >nul 2>&1
if errorlevel 1 (
    echo ❌ Playwright 未安装，正在安装...
    pip install playwright
    python -m playwright install chromium
)

echo.
echo 2. 启动后端服务...
start "Backend" cmd /k "cd backend && python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000"

echo.
echo 3. 等待后端启动...
timeout /t 5 /nobreak >nul

echo.
echo 4. 启动前端服务...
start "Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo 5. 等待前端启动...
timeout /t 10 /nobreak >nul

echo.
echo 6. 运行高级功能自动化测试...
python test_advanced_canvas_features.py

echo.
echo 测试完成！
pause
