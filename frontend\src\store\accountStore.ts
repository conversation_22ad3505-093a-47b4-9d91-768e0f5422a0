/**
 * 账号管理状态管理
 * 使用Zustand管理账号相关的状态和操作
 */

import { create } from 'zustand'
import { 
  Account, 
  AccountCreate, 
  AccountUpdate, 
  AccountList, 
  AccountStats, 
  AccountState,
  PlatformType,
  AccountStatus 
} from '../types/store'
import DirectHttpClient from '../lib/api/directHttpClient'

// 创建账号管理API客户端
const accountClient = new DirectHttpClient('/api/accounts')

export const useAccountStore = create<AccountState>((set, get) => ({
  accounts: [],
  selectedAccounts: [],
  currentAccount: null,
  stats: null,
  loading: false,

  // 加载账号列表
  loadAccounts: async (params = {}) => {
    set({ loading: true })
    try {
      const searchParams = new URLSearchParams()
      if (params.page) searchParams.append('page', params.page.toString())
      if (params.page_size) searchParams.append('page_size', params.page_size.toString())
      if (params.platform) searchParams.append('platform', params.platform)
      if (params.status) searchParams.append('status', params.status)
      if (params.search) searchParams.append('search', params.search)

      const data: AccountList = await accountClient.get<AccountList>(`/?${searchParams}`)
      set({ accounts: data.accounts, loading: false })
    } catch (error) {
      console.error('Failed to load accounts:', error)
      set({ loading: false })
      throw error
    }
  },

  // 加载账号统计
  loadAccountStats: async () => {
    try {
      const stats: AccountStats = await accountClient.get<AccountStats>('/stats')
      set({ stats })
    } catch (error) {
      console.error('Failed to load account stats:', error)
      throw error
    }
  },

  // 创建账号
  createAccount: async (account: AccountCreate) => {
    set({ loading: true })
    try {
      const newAccount: Account = await accountClient.post<Account>('/', account)
      
      // 更新本地状态
      set(state => ({
        accounts: [newAccount, ...state.accounts],
        loading: false
      }))

      return newAccount
    } catch (error) {
      set({ loading: false })
      throw error
    }
  },

  // 更新账号
  updateAccount: async (id: number, updates: AccountUpdate) => {
    set({ loading: true })
    try {
      const updatedAccount: Account = await accountClient.put<Account>(`/${id}`, updates)
      
      // 更新本地状态
      set(state => ({
        accounts: state.accounts.map(acc => 
          acc.id === id ? updatedAccount : acc
        ),
        currentAccount: state.currentAccount?.id === id ? updatedAccount : state.currentAccount,
        loading: false
      }))

      return updatedAccount
    } catch (error) {
      set({ loading: false })
      throw error
    }
  },

  // 删除账号
  deleteAccount: async (id: number) => {
    set({ loading: true })
    try {
      await accountClient.delete(`/${id}`)

      // 更新本地状态
      set(state => ({
        accounts: state.accounts.filter(acc => acc.id !== id),
        selectedAccounts: state.selectedAccounts.filter(accId => accId !== id),
        currentAccount: state.currentAccount?.id === id ? null : state.currentAccount,
        loading: false
      }))
    } catch (error) {
      set({ loading: false })
      throw error
    }
  },

  // 批量删除账号
  bulkDeleteAccounts: async (ids: number[]) => {
    set({ loading: true })
    try {
      await accountClient.post('/bulk/delete', ids)

      // 更新本地状态
      set(state => ({
        accounts: state.accounts.filter(acc => !ids.includes(acc.id)),
        selectedAccounts: [],
        currentAccount: ids.includes(state.currentAccount?.id || 0) ? null : state.currentAccount,
        loading: false
      }))
    } catch (error) {
      set({ loading: false })
      throw error
    }
  },

  // 批量更新状态
  bulkUpdateStatus: async (ids: number[], status: AccountStatus) => {
    set({ loading: true })
    try {
      await accountClient.post('/bulk/status', { account_ids: ids, status })

      // 更新本地状态
      set(state => ({
        accounts: state.accounts.map(acc => 
          ids.includes(acc.id) ? { ...acc, status } : acc
        ),
        loading: false
      }))
    } catch (error) {
      set({ loading: false })
      throw error
    }
  },

  // 上传头像
  uploadAvatar: async (id: number, file: File) => {
    set({ loading: true })
    try {
      const formData = new FormData()
      formData.append('file', file)

      const result = await accountClient.uploadFile<{avatar_url: string}>(`/${id}/avatar`, formData)
      
      // 更新本地状态
      set(state => ({
        accounts: state.accounts.map(acc => 
          acc.id === id ? { ...acc, avatar_url: result.avatar_url } : acc
        ),
        currentAccount: state.currentAccount?.id === id 
          ? { ...state.currentAccount, avatar_url: result.avatar_url } 
          : state.currentAccount,
        loading: false
      }))

      return result.avatar_url
    } catch (error) {
      set({ loading: false })
      throw error
    }
  },

  // 使用账号（标记为已使用）
  useAccount: async (id: number) => {
    try {
      const updatedAccount: Account = await accountClient.post<Account>(`/${id}/use`)
      
      // 更新本地状态
      set(state => ({
        accounts: state.accounts.map(acc => 
          acc.id === id ? updatedAccount : acc
        ),
        currentAccount: state.currentAccount?.id === id ? updatedAccount : state.currentAccount
      }))

      return updatedAccount
    } catch (error) {
      throw error
    }
  },

  // 设置选中的账号
  setSelectedAccounts: (ids: number[]) => {
    set({ selectedAccounts: ids })
  },

  // 设置当前账号
  setCurrentAccount: (account: Account | null) => {
    set({ currentAccount: account })
  },

  // 清空状态
  clearState: () => {
    set({
      accounts: [],
      selectedAccounts: [],
      currentAccount: null,
      stats: null,
      loading: false
    })
  }
}))
