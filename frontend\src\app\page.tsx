/**
 * 首页仪表板 - 按照原型设计实现
 */

'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { ExtensionProtection } from '../components/ExtensionProtection'

export default function HomePage() {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [stats, setStats] = useState({
    totalTasks: 15,
    pendingTasks: 0,
    runningTasks: 0,
    completedTasks: 15,
    todayGenerated: 3,
    thisWeekGenerated: 8
  })

  useEffect(() => {
    const timer = setTimeout(() => {
      setMounted(true)
    }, 50)
    return () => clearTimeout(timer)
  }, [])

  if (!mounted) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        minHeight: '400px' 
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ 
            width: '32px', 
            height: '32px', 
            border: '3px solid var(--border-primary)',
            borderTop: '3px solid var(--theme-primary)',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: 'var(--text-secondary)' }}>加载中...</p>
        </div>
      </div>
    )
  }

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  return (
    <>
      <ExtensionProtection />
      
      {/* 页面样式 */}
      <style jsx>{`
        .dashboard-container {
          max-width: 1200px;
          margin: 0 auto;
        }

        .welcome-section {
          margin-bottom: 32px;
        }

        .welcome-title {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
          color: var(--text-primary);
        }

        .welcome-subtitle {
          font-size: 16px;
          color: var(--text-secondary);
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
          gap: 20px;
          margin-bottom: 32px;
        }

        .stat-card {
          background: var(--bg-secondary);
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--border-primary);
          transition: all 0.2s;
        }

        .stat-card:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .stat-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
        }

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .stat-icon.primary {
          background: var(--theme-primary-light);
          color: var(--theme-primary);
        }

        .stat-icon.success {
          background: #dcfce7;
          color: #166534;
        }

        .stat-icon.warning {
          background: #fef3c7;
          color: #d97706;
        }

        .stat-icon.info {
          background: #e0f2fe;
          color: #0277bd;
        }

        .stat-value {
          font-size: 32px;
          font-weight: 700;
          color: var(--text-primary);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--text-secondary);
          margin-bottom: 8px;
        }

        .stat-trend {
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .stat-trend.positive {
          color: #166534;
        }

        .stat-trend.neutral {
          color: var(--text-secondary);
        }

        .content-grid {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 24px;
          margin-bottom: 32px;
        }

        .quick-actions {
          background: var(--bg-secondary);
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--border-primary);
        }

        .section-title {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 16px;
          color: var(--text-primary);
        }

        .action-buttons {
          display: grid;
          gap: 12px;
        }

        .action-btn {
          display: flex;
          align-items: center;
          padding: 16px;
          background: var(--bg-tertiary);
          border: 1px solid var(--border-primary);
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s;
          text-decoration: none;
        }

        .action-btn:hover {
          background: var(--theme-primary-light);
          border-color: var(--theme-primary);
          color: var(--theme-primary);
        }

        .action-btn.primary {
          background: var(--theme-primary);
          color: white;
          border-color: var(--theme-primary);
        }

        .action-btn.primary:hover {
          background: var(--theme-primary-hover);
        }

        .action-icon {
          width: 20px;
          height: 20px;
          margin-right: 12px;
          fill: currentColor;
        }

        .action-text {
          flex: 1;
        }

        .action-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 2px;
        }

        .action-desc {
          font-size: 12px;
          opacity: 0.8;
        }

        .recent-activity {
          background: var(--bg-secondary);
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--border-primary);
        }

        .activity-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .activity-item {
          display: flex;
          align-items: center;
          padding: 12px;
          background: var(--bg-tertiary);
          border-radius: 8px;
        }

        .activity-status {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .activity-status.success {
          background: #10b981;
        }

        .activity-status.pending {
          background: #f59e0b;
        }

        .activity-content {
          flex: 1;
        }

        .activity-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: 2px;
        }

        .activity-time {
          font-size: 12px;
          color: var(--text-secondary);
        }

        .system-status {
          background: var(--bg-secondary);
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--border-primary);
        }

        .status-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        }

        .status-item {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }

        .status-dot.online {
          background: #10b981;
        }

        .status-dot.offline {
          background: #ef4444;
        }

        .status-text {
          font-size: 14px;
          color: var(--text-tertiary);
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
          .content-grid {
            grid-template-columns: 1fr;
          }

          .stats-grid {
            grid-template-columns: 1fr;
          }

          .status-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>

      <div className="dashboard-container">
        {/* 欢迎区域 */}
        <div className="welcome-section">
          <h1 className="welcome-title">欢迎使用 Reddit 故事视频生成器（别看，本页数据假的）</h1>
          <p className="welcome-subtitle">
            使用 AI 技术自动生成引人入胜的 Reddit 故事视频，支持智能配音、字幕和专业视频编辑
          </p>
        </div>

        {/* 统计数据 */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon primary">
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <div className="stat-value">{stats.totalTasks}</div>
            <div className="stat-label">总任务数</div>
            <div className="stat-trend neutral">
              全部视频生成任务
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon warning">
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <div className="stat-value">{stats.runningTasks}</div>
            <div className="stat-label">处理中</div>
            <div className="stat-trend neutral">
              正在生成的任务
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon success">
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <div className="stat-value">{stats.completedTasks}</div>
            <div className="stat-label">已完成</div>
            <div className="stat-trend positive">
              <svg width="12" height="12" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd"/>
              </svg>
              成功率 100%
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon info">
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <div className="stat-value">{stats.todayGenerated}</div>
            <div className="stat-label">今日生成</div>
            <div className="stat-trend positive">
              本周共 {stats.thisWeekGenerated} 个
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="content-grid">
          {/* 快速操作 */}
          <div className="quick-actions">
            <h2 className="section-title">快速操作</h2>
            <div className="action-buttons">
              <div 
                className="action-btn primary"
                onClick={() => handleNavigation('/generate')}
              >
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd"/>
                </svg>
                <div className="action-text">
                  <div className="action-title">创建新任务</div>
                  <div className="action-desc">开始生成新的 Reddit 故事视频</div>
                </div>
              </div>

              <div
                className="action-btn"
                onClick={() => handleNavigation('/batch-generate')}
              >
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm8 8a1 1 0 01-1 1H9a1 1 0 01-1-1V9a1 1 0 011-1h2a1 1 0 011 1v4zM7 9a1 1 0 000 2h.01a1 1 0 100-2H7zm0 4a1 1 0 100 2h.01a1 1 0 100-2H7zm4 0a1 1 0 100 2h.01a1 1 0 100-2H11z" clipRule="evenodd"/>
                </svg>
                <div className="action-text">
                  <div className="action-title">批量生成视频</div>
                  <div className="action-desc">基于Excel文案列表批量生成</div>
                </div>
              </div>

              <div
                className="action-btn"
                onClick={() => handleNavigation('/tasks')}
              >
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd"/>
                </svg>
                <div className="action-text">
                  <div className="action-title">查看任务队列</div>
                  <div className="action-desc">管理和监控视频生成进度</div>
                </div>
              </div>

              <div 
                className="action-btn"
                onClick={() => handleNavigation('/resources/music')}
              >
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                </svg>
                <div className="action-text">
                  <div className="action-title">管理背景音乐</div>
                  <div className="action-desc">上传和组织背景音乐文件</div>
                </div>
              </div>

              <div 
                className="action-btn"
                onClick={() => handleNavigation('/settings')}
              >
                <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
                </svg>
                <div className="action-text">
                  <div className="action-title">系统设置</div>
                  <div className="action-desc">配置 TTS 和 LLM 服务</div>
                </div>
              </div>
            </div>
          </div>

          {/* 最近活动 */}
          <div className="recent-activity">
            <h2 className="section-title">最近活动</h2>
            <div className="activity-list">
              <div className="activity-item">
                <div className="activity-status success"></div>
                <div className="activity-content">
                  <div className="activity-title">视频生成完成</div>
                  <div className="activity-time">2 分钟前</div>
                </div>
              </div>
              <div className="activity-item">
                <div className="activity-status success"></div>
                <div className="activity-content">
                  <div className="activity-title">TTS 配置更新</div>
                  <div className="activity-time">1 小时前</div>
                </div>
              </div>
              <div className="activity-item">
                <div className="activity-status success"></div>
                <div className="activity-content">
                  <div className="activity-title">背景音乐上传</div>
                  <div className="activity-time">3 小时前</div>
                </div>
              </div>
              <div className="activity-item">
                <div className="activity-status success"></div>
                <div className="activity-content">
                  <div className="activity-title">提示词模板创建</div>
                  <div className="activity-time">昨天</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 系统状态 */}
        <div className="system-status">
          <h2 className="section-title">系统状态</h2>
          <div className="status-grid">
            <div className="status-item">
              <div className="status-dot online"></div>
              <span className="status-text">TTS 服务: 正常</span>
            </div>
            <div className="status-item">
              <div className="status-dot online"></div>
              <span className="status-text">LLM 服务: 正常</span>
            </div>
            <div className="status-item">
              <div className="status-dot online"></div>
              <span className="status-text">视频渲染: 正常</span>
            </div>
            <div className="status-item">
              <div className="status-dot online"></div>
              <span className="status-text">存储空间: 充足</span>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
