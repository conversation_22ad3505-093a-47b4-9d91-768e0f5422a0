"""
统一响应格式
"""

from typing import Any, Optional, Dict, Generic, TypeVar
from fastapi import Request
from fastapi.responses import JSONResponse
from datetime import datetime
import uuid
from pydantic import BaseModel

T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    """API响应基类"""
    success: bool
    data: Optional[T] = None
    message: str
    timestamp: str
    requestId: str
    
    @classmethod
    def create_success(
        cls,
        data: Optional[T] = None,
        message: str = "操作成功",
        request_id: Optional[str] = None
    ) -> "ApiResponse[T]":
        """创建成功响应"""
        return cls(
            success=True,
            data=data,
            message=message,
            timestamp=datetime.utcnow().isoformat() + "Z",
            requestId=request_id or str(uuid.uuid4())
        )
    
    @classmethod
    def create_error(
        cls,
        message: str = "操作失败",
        error_code: str = "UNKNOWN_ERROR",
        data: Optional[T] = None,
        request_id: Optional[str] = None
    ) -> "ApiResponse[T]":
        """创建错误响应"""
        return cls(
            success=False,
            data=data,
            message=message,
            timestamp=datetime.utcnow().isoformat() + "Z",
            requestId=request_id or str(uuid.uuid4())
        )

# 保持向后兼容的旧版响应类
class APIResponseUtils:
    """统一API响应格式工具类"""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "操作成功",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """成功响应"""
        return {
            "success": True,
            "data": data,
            "message": message,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "requestId": request_id or str(uuid.uuid4())
        }
    
    @staticmethod
    def error(
        message: str = "操作失败",
        error: str = "UNKNOWN_ERROR",
        details: Any = None,
        status_code: int = 400,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """错误响应"""
        return {
            "success": False,
            "error": error,
            "message": message,
            "details": details,
            "status": status_code,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "requestId": request_id or str(uuid.uuid4())
        }

def success_response(
    data: Any = None,
    message: str = "操作成功",
    request: Optional[Request] = None
) -> JSONResponse:
    """创建成功响应"""
    request_id = getattr(request.state, 'request_id', None) if request else None
    content = APIResponseUtils.success(data, message, request_id)
    return JSONResponse(content=content, status_code=200)

def error_response(
    message: str = "操作失败",
    error: str = "UNKNOWN_ERROR",
    details: Any = None,
    status_code: int = 400,
    request: Optional[Request] = None
) -> JSONResponse:
    """创建错误响应"""
    request_id = getattr(request.state, 'request_id', None) if request else None
    content = APIResponseUtils.error(message, error, details, status_code, request_id)
    return JSONResponse(content=content, status_code=status_code)
