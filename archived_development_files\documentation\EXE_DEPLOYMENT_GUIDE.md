# Reddit Story Video Generator - EXE 部署指南

## 概述

本项目已经配置为支持轻量级 EXE 打包部署，将前端（Next.js）和后端（FastAPI）打包成一个独立的可执行文件。

## 功能特性

- ✅ 前端静态文件集成到后端
- ✅ 自动路径适配（开发/生产环境）
- ✅ CORS 配置自动化
- ✅ FFmpeg 路径自动解析
- ✅ 数据库路径相对化
- ✅ 静态文件服务（SPA 支持）
- ✅ 环境特定配置文件

## 目录结构

```
RedditStoryVideoGenerator/
├── build_exe.bat              # 完整构建脚本
├── build_frontend.bat         # 前端构建脚本（测试用）
├── test_production.bat        # 生产配置测试脚本
├── frontend/
│   ├── .env.production        # 前端生产环境配置
│   ├── next.config.js         # 支持静态导出
│   └── package.json           # 新增构建脚本
└── backend/
    ├── .env.production        # 后端生产环境配置
    ├── build.spec             # PyInstaller 配置
    ├── main.py                # 支持静态文件服务
    ├── src/core/config.py     # 路径自动适配
    ├── frontend_dist/         # 前端构建输出（自动生成）
    └── tools/                 # FFmpeg 等工具目录
```

## 构建步骤

### 1. 环境准备

确保已安装以下工具：
- Python 3.8+
- Node.js 16+
- Git

### 2. 克隆项目并安装依赖

```bash
# 安装后端依赖
cd backend
pip install -r requirements.txt

# 安装前端依赖
cd ../frontend
npm install
```

### 3. 准备 FFmpeg

下载 FFmpeg 并放置在 `backend/tools/ffmpeg.exe`：
- 访问 https://ffmpeg.org/download.html
- 下载 Windows 版本
- 解压并复制 `ffmpeg.exe` 到 `backend/tools/` 目录

### 4. 配置 API 密钥

编辑 `backend/.env.production`，配置必要的 API 密钥：

```env
# AI Service Configuration
OPENAI_API_KEY=your_openai_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here
AZURE_OPENAI_API_KEY=your_azure_api_key_here

# 其他配置项已预设好，通常不需要修改
```

### 5. 执行构建

运行主构建脚本：

```bash
build_exe.bat
```

这个脚本会：
1. 检查依赖项
2. 安装 Python 包
3. 构建前端静态文件
4. 复制前端文件到后端
5. 使用 PyInstaller 打包成 EXE

### 6. 测试构建

在构建前，可以测试生产配置：

```bash
# 仅构建前端（快速测试）
build_frontend.bat

# 测试生产配置
test_production.bat
```

## 部署文件

构建成功后，会在 `backend/dist/` 目录下生成：

```
backend/dist/
└── RedditStoryVideoGenerator.exe    # 主可执行文件
```

## 运行应用

1. 复制 `RedditStoryVideoGenerator.exe` 到目标机器
2. 双击运行，或在命令行中执行
3. 打开浏览器访问 `http://localhost:8000`

## 配置说明

### 后端配置 (.env.production)

```env
# 环境设置
ENVIRONMENT=production
DEBUG=false

# API 配置
API_HOST=0.0.0.0
API_PORT=8000

# 安全配置（生产环境请修改）
SECRET_KEY=change-this-secret-key-in-production-environment

# 文件路径（相对于 EXE 文件）
DATABASE_URL=sqlite:///./reddit_story_generator.db
UPLOAD_DIR=./uploads
FFMPEG_PATH=./tools/ffmpeg.exe
FRONTEND_STATIC_DIR=./frontend_dist

# CORS 配置（EXE 模式下允许所有源）
CORS_ORIGINS=*
CORS_ALLOW_CREDENTIALS=true
```

### 前端配置 (.env.production)

```env
# API 地址（指向同一主机）
NEXT_PUBLIC_API_URL=http://localhost:8000/api

# 生产环境设置
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
```

## 架构说明

### 路径适配机制

`backend/src/core/config.py` 中的 `Settings` 类提供自动路径适配：

- `get_app_base_dir()`: 检测是否在 EXE 环境中，返回正确的基础目录
- `resolved_*` 属性: 自动解析相对路径为绝对路径
- 开发环境: 使用项目目录结构
- EXE 环境: 使用可执行文件所在目录

### 静态文件服务

后端 `main.py` 提供完整的静态文件服务：

- `/static/*`: 前端静态资源
- `/templates/*`: 后端模板资源
- `/`: 前端首页
- `/*`: SPA 路由回退支持

### CORS 配置

- 开发环境: 限制为 localhost:3000
- 生产环境: 允许所有源（用于 EXE 部署）

## 故障排除

### 1. PyInstaller 构建失败

```bash
# 清理并重新构建
cd backend
pyinstaller build.spec --clean --noconfirm
```

### 2. 前端构建失败

```bash
# 清理 Node.js 缓存
cd frontend
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
npm run build:production
```

### 3. FFmpeg 路径问题

确保 FFmpeg 文件路径正确：
- 开发: 系统 PATH 中或绝对路径
- 生产: `backend/tools/ffmpeg.exe`

### 4. 数据库权限问题

确保 EXE 运行目录有写权限，或将数据库放在用户文档目录。

### 5. 端口被占用

修改 `.env.production` 中的 `API_PORT` 为其他端口（如 8001）。

## 高级配置

### 自定义图标

编辑 `backend/build.spec`，在 EXE 部分添加：

```python
icon='path/to/your/icon.ico'
```

### 窗口模式

编辑 `backend/build.spec`，修改：

```python
console=False  # 无控制台窗口
```

### 优化包大小

在 `build.spec` 的 `excludes` 中添加不需要的包：

```python
excludes=['tkinter', 'matplotlib', 'pandas', 'numpy', 'opencv-python']
```

## 版本历史

- v1.0: 初始 EXE 打包支持
  - 前端静态文件集成
  - 路径自动适配
  - CORS 自动配置
  - PyInstaller 打包脚本

## 注意事项

1. **安全**: 生产环境请务必修改 `SECRET_KEY`
2. **API 密钥**: 不要在源代码中包含真实的 API 密钥
3. **FFmpeg**: 确保有合法的 FFmpeg 使用许可
4. **防火墙**: 某些防火墙可能阻止 EXE 网络访问
5. **杀毒软件**: 可能将打包的 EXE 误报为病毒

## 支持

如有问题，请检查：
1. 构建日志输出
2. EXE 运行时的控制台输出
3. 浏览器开发者工具的网络请求
4. 后端日志文件
