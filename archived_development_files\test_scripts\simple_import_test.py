#!/usr/bin/env python3
"""
简化的模板导入测试
"""

import requests
import json
from pathlib import Path

def test_simple_import():
    """简单的导入测试"""
    print("🧪 简化测试开始...")
    
    backend_url = "http://localhost:8000"
    template_file = Path("reddit-template/social_post_template.html")
    
    if not template_file.exists():
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        # 测试健康检查
        print("🔍 测试后端连接...")
        health_response = requests.get(f"{backend_url}/health", timeout=5)
        print(f"健康检查状态: {health_response.status_code}")
        if health_response.status_code == 200:
            print("✅ 后端连接正常")
        else:
            print("❌ 后端连接异常")
            return False
        
        # 读取模板文件
        with open(template_file, 'rb') as f:  # 使用二进制模式
            file_content = f.read()
        
        # 准备请求数据
        files = {
            'file': (template_file.name, file_content, 'text/html')
        }
        data = {
            'name': 'Redis测试模板',
            'description': '简化测试',
            'category': '社交媒体'
        }
        
        print("🚀 发送导入请求...")
        response = requests.post(
            f"{backend_url}/api/cover-templates/import-html",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📄 响应内容前500字符: {response.text[:500]}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 导入成功!")
                print(f"📊 响应格式检查:")
                print(f"  - success: {result.get('success')}")
                print(f"  - message: {result.get('message')}")
                print(f"  - data存在: {'data' in result}")
                
                if result.get('success'):
                    print("🎉 前端应该能正确处理这个响应")
                    return True
                else:
                    print("❌ success字段为false")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误详情: {response.text}")
        
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    test_simple_import()
