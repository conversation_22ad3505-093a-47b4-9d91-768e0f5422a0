"""
测试新建模板的404修复
验证新建模板流程：创建模板 -> 保存到后端 -> 进入编辑器 -> 成功加载
"""

import requests
import json
import time

def test_new_template_workflow():
    """测试完整的新建模板工作流程"""
    
    base_url = "http://localhost:3001"
    backend_url = "http://localhost:8000"
    
    print("🧪 测试新建模板工作流程")
    print("=" * 50)
    
    # 1. 测试后端API可用性
    print("1. 检查后端API可用性...")
    try:
        response = requests.get(f"{backend_url}/api/cover-templates")
        print(f"   ✅ 后端API响应: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 后端API连接失败: {e}")
        return False
    
    # 2. 创建新模板（模拟前端的POST请求）
    print("\n2. 创建新模板...")
    template_data = {
        "name": "测试模板 - 新建流程",
        "category": "测试",
        "description": "用于测试新建模板404修复的模板",
        "elements": [],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        }
    }
    
    try:
        response = requests.post(
            f"{backend_url}/api/cover-templates",
            json=template_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            saved_template = response.json()
            template_id = saved_template["id"]
            print(f"   ✅ 模板创建成功: ID={template_id}, 名称={saved_template['name']}")
        else:
            print(f"   ❌ 模板创建失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 创建模板请求失败: {e}")
        return False
    
    # 3. 模拟编辑器加载模板（模拟进入编辑器后的GET请求）
    print(f"\n3. 模拟编辑器加载模板 ID={template_id}...")
    try:
        response = requests.get(f"{backend_url}/api/cover-templates/{template_id}")
        
        if response.status_code == 200:
            loaded_template = response.json()
            print(f"   ✅ 模板加载成功: {loaded_template['name']}")
            print(f"   📋 模板数据: elements={len(loaded_template.get('elements', []))}个, background={loaded_template.get('background', {}).get('type', 'N/A')}")
        else:
            print(f"   ❌ 模板加载失败: {response.status_code} - {response.text}")
            print("   🚨 这说明404问题仍然存在！")
            return False
            
    except Exception as e:
        print(f"   ❌ 加载模板请求失败: {e}")
        return False
    
    # 4. 测试模板更新（模拟编辑器保存）
    print(f"\n4. 测试模板更新...")
    update_data = {
        "name": loaded_template["name"],
        "category": loaded_template["category"],
        "description": loaded_template.get("description", ""),
        "elements": [
            {
                "id": "test-element-1",
                "type": "text",
                "x": 100,
                "y": 50,
                "content": "测试文本",
                "fontSize": 24,
                "color": "#000000"
            }
        ],
        "background": loaded_template["background"]
    }
    
    try:
        response = requests.put(
            f"{backend_url}/api/cover-templates/{template_id}",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            updated_template = response.json()
            print(f"   ✅ 模板更新成功: elements={len(updated_template.get('elements', []))}个")
        else:
            print(f"   ❌ 模板更新失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"   ❌ 更新模板请求失败: {e}")
    
    # 5. 清理测试数据
    print(f"\n5. 清理测试数据...")
    try:
        response = requests.delete(f"{backend_url}/api/cover-templates/{template_id}")
        if response.status_code == 200:
            print(f"   ✅ 测试模板已删除")
        else:
            print(f"   ⚠️ 删除测试模板失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ⚠️ 删除请求失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 新建模板工作流程测试完成！")
    print("✅ 修复成功: 新建模板 -> 保存到后端 -> 编辑器加载 -> 无404错误")
    return True

def test_edge_cases():
    """测试边界情况"""
    
    backend_url = "http://localhost:8000"
    
    print("\n🔍 测试边界情况")
    print("=" * 30)
    
    # 测试不存在的模板ID
    print("1. 测试不存在的模板ID...")
    try:
        response = requests.get(f"{backend_url}/api/cover-templates/99999")
        if response.status_code == 404:
            print("   ✅ 正确返回404 (模板不存在)")
        else:
            print(f"   ⚠️ 意外响应: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试无效的创建数据
    print("\n2. 测试无效的创建数据...")
    try:
        response = requests.post(
            f"{backend_url}/api/cover-templates",
            json={"invalid": "data"},
            headers={"Content-Type": "application/json"}
        )
        print(f"   📋 无效数据响应: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试新建模板404修复")
    print("请确保后端服务正在运行 (python backend/main.py)")
    print("请确保前端服务正在运行 (npm run dev)")
    print()
    
    success = test_new_template_workflow()
    if success:
        test_edge_cases()
        print("\n🎯 所有测试完成！")
        print("💡 建议: 现在可以在浏览器中测试前端界面")
        print("   1. 访问 http://localhost:3000/covers")
        print("   2. 点击'新建模板'")
        print("   3. 填写信息并创建")
        print("   4. 观察是否成功进入编辑器（无404错误）")
    else:
        print("\n❌ 测试失败，请检查代码修复")
