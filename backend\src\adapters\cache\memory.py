"""
Memory cache adapter implementation
"""

import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from loguru import logger

from ..base import CacheAdapter


class MemoryCacheAdapter(CacheAdapter):
    """In-memory cache adapter implementation"""
    
    def __init__(self):
        self._cache: Dict[str, Tuple[str, Optional[datetime]]] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[str]:
        """Get value by key"""
        async with self._lock:
            if key in self._cache:
                value, expire_time = self._cache[key]
                if expire_time is None or expire_time > datetime.now():
                    return value
                else:
                    # Key has expired, remove it
                    del self._cache[key]
            return None
    
    async def set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Set key-value pair with optional TTL"""
        async with self._lock:
            expire_time = None
            if ttl:
                expire_time = datetime.now() + timedelta(seconds=ttl)
            self._cache[key] = (value, expire_time)
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete key"""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        value = await self.get(key)
        return value is not None
    
    async def clear(self) -> bool:
        """Clear all cache"""
        async with self._lock:
            self._cache.clear()
            logger.info("Memory cache cleared")
            return True
    
    def _cleanup_expired(self) -> None:
        """Remove expired keys (called periodically)"""
        now = datetime.now()
        expired_keys = [
            key for key, (_, expire_time) in self._cache.items()
            if expire_time and expire_time <= now
        ]
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache keys")
