@echo off
echo 启动简化画布编辑器测试...
echo.

cd /d "%~dp0"

echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装或未添加到PATH
    pause
    exit /b 1
)

echo 检查前端目录...
if not exist "frontend" (
    echo ❌ frontend目录不存在
    pause
    exit /b 1
)

cd frontend

echo 安装依赖...
call npm install

echo.
echo 启动开发服务器...
echo 🌐 访问 http://localhost:3000/covers 查看模板列表
echo 🎨 点击"编辑"按钮进入简化画布编辑器
echo ⚡ 无需后端，纯前端本地交互
echo.

start http://localhost:3000/covers
call npm run dev
