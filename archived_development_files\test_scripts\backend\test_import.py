import sys
import os
sys.path.append('src')

try:
    from api.video_categories import router
    print('✓ 视频分类API导入成功')
except Exception as e:
    print(f'✗ 视频分类API导入失败: {e}')

try:
    from api.routes import api_router
    print('✓ 主路由导入成功')
except Exception as e:
    print(f'✗ 主路由导入失败: {e}')

try:
    from models.resources import VideoCategory
    print('✓ 视频分类模型导入成功')
except Exception as e:
    print(f'✗ 视频分类模型导入失败: {e}')
