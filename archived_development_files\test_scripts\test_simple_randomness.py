#!/usr/bin/env python3
"""
简单测试素材随机性
"""

import logging
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_material_randomness():
    """测试素材选择的随机性"""
    logger.info("=== 测试素材选择随机性 ===")
    
    # 模拟素材数据
    class MockVideoMaterial:
        def __init__(self, id, duration):
            self.id = id
            self.duration = duration
    
    materials = [
        MockVideoMaterial(1, 5.0),   # 长素材
        MockVideoMaterial(2, 4.5),
        MockVideoMaterial(3, 3.8),
        MockVideoMaterial(4, 2.5),   # 中等素材
        MockVideoMaterial(5, 2.0),
        MockVideoMaterial(6, 1.8),
        MockVideoMaterial(7, 1.5),
        MockVideoMaterial(8, 1.2),
        MockVideoMaterial(9, 0.8),   # 短素材
        MockVideoMaterial(10, 0.5),
    ]
    
    # 模拟新的素材选择算法
    def smart_select_materials_with_randomness(materials, audio_duration):
        # 获取素材时长
        material_durations = [(m, float(m.duration)) for m in materials]
        
        # 先随机打乱
        random.shuffle(material_durations)
        logger.debug(f"随机打乱后: {[(m.id, d) for m, d in material_durations]}")
        
        # 按时长分组
        long_materials = [(m, d) for m, d in material_durations if d > 3.0]
        medium_materials = [(m, d) for m, d in material_durations if 1.0 <= d <= 3.0]
        short_materials = [(m, d) for m, d in material_durations if d < 1.0]
        
        # 每组内部再次随机打乱
        random.shuffle(long_materials)
        random.shuffle(medium_materials)
        random.shuffle(short_materials)
        
        logger.debug(f"长素材: {[(m.id, d) for m, d in long_materials]}")
        logger.debug(f"中等素材: {[(m.id, d) for m, d in medium_materials]}")
        logger.debug(f"短素材: {[(m.id, d) for m, d in short_materials]}")
        
        # 重新组合
        material_durations = long_materials + medium_materials + short_materials
        
        # 选择素材
        selected_materials = []
        total_duration = 0.0
        
        for material, duration in material_durations:
            selected_materials.append(material)
            total_duration += duration
            
            if total_duration >= audio_duration:
                break
        
        # 最终随机打乱播放顺序
        random.shuffle(selected_materials)
        
        return selected_materials
    
    # 测试多次选择的随机性
    audio_duration = 15.0
    selections = []
    
    logger.info(f"测试音频时长: {audio_duration}s")
    logger.info("开始多次素材选择测试...")
    
    for i in range(10):
        selected = smart_select_materials_with_randomness(materials, audio_duration)
        selection_ids = [m.id for m in selected]
        total_duration = sum(m.duration for m in selected)
        selections.append(selection_ids)
        logger.info(f"第 {i+1:2d} 次选择: {selection_ids} (总时长: {total_duration:.1f}s)")
    
    # 检查随机性
    unique_selections = set(tuple(s) for s in selections)
    randomness_score = len(unique_selections) / len(selections) * 100
    
    logger.info(f"\n随机性测试结果:")
    logger.info(f"  总测试次数: {len(selections)}")
    logger.info(f"  不同组合数: {len(unique_selections)}")
    logger.info(f"  随机性评分: {randomness_score:.1f}%")
    
    # 分析选择模式
    all_selected_ids = [id for selection in selections for id in selection]
    id_counts = {}
    for id in all_selected_ids:
        id_counts[id] = id_counts.get(id, 0) + 1
    
    logger.info(f"\n素材使用频率:")
    for id in sorted(id_counts.keys()):
        material = next(m for m in materials if m.id == id)
        frequency = id_counts[id] / len(selections) * 100
        logger.info(f"  素材 {id} (时长{material.duration}s): 使用 {id_counts[id]} 次 ({frequency:.1f}%)")
    
    if randomness_score >= 80:
        logger.info("✅ 素材选择随机性良好")
        return True
    elif randomness_score >= 60:
        logger.info("⚠️ 素材选择随机性一般")
        return True
    else:
        logger.error("❌ 素材选择随机性不足")
        return False

def test_transition_types():
    """测试转场类型映射"""
    logger.info("\n=== 测试转场类型映射 ===")
    
    # 转场类型别名映射
    TRANSITION_ALIASES = {
        'slide_left': 'slideleft',
        'slide_right': 'slideright', 
        'slide_up': 'slideup',
        'slide_down': 'slidedown',
        'wipe_left': 'wipeleft',
        'wipe_right': 'wiperight',
        'wipe_up': 'wipeup',
        'wipe_down': 'wipedown',
        'circle_open': 'circleopen',
        'circle_close': 'circleclose',
        'smooth_left': 'smoothleft',
        'smooth_right': 'smoothright',
        'smooth_up': 'smoothup',
        'smooth_down': 'smoothdown'
    }
    
    # 基础转场类型
    BASIC_TRANSITIONS = {
        'none': None,
        'fade': 'fade',
        'dissolve': 'dissolve',
        'wipeleft': 'wipeleft',
        'wiperight': 'wiperight',
        'wipeup': 'wipeup',
        'wipedown': 'wipedown',
        'slideleft': 'slideleft',
        'slideright': 'slideright',
        'slideup': 'slideup',
        'slidedown': 'slidedown',
        'circlecrop': 'circlecrop',
        'rectcrop': 'rectcrop',
        'distance': 'distance',
        'fadeblack': 'fadeblack',
        'fadewhite': 'fadewhite',
        'radial': 'radial',
        'smoothleft': 'smoothleft',
        'smoothright': 'smoothright',
        'smoothup': 'smoothup',
        'smoothdown': 'smoothdown',
        'circleopen': 'circleopen',
        'circleclose': 'circleclose'
    }
    
    logger.info("前端转场类型 -> FFmpeg转场类型映射:")
    for frontend_name, ffmpeg_name in TRANSITION_ALIASES.items():
        if ffmpeg_name in BASIC_TRANSITIONS:
            logger.info(f"  ✅ {frontend_name} -> {ffmpeg_name}")
        else:
            logger.error(f"  ❌ {frontend_name} -> {ffmpeg_name} (不支持)")
    
    logger.info(f"\n总共支持 {len(BASIC_TRANSITIONS)} 种转场效果")
    logger.info(f"前端别名映射 {len(TRANSITION_ALIASES)} 种")
    
    return True

def main():
    """主测试函数"""
    logger.info("🧪 开始测试素材随机性和转场效果...")
    
    # 测试素材随机性
    randomness_success = test_material_randomness()
    
    # 测试转场类型
    transition_success = test_transition_types()
    
    logger.info("\n🎉 测试总结:")
    if randomness_success:
        logger.info("✅ 素材随机性测试通过")
    else:
        logger.error("❌ 素材随机性测试失败")
    
    if transition_success:
        logger.info("✅ 转场类型映射测试通过")
    else:
        logger.error("❌ 转场类型映射测试失败")
    
    if randomness_success and transition_success:
        logger.info("\n🚀 所有测试通过！")
        logger.info("\n📋 改进总结:")
        logger.info("1. ✅ 新增多种转场效果 (滑动、擦除、圆形、平滑等)")
        logger.info("2. ✅ 修复批量生成素材重复问题")
        logger.info("3. ✅ 每次生成都会随机选择不同的素材组合")
        logger.info("4. ✅ 保持长素材优先，但顺序随机化")
        logger.info("\n🎬 现在可以测试批量生成，每个视频都会有不同的素材组合和转场效果！")
    else:
        logger.error("\n❌ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
