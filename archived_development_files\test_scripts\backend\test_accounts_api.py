#!/usr/bin/env python3
"""
快速测试账号API功能
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_account_apis():
    """测试账号相关API"""
    print("🧪 测试账号API...")
    
    # 1. 测试获取账号列表
    print("\n1. 测试获取账号列表...")
    try:
        response = requests.get(f"{BASE_URL}/accounts/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"账号数量: {data.get('total', 0)}")
            print("✅ 获取账号列表成功")
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 2. 测试获取账号统计
    print("\n2. 测试获取账号统计...")
    try:
        response = requests.get(f"{BASE_URL}/accounts/stats")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"账号总数: {data.get('total_accounts', 0)}")
            print("✅ 获取账号统计成功")
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 3. 测试创建账号
    print("\n3. 测试创建账号...")
    try:
        test_account = {
            "name": "测试账号",
            "description": "这是一个测试账号",
            "platform": "reddit",
            "brand_color": "#ff6b6b",
            "font_style": "modern",
            "content_style": "casual"
        }
        
        response = requests.post(
            f"{BASE_URL}/accounts/",
            json=test_account,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"创建的账号ID: {data.get('id')}")
            print(f"账号名称: {data.get('name')}")
            print("✅ 创建账号成功")
            
            # 保存账号ID用于后续测试
            global test_account_id
            test_account_id = data.get('id')
            
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 4. 再次获取账号列表确认创建成功
    print("\n4. 再次获取账号列表...")
    try:
        response = requests.get(f"{BASE_URL}/accounts/")
        if response.status_code == 200:
            data = response.json()
            print(f"当前账号数量: {data.get('total', 0)}")
            if data.get('total', 0) > 0:
                accounts = data.get('accounts', [])
                for account in accounts:
                    print(f"  - {account.get('name')} ({account.get('platform')})")
            print("✅ 账号创建确认成功")
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试账号管理API...")
    test_account_apis()
    print("\n🎉 测试完成!")
