<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit故事视频生成器 - 账号名称管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: all 0.3s ease;
        }

        /* 主题变量定义 */
        :root {
            /* 蓝色主题（默认） */
            --theme-primary: #2563eb;
            --theme-primary-hover: #1d4ed8;
            --theme-primary-light: #eff6ff;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f9fafb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #374151;
            --border-primary: #e5e7eb;
            --border-secondary: #d1d5db;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
        }

        /* 绿色主题 */
        [data-theme="green"] {
            --theme-primary: #059669;
            --theme-primary-hover: #047857;
            --theme-primary-light: #ecfdf5;
        }

        /* 紫色主题 */
        [data-theme="purple"] {
            --theme-primary: #7c3aed;
            --theme-primary-hover: #6d28d9;
            --theme-primary-light: #f3f4f6;
        }

        /* 橙色主题 */
        [data-theme="orange"] {
            --theme-primary: #ea580c;
            --theme-primary-hover: #dc2626;
            --theme-primary-light: #fff7ed;
        }

        /* 红色主题 */
        [data-theme="red"] {
            --theme-primary: #dc2626;
            --theme-primary-hover: #b91c1c;
            --theme-primary-light: #fef2f2;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 32px;
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .page-description {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .btn-primary:hover {
            background: var(--theme-primary-hover);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-tertiary);
            border-color: var(--border-secondary);
        }

        .btn-secondary:hover {
            border-color: var(--theme-primary);
            color: var(--theme-primary);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--theme-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 分类标签 */
        .category-tabs {
            display: flex;
            gap: 2px;
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
        }

        .category-tab {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            background: transparent;
            color: var(--text-secondary);
        }

        .category-tab.active {
            background: var(--theme-primary);
            color: white;
        }

        .category-tab:not(.active):hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        /* 账号创建区域 */
        .create-section {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .create-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .create-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .toggle-btn {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: 1px solid var(--border-secondary);
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .toggle-btn:hover {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .create-form {
            display: none;
        }

        .create-form.show {
            display: block;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 6px;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: border-color 0.2s;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-help {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .create-method {
            margin-top: 16px;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }

        /* 工具栏 */
        .toolbar {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 8px 36px 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            color: var(--text-secondary);
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 6px 12px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
        }

        .view-btn.active {
            background: var(--theme-primary);
            color: white;
        }

        /* 账号列表 */
        .accounts-section {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .accounts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .accounts-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 批量操作栏 */
        .bulk-actions {
            display: none;
            background: var(--theme-primary);
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
            align-items: center;
            justify-content: space-between;
        }

        .bulk-actions.show {
            display: flex;
        }

        .bulk-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .bulk-buttons {
            display: flex;
            gap: 8px;
        }

        .bulk-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .bulk-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 账号表格 */
        .accounts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .accounts-table th,
        .accounts-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-primary);
            font-size: 14px;
        }

        .accounts-table th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .accounts-table tr:hover {
            background: var(--bg-tertiary);
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-unused {
            background: var(--theme-primary-light);
            color: var(--theme-primary);
        }

        .status-used {
            background: #ecfdf5;
            color: var(--success-color);
        }

        .status-disabled {
            background: #fef2f2;
            color: var(--error-color);
        }

        .type-badge {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 操作按钮组 */
        .action-group {
            display: flex;
            gap: 6px;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            color: var(--text-secondary);
        }

        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .toolbar-left {
                justify-content: space-between;
            }

            .search-input {
                width: 100%;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .accounts-table {
                font-size: 12px;
            }

            .accounts-table th,
            .accounts-table td {
                padding: 8px;
            }

            .bulk-actions {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title-section">
                <h1 class="page-title">账号名称管理</h1>
                <p class="page-description">管理用于视频发布的账号名称，支持批量创建、导入和使用状态跟踪</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-secondary" onclick="exportAccounts()">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    导出账号
                </button>
                <button class="btn btn-primary" onclick="showCreateForm()">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    新建账号
                </button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalAccounts">4</div>
                <div class="stat-label">总账号数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="unusedAccounts">1</div>
                <div class="stat-label">未使用</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="usedAccounts">2</div>
                <div class="stat-label">已使用</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="disabledAccounts">1</div>
                <div class="stat-label">已禁用</div>
            </div>
        </div>

        <!-- 状态标签 -->
        <div class="category-tabs">
            <button class="category-tab active" data-status="all">全部账号</button>
            <button class="category-tab" data-status="unused">未使用</button>
            <button class="category-tab" data-status="used">已使用</button>
            <button class="category-tab" data-status="disabled">已禁用</button>
        </div>

        <!-- 账号创建区域 -->
        <div class="create-section">
            <div class="create-header">
                <h3 class="create-title">创建账号名称</h3>
                <button class="toggle-btn" onclick="toggleCreateForm()">
                    <span id="createToggleText">展开</span>
                </button>
            </div>
            <div class="create-form" id="createForm">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">创建方式</label>
                        <select class="form-select" id="createMethod" onchange="toggleCreateMethod()">
                            <option value="single">单个创建</option>
                            <option value="batch">批量创建</option>
                            <option value="import">文件导入</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">账号类型</label>
                        <select class="form-select" id="accountType">
                            <option value="reddit">Reddit账号</option>
                            <option value="youtube">YouTube频道</option>
                            <option value="tiktok">TikTok账号</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                </div>

                <!-- 单个创建 -->
                <div id="singleCreate" class="create-method">
                    <div class="form-group">
                        <label class="form-label">账号名称</label>
                        <input type="text" class="form-input" id="accountName" placeholder="输入账号名称">
                        <div class="form-help">建议使用有意义的名称，避免特殊字符</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">描述信息</label>
                        <input type="text" class="form-input" id="accountDesc" placeholder="可选：账号描述">
                    </div>
                </div>

                <!-- 批量创建 -->
                <div id="batchCreate" class="create-method" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">批量账号名称</label>
                        <textarea class="form-textarea" id="batchNames" placeholder="每行一个账号名称&#10;例如：&#10;StoryTeller01&#10;NightReader&#10;TaleMaster"></textarea>
                        <div class="form-help">每行输入一个账号名称，系统会自动去重</div>
                    </div>
                </div>

                <!-- 文件导入 -->
                <div id="fileImport" class="create-method" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">选择文件</label>
                        <input type="file" class="form-input" id="importFile" accept=".txt,.csv" onchange="handleFileImport()">
                        <div class="form-help">支持TXT或CSV文件，每行一个账号名称</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">预览内容</label>
                        <textarea class="form-textarea" id="importPreview" readonly placeholder="选择文件后显示预览"></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button class="btn btn-primary" onclick="createAccounts()">
                        <svg class="btn-icon" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        <span id="createBtnText">创建账号</span>
                    </button>
                    <button class="btn btn-secondary" onclick="resetCreateForm()">
                        <svg class="btn-icon" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                        </svg>
                        重置
                    </button>
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索账号名称..." id="searchInput" oninput="filterAccounts()">
                    <svg class="search-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <select class="filter-select" id="typeFilter" onchange="filterAccounts()">
                    <option value="">全部类型</option>
                    <option value="reddit">Reddit</option>
                    <option value="youtube">YouTube</option>
                    <option value="tiktok">TikTok</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
            <div class="toolbar-right">
                <div class="view-toggle">
                    <button class="view-btn active" data-view="table" onclick="switchView('table')">表格</button>
                    <button class="view-btn" data-view="grid" onclick="switchView('grid')">网格</button>
                </div>
                <select class="filter-select">
                    <option value="created">创建时间</option>
                    <option value="name">名称排序</option>
                    <option value="usage">使用次数</option>
                </select>
            </div>
        </div>

        <!-- 批量操作栏 -->
        <div class="bulk-actions" id="bulkActions">
            <div class="bulk-info">
                <span>已选择 <strong id="selectedCount">0</strong> 个账号</span>
            </div>
            <div class="bulk-buttons">
                <button class="bulk-btn" onclick="bulkSetStatus('used')">标记已使用</button>
                <button class="bulk-btn" onclick="bulkSetStatus('unused')">标记未使用</button>
                <button class="bulk-btn" onclick="bulkSetStatus('disabled')">禁用</button>
                <button class="bulk-btn" onclick="bulkDelete()">删除</button>
                <button class="bulk-btn" onclick="clearSelection()">取消选择</button>
            </div>
        </div>

        <!-- 账号列表 -->
        <div class="accounts-section">
            <div class="accounts-header">
                <h3 class="accounts-title">账号列表</h3>
                <div class="action-group">
                    <button class="btn btn-sm btn-secondary" onclick="selectAll()">全选</button>
                    <button class="btn btn-sm btn-secondary" onclick="clearSelection()">取消</button>
                </div>
            </div>

            <!-- 表格视图 -->
            <div id="tableView">
                <table class="accounts-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th>账号名称</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>使用次数</th>
                            <th>最后使用</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="accountsTableBody">
                        <!-- 示例数据 -->
                        <tr>
                            <td><input type="checkbox" value="1" onchange="toggleSelectAccount(1)"></td>
                            <td>
                                <strong>StoryTeller01</strong>
                                <br><small style="color: var(--text-secondary);">Reddit故事频道主账号</small>
                            </td>
                            <td><span class="type-badge">Reddit</span></td>
                            <td><span class="status-badge status-unused">未使用</span></td>
                            <td>2024-01-15</td>
                            <td>0</td>
                            <td>-</td>
                            <td>
                                <div class="action-group">
                                    <button class="btn btn-sm btn-secondary" onclick="editAccount(1)">编辑</button>
                                    <button class="btn btn-sm btn-success" onclick="toggleAccountStatus(1)">使用</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteAccount(1)">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" value="2" onchange="toggleSelectAccount(2)"></td>
                            <td>
                                <strong>NightReader</strong>
                                <br><small style="color: var(--text-secondary);">YouTube夜读频道</small>
                            </td>
                            <td><span class="type-badge">YouTube</span></td>
                            <td><span class="status-badge status-used">已使用</span></td>
                            <td>2024-01-10</td>
                            <td>5</td>
                            <td>2024-01-20</td>
                            <td>
                                <div class="action-group">
                                    <button class="btn btn-sm btn-secondary" onclick="editAccount(2)">编辑</button>
                                    <button class="btn btn-sm btn-secondary" onclick="toggleAccountStatus(2)">重置</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteAccount(2)">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" value="3" onchange="toggleSelectAccount(3)"></td>
                            <td>
                                <strong>TaleMaster</strong>
                                <br><small style="color: var(--text-secondary);">TikTok故事短视频</small>
                            </td>
                            <td><span class="type-badge">TikTok</span></td>
                            <td><span class="status-badge status-used">已使用</span></td>
                            <td>2024-01-08</td>
                            <td>12</td>
                            <td>2024-01-22</td>
                            <td>
                                <div class="action-group">
                                    <button class="btn btn-sm btn-secondary" onclick="editAccount(3)">编辑</button>
                                    <button class="btn btn-sm btn-secondary" onclick="toggleAccountStatus(3)">重置</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteAccount(3)">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" value="4" onchange="toggleSelectAccount(4)"></td>
                            <td>
                                <strong>QuickStory</strong>
                                <br><small style="color: var(--text-secondary);">备用账号</small>
                            </td>
                            <td><span class="type-badge">自定义</span></td>
                            <td><span class="status-badge status-disabled">已禁用</span></td>
                            <td>2024-01-05</td>
                            <td>2</td>
                            <td>2024-01-18</td>
                            <td>
                                <div class="action-group">
                                    <button class="btn btn-sm btn-secondary" onclick="editAccount(4)">编辑</button>
                                    <button class="btn btn-sm btn-success" onclick="toggleAccountStatus(4)">启用</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteAccount(4)">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 网格视图 -->
            <div id="gridView" style="display: none;">
                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 16px; margin-top: 16px;">
                    <!-- 网格内容将通过JavaScript动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑账号模态框 -->
    <div class="modal-overlay" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑账号</h3>
                <button class="modal-close" onclick="closeEditModal()">&times;</button>
            </div>
            <form id="editForm">
                <div class="form-group">
                    <label class="form-label">账号名称</label>
                    <input type="text" class="form-input" id="editAccountName">
                </div>
                <div class="form-group">
                    <label class="form-label">账号类型</label>
                    <select class="form-select" id="editAccountType">
                        <option value="reddit">Reddit账号</option>
                        <option value="youtube">YouTube频道</option>
                        <option value="tiktok">TikTok账号</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <select class="form-select" id="editAccountStatus">
                        <option value="unused">未使用</option>
                        <option value="used">已使用</option>
                        <option value="disabled">已禁用</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">描述信息</label>
                    <textarea class="form-textarea" id="editAccountDesc"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEditModal()">取消</button>
                <button class="btn btn-primary" onclick="saveAccount()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 示例账号数据
        let accounts = [
            {
                id: 1,
                name: 'StoryTeller01',
                type: 'reddit',
                status: 'unused',
                description: 'Reddit故事频道主账号',
                createTime: '2024-01-15',
                useCount: 0,
                lastUsed: '-'
            },
            {
                id: 2,
                name: 'NightReader',
                type: 'youtube',
                status: 'used',
                description: 'YouTube夜读频道',
                createTime: '2024-01-10',
                useCount: 5,
                lastUsed: '2024-01-20'
            },
            {
                id: 3,
                name: 'TaleMaster',
                type: 'tiktok',
                status: 'used',
                description: 'TikTok故事短视频',
                createTime: '2024-01-08',
                useCount: 12,
                lastUsed: '2024-01-22'
            },
            {
                id: 4,
                name: 'QuickStory',
                type: 'custom',
                status: 'disabled',
                description: '备用账号',
                createTime: '2024-01-05',
                useCount: 2,
                lastUsed: '2024-01-18'
            }
        ];

        let selectedAccounts = new Set();
        let currentEditId = null;
        let currentStatus = 'all';

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 恢复保存的主题
            const savedTheme = localStorage.getItem('theme') || 'blue';
            if (savedTheme !== 'blue') {
                document.documentElement.setAttribute('data-theme', savedTheme);
            }

            updateStats();
            updateBulkActions();
        });

        // 更新统计信息
        function updateStats() {
            const total = accounts.length;
            const unused = accounts.filter(a => a.status === 'unused').length;
            const used = accounts.filter(a => a.status === 'used').length;
            const disabled = accounts.filter(a => a.status === 'disabled').length;

            document.getElementById('totalAccounts').textContent = total;
            document.getElementById('unusedAccounts').textContent = unused;
            document.getElementById('usedAccounts').textContent = used;
            document.getElementById('disabledAccounts').textContent = disabled;
        }

        // 状态标签切换
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                currentStatus = this.dataset.status;
                filterByStatus();
            });
        });

        function filterByStatus() {
            const rows = document.querySelectorAll('#accountsTableBody tr');
            rows.forEach(row => {
                const statusSpan = row.querySelector('.status-badge');
                if (statusSpan) {
                    const status = statusSpan.classList.contains('status-unused') ? 'unused' :
                                  statusSpan.classList.contains('status-used') ? 'used' :
                                  statusSpan.classList.contains('status-disabled') ? 'disabled' : '';
                    
                    if (currentStatus === 'all' || status === currentStatus) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        }

        // 切换账号选择
        function toggleSelectAccount(id) {
            if (selectedAccounts.has(id)) {
                selectedAccounts.delete(id);
            } else {
                selectedAccounts.add(id);
            }
            updateBulkActions();
            updateSelectAllCheckbox();
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const checkbox = document.getElementById('selectAllCheckbox');
            const visibleRows = Array.from(document.querySelectorAll('#accountsTableBody tr')).filter(row => row.style.display !== 'none');
            
            if (checkbox.checked) {
                visibleRows.forEach(row => {
                    const id = parseInt(row.querySelector('input[type="checkbox"]').value);
                    selectedAccounts.add(id);
                    row.querySelector('input[type="checkbox"]').checked = true;
                });
            } else {
                visibleRows.forEach(row => {
                    const id = parseInt(row.querySelector('input[type="checkbox"]').value);
                    selectedAccounts.delete(id);
                    row.querySelector('input[type="checkbox"]').checked = false;
                });
            }
            updateBulkActions();
        }

        // 更新全选复选框状态
        function updateSelectAllCheckbox() {
            const checkbox = document.getElementById('selectAllCheckbox');
            const visibleRows = Array.from(document.querySelectorAll('#accountsTableBody tr')).filter(row => row.style.display !== 'none');
            const visibleIds = visibleRows.map(row => parseInt(row.querySelector('input[type="checkbox"]').value));
            const selectedVisibleCount = visibleIds.filter(id => selectedAccounts.has(id)).length;
            
            if (selectedVisibleCount === 0) {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            } else if (selectedVisibleCount === visibleIds.length) {
                checkbox.checked = true;
                checkbox.indeterminate = false;
            } else {
                checkbox.checked = false;
                checkbox.indeterminate = true;
            }
        }

        // 更新批量操作栏
        function updateBulkActions() {
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            selectedCount.textContent = selectedAccounts.size;
            
            if (selectedAccounts.size > 0) {
                bulkActions.classList.add('show');
            } else {
                bulkActions.classList.remove('show');
            }
        }

        // 清除选择
        function clearSelection() {
            selectedAccounts.clear();
            document.querySelectorAll('#accountsTableBody input[type="checkbox"]').forEach(cb => {
                cb.checked = false;
            });
            updateBulkActions();
            updateSelectAllCheckbox();
        }

        // 全选
        function selectAll() {
            accounts.forEach(account => selectedAccounts.add(account.id));
            document.querySelectorAll('#accountsTableBody input[type="checkbox"]').forEach(cb => {
                cb.checked = true;
            });
            updateBulkActions();
            updateSelectAllCheckbox();
        }

        // 创建账号功能
        function showCreateForm() {
            const form = document.getElementById('createForm');
            form.classList.add('show');
            document.getElementById('createToggleText').textContent = '收起';
        }

        function toggleCreateForm() {
            const form = document.getElementById('createForm');
            const toggleText = document.getElementById('createToggleText');
            
            if (form.classList.contains('show')) {
                form.classList.remove('show');
                toggleText.textContent = '展开';
            } else {
                form.classList.add('show');
                toggleText.textContent = '收起';
            }
        }

        function toggleCreateMethod() {
            const method = document.getElementById('createMethod').value;
            const createBtnText = document.getElementById('createBtnText');
            
            // 隐藏所有方法
            document.querySelectorAll('.create-method').forEach(el => {
                el.style.display = 'none';
            });
            
            // 显示选中的方法
            if (method === 'single') {
                document.getElementById('singleCreate').style.display = 'block';
                createBtnText.textContent = '创建账号';
            } else if (method === 'batch') {
                document.getElementById('batchCreate').style.display = 'block';
                createBtnText.textContent = '批量创建';
            } else if (method === 'import') {
                document.getElementById('fileImport').style.display = 'block';
                createBtnText.textContent = '导入账号';
            }
        }

        function handleFileImport() {
            const file = document.getElementById('importFile').files[0];
            const preview = document.getElementById('importPreview');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.value = e.target.result;
                };
                reader.readAsText(file);
            }
        }

        function createAccounts() {
            const method = document.getElementById('createMethod').value;
            const type = document.getElementById('accountType').value;
            let newAccounts = [];
            
            if (method === 'single') {
                const name = document.getElementById('accountName').value.trim();
                const desc = document.getElementById('accountDesc').value.trim();
                
                if (!name) {
                    alert('请输入账号名称');
                    return;
                }
                
                newAccounts.push({
                    id: Date.now(),
                    name: name,
                    type: type,
                    status: 'unused',
                    description: desc,
                    createTime: new Date().toISOString().split('T')[0],
                    useCount: 0,
                    lastUsed: '-'
                });
            } else if (method === 'batch') {
                const names = document.getElementById('batchNames').value
                    .split('\n')
                    .map(name => name.trim())
                    .filter(name => name);
                
                if (names.length === 0) {
                    alert('请输入账号名称');
                    return;
                }
                
                names.forEach((name, index) => {
                    newAccounts.push({
                        id: Date.now() + index,
                        name: name,
                        type: type,
                        status: 'unused',
                        description: '',
                        createTime: new Date().toISOString().split('T')[0],
                        useCount: 0,
                        lastUsed: '-'
                    });
                });
            } else if (method === 'import') {
                const content = document.getElementById('importPreview').value;
                const names = content
                    .split('\n')
                    .map(name => name.trim())
                    .filter(name => name);
                
                if (names.length === 0) {
                    alert('请选择文件或输入内容');
                    return;
                }
                
                names.forEach((name, index) => {
                    newAccounts.push({
                        id: Date.now() + index,
                        name: name,
                        type: type,
                        status: 'unused',
                        description: '',
                        createTime: new Date().toISOString().split('T')[0],
                        useCount: 0,
                        lastUsed: '-'
                    });
                });
            }
            
            // 检查重复
            const existingNames = accounts.map(a => a.name.toLowerCase());
            const duplicates = newAccounts.filter(a => existingNames.includes(a.name.toLowerCase()));
            
            if (duplicates.length > 0) {
                if (!confirm(`发现 ${duplicates.length} 个重复账号名称，是否继续添加其他账号？`)) {
                    return;
                }
                newAccounts = newAccounts.filter(a => !existingNames.includes(a.name.toLowerCase()));
            }
            
            accounts.push(...newAccounts);
            updateStats();
            resetCreateForm();
            
            alert(`成功创建 ${newAccounts.length} 个账号`);
            location.reload(); // 简单刷新，实际项目中应该动态更新表格
        }

        function resetCreateForm() {
            document.getElementById('accountName').value = '';
            document.getElementById('accountDesc').value = '';
            document.getElementById('batchNames').value = '';
            document.getElementById('importFile').value = '';
            document.getElementById('importPreview').value = '';
        }

        // 编辑账号
        function editAccount(id) {
            const account = accounts.find(a => a.id === id);
            if (!account) return;
            
            currentEditId = id;
            document.getElementById('editAccountName').value = account.name;
            document.getElementById('editAccountType').value = account.type;
            document.getElementById('editAccountStatus').value = account.status;
            document.getElementById('editAccountDesc').value = account.description || '';
            document.getElementById('editModal').classList.add('show');
        }

        function closeEditModal() {
            document.getElementById('editModal').classList.remove('show');
            currentEditId = null;
        }

        function saveAccount() {
            if (!currentEditId) return;
            
            const account = accounts.find(a => a.id === currentEditId);
            if (!account) return;
            
            const newName = document.getElementById('editAccountName').value.trim();
            if (!newName) {
                alert('请输入账号名称');
                return;
            }
            
            account.name = newName;
            account.type = document.getElementById('editAccountType').value;
            account.status = document.getElementById('editAccountStatus').value;
            account.description = document.getElementById('editAccountDesc').value.trim();
            
            updateStats();
            closeEditModal();
            
            alert('账号信息已更新');
            location.reload(); // 简单刷新，实际项目中应该动态更新表格
        }

        // 切换账号状态
        function toggleAccountStatus(id) {
            const account = accounts.find(a => a.id === id);
            if (!account) return;
            
            if (account.status === 'used') {
                account.status = 'unused';
                account.useCount = 0;
                account.lastUsed = '-';
            } else if (account.status === 'unused') {
                account.status = 'used';
                account.useCount = (account.useCount || 0) + 1;
                account.lastUsed = new Date().toISOString().split('T')[0];
            } else {
                account.status = 'unused';
            }
            
            updateStats();
            location.reload(); // 简单刷新，实际项目中应该动态更新表格
        }

        // 删除账号
        function deleteAccount(id) {
            if (confirm('确定要删除这个账号吗？')) {
                accounts = accounts.filter(a => a.id !== id);
                selectedAccounts.delete(id);
                updateStats();
                updateBulkActions();
                location.reload(); // 简单刷新，实际项目中应该动态更新表格
            }
        }

        // 批量操作
        function bulkSetStatus(status) {
            if (selectedAccounts.size === 0) return;
            
            const statusText = {'unused': '未使用', 'used': '已使用', 'disabled': '已禁用'}[status];
            if (confirm(`确定要将选中的 ${selectedAccounts.size} 个账号设置为"${statusText}"吗？`)) {
                accounts.forEach(account => {
                    if (selectedAccounts.has(account.id)) {
                        account.status = status;
                        if (status === 'used') {
                            account.useCount = (account.useCount || 0) + 1;
                            account.lastUsed = new Date().toISOString().split('T')[0];
                        } else if (status === 'unused') {
                            account.useCount = 0;
                            account.lastUsed = '-';
                        }
                    }
                });
                
                updateStats();
                clearSelection();
                location.reload(); // 简单刷新，实际项目中应该动态更新表格
            }
        }

        function bulkDelete() {
            if (selectedAccounts.size === 0) return;
            
            if (confirm(`确定要删除选中的 ${selectedAccounts.size} 个账号吗？此操作不可恢复。`)) {
                accounts = accounts.filter(a => !selectedAccounts.has(a.id));
                selectedAccounts.clear();
                updateStats();
                updateBulkActions();
                location.reload(); // 简单刷新，实际项目中应该动态更新表格
            }
        }

        // 搜索和筛选
        function filterAccounts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('typeFilter').value;
            
            const rows = document.querySelectorAll('#accountsTableBody tr');
            rows.forEach(row => {
                const nameCell = row.cells[1].textContent.toLowerCase();
                const typeCell = row.cells[2].textContent.toLowerCase();
                
                let showRow = true;
                
                if (searchTerm && !nameCell.includes(searchTerm)) {
                    showRow = false;
                }
                
                if (typeFilter && !typeCell.includes(typeFilter.toLowerCase())) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
            });
            
            // 重新应用状态过滤
            filterByStatus();
        }

        // 视图切换
        function switchView(view) {
            const tableView = document.getElementById('tableView');
            const gridView = document.getElementById('gridView');
            const viewBtns = document.querySelectorAll('.view-btn');
            
            viewBtns.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === view);
            });
            
            if (view === 'table') {
                tableView.style.display = 'block';
                gridView.style.display = 'none';
            } else {
                tableView.style.display = 'none';
                gridView.style.display = 'block';
                // 这里可以实现网格视图的渲染
            }
        }

        // 导出账号
        function exportAccounts() {
            const csv = [
                ['账号名称', '类型', '状态', '描述', '创建时间', '使用次数', '最后使用'],
                ...accounts.map(account => [
                    account.name,
                    account.type,
                    account.status,
                    account.description || '',
                    account.createTime,
                    account.useCount,
                    account.lastUsed
                ])
            ].map(row => row.join(',')).join('\n');
            
            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `账号名称_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>
</html>
