"""
简化的后端测试启动文件
用于快速验证后端基本功能
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="Reddit Story Video Generator API",
    description="Backend API for Reddit Story Video Generator",
    version="0.1.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径健康检查"""
    return {"message": "Reddit Story Video Generator Backend API", "status": "running"}

@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "message": "Backend is running successfully",
        "version": "0.1.0"
    }

@app.get("/api/test")
async def test_endpoint():
    """测试端点"""
    return {
        "message": "Test endpoint working",
        "data": {
            "backend_status": "operational",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    }

if __name__ == "__main__":
    print("🚀 Starting simplified backend server...")
    print("📍 API endpoints:")
    print("   • Health: http://localhost:8000/api/health")
    print("   • Test:   http://localhost:8000/api/test")
    print("   • Docs:   http://localhost:8000/docs")
    print()
    
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
