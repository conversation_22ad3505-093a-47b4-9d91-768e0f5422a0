/**
 * 直接HTTP客户端 - 不使用fetch，直接访问后端API
 * 移除所有超时限制，确保稳定的API调用
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

export class DirectHttpClient {
  private client: AxiosInstance

  constructor(baseURL: string = '') {
    // 始终指向后端服务器，不使用Next.js代理
    const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
    
    this.client = axios.create({
      baseURL: `${API_BASE}${baseURL}`,
      timeout: 0, // 无超时限制
      headers: {
        'Content-Type': 'application/json',
      },
      // 禁用所有超时设置
      maxRedirects: 5,
      validateStatus: (status) => status < 500, // 只有5xx错误才抛出异常
    })

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🟢 DirectHttpClient 请求: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('🔴 DirectHttpClient 请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(`🟢 DirectHttpClient 响应: ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        console.error('🔴 DirectHttpClient 响应错误:', error)
        if (error.response) {
          // 服务器响应了错误状态码
          throw new Error(error.response.data?.detail || `HTTP ${error.response.status}`)
        } else if (error.request) {
          // 请求已发出但没有收到响应
          throw new Error('网络连接错误')
        } else {
          // 其他错误
          throw new Error(error.message || '未知错误')
        }
      }
    )
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.get(url, config)
    return response.data
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.post(url, data, config)
    return response.data
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.put(url, data, config)
    return response.data
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.delete(url, config)
    return response.data
  }

  /**
   * 上传文件 - 支持进度回调，无超时限制
   */
  async uploadFile<T>(
    url: string,
    formData: FormData,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const config: AxiosRequestConfig = {
      timeout: 0, // 上传文件无超时限制
      headers: {
        // 明确删除 Content-Type，让 axios 自动处理 FormData
        'Content-Type': undefined,
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = (progressEvent.loaded / progressEvent.total) * 100
          onProgress(progress)
        }
      },
    }

    // 不要手动设置 Content-Type，让axios自动处理FormData的Content-Type和boundary
    console.log('📤 DirectHttpClient.uploadFile: 发送文件上传请求到', url)
    
    // 调试：检查 FormData 内容
    console.log('🔍 DirectHttpClient.uploadFile: FormData 内容检查');
    console.log('  file:', formData.get('file'));
    console.log('  name:', formData.get('name'));
    console.log('  description:', formData.get('description'));
    console.log('  category:', formData.get('category'));
    
    const response: AxiosResponse<T> = await this.client.post(url, formData, config)
    console.log('📥 DirectHttpClient.uploadFile: 收到响应', response.status, response.statusText)
    return response.data
  }

  /**
   * 获取原始响应对象
   */
  async getRaw(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.client.get(url, config)
  }

  async postRaw(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.client.post(url, data, config)
  }
}

export default DirectHttpClient
