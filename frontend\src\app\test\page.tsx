'use client'

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            测试页面
          </h1>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded">
              <p className="text-green-800">
                ✅ 如果你能看到这个页面，说明前端基本正常运行
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 border border-blue-200 rounded">
              <p className="text-blue-800">
                📍 当前时间: {new Date().toLocaleString('zh-CN')}
              </p>
            </div>
            
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-yellow-800">
                🔧 这是一个纯客户端渲染的页面，应该不会有 hydration 错误
              </p>
            </div>
            
            <div className="flex space-x-4">
              <a 
                href="/" 
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                返回首页
              </a>
              <a 
                href="/diagnostics" 
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                查看诊断
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
