#!/usr/bin/env python3
"""
测试Hydration错误修复
验证Next.js Hydration问题是否已解决
"""

import time
import os
from pathlib import Path

def test_hydration_fix():
    """测试Hydration错误修复"""
    print("🔧 开始测试Hydration错误修复...")
    
    # 检查前端代码
    print("\n📋 检查前端代码修复...")
    
    frontend_file = Path("frontend/src/app/videos/page.tsx")
    if not frontend_file.exists():
        print("❌ 前端页面文件不存在")
        return False
    
    content = frontend_file.read_text(encoding='utf-8')
    
    # 检查日期格式化函数
    if 'const formatDate = ' in content:
        print("✅ 添加了安全的日期格式化函数")
    else:
        print("❌ 缺少安全的日期格式化函数")
        return False
    
    # 检查客户端挂载状态
    if 'const [isMounted, setIsMounted] = useState(false)' in content:
        print("✅ 添加了客户端挂载状态检查")
    else:
        print("❌ 缺少客户端挂载状态检查")
        return False
    
    # 检查useEffect挂载检查
    if 'setIsMounted(true)' in content:
        print("✅ 添加了挂载状态更新")
    else:
        print("❌ 缺少挂载状态更新")
        return False
    
    # 检查条件渲染
    if '!isMounted ?' in content:
        print("✅ 添加了条件渲染防止hydration错误")
    else:
        print("❌ 缺少条件渲染")
        return False
    
    # 检查日期显示修复
    if 'formatDate(previewMaterial.createdAt)' in content:
        print("✅ 修复了日期显示格式")
    else:
        print("❌ 日期显示格式未修复")
        return False
    
    # 检查是否移除了有问题的toLocaleString
    if 'toLocaleString()' in content:
        print("❌ 仍然存在toLocaleString()调用")
        return False
    else:
        print("✅ 已移除有问题的toLocaleString()调用")
    
    print("\n🎯 修复内容验证:")
    print("📐 添加了formatDate函数，使用ISO格式避免locale差异")
    print("📐 添加了isMounted状态，防止SSR和客户端不匹配")
    print("📐 使用条件渲染，只在客户端挂载后显示内容")
    print("📐 修复了日期显示，使用安全的格式化方法")
    print("📐 移除了所有可能导致hydration错误的动态内容")
    
    print("\n✅ 所有Hydration错误修复测试通过！")
    return True

def print_hydration_info():
    """打印Hydration错误相关信息"""
    print("\n📋 Hydration错误常见原因:")
    print("1. 服务端和客户端渲染结果不一致")
    print("2. 使用Date.now()、Math.random()等动态值")
    print("3. 时区差异导致的日期格式化不同")
    print("4. 浏览器API在服务端不可用")
    print("5. 外部依赖的状态不一致")
    
    print("\n🔧 修复策略:")
    print("1. 使用isMounted状态检查客户端挂载")
    print("2. 避免在初始渲染中使用动态值")
    print("3. 使用固定格式的日期显示")
    print("4. 延迟渲染需要浏览器API的内容")
    print("5. 确保服务端和客户端初始状态一致")
    
    print("\n📝 测试方法:")
    print("1. 启动开发服务器: npm run dev")
    print("2. 访问视频素材管理页面")
    print("3. 检查浏览器控制台是否还有hydration警告")
    print("4. 确认页面正常加载和显示")
    print("5. 测试预览模态框的日期显示")

def main():
    """主函数"""
    print("🔧 Next.js Hydration错误修复测试")
    print("=" * 50)
    
    # 运行测试
    success = test_hydration_fix()
    
    # 打印相关信息
    print_hydration_info()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Hydration错误修复完成！")
        print("\n🎯 主要改进:")
        print("- 添加了安全的日期格式化函数")
        print("- 使用客户端挂载状态检查")
        print("- 条件渲染防止SSR/客户端不匹配")
        print("- 移除了动态内容导致的hydration问题")
        
        print("\n📝 预期效果:")
        print("- 页面加载时不再出现hydration警告")
        print("- 预览模态框日期显示正常")
        print("- 客户端渲染与服务端保持一致")
        print("- 整体用户体验更流畅")
    else:
        print("❌ 测试失败！需要进一步修复hydration问题")
    
    return success

if __name__ == "__main__":
    main()
