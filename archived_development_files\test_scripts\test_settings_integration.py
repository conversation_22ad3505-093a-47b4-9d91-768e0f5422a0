#!/usr/bin/env python3
"""
设置页面集成测试
测试云雾API的设置保存和连通性测试功能
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# 测试配置
BACKEND_URL = "http://localhost:8000"
TEST_API_KEY = "yk-test-key-12345"  # 测试用的假API Key

async def test_settings_api():
    """测试设置API的各个功能"""
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("🔧 开始测试设置API...")
        
        # 1. 测试获取默认设置
        print("\n1. 测试获取默认设置")
        try:
            response = await client.get(f"{BACKEND_URL}/api/settings")
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   获取设置成功: {data.get('success', False)}")
                if data.get('data'):
                    print(f"   LLM提供商: {data['data']['llm']['provider']}")
            else:
                print(f"   ❌ 错误: {response.text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 2. 测试更新设置为云雾API
        print("\n2. 测试更新设置为云雾API")
        update_data = {
            "llm": {
                "provider": "yunwu",
                "apiKey": TEST_API_KEY,
                "model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "maxTokens": 2000,
                "systemPrompt": "你是一个专业的Reddit故事视频内容创作者。"
            }
        }
        
        try:
            response = await client.put(
                f"{BACKEND_URL}/api/settings",
                json=update_data
            )
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   更新设置成功: {data.get('success', False)}")
                if data.get('data'):
                    print(f"   新的LLM提供商: {data['data']['llm']['provider']}")
            else:
                print(f"   ❌ 错误: {response.text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 3. 测试LLM连通性（注意：这会失败，因为是测试API Key）
        print("\n3. 测试LLM连通性")
        test_data = {
            "api_key": TEST_API_KEY,
            "model": "gpt-3.5-turbo"
        }
        
        try:
            response = await client.post(
                f"{BACKEND_URL}/api/settings/test-llm",
                json=test_data
            )
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   测试结果: {data.get('success', False)}")
                print(f"   消息: {data.get('message', 'N/A')}")
            else:
                print(f"   ❌ 错误: {response.text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 4. 测试设置验证
        print("\n4. 测试设置验证")
        try:
            response = await client.get(f"{BACKEND_URL}/api/settings/validate")
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   验证成功: {data.get('success', False)}")
                if data.get('data'):
                    validation_data = data['data']
                    print(f"   设置有效: {validation_data.get('valid', False)}")
                    if validation_data.get('errors'):
                        print(f"   错误: {validation_data['errors']}")
                    if validation_data.get('warnings'):
                        print(f"   警告: {validation_data['warnings']}")
            else:
                print(f"   ❌ 错误: {response.text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        print("\n✅ 设置API测试完成!")

async def test_yunwu_api_format():
    """测试云雾API的请求格式（用于验证我们的集成是否正确）"""
    print("\n🌙 测试云雾API请求格式...")
    
    # 注意：这里使用真实的云雾API测试，需要有效的API Key
    # 如果没有有效的API Key，这个测试会失败，这是正常的
    test_request = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {
                "role": "user",
                "content": "你好，这是一个连接测试。请回复确认。"
            }
        ],
        "max_tokens": 50,
        "temperature": 0.1
    }
    
    print(f"📋 请求格式验证:")
    print(f"   URL: https://yunwu.ai/v1/chat/completions")
    print(f"   方法: POST")
    print(f"   请求体: {json.dumps(test_request, indent=2, ensure_ascii=False)}")
    print("   请求头: Authorization: Bearer <api_key>")
    print("   请求头: Content-Type: application/json")
    
    print("\n💡 说明: 要进行真实的API测试，请提供有效的云雾API密钥")

def print_frontend_guide():
    """打印前端使用指南"""
    print("\n" + "="*60)
    print("📱 前端集成指南")
    print("="*60)
    print("""
1. 启动后端服务:
   cd backend
   python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

2. 启动前端服务:
   cd frontend
   npm run dev

3. 访问设置页面:
   http://localhost:3000/settings

4. 测试云雾API:
   - 选择服务提供商: 云雾API
   - 输入有效的API Key (yk-开头)
   - 选择模型: gpt-3.5-turbo 或其他支持的模型
   - 点击"测试连接"按钮
   - 保存设置

5. 云雾API支持的模型:
   - gpt-4o
   - gpt-4o-mini
   - gpt-4-turbo
   - gpt-3.5-turbo
   - claude-3-5-sonnet-20241022
   - claude-3-5-haiku-20241022

⚠️  注意事项:
   - API Key格式应为 yk-xxx
   - 确保网络可以访问 https://yunwu.ai/
   - 测试时需要有效的API配额
    """)
    print("="*60)

async def main():
    """主测试函数"""
    print("🚀 Reddit故事视频生成器 - 设置页面集成测试")
    print("="*60)
    
    # 测试后端API
    await test_settings_api()
    
    # 测试云雾API格式
    await test_yunwu_api_format()
    
    # 显示前端指南
    print_frontend_guide()

if __name__ == "__main__":
    asyncio.run(main())
