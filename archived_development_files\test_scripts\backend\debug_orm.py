#!/usr/bin/env python3
"""
调试ORM查询问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.src.core.database import get_db
from backend.src.models.resources import VideoMaterial
import sqlite3

def debug_orm_vs_sql():
    """调试ORM vs 原始SQL"""
    print("=== 调试ORM查询问题 ===")
    
    # 1. 原始SQL查询
    print("\n1. 原始SQL查询:")
    try:
        conn = sqlite3.connect('D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db')
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM video_materials")
        count = cursor.fetchone()[0]
        print(f"  SQL查询结果: {count} 条记录")
        
        cursor.execute("SELECT id, name, file_path FROM video_materials LIMIT 3")
        rows = cursor.fetchall()
        for row in rows:
            print(f"  - {row}")
        conn.close()
    except Exception as e:
        print(f"  SQL查询失败: {e}")
    
    # 2. ORM查询
    print("\n2. ORM查询:")
    try:
        db_gen = get_db()
        db = next(db_gen)
        
        count = db.query(VideoMaterial).count()
        print(f"  ORM查询结果: {count} 条记录")
        
        materials = db.query(VideoMaterial).limit(3).all()
        for material in materials:
            print(f"  - {material.id}, {material.name}, {material.file_path}")
            
        db.close()
    except Exception as e:
        print(f"  ORM查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_orm_vs_sql()
