#!/usr/bin/env python3
"""
测试视频预览功能修复
验证视频文件能否正常播放
"""

import requests
import json

def test_video_preview_fix():
    """测试视频预览功能修复"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试视频预览功能修复")
    print("=" * 50)
    
    try:
        # 1. 获取视频素材列表
        print("1. 获取视频素材列表...")
        response = requests.get(f"{base_url}/api/video-materials")
        if response.status_code != 200:
            print(f"❌ 获取素材列表失败: {response.status_code}")
            return False
        
        data = response.json()
        materials = data.get('data', [])
        video_materials = [m for m in materials if m.get('type') == 'video']
        
        print(f"✅ 找到 {len(video_materials)} 个视频素材")
        
        if len(video_materials) == 0:
            print("ℹ️ 没有视频素材可测试，请先上传一些视频文件")
            return True
        
        # 2. 测试视频文件访问
        for i, material in enumerate(video_materials[:3]):  # 只测试前3个
            material_id = material.get('id')
            material_name = material.get('name')
            material_url = material.get('url')
            
            print(f"\n2.{i+1} 测试视频: {material_name}")
            print(f"   - 素材ID: {material_id}")
            print(f"   - URL路径: {material_url}")
            
            if material_url:
                # 测试文件端点
                file_url = f"{base_url}{material_url}"
                print(f"   - 完整URL: {file_url}")
                
                try:
                    # 发送HEAD请求检查文件是否可访问
                    head_response = requests.head(file_url, timeout=10)
                    if head_response.status_code == 200:
                        print(f"   ✅ 文件可访问 (状态码: {head_response.status_code})")
                        
                        # 检查Content-Type
                        content_type = head_response.headers.get('content-type', 'unknown')
                        print(f"   - Content-Type: {content_type}")
                        
                        # 检查文件大小
                        content_length = head_response.headers.get('content-length')
                        if content_length:
                            size_mb = int(content_length) / (1024 * 1024)
                            print(f"   - 文件大小: {size_mb:.2f} MB")
                        
                    else:
                        print(f"   ❌ 文件不可访问 (状态码: {head_response.status_code})")
                        
                except requests.exceptions.Timeout:
                    print(f"   ❌ 请求超时")
                except Exception as e:
                    print(f"   ❌ 访问失败: {e}")
            else:
                print(f"   ❌ 没有URL路径")
        
        # 3. 显示前端预览URL构建逻辑
        print(f"\n3. 前端预览URL构建:")
        print(f"   - 后端基础URL: {base_url}")
        print(f"   - 视频端点格式: /api/video-materials/file/{{material_id}}")
        print(f"   - 完整URL格式: {base_url}/api/video-materials/file/{{material_id}}")
        
        print(f"\n🎯 修复内容:")
        print(f"✅ 后端添加了文件服务端点: /api/video-materials/file/{{material_id}}")
        print(f"✅ 修改了VideoMaterial模型的url字段生成逻辑")
        print(f"✅ 前端预览组件构建完整的HTTP URL")
        print(f"✅ 添加了视频加载错误处理和调试信息")
        
        print(f"\n📋 测试验证:")
        print(f"□ 点击视频预览按钮")
        print(f"□ 检查预览模态框是否打开")
        print(f"□ 检查视频是否能正常加载和播放")
        print(f"□ 检查视频控制按钮是否正常工作")
        print(f"□ 检查控制台是否有错误信息")
        
        print(f"\n✅ 视频预览功能测试完成!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保服务器已启动")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_video_preview_fix()
