#!/usr/bin/env python3
"""
端到端测试封面生成，检查头像是否正确显示
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

async def test_cover_generation():
    """测试封面生成"""
    try:
        from src.core.database import get_db_session
        from src.models.accounts import Account
        from src.models.resources import CoverTemplate
        from src.services.cover_screenshot_service import cover_screenshot_service
        
        print("=== 端到端测试封面生成 ===")
        
        db = get_db_session()
        
        # 获取第一个账号
        account = db.query(Account).first()
        if not account:
            print("未找到账号")
            return
        
        # 获取第一个模板
        template = db.query(CoverTemplate).first()
        if not template:
            print("未找到封面模板")
            return
        
        print(f"使用账号: {account.name} (ID: {account.id})")
        print(f"使用模板: {template.name} (ID: {template.id})")
        print(f"账号头像文件路径: {account.avatar_file_path}")
        
        # 检查头像文件是否存在
        if account.avatar_file_path:
            backend_dir = Path(__file__).parent / 'backend'
            avatar_full_path = backend_dir / account.avatar_file_path
            print(f"头像完整路径: {avatar_full_path}")
            print(f"头像文件存在: {avatar_full_path.exists()}")
        
        # 生成封面
        output_path = "test_cover_with_avatar.png"
        title = "测试封面生成，检查头像是否正确显示"
        
        print(f"\n开始生成封面...")
        print(f"输出路径: {output_path}")
        print(f"标题: {title}")
        
        # 启用详细日志
        import logging
        logging.basicConfig(level=logging.INFO)
        
        success = await cover_screenshot_service.generate_cover_screenshot(
            template_id=template.id,
            account=account,
            title=title,
            output_path=output_path,
            db=db
        )
        
        if success:
            print(f"\n✅ 封面生成成功！")
            print(f"生成的文件: {output_path}")
            print(f"文件大小: {os.path.getsize(output_path) if os.path.exists(output_path) else '文件不存在'} bytes")
        else:
            print(f"\n❌ 封面生成失败")
        
        db.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_cover_generation())
