'use client'

import { useEffect } from 'react'

/**
 * 扩展干扰防护组件
 * 处理浏览器扩展对DOM的修改，防止hydration错误
 */
export function ExtensionProtection() {
  useEffect(() => {
    // 清理扩展添加的属性
    const cleanupExtensionAttributes = () => {
      const body = document.body
      const attributesToRemove = [
        'mpa-version',
        'mpa-extension-id',
        'data-extension-id',
        'data-mpa-version'
      ]
      
      attributesToRemove.forEach(attr => {
        if (body.hasAttribute(attr)) {
          body.removeAttribute(attr)
        }
      })
    }

    // 立即清理一次
    cleanupExtensionAttributes()

    // 监听DOM变化，防止扩展重新添加属性
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.target === document.body) {
          const attrName = mutation.attributeName
          if (attrName && (attrName.includes('mpa-') || attrName.includes('extension'))) {
            cleanupExtensionAttributes()
          }
        }
      })
    })

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['mpa-version', 'mpa-extension-id', 'data-extension-id', 'data-mpa-version']
    })

    // 定期清理（防止某些扩展频繁添加属性）
    const intervalId = setInterval(cleanupExtensionAttributes, 1000)

    return () => {
      observer.disconnect()
      clearInterval(intervalId)
    }
  }, [])

  return null // 这个组件不渲染任何内容
}
