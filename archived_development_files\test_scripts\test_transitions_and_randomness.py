#!/usr/bin/env python3
"""
测试新的转场效果和素材随机性
"""

import os
import sys
import asyncio
import ffmpeg
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "backend" / "src"))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置路径
MATERIALS_DIR = Path("backend/uploads/video_materials")
OUTPUT_DIR = Path("test_outputs")
OUTPUT_DIR.mkdir(exist_ok=True)

async def run_ffmpeg_async(stream, description="FFmpeg操作"):
    """异步运行FFmpeg命令"""
    try:
        logger.info(f"开始执行: {description}")
        cmd = ffmpeg.compile(stream)
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            logger.info(f"✅ {description} 成功完成")
            return True
        else:
            logger.error(f"❌ {description} 失败")
            logger.error(f"错误输出: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ {description} 执行异常: {e}")
        return False

def get_video_info(video_path):
    """获取视频信息"""
    try:
        probe = ffmpeg.probe(str(video_path))
        video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
        if video_stream:
            duration = float(video_stream.get('duration', 0))
            return {'duration': duration}
    except Exception as e:
        logger.error(f"获取视频信息失败: {e}")
        return None

def select_test_videos(count=5):
    """选择测试视频"""
    video_files = []
    
    for file_path in MATERIALS_DIR.glob("*.mp4"):
        if "thumb_" not in file_path.name:
            info = get_video_info(file_path)
            if info and info['duration'] > 0:
                video_files.append({
                    'path': file_path,
                    'duration': info['duration']
                })
                
                if len(video_files) >= count:
                    break
    
    return video_files

def create_transitions_with_new_effects(input_streams, transition_type, transition_duration=0.5, durations=None):
    """使用新的转场效果创建视频"""
    if len(input_streams) < 2:
        return input_streams[0]
    
    if durations is None:
        durations = [3.0] * len(input_streams)
    
    # 转场类型别名映射
    TRANSITION_ALIASES = {
        'slide_left': 'slideleft',
        'slide_right': 'slideright', 
        'slide_up': 'slideup',
        'slide_down': 'slidedown',
        'wipe_left': 'wipeleft',
        'wipe_right': 'wiperight',
        'wipe_up': 'wipeup',
        'wipe_down': 'wipedown',
        'circle_open': 'circleopen',
        'circle_close': 'circleclose',
        'smooth_left': 'smoothleft',
        'smooth_right': 'smoothright',
        'smooth_up': 'smoothup',
        'smooth_down': 'smoothdown'
    }
    
    # 处理别名
    actual_transition_type = TRANSITION_ALIASES.get(transition_type, transition_type)
    
    # 统一帧率
    normalized_streams = []
    for stream in input_streams:
        normalized = stream.filter('fps', fps=30)
        normalized_streams.append(normalized)
    
    # 逐步构建转场链
    result = normalized_streams[0]
    current_result_duration = durations[0]
    
    for i in range(1, len(normalized_streams)):
        next_segment_duration = durations[i]
        offset = max(0, current_result_duration - transition_duration)
        
        logger.info(f"转场 {i}: 类型={actual_transition_type}, offset={offset:.2f}s, duration={transition_duration}s")
        
        # 应用转场
        result = ffmpeg.filter(
            [result, normalized_streams[i]],
            'xfade',
            transition=actual_transition_type,
            duration=transition_duration,
            offset=offset
        )
        
        # 更新结果时长
        current_result_duration = offset + next_segment_duration
    
    return result

async def test_transition_effects():
    """测试不同的转场效果"""
    logger.info("=== 测试新的转场效果 ===")
    
    # 选择测试视频
    video_files = select_test_videos(4)
    if len(video_files) < 4:
        logger.error("需要至少4个测试视频")
        return False
    
    # 测试的转场效果
    transitions_to_test = [
        'fade',
        'dissolve', 
        'slide_left',
        'slide_right',
        'wipe_left',
        'wipe_right',
        'circle_open',
        'smooth_left'
    ]
    
    success_count = 0
    
    for transition_type in transitions_to_test:
        logger.info(f"\n--- 测试转场效果: {transition_type} ---")
        
        try:
            # 创建输入流
            input_streams = []
            durations = []
            
            for video_file in video_files:
                stream = ffmpeg.input(str(video_file['path']))
                stream = stream.filter('scale', 1080, 1920).filter('fps', fps=30)
                input_streams.append(stream)
                durations.append(video_file['duration'])
            
            # 应用转场效果
            video_stream = create_transitions_with_new_effects(
                input_streams, transition_type, 0.5, durations
            )
            
            # 输出文件
            output_path = OUTPUT_DIR / f"test_transition_{transition_type}.mp4"
            output_stream = ffmpeg.output(
                video_stream, 
                str(output_path), 
                vcodec='libx264', 
                preset='fast', 
                pix_fmt='yuv420p'
            )
            
            success = await run_ffmpeg_async(output_stream.overwrite_output(), f"转场效果 {transition_type}")
            
            if success and output_path.exists():
                info = get_video_info(output_path)
                if info:
                    logger.info(f"✅ {transition_type} 转场测试成功，时长: {info['duration']:.2f}s")
                    success_count += 1
                else:
                    logger.error(f"❌ {transition_type} 转场测试失败：无法获取视频信息")
            else:
                logger.error(f"❌ {transition_type} 转场测试失败")
                
        except Exception as e:
            logger.error(f"❌ {transition_type} 转场测试异常: {e}")
    
    logger.info(f"\n转场效果测试完成: {success_count}/{len(transitions_to_test)} 成功")
    return success_count > 0

def test_material_randomness():
    """测试素材选择的随机性"""
    logger.info("\n=== 测试素材选择随机性 ===")
    
    # 模拟素材数据
    class MockVideoMaterial:
        def __init__(self, id, duration):
            self.id = id
            self.duration = duration
    
    materials = [
        MockVideoMaterial(1, 5.0),   # 长素材
        MockVideoMaterial(2, 4.5),
        MockVideoMaterial(3, 3.8),
        MockVideoMaterial(4, 2.5),   # 中等素材
        MockVideoMaterial(5, 2.0),
        MockVideoMaterial(6, 1.8),
        MockVideoMaterial(7, 1.5),
        MockVideoMaterial(8, 1.2),
        MockVideoMaterial(9, 0.8),   # 短素材
        MockVideoMaterial(10, 0.5),
    ]
    
    # 模拟新的素材选择算法
    def smart_select_materials_with_randomness(materials, audio_duration):
        import random
        
        # 获取素材时长
        material_durations = [(m, float(m.duration)) for m in materials]
        
        # 先随机打乱
        random.shuffle(material_durations)
        
        # 按时长分组
        long_materials = [(m, d) for m, d in material_durations if d > 3.0]
        medium_materials = [(m, d) for m, d in material_durations if 1.0 <= d <= 3.0]
        short_materials = [(m, d) for m, d in material_durations if d < 1.0]
        
        # 每组内部再次随机打乱
        random.shuffle(long_materials)
        random.shuffle(medium_materials)
        random.shuffle(short_materials)
        
        # 重新组合
        material_durations = long_materials + medium_materials + short_materials
        
        # 选择素材
        selected_materials = []
        total_duration = 0.0
        
        for material, duration in material_durations:
            selected_materials.append(material)
            total_duration += duration
            
            if total_duration >= audio_duration:
                break
        
        # 最终随机打乱播放顺序
        random.shuffle(selected_materials)
        
        return selected_materials
    
    # 测试多次选择的随机性
    audio_duration = 15.0
    selections = []
    
    for i in range(5):
        selected = smart_select_materials_with_randomness(materials, audio_duration)
        selection_ids = [m.id for m in selected]
        selections.append(selection_ids)
        logger.info(f"第 {i+1} 次选择: {selection_ids}")
    
    # 检查随机性
    unique_selections = set(tuple(s) for s in selections)
    randomness_score = len(unique_selections) / len(selections) * 100
    
    logger.info(f"\n随机性测试结果:")
    logger.info(f"  总测试次数: {len(selections)}")
    logger.info(f"  不同组合数: {len(unique_selections)}")
    logger.info(f"  随机性评分: {randomness_score:.1f}%")
    
    if randomness_score >= 80:
        logger.info("✅ 素材选择随机性良好")
        return True
    elif randomness_score >= 60:
        logger.info("⚠️ 素材选择随机性一般")
        return True
    else:
        logger.error("❌ 素材选择随机性不足")
        return False

async def main():
    """主测试函数"""
    logger.info("🧪 开始测试转场效果和素材随机性...")
    
    # 测试转场效果
    transition_success = await test_transition_effects()
    
    # 测试素材随机性
    randomness_success = test_material_randomness()
    
    logger.info("\n🎉 测试总结:")
    if transition_success:
        logger.info("✅ 转场效果测试通过")
    else:
        logger.error("❌ 转场效果测试失败")
    
    if randomness_success:
        logger.info("✅ 素材随机性测试通过")
    else:
        logger.error("❌ 素材随机性测试失败")
    
    if transition_success and randomness_success:
        logger.info("\n🚀 所有测试通过！现在支持更多转场效果，且批量生成时每个视频都会使用不同的素材组合！")
        logger.info("\n📋 新增转场效果:")
        logger.info("- slide_left/right/up/down: 滑动转场")
        logger.info("- wipe_left/right/up/down: 擦除转场") 
        logger.info("- circle_open/close: 圆形转场")
        logger.info("- smooth_left/right/up/down: 平滑转场")
        logger.info("\n🔀 素材随机性改进:")
        logger.info("- 每次生成都会随机选择不同的素材组合")
        logger.info("- 批量生成时不会重复使用相同的素材序列")
    else:
        logger.error("\n❌ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
