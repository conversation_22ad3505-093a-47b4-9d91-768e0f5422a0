#!/usr/bin/env python3
"""
测试前端API集成 - 验证所有API端点的可用性和数据格式
"""

import requests
import json
from typing import Dict, Any

API_BASE = "http://localhost:8000/api"

def test_api_endpoint(endpoint: str, method: str = "GET") -> Dict[str, Any]:
    """测试单个API端点"""
    try:
        url = f"{API_BASE}{endpoint}"
        response = requests.request(method, url)
        
        print(f"\n🔗 {method} {endpoint}")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success")
            
            # 检查后端响应格式
            if isinstance(data, dict) and 'success' in data and 'data' in data:
                print(f"   📦 Backend format: {{success: {data.get('success')}, data: [...]}}") 
                if isinstance(data['data'], list):
                    print(f"   📊 Data count: {len(data['data'])}")
                    if len(data['data']) > 0:
                        print(f"   🔍 Sample item keys: {list(data['data'][0].keys())}")
                else:
                    print(f"   📊 Data type: {type(data['data'])}")
            else:
                print(f"   📦 Direct format: {type(data)}")
                
            return {"success": True, "data": data}
        else:
            error = response.text
            print(f"   ❌ Error: {error}")
            return {"success": False, "error": error}
            
    except Exception as e:
        print(f"   💥 Exception: {e}")
        return {"success": False, "error": str(e)}

def main():
    """测试所有前端需要的API端点"""
    print("🚀 测试前端API集成")
    print("=" * 50)
    
    # 测试所有前端使用的API端点
    endpoints = [
        "/accounts",           # 账号列表
        "/video-categories",   # 视频素材分类
        "/video-materials",    # 视频素材列表
        "/prompts",           # 提示词列表
        "/cover-templates",   # 封面模板列表
        "/settings",          # 系统设置
    ]
    
    results = {}
    
    for endpoint in endpoints:
        results[endpoint] = test_api_endpoint(endpoint)
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    success_count = sum(1 for r in results.values() if r["success"])
    total_count = len(results)
    
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 所有API端点测试通过！前端数据联动应该可以正常工作。")
    else:
        print("\n⚠️  有API端点测试失败，需要检查后端服务。")
        
    # 特别检查重要的数据结构
    print("\n📋 关键数据结构检查:")
    
    if "/video-categories" in results and results["/video-categories"]["success"]:
        data = results["/video-categories"]["data"]
        if "data" in data and len(data["data"]) > 0:
            sample = data["data"][0]
            required_fields = ["id", "name", "material_count"]
            missing = [f for f in required_fields if f not in sample]
            if missing:
                print(f"   ⚠️  video-categories 缺少字段: {missing}")
            else:
                print(f"   ✅ video-categories 数据结构正确")
    
    if "/prompts" in results and results["/prompts"]["success"]:
        data = results["/prompts"]["data"]
        if "data" in data and len(data["data"]) > 0:
            sample = data["data"][0]
            required_fields = ["id", "name", "category", "content"]
            missing = [f for f in required_fields if f not in sample]
            if missing:
                print(f"   ⚠️  prompts 缺少字段: {missing}")
            else:
                print(f"   ✅ prompts 数据结构正确")
                
                # 检查分类提取
                categories = list(set(item["category"] for item in data["data"]))
                print(f"   📂 提示词分类: {categories}")
    
    if "/cover-templates" in results and results["/cover-templates"]["success"]:
        data = results["/cover-templates"]["data"]
        if "data" in data and len(data["data"]) > 0:
            sample = data["data"][0]
            required_fields = ["id", "name"]
            missing = [f for f in required_fields if f not in sample]
            if missing:
                print(f"   ⚠️  cover-templates 缺少字段: {missing}")
            else:
                print(f"   ✅ cover-templates 数据结构正确")

if __name__ == "__main__":
    main()
