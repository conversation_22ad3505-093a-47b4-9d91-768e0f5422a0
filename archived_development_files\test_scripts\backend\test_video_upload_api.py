#!/usr/bin/env python3
"""
测试视频上传API
"""

import requests
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

def test_video_upload_api():
    """测试视频上传API"""
    base_url = "http://localhost:8000"
    
    print("=== 测试视频上传API ===")
    
    # 1. 测试上传接口是否可访问
    try:
        response = requests.get(f"{base_url}/api/video-materials/")
        print(f"✓ API基础连接测试: {response.status_code}")
        if response.status_code == 200:
            materials = response.json()
            print(f"  当前素材数量: {len(materials)}")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"✗ API连接失败: {e}")
        return False
    
    # 2. 创建测试视频文件(如果不存在)
    test_video_path = Path("test_video.mp4")
    if not test_video_path.exists():
        print("⚠ 未找到测试视频文件，请提供一个MP4文件用于测试")
        return False
    
    # 3. 测试单文件上传
    print("\n--- 测试单文件上传 ---")
    try:
        with open(test_video_path, 'rb') as f:
            files = {'file': (test_video_path.name, f, 'video/mp4')}
            data = {
                'category': 'test',
                'tags': 'test,upload'
            }
            
            response = requests.post(
                f"{base_url}/api/video-materials/upload",
                files=files,
                data=data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 单文件上传成功")
                print(f"  响应: {result}")
            else:
                print(f"✗ 单文件上传失败: {response.status_code}")
                print(f"  错误: {response.text}")
                
    except Exception as e:
        print(f"✗ 单文件上传异常: {e}")
    
    # 4. 测试批量上传
    print("\n--- 测试批量上传 ---")
    try:
        with open(test_video_path, 'rb') as f1, open(test_video_path, 'rb') as f2:
            files = [
                ('files', (f"{test_video_path.stem}_1.mp4", f1, 'video/mp4')),
                ('files', (f"{test_video_path.stem}_2.mp4", f2, 'video/mp4'))
            ]
            data = {'category': 'test_batch'}
            
            response = requests.post(
                f"{base_url}/api/video-materials/upload/bulk",
                files=files,
                data=data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 批量上传成功")
                print(f"  成功: {result.get('success_count', 0)}")
                print(f"  失败: {result.get('failed_count', 0)}")
            else:
                print(f"✗ 批量上传失败: {response.status_code}")
                print(f"  错误: {response.text}")
                
    except Exception as e:
        print(f"✗ 批量上传异常: {e}")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    test_video_upload_api()
