#!/usr/bin/env python3
"""
最简单的fade测试
"""

import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def simple_fade_test():
    """最简单的fade测试"""
    
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    if not Path(cover_path).exists():
        logger.error("封面文件不存在")
        return
    
    # 测试1: 只对封面做fade，不overlay
    logger.info("测试1: 纯封面fade")
    cmd1 = [
        'ffmpeg', '-y', '-v', 'verbose',
        '-loop', '1', '-i', cover_path,
        '-vf', 'scale=400:300,fade=in:0:30,fade=out:120:30',
        '-t', '5',
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        'simple_fade_only.mp4'
    ]
    
    logger.info(f"执行: {' '.join(cmd1)}")
    result1 = subprocess.run(cmd1)
    
    if result1.returncode == 0:
        logger.info("✅ simple_fade_only.mp4 生成成功")
    else:
        logger.error("❌ 生成失败")
    
    # 测试2: 使用alpha通道fade
    logger.info("测试2: alpha通道fade")
    cmd2 = [
        'ffmpeg', '-y', '-v', 'verbose',
        '-loop', '1', '-i', cover_path,
        '-vf', 'scale=400:300,format=rgba,fade=in:0:30:alpha=1,fade=out:120:30:alpha=1',
        '-t', '5',
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        'alpha_fade_only.mp4'
    ]
    
    logger.info(f"执行: {' '.join(cmd2)}")
    result2 = subprocess.run(cmd2)
    
    if result2.returncode == 0:
        logger.info("✅ alpha_fade_only.mp4 生成成功")
    else:
        logger.error("❌ 生成失败")

if __name__ == "__main__":
    simple_fade_test()
