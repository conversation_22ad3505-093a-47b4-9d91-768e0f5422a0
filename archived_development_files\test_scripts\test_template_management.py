"""
测试封面模板管理重构功能
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from src.services.template_import_service import template_import_service
from src.services.app_init_service import app_init_service
from src.core.database import get_db_session
from src.models.resources import CoverTemplate

def test_template_import():
    """测试模板导入功能"""
    print("=== 测试封面模板管理重构功能 ===\n")
    
    try:
        # 1. 测试应用初始化
        print("1. 测试应用初始化...")
        app_init_service.init_directories()
        print("✓ 目录初始化成功")
        
        # 2. 测试模板导入
        print("\n2. 测试HTML模板导入...")
        if os.path.exists("social_post_template.html"):
            db = get_db_session()
            try:
                # 检查是否已存在
                existing = db.query(CoverTemplate).filter(
                    CoverTemplate.name == "社交媒体帖子模板"
                ).first()
                
                if existing:
                    print("✓ 社交媒体帖子模板已存在")
                    template = existing
                else:
                    template = template_import_service.import_html_template(
                        html_file_path="social_post_template.html",
                        name="社交媒体帖子模板",
                        description="适用于各种社交媒体平台的帖子封面模板",
                        category="社交媒体",
                        db=db
                    )
                    print("✓ 社交媒体帖子模板导入成功")
                
                # 3. 测试变量提取
                print("\n3. 测试变量提取...")
                variables = template_import_service.list_template_variables(template.id, db)
                print(f"✓ 提取到变量: {variables}")
                
                # 4. 测试模板渲染
                print("\n4. 测试模板渲染...")
                test_variables = {
                    "avatar": "https://example.com/avatar.jpg",
                    "account_name": "测试用户",
                    "title": "这是一个测试标题",
                    "description": "这是一个测试描述内容"
                }
                
                rendered_html = template_import_service.render_template(
                    template_id=template.id,
                    variables=test_variables,
                    db=db
                )
                print("✓ 模板渲染成功")
                print(f"渲染后的HTML长度: {len(rendered_html)} 字符")
                
                # 5. 测试前端格式转换
                print("\n5. 测试前端格式转换...")
                frontend_data = template.to_frontend_format()
                print(f"✓ 模板名称: {frontend_data['name']}")
                print(f"✓ 模板类型: {frontend_data['templateType']}")
                print(f"✓ 变量数量: {frontend_data['variableCount']}")
                print(f"✓ 有变量: {frontend_data['hasVariables']}")
                
            finally:
                db.close()
        else:
            print("✗ 社交媒体帖子模板文件不存在")
        
        # 6. 测试数据库初始化
        print("\n6. 测试数据库模板初始化...")
        db = get_db_session()
        try:
            before_count = db.query(CoverTemplate).count()
            app_init_service.init_database_templates(db)
            after_count = db.query(CoverTemplate).count()
            print(f"✓ 初始化前模板数量: {before_count}")
            print(f"✓ 初始化后模板数量: {after_count}")
            
            # 列出所有模板
            templates = db.query(CoverTemplate).all()
            print("\n现有模板:")
            for template in templates:
                frontend_data = template.to_frontend_format()
                print(f"  - {template.name} ({frontend_data['templateType']}) - {template.category}")
                if frontend_data['hasVariables']:
                    print(f"    变量: {template.variables}")
        
        finally:
            db.close()
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_template_import()
