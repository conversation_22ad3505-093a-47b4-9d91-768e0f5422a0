#!/usr/bin/env python
"""
快速验证修复效果的脚本
"""

import subprocess
import time
import requests
import sys
import os

def start_backend():
    """启动后端服务"""
    try:
        print("正在启动后端服务...")
        backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
        cmd = [sys.executable, '-m', 'uvicorn', 'main:app', '--host', '0.0.0.0', '--port', '8001']
        
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一段时间让服务启动
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ 后端服务启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 后端服务启动失败:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 启动后端服务时出错: {str(e)}")
        return None

def test_api():
    """测试API接口"""
    try:
        print("测试获取作业列表API...")
        response = requests.get("http://localhost:8001/api/video-generator/jobs?page=1&limit=10", timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API调用成功!")
            data = response.json()
            print(f"返回的作业数量: {len(data.get('jobs', []))}")
            return True
        else:
            print(f"❌ API调用失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务")
        return False
    except Exception as e:
        print(f"❌ API测试出错: {str(e)}")
        return False

def main():
    print("=== 视频生成API修复验证 ===")
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("无法启动后端服务，退出测试")
        return
    
    try:
        # 测试API
        if test_api():
            print("🎉 修复验证成功！")
        else:
            print("❌ API仍有问题，需要进一步调试")
    finally:
        # 关闭后端服务
        print("正在关闭后端服务...")
        backend_process.terminate()
        backend_process.wait()
        print("后端服务已关闭")

if __name__ == "__main__":
    main()
