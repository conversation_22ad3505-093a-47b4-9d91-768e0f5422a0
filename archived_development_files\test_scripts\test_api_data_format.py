"""
测试单个模板API返回格式的脚本
验证后端返回的数据结构是否符合前端期望
"""
import requests
import json
import sys
import time

def test_template_api():
    """测试单个模板API返回格式"""
    
    # 首先获取模板列表，找一个可用的模板ID
    print("1. 获取模板列表...")
    try:
        response = requests.get("http://localhost:8001/api/cover-templates/")
        if response.status_code == 200:
            data = response.json()
            print(f"模板列表API响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success') and data.get('data', {}).get('templates'):
                template_list = data['data']['templates']
                if template_list:
                    template_id = template_list[0]['id']
                    print(f"找到模板ID: {template_id}")
                    
                    # 测试单个模板API
                    print(f"\n2. 获取单个模板详情 (ID: {template_id})...")
                    detail_response = requests.get(f"http://localhost:8001/api/cover-templates/{template_id}")
                    print(f"状态码: {detail_response.status_code}")
                    
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        print(f"单个模板API响应: {json.dumps(detail_data, indent=2, ensure_ascii=False)}")
                        
                        # 验证数据结构
                        print("\n3. 验证数据结构...")
                        if detail_data.get('success'):
                            template_data = detail_data.get('data')
                            if template_data:
                                print(f"✓ 模板数据存在")
                                print(f"  - id: {template_data.get('id')}")
                                print(f"  - name: {template_data.get('name')}")
                                print(f"  - elements: {len(template_data.get('elements', []))} 个元素")
                                print(f"  - background: {template_data.get('background')}")
                                
                                # 检查elements字段
                                elements = template_data.get('elements', [])
                                if elements:
                                    print(f"  元素详情:")
                                    for i, elem in enumerate(elements[:3]):  # 只显示前3个
                                        print(f"    [{i}] type: {elem.get('type')}, x: {elem.get('x')}, y: {elem.get('y')}")
                                
                                # 检查background字段
                                background = template_data.get('background')
                                if background:
                                    print(f"  背景详情: type={background.get('type')}, value={background.get('value')}")
                                
                                return True
                            else:
                                print("✗ 模板数据为空")
                        else:
                            print(f"✗ API返回success=false: {detail_data.get('message')}")
                    else:
                        print(f"✗ 获取模板详情失败: {detail_response.status_code}")
                        print(detail_response.text)
                else:
                    print("✗ 模板列表为空")
            else:
                print(f"✗ 模板列表API失败: {data}")
        else:
            print(f"✗ 获取模板列表失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    return False

def create_test_template():
    """创建一个测试模板用于验证"""
    print("\n4. 创建测试模板...")
    
    test_template = {
        "name": f"测试模板_{int(time.time())}",
        "description": "API测试用模板",
        "category": "test",
        "width": 1920,
        "height": 1080,
        "elements": [
            {
                "id": "text_0",
                "type": "text",
                "x": 100,
                "y": 100,
                "width": 200,
                "height": 50,
                "content": "测试文本",
                "fontSize": 24,
                "color": "#000000"
            },
            {
                "id": "rect_1", 
                "type": "rectangle",
                "x": 200,
                "y": 200,
                "width": 150,
                "height": 100,
                "backgroundColor": "#ff0000"
            }
        ],
        "background": {
            "type": "gradient",
            "value": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        }
    }
    
    try:
        response = requests.post("http://localhost:8001/api/cover-templates/", json=test_template)
        if response.status_code == 201:
            data = response.json()
            print(f"✓ 创建测试模板成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success') and data.get('data'):
                template_id = data['data'].get('id')
                print(f"新模板ID: {template_id}")
                
                # 立即获取这个模板验证数据
                print(f"\n5. 验证新创建的模板...")
                detail_response = requests.get(f"http://localhost:8001/api/cover-templates/{template_id}")
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print(f"新模板详情: {json.dumps(detail_data, indent=2, ensure_ascii=False)}")
                    
                    # 验证elements和background是否正确保存
                    if detail_data.get('success'):
                        saved_template = detail_data.get('data')
                        saved_elements = saved_template.get('elements', [])
                        saved_background = saved_template.get('background')
                        
                        print(f"✓ 保存的元素数量: {len(saved_elements)}")
                        print(f"✓ 保存的背景: {saved_background}")
                        
                        # 详细对比
                        if len(saved_elements) == len(test_template['elements']):
                            print("✓ 元素数量匹配")
                        else:
                            print(f"✗ 元素数量不匹配: 期望{len(test_template['elements'])}, 实际{len(saved_elements)}")
                        
                        if saved_background == test_template['background']:
                            print("✓ 背景配置匹配")
                        else:
                            print(f"✗ 背景配置不匹配: 期望{test_template['background']}, 实际{saved_background}")
                        
                        return template_id
                    else:
                        print(f"✗ 获取新模板失败: {detail_data.get('message')}")
                else:
                    print(f"✗ 获取新模板详情失败: {detail_response.status_code}")
            else:
                print(f"✗ 创建模板响应格式错误: {data}")
        else:
            print(f"✗ 创建模板失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"✗ 创建测试模板失败: {e}")
    
    return None

if __name__ == "__main__":
    print("=== 测试封面模板API数据格式 ===\n")
    
    # 测试现有模板
    success = test_template_api()
    
    # 创建新模板测试
    new_template_id = create_test_template()
    
    print(f"\n=== 测试完成 ===")
    print(f"现有模板测试: {'成功' if success else '失败'}")
    print(f"新模板测试: {'成功' if new_template_id else '失败'}")
    
    if success or new_template_id:
        print("\n✓ API数据格式符合前端期望")
    else:
        print("\n✗ API数据格式需要调整")
