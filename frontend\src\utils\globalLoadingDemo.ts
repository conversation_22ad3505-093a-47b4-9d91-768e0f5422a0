/**
 * 全局Loading功能演示脚本
 * 快速验证所有改进效果
 */

import { useGlobalLoadingStore } from '@/store/globalLoadingStore'

export const demoGlobalLoading = () => {
  const { startGlobalLoading, updateTaskProgress, finishTask } = useGlobalLoadingStore.getState()

  console.log('🎬 开始全局Loading功能演示...')

  // 演示1: 模拟文件上传
  const demoFileUpload = () => {
    console.log('📁 演示1: 模拟文件上传')
    
    const taskId = startGlobalLoading({
      type: 'upload',
      title: '上传视频素材',
      description: '正在上传文件: demo-video.mp4',
      progress: 0,
      estimatedDuration: 12,
      simulateProgress: true
    })

    // 模拟真实上传进度覆盖
    setTimeout(() => {
      updateTaskProgress(taskId, 30, '正在上传文件: demo-video.mp4 (30%)')
    }, 3000)

    setTimeout(() => {
      updateTaskProgress(taskId, 60, '正在上传文件: demo-video.mp4 (60%)')
    }, 6000)

    setTimeout(() => {
      updateTaskProgress(taskId, 85, '正在处理视频...')
    }, 9000)

    setTimeout(() => {
      updateTaskProgress(taskId, 100, '上传完成!')
      setTimeout(() => finishTask(taskId), 1500)
    }, 12000)
  }

  // 演示2: 批量上传
  const demoBulkUpload = () => {
    console.log('📦 演示2: 批量上传')
    
    const taskId = startGlobalLoading({
      type: 'upload',
      title: '批量上传视频素材',
      description: '正在批量上传 5 个文件',
      progress: 0,
      estimatedDuration: 20,
      simulateProgress: true
    })

    setTimeout(() => {
      updateTaskProgress(taskId, 100, '批量上传完成! 成功: 5, 失败: 0')
      setTimeout(() => finishTask(taskId), 2000)
    }, 20000)
  }

  // 演示3: 快速任务
  const demoQuickTask = () => {
    console.log('⚡ 演示3: 快速任务')
    
    const taskId = startGlobalLoading({
      type: 'processing',
      title: '快速处理',
      description: '正在快速处理数据...',
      progress: 0,
      estimatedDuration: 5,
      simulateProgress: true
    })

    setTimeout(() => {
      updateTaskProgress(taskId, 100, '快速处理完成!')
      setTimeout(() => finishTask(taskId), 800)
    }, 5000)
  }

  return {
    demoFileUpload,
    demoBulkUpload,
    demoQuickTask
  }
}

// 在浏览器控制台中使用
if (typeof window !== 'undefined') {
  (window as any).demoGlobalLoading = demoGlobalLoading
  console.log('🎮 全局Loading演示已加载到 window.demoGlobalLoading()')
  console.log('使用方法:')
  console.log('const { demoFileUpload, demoBulkUpload, demoQuickTask } = window.demoGlobalLoading()')
  console.log('demoFileUpload() // 演示文件上传')
  console.log('demoBulkUpload() // 演示批量上传')
  console.log('demoQuickTask() // 演示快速任务')
}
