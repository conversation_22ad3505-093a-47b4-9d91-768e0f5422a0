#!/usr/bin/env python3
"""
测试修复后的zoom动画效果
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_zoom_fix():
    """测试修复后的zoom动画"""
    
    # 测试文件路径
    video_path = "test.mp4"
    cover_path = "test.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试修复后的zoom动画...")
    
    # Zoom动画测试配置
    test_configs = [
        {
            'name': '修复后缩放进入',
            'animation': 'zoom_in',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'fixed_zoom_in.mp4'
        },
        {
            'name': '修复后缩放退出',
            'animation': 'zoom_out',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'fixed_zoom_out.mp4'
        },
        {
            'name': '修复后缩放进入退出',
            'animation': 'zoom_in_out',
            'duration': 8.0,
            'animation_duration': 2.0,
            'output': 'fixed_zoom_in_out.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.6)  # 60%宽度
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"zoom_fix_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    start_time = time.time()
                    
                    # 应用封面叠加效果
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    total_duration = config['duration'] + 1  # 多1秒缓冲
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=total_duration
                    ).overwrite_output()
                    
                    # 执行，设置超时
                    import subprocess
                    
                    cmd = ffmpeg.compile(out)
                    
                    # 使用subprocess执行，设置超时
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    try:
                        # 等待最多60秒
                        stdout, stderr = process.communicate(timeout=60)
                        
                        if process.returncode == 0:
                            end_time = time.time()
                            processing_time = end_time - start_time
                            
                            # 检查结果
                            if Path(config['output']).exists():
                                file_size = Path(config['output']).stat().st_size
                                logger.info(f"✅ {config['name']} 测试成功!")
                                logger.info(f"   文件大小: {file_size} bytes")
                                logger.info(f"   处理时间: {processing_time:.2f}秒")
                                
                                # 详细说明预期效果
                                if config['animation'] == 'zoom_in':
                                    logger.info(f"   预期效果: 0-{config['animation_duration']}秒封面从小缩放到正常大小")
                                elif config['animation'] == 'zoom_out':
                                    zoom_start = config['duration'] - config['animation_duration']
                                    logger.info(f"   预期效果: {zoom_start}-{config['duration']}秒封面从正常大小缩放到小")
                                elif config['animation'] == 'zoom_in_out':
                                    zoom_out_start = config['duration'] - config['animation_duration']
                                    logger.info(f"   预期效果: 0-{config['animation_duration']}秒缩放进入，{zoom_out_start}-{config['duration']}秒缩放退出")
                                
                                success_count += 1
                            else:
                                logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        else:
                            logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                            if stderr:
                                logger.error(f"stderr: {stderr.decode('utf-8', errors='ignore')[:500]}...")
                            
                    except subprocess.TimeoutExpired:
                        logger.error(f"❌ {config['name']} 测试超时（60秒）")
                        process.kill()
                        process.communicate()
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
        
        logger.info(f"\n=== Zoom动画修复测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count > 0:
            logger.info("\n📋 修复后的Zoom动画测试文件:")
            logger.info("- fixed_zoom_in.mp4 (修复后缩放进入)")
            logger.info("- fixed_zoom_out.mp4 (修复后缩放退出)")
            logger.info("- fixed_zoom_in_out.mp4 (修复后缩放进入退出)")
            
            logger.info("\n🔍 关键修复点:")
            logger.info("1. 简化scale表达式：避免复杂的时间函数")
            logger.info("2. 添加括号：确保表达式正确解析")
            logger.info("3. 使用min/max函数：限制缩放范围")
            logger.info("4. 修复gte函数：使用正确的FFmpeg函数名")
            
            logger.info("\n🎬 如果zoom动画正常工作，说明修复成功！")
            
            return True
        else:
            logger.error("❌ 所有zoom动画测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("🚀 开始Zoom动画修复测试")
    
    success = test_zoom_fix()
    
    if success:
        logger.info("\n🎉 Zoom动画修复测试完成!")
        logger.info("如果zoom动画正常工作，说明所有动画效果都集成成功了！")
    else:
        logger.error("\n❌ Zoom动画修复测试失败")
        logger.error("需要进一步调试zoom动画问题")
        sys.exit(1)
