"""
快速测试修复后的封面模板API
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def quick_test():
    try:
        from main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        print("🔧 测试修复后的API...")
        
        # 1. 测试健康检查
        print("\n1. 健康检查...")
        response = client.get("/health")
        print(f"   Health: {response.status_code}")
        
        response = client.get("/api/health") 
        print(f"   API Health: {response.status_code}")
        
        # 2. 测试获取模板列表（这是错误截图中的问题）
        print("\n2. 测试模板列表...")
        response = client.get("/api/cover-templates")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response structure: {list(data.keys()) if isinstance(data, dict) else 'List'}")
            if isinstance(data, dict) and 'data' in data:
                templates = data.get('data', {}).get('templates', [])
                print(f"   Templates found: {len(templates)}")
            else:
                print(f"   Data type: {type(data)}")
        else:
            print(f"   Error: {response.text}")
        
        # 3. 测试统计信息
        print("\n3. 测试统计信息...")
        response = client.get("/api/cover-templates/stats")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Stats: {data}")
        else:
            print(f"   Error: {response.text}")
        
        # 4. 测试可用变量
        print("\n4. 测试可用变量...")
        response = client.get("/api/cover-templates/variables")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Variables: {data}")
        else:
            print(f"   Error: {response.text}")
        
        print("\n✅ API修复测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
