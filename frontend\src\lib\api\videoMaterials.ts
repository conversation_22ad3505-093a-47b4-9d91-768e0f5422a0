/**
 * 视频素材管理 API 客户端
 */

import { ApiResponse } from '@/types/store'

export interface VideoMaterialAPI {
  id: string
  name: string
  type: string  // 'video', 'image', 'gif'
  format: string  // 格式字符串 (如 'MP4')
  size: string  // 格式化的文件大小字符串 (如 '1000.0 KB')
  duration?: string  // 格式化的时长字符串 (如 '2:00')
  dimensions: {
    width: number
    height: number
  }
  aspectRatio: string
  path: string
  url: string
  thumbnailUrl?: string | null
  thumbnailPath?: string | null
  filePath: string  // 兼容字段
  category: string
  tags: string[]
  isBuiltIn: boolean
  createdAt?: string
  lastModified?: string
  metadata: {
    fileSize?: number
    format?: string
    frameRate?: number
    bitrate?: number
    thumbnailPath?: string
  }
}

export interface VideoMaterialCreateRequest {
  name: string
  file_path: string
  duration: number
  resolution: string
  category?: string
  tags?: string[]
  is_built_in?: boolean
  file_size?: number
  format?: string
  frame_rate?: number
  bitrate?: number
  thumbnail_path?: string
}

export interface VideoMaterialUpdateRequest {
  name?: string
  category?: string
  tags?: string[]
}

export interface VideoMaterialQueryParams {
  category?: string
  search?: string
  skip?: number
  limit?: number
}

export interface BulkVideoMaterialResponse {
  success: VideoMaterialAPI[]
  failed: Array<{ name: string; error: string }>
  total: number
  success_count: number
  failed_count: number
}

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

class VideoMaterialApiClient {
  private baseURL = `${API_BASE}/api/video-materials/`

  async fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
    const headers: Record<string, string> = {
      ...(options.headers as Record<string, string>),
    }

    // 只有在没有FormData的情况下才设置JSON Content-Type
    if (!(options.body instanceof FormData) && !headers['Content-Type']) {
      headers['Content-Type'] = 'application/json'
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Network error' }))
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }

    return response
  }

  /**
   * 获取视频素材列表
   */
  async getVideoMaterials(params?: VideoMaterialQueryParams): Promise<VideoMaterialAPI[]> {
    const searchParams = new URLSearchParams()
    if (params?.category) searchParams.set('category', params.category)
    if (params?.search) searchParams.set('search', params.search)
    if (params?.skip !== undefined) searchParams.set('skip', params.skip.toString())
    if (params?.limit !== undefined) searchParams.set('limit', params.limit.toString())

    const url = `${this.baseURL}?${searchParams.toString()}`
    const response = await this.fetchWithAuth(url)
    return response.json()
  }

  /**
   * 获取单个视频素材
   */
  async getVideoMaterial(id: string): Promise<VideoMaterialAPI> {
    const response = await this.fetchWithAuth(`${this.baseURL}/${id}`)
    return response.json()
  }

  /**
   * 创建视频素材
   */
  async createVideoMaterial(data: VideoMaterialCreateRequest): Promise<VideoMaterialAPI> {
    const response = await this.fetchWithAuth(this.baseURL, {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.json()
  }

  /**
   * 批量创建视频素材
   */
  async bulkCreateVideoMaterials(materials: VideoMaterialCreateRequest[]): Promise<BulkVideoMaterialResponse> {
    const response = await this.fetchWithAuth(`${this.baseURL}/bulk`, {
      method: 'POST',
      body: JSON.stringify(materials),
    })
    return response.json()
  }

  /**
   * 更新视频素材
   */
  async updateVideoMaterial(id: string, data: VideoMaterialUpdateRequest): Promise<VideoMaterialAPI> {
    const response = await this.fetchWithAuth(`${this.baseURL}/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.json()
  }

  /**
   * 删除视频素材
   */
  async deleteVideoMaterial(id: string): Promise<ApiResponse> {
    const response = await this.fetchWithAuth(`${this.baseURL}/${id}`, {
      method: 'DELETE',
    })
    return response.json()
  }

  /**
   * 批量删除视频素材
   */
  async bulkDeleteVideoMaterials(ids: string[]): Promise<ApiResponse> {
    const searchParams = new URLSearchParams()
    ids.forEach(id => searchParams.append('material_ids', id))
    
    const response = await this.fetchWithAuth(`${this.baseURL}/bulk?${searchParams.toString()}`, {
      method: 'DELETE',
    })
    return response.json()
  }

  /**
   * 获取视频素材分类列表
   */
  async getVideoMaterialCategories(): Promise<{ categories: string[] }> {
    const response = await this.fetchWithAuth(`${this.baseURL}/categories/list`)
    const result = await response.json()
    return result.data || { categories: [] }
  }

  /**
   * 上传视频文件
   */
  async uploadVideoFile(
    file: File, 
    category: string = 'general',
    tags: string = '',
    onProgress?: (progress: number) => void
  ): Promise<{ 
    id: string
    filePath: string
    thumbnailPath?: string
    duration: number
    resolution: string
    fileSize: number
    format: string
    frameRate?: number
    bitrate?: number
    width: number
    height: number
  }> {
    console.log('🟡 API客户端.uploadVideoFile 被调用:', {
      fileName: file.name,
      category: category,
      tags: tags,
      fileSize: file.size
    })
    
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', category)
    formData.append('tags', tags)

    console.log('🟡 FormData内容:', {
      file: file.name,
      category: category,
      tags: tags
    })

    const xhr = new XMLHttpRequest()
    
    return new Promise((resolve, reject) => {
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable && onProgress) {
          const progress = (e.loaded / e.total) * 100
          onProgress(progress)
        }
      })

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText)
            console.log('🟡 上传成功，服务器响应:', response)
            resolve(response.data)
          } catch (e) {
            console.error('🔴 解析响应失败:', e)
            reject(new Error('Invalid response format'))
          }
        } else {
          console.error('🔴 上传失败，状态码:', xhr.status, '响应:', xhr.responseText)
          reject(new Error(`Upload failed: ${xhr.status}`))
        }
      })

      xhr.addEventListener('error', () => {
        console.error('🔴 网络错误')
        reject(new Error('Network error during upload'))
      })

      console.log('🟡 发送请求到:', `${this.baseURL}/upload`)
      xhr.open('POST', `${this.baseURL}/upload`)
      xhr.send(formData)
    })
  }

  /**
   * 批量上传视频文件
   */
  async bulkUploadVideoFiles(
    files: File[], 
    category: string = 'general'
  ): Promise<BulkVideoMaterialResponse> {
    console.log('🟡 API客户端.bulkUploadVideoFiles 被调用:', {
      fileCount: files.length,
      fileNames: files.map(f => f.name),
      category: category
    })
    
    const formData = new FormData()
    files.forEach(file => formData.append('files', file))
    formData.append('category', category)

    console.log('🟡 批量上传 FormData 内容:', {
      filesCount: files.length,
      category: category
    })

    const response = await this.fetchWithAuth(`${this.baseURL}/upload/bulk`, {
      method: 'POST',
      body: formData,
    })
    
    const result = await response.json()
    console.log('🟡 批量上传服务器响应:', result)
    
    return result
  }
}

export const videoMaterialApi = new VideoMaterialApiClient()
