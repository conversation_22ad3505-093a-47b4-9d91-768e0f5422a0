#!/usr/bin/env python3
"""
重置数据库脚本
删除现有数据库并重新创建所有表
"""

import os
import sys
from pathlib import Path

# 添加后端目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from src.core.config import get_settings
from src.core.database import engine
from src.models import Base
from src.models.accounts import Account
from src.models.resources import BackgroundMusic, VideoMaterial, Prompt, CoverTemplate
from src.models.settings import Settings

def reset_database():
    """重置数据库"""
    settings = get_settings()
    
    # 获取数据库文件路径
    db_file = settings.database_url.replace("sqlite:///./", "")
    print(f"数据库文件路径: {db_file}")
    
    # 删除现有数据库文件（如果存在）
    if os.path.exists(db_file):
        try:
            os.remove(db_file)
            print(f"✅ 已删除现有数据库文件: {db_file}")
        except OSError as e:
            print(f"❌ 无法删除数据库文件: {e}")
            print("请手动停止后端服务后再试")
            return False
    
    # 重新创建所有表
    try:
        print("🔄 正在创建数据库表...")
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功!")
        
        # 打印创建的表
        print(f"📋 创建的表:")
        for table_name in Base.metadata.tables.keys():
            print(f"  - {table_name}")
            
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库表失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 重置数据库...")
    if reset_database():
        print("🎉 数据库重置完成!")
    else:
        print("💥 数据库重置失败!")
        sys.exit(1)
