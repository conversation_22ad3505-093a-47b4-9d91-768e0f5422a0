"""
Base adapter interfaces for the adapter pattern implementation
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import asyncio


class DatabaseAdapter(ABC):
    """Abstract base class for database adapters"""
    
    @abstractmethod
    async def connect(self) -> None:
        """Establish database connection"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Close database connection"""
        pass
    
    @abstractmethod
    async def execute(self, query: str, params: Optional[Dict[str, Any]] = None) -> None:
        """Execute a query without returning results"""
        pass
    
    @abstractmethod
    async def fetch_one(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Fetch a single row"""
        pass
    
    @abstractmethod
    async def fetch_all(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Fetch all matching rows"""
        pass
    
    @abstractmethod
    async def fetch_many(self, query: str, params: Optional[Dict[str, Any]] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch limited number of rows"""
        pass


class CacheAdapter(ABC):
    """Abstract base class for cache adapters"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[str]:
        """Get value by key"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Set key-value pair with optional TTL"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete key"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache"""
        pass


class TTSAdapter(ABC):
    """Abstract base class for Text-to-Speech adapters"""
    
    @abstractmethod
    async def synthesize(
        self, 
        text: str, 
        voice: str = "default",
        speed: float = 1.0,
        output_format: str = "mp3"
    ) -> bytes:
        """Synthesize text to speech"""
        pass
    
    @abstractmethod
    async def get_voices(self) -> List[Dict[str, str]]:
        """Get available voices"""
        pass
    
    @abstractmethod
    async def validate_config(self) -> bool:
        """Validate TTS service configuration"""
        pass


class LLMAdapter(ABC):
    """Abstract base class for Large Language Model adapters"""
    
    @abstractmethod
    async def generate_text(
        self, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        model: str = "default"
    ) -> str:
        """Generate text from prompt"""
        pass
    
    @abstractmethod
    async def get_models(self) -> List[str]:
        """Get available models"""
        pass
    
    @abstractmethod
    async def validate_config(self) -> bool:
        """Validate LLM service configuration"""
        pass
