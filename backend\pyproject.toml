[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "reddit-story-generator-backend"
version = "0.1.0"
description = "Backend API for Reddit Story Video Generator"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.0"
alembic = "^1.12.0"
pydantic = {extras = ["email"], version = "^2.4.0"}
pydantic-settings = "^2.0.3"
python-multipart = "^0.0.6"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
aiofiles = "^23.2.1"
httpx = "^0.25.0"
redis = "^5.0.0"
celery = "^5.3.0"
opencv-python = "^4.8.0"
pillow = "^10.0.0"
moviepy = "^1.0.3"
pydub = "^0.25.1"
openai = "^1.3.0"
websockets = "^12.0"
python-dotenv = "^1.0.0"
loguru = "^0.7.2"
aiosqlite = "^0.19.0"
pyyaml = "^6.0.1"
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"

[tool.poetry.group.dev.dependencies]
black = "^23.9.0"
flake8 = "^6.1.0"
mypy = "^1.6.0"
pre-commit = "^3.5.0"
pytest-cov = "^4.1.0"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q"
testpaths = [
    "tests",
]
asyncio_mode = "auto"
