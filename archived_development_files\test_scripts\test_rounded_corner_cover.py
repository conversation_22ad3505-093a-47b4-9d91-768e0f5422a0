"""
测试封面圆角遮罩效果
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

# 加载环境变量
load_dotenv(dotenv_path=backend_path / '.env')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate, VideoMaterial, BackgroundMusic, Prompt
from backend.src.services.video_generation_helpers import VideoCompositionService

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_rounded_corner_cover():
    """测试封面圆角遮罩效果"""
    db = SessionLocal()
    try:
        print("=== 测试封面圆角遮罩效果 ===")
        
        # 获取测试资源
        account = db.query(Account).first()
        materials = db.query(VideoMaterial).filter(VideoMaterial.is_deleted == False).limit(2).all()
        music = db.query(BackgroundMusic).filter(BackgroundMusic.is_deleted == False).first()
        
        if not account or not materials or not music:
            print("❌ 缺少必要的测试资源")
            return
        
        print(f"✅ 使用账号: {account.name}")
        print(f"✅ 使用素材数量: {len(materials)}")
        print(f"✅ 使用背景音乐: {music.name}")
        
        # 创建模拟任务
        class MockTask:
            def __init__(self):
                self.id = "rounded-corner-test"
                self.account_id = account.id
                self.first_sentence = "测试圆角封面效果"
                self.audio_file_path = "uploads/audio/test_audio.mp3"
        
        task = MockTask()
        
        # 使用之前生成的封面图片
        cover_image_path = "uploads/covers/1_subprocess-test-task_cover.png"
        
        # 检查封面文件是否存在
        cover_full_path = backend_path / cover_image_path
        if not cover_full_path.exists():
            print(f"❌ 封面文件不存在: {cover_full_path}")
            print("请先运行封面生成测试")
            return
        
        print(f"✅ 使用封面文件: {cover_image_path}")
        
        # 创建简单的测试音频文件（如果不存在）
        audio_path = backend_path / task.audio_file_path
        if not audio_path.exists():
            print("⚠️ 创建测试音频文件...")
            audio_path.parent.mkdir(parents=True, exist_ok=True)
            # 创建一个5秒的静音音频文件
            import subprocess
            try:
                subprocess.run([
                    'ffmpeg', '-f', 'lavfi', '-i', 'anullsrc=r=44100:cl=mono', 
                    '-t', '5', '-y', str(audio_path)
                ], check=True, capture_output=True)
                print("✅ 测试音频文件创建成功")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("❌ 无法创建测试音频文件，需要ffmpeg")
                return
        
        # 创建简单的字幕文件
        subtitle_path = "uploads/subtitles/test_rounded_subtitles.srt"
        subtitle_full_path = backend_path / subtitle_path
        subtitle_full_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(subtitle_full_path, 'w', encoding='utf-8') as f:
            f.write("""1
00:00:00,000 --> 00:00:02,000
测试圆角封面效果

2
00:00:02,000 --> 00:00:05,000
圆角遮罩已应用
""")
        
        print("✅ 测试字幕文件创建成功")
        
        # 设置输出路径
        output_path = "uploads/videos/rounded_corner_test.mp4"
        
        print("🔄 开始测试视频合成（圆角封面遮罩）...")
        
        # 视频设置
        video_settings = {
            'resolution': '1080x1920', 
            'fps': 30, 
            'format': 'mp4'
        }
        
        # 调用视频合成
        success = VideoCompositionService.compose_video(
            task=task,
            materials=materials,
            background_music=music,
            audio_duration=5.0,  # 5秒测试视频
            cover_image_path=cover_image_path,
            first_sentence_duration=3.0,  # 封面显示3秒
            subtitle_file_path=subtitle_path,
            output_path=output_path,
            video_settings=video_settings
        )
        
        if success:
            output_full_path = backend_path / output_path
            if output_full_path.exists():
                file_size = output_full_path.stat().st_size
                print(f"✅ 视频合成成功!")
                print(f"   输出路径: {output_path}")
                print(f"   文件大小: {file_size} 字节")
                print("✅ 封面现在应该具有圆角效果！")
                print("🎬 请查看生成的视频，验证封面是否有圆角遮罩效果")
            else:
                print("❌ 视频文件未生成")
        else:
            print("❌ 视频合成失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    test_rounded_corner_cover()
