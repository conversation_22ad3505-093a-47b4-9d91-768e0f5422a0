"""
Core configuration management with environment-based settings
"""

import os
import sys
from pathlib import Path
from typing import Literal
from pydantic_settings import BaseSettings
from functools import lru_cache


def get_app_base_dir() -> Path:
    """获取应用程序基础目录，支持EXE和开发环境"""
    if getattr(sys, 'frozen', False):
        # 在打包的EXE环境中
        return Path(sys.executable).parent
    else:
        # 在开发环境中
        return Path(__file__).parent.parent.parent


class Settings(BaseSettings):
    """Application settings with environment-based configuration"""
    
    # Environment configuration
    environment: Literal["development", "single_machine", "production"] = "development"
    debug: bool = True
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_prefix: str = "/api/v1"
    
    # Database Configuration
    database_url: str = "sqlite:///./reddit_story_generator.db"
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # File upload limits
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    upload_dir: str = "./uploads"
    
    # Frontend Static Files Configuration
    frontend_static_dir: str = "./frontend_dist"
    
    # CORS Configuration
    cors_origins: str = "*"
    cors_allow_credentials: bool = True
    
    # AI Service Configuration
    openai_api_key: str = ""
    azure_openai_endpoint: str = ""
    azure_openai_api_key: str = ""
    
    # TTS Configuration
    tts_provider: str = "openai"  # openai, azure, f5-tts
    f5_tts_endpoint: str = "http://localhost:5000"
    
    # Video Processing
    ffmpeg_path: str = "ffmpeg"
    output_format: str = "mp4"
    video_quality: str = "high"
    
    # Performance Settings
    max_workers: int = 4
    keep_alive_timeout: int = 65
    log_level: str = "INFO"
    
    @property
    def resolved_database_url(self) -> str:
        """解析数据库URL，适配EXE和开发环境"""
        if self.database_url.startswith("sqlite:///./"):
            # 相对路径，需要解析为绝对路径
            db_file = self.database_url.replace("sqlite:///./", "")
            app_base = get_app_base_dir()
            return f"sqlite:///{app_base / db_file}"
        elif self.database_url.startswith("sqlite:///") and not os.path.isabs(self.database_url.replace("sqlite:///", "")):
            # 相对路径形式的SQLite URL
            db_file = self.database_url.replace("sqlite:///", "")
            app_base = get_app_base_dir()
            return f"sqlite:///{app_base / db_file}"
        return self.database_url
    
    @property
    def resolved_upload_dir(self) -> Path:
        """解析上传目录，适配EXE和开发环境"""
        if os.path.isabs(self.upload_dir):
            return Path(self.upload_dir)
        else:
            app_base = get_app_base_dir()
            return app_base / self.upload_dir
    
    @property
    def resolved_frontend_static_dir(self) -> Path:
        """解析前端静态文件目录，适配EXE和开发环境"""
        if os.path.isabs(self.frontend_static_dir):
            return Path(self.frontend_static_dir)
        else:
            app_base = get_app_base_dir()
            return app_base / self.frontend_static_dir
    
    @property
    def resolved_ffmpeg_path(self) -> str:
        """解析FFmpeg路径，适配EXE和开发环境"""
        if os.path.isabs(self.ffmpeg_path):
            return self.ffmpeg_path
        elif self.ffmpeg_path.startswith("./"):
            # 相对路径，解析为绝对路径
            app_base = get_app_base_dir()
            return str(app_base / self.ffmpeg_path[2:])
        else:
            # 假设在PATH中
            return self.ffmpeg_path
    
    @property
    def cors_origins_list(self) -> list:
        """解析CORS origins为列表"""
        if self.cors_origins == "*":
            return ["*"]
        return [origin.strip() for origin in self.cors_origins.split(",") if origin.strip()]
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        upload_dir = self.resolved_upload_dir
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建静态文件目录（如果不存在）
        static_dir = self.resolved_frontend_static_dir
        static_dir.mkdir(parents=True, exist_ok=True)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        @classmethod
        def customise_sources(cls, init_settings, env_settings, file_secret_settings):
            """自定义配置源，支持环境特定的.env文件"""
            env_file = ".env"
            
            # 检查是否有环境特定的配置文件
            if os.path.exists(".env.production") and os.getenv("ENVIRONMENT") == "production":
                env_file = ".env.production"
            elif os.path.exists(".env.development") and os.getenv("ENVIRONMENT") == "development":
                env_file = ".env.development"
            
            # 更新env_file
            env_settings.env_file = env_file
            
            return (
                init_settings,
                env_settings,
                file_secret_settings,
            )


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()
