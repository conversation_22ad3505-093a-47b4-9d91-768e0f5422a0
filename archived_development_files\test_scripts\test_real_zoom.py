#!/usr/bin/env python3
"""
测试真正的zoom动画效果 - 使用帧数控制的scale
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_real_zoom():
    """测试真正的zoom动画"""
    
    # 测试文件路径
    video_path = "test.mp4"
    cover_path = "test.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试真正的zoom动画...")
    logger.info("使用帧数控制的scale滤镜实现真正的缩放效果")
    
    # Zoom动画测试配置
    test_configs = [
        {
            'name': '真正的缩放进入',
            'animation': 'zoom_in',
            'duration': 5.0,
            'animation_duration': 2.0,
            'output': 'real_zoom_in.mp4'
        },
        {
            'name': '真正的缩放退出',
            'animation': 'zoom_out',
            'duration': 5.0,
            'animation_duration': 2.0,
            'output': 'real_zoom_out.mp4'
        },
        {
            'name': '真正的缩放进入退出',
            'animation': 'zoom_in_out',
            'duration': 6.0,
            'animation_duration': 1.5,
            'output': 'real_zoom_in_out.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.5)  # 50%宽度，更容易看到缩放效果
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"real_zoom_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    start_time = time.time()
                    
                    # 应用封面叠加效果
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    total_duration = config['duration'] + 1  # 多1秒缓冲
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=total_duration
                    ).overwrite_output()
                    
                    # 执行，设置超时
                    import subprocess
                    
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
                    
                    # 使用subprocess执行，设置超时
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    try:
                        # 等待最多60秒
                        stdout, stderr = process.communicate(timeout=60)
                        
                        if process.returncode == 0:
                            end_time = time.time()
                            processing_time = end_time - start_time
                            
                            # 检查结果
                            if Path(config['output']).exists():
                                file_size = Path(config['output']).stat().st_size
                                logger.info(f"✅ {config['name']} 测试成功!")
                                logger.info(f"   文件大小: {file_size} bytes")
                                logger.info(f"   处理时间: {processing_time:.2f}秒")
                                
                                # 详细说明预期效果
                                if config['animation'] == 'zoom_in':
                                    logger.info(f"   预期效果: 0-{config['animation_duration']}秒封面从很小缩放到正常大小")
                                elif config['animation'] == 'zoom_out':
                                    zoom_start = config['duration'] - config['animation_duration']
                                    logger.info(f"   预期效果: {zoom_start}-{config['duration']}秒封面从正常大小缩放到很小")
                                elif config['animation'] == 'zoom_in_out':
                                    zoom_out_start = config['duration'] - config['animation_duration']
                                    logger.info(f"   预期效果: 0-{config['animation_duration']}秒缩放进入，{zoom_out_start}-{config['duration']}秒缩放退出")
                                
                                success_count += 1
                            else:
                                logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        else:
                            logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                            if stderr:
                                stderr_text = stderr.decode('utf-8', errors='ignore')
                                logger.error(f"stderr: {stderr_text[:500]}...")
                            
                    except subprocess.TimeoutExpired:
                        logger.error(f"❌ {config['name']} 测试超时（60秒）")
                        process.kill()
                        process.communicate()
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"\n=== 真正Zoom动画测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count > 0:
            logger.info("\n📋 真正的Zoom动画测试文件:")
            logger.info("- real_zoom_in.mp4 (真正的缩放进入)")
            logger.info("- real_zoom_out.mp4 (真正的缩放退出)")
            logger.info("- real_zoom_in_out.mp4 (真正的缩放进入退出)")
            
            logger.info("\n🔍 关键实现点:")
            logger.info("1. 使用帧数控制：基于n（帧号）而不是t（时间）")
            logger.info("2. 缩放范围：从0.1到1.0，避免完全消失")
            logger.info("3. 条件表达式：if(lt(n,frames),scale1,scale2)")
            logger.info("4. 组合动画：先放大再缩小")
            
            logger.info("\n🎬 验证方法:")
            logger.info("播放real_zoom_*.mp4文件，应该能看到:")
            logger.info("- 明显的尺寸变化动画")
            logger.info("- 平滑的缩放过渡")
            logger.info("- 封面从小变大或从大变小")
            
            logger.info("\n如果能看到明显的缩放动画，说明zoom效果修复成功！")
            
            return True
        else:
            logger.error("❌ 所有真正zoom动画测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("🚀 开始真正Zoom动画测试")
    logger.info("本次使用帧数控制的scale滤镜实现真正的缩放效果")
    
    success = test_real_zoom()
    
    if success:
        logger.info("\n🎉 真正Zoom动画测试完成!")
        logger.info("如果能看到明显的缩放动画，说明zoom效果完全修复成功了！")
    else:
        logger.error("\n❌ 真正Zoom动画测试失败")
        logger.error("需要进一步调试zoom动画问题")
        sys.exit(1)
