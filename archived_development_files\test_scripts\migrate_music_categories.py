#!/usr/bin/env python3
"""
创建音乐分类表的数据库迁移脚本
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from sqlalchemy import create_engine, text
from src.core.config import get_settings
from src.core.database import get_database_url
from src.models.resources import MusicCategory

def create_music_categories_table():
    """创建音乐分类表"""
    
    print("🔧 创建音乐分类表...")
    
    try:
        # 获取数据库连接
        settings = get_settings()
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        # 创建分类表的SQL
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS music_categories (
            id VARCHAR PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # 执行创建表的SQL
        with engine.connect() as conn:
            conn.execute(text(create_table_sql))
            conn.commit()
        
        print("✅ 音乐分类表创建成功")
        
        # 插入默认分类
        insert_default_sql = """
        INSERT OR IGNORE INTO music_categories (id, name, description)
        VALUES ('general-id', 'general', '默认分类');
        """
        
        with engine.connect() as conn:
            conn.execute(text(insert_default_sql))
            conn.commit()
            
        print("✅ 默认分类插入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建音乐分类表失败: {str(e)}")
        return False

def migrate_existing_categories():
    """将现有音乐文件的分类迁移到分类表"""
    
    print("🔄 迁移现有分类...")
    
    try:
        settings = get_settings()
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        # 获取现有的不重复分类
        get_categories_sql = """
        SELECT DISTINCT category FROM background_music 
        WHERE category IS NOT NULL AND category != '';
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(get_categories_sql))
            existing_categories = [row[0] for row in result.fetchall()]
        
        print(f"找到现有分类: {existing_categories}")
        
        # 插入到分类表
        for category in existing_categories:
            if category and category != 'general':  # general已经插入了
                insert_sql = text("""
                INSERT OR IGNORE INTO music_categories (id, name, description)
                VALUES (:id, :name, :description);
                """)
                
                with engine.connect() as conn:
                    conn.execute(insert_sql, {
                        "id": f"{category}-id",
                        "name": category,
                        "description": f"{category}分类"
                    })
                    conn.commit()
        
        print("✅ 分类迁移完成")
        return True
        
    except Exception as e:
        print(f"❌ 分类迁移失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎵 音乐分类表迁移脚本")
    print("=" * 40)
    
    # 创建分类表
    if not create_music_categories_table():
        return False
    
    # 迁移现有分类
    if not migrate_existing_categories():
        return False
    
    print("\n🎉 迁移完成！")
    print("现在可以启动后端服务测试分类管理功能。")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
