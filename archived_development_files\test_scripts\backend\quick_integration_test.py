#!/usr/bin/env python3
"""
快速前后端联调验证脚本
使用标准库进行基本的API连接测试
"""

import urllib.request
import urllib.error
import json
import time
import subprocess
import sys
import os
from pathlib import Path

class QuickIntegrationTest:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.api_base = f"{self.backend_url}/api/v1"
        
    def test_url(self, url: str, description: str, timeout: int = 10) -> tuple[bool, str]:
        """测试URL连接"""
        try:
            with urllib.request.urlopen(url, timeout=timeout) as response:
                if response.status == 200:
                    data = response.read().decode('utf-8')
                    return True, data
                else:
                    return False, f"HTTP {response.status}"
        except urllib.error.HTTPError as e:
            return False, f"HTTP Error {e.code}: {e.reason}"
        except urllib.error.URLError as e:
            return False, f"Connection Error: {e.reason}"
        except Exception as e:
            return False, f"Error: {str(e)}"
    
    def wait_for_server(self, url: str, timeout: int = 30) -> bool:
        """等待服务器启动"""
        print(f"⏳ Waiting for server at {url}...")
        
        for i in range(timeout):
            success, _ = self.test_url(url, "health check", 2)
            if success:
                print(f"✅ Server at {url} is ready!")
                return True
            time.sleep(1)
            if i % 5 == 0:
                print(f"   Still waiting... ({i}/{timeout}s)")
          print(f"❌ Server at {url} failed to start within {timeout} seconds")
        return False
    
    def test_api_endpoints(self) -> dict:
        """测试所有API端点"""
        endpoints = [
            ("/health", "Health Check"),
            ("/api/v1/settings", "Settings API"),
            ("/api/v1/background-music", "Background Music API"),
            ("/api/v1/background-music/categories/list", "Music Categories API"),
            ("/api/v1/video-materials", "Video Materials API"),
            ("/api/v1/video-materials/categories/list", "Video Categories API"),
            ("/api/v1/prompts", "Prompts API"),
            ("/api/v1/prompts/categories/list", "Prompt Categories API"),
            ("/api/v1/accounts", "Accounts API"),
            ("/api/v1/accounts/platforms/list", "Account Platforms API"),
            ("/api/v1/cover-templates", "Cover Templates API"),
            ("/api/v1/cover-templates/categories/list", "Template Categories API")
        ]
        
        results = {
            "total": len(endpoints),
            "passed": 0,
            "failed": 0,
            "details": []
        }
        
        print("\n📡 Testing API Endpoints")
        print("-" * 30)
        
        for endpoint, description in endpoints:
            url = f"{self.backend_url}{endpoint}"
            success, response = self.test_url(url, description, 10)
            
            if success:
                results["passed"] += 1
                print(f"✅ {description}")
                
                # 尝试解析JSON响应
                try:
                    data = json.loads(response)
                    results["details"].append({
                        "endpoint": endpoint,
                        "description": description,
                        "status": "✅ PASS",
                        "response_type": type(data).__name__,
                        "has_data": "data" in data if isinstance(data, dict) else False
                    })
                except json.JSONDecodeError:
                    results["details"].append({
                        "endpoint": endpoint,
                        "description": description,
                        "status": "✅ PASS (non-JSON)",
                        "response_type": "text",
                        "has_data": False
                    })
            else:
                results["failed"] += 1
                print(f"❌ {description} - {response}")
                results["details"].append({
                    "endpoint": endpoint,
                    "description": description,
                    "status": f"❌ FAIL",
                    "error": response
                })
        
        return results
    
    def check_frontend(self) -> bool:
        """检查前端可访问性"""
        print("\n🎯 Testing Frontend")
        print("-" * 20)
        
        success, response = self.test_url(self.frontend_url, "Frontend", 10)
        if success:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend failed - {response}")
            return False
    
    def start_servers_if_needed(self) -> tuple[bool, bool]:
        """检查服务器状态，如果需要则启动"""
        print("🔍 Checking server status...")
        
        # 检查后端
        backend_running = False
        success, _ = self.test_url(f"{self.backend_url}/health", "backend health", 2)
        if success:
            print("✅ Backend server is already running")
            backend_running = True
        else:
            print("⚠️ Backend server is not running")
        
        # 检查前端
        frontend_running = False
        success, _ = self.test_url(self.frontend_url, "frontend", 2)
        if success:
            print("✅ Frontend server is already running")
            frontend_running = True
        else:
            print("⚠️ Frontend server is not running")
        
        return backend_running, frontend_running
    
    def print_manual_start_instructions(self):
        """打印手动启动指令"""
        print("\n🚀 Manual Server Start Instructions")
        print("=" * 40)
        print("\n1. Start Backend Server:")
        print("   cd backend")
        print("   python start_server.py")
        print("   # Wait for 'Application startup complete'")
        
        print("\n2. Start Frontend Server (in new terminal):")
        print("   cd frontend")
        print("   npm install  # if first time")
        print("   npm run dev")
        print("   # Wait for 'Local: http://localhost:3000'")
        
        print("\n3. Verify URLs:")
        print(f"   Frontend: {self.frontend_url}")
        print(f"   Backend:  {self.backend_url}")
        print(f"   API Docs: {self.backend_url}/docs")
    
    def run_quick_test(self) -> bool:
        """运行快速测试"""
        print("🚀 Reddit Story Video Generator - Quick Integration Test")
        print("=" * 60)
        
        # 1. 检查服务器状态
        backend_running, frontend_running = self.start_servers_if_needed()
        
        if not backend_running or not frontend_running:
            print("\n⚠️ Some servers are not running!")
            self.print_manual_start_instructions()
            
            print("\n❓ Do you want to proceed with testing running servers only? (y/N): ", end="")
            try:
                response = input().lower()
                if response != 'y':
                    print("👋 Test cancelled. Please start the servers and try again.")
                    return False
            except KeyboardInterrupt:
                print("\n👋 Test cancelled.")
                return False
        
        # 2. 等待服务器就绪
        all_ready = True
        
        if backend_running:
            backend_ready = self.wait_for_server(f"{self.backend_url}/health", 15)
            all_ready = all_ready and backend_ready
        
        if frontend_running:
            frontend_ready = self.wait_for_server(self.frontend_url, 15)
            all_ready = all_ready and frontend_ready
        
        if not all_ready:
            print("❌ Some servers failed to become ready")
            return False
        
        # 3. 测试API端点
        if backend_running:
            api_results = self.test_api_endpoints()
        else:
            api_results = {"total": 0, "passed": 0, "failed": 0, "details": []}
        
        # 4. 测试前端
        if frontend_running:
            frontend_ok = self.check_frontend()
        else:
            frontend_ok = False
        
        # 5. 生成报告
        print("\n📊 Test Results Summary")
        print("=" * 30)
        
        if backend_running:
            print(f"📡 API Tests: {api_results['passed']}/{api_results['total']} passed")
            if api_results['failed'] > 0:
                print("   Failed endpoints:")
                for detail in api_results['details']:
                    if 'FAIL' in detail['status']:
                        print(f"     - {detail['description']}: {detail.get('error', 'Unknown error')}")
        else:
            print("📡 API Tests: Skipped (backend not running)")
        
        print(f"🎯 Frontend: {'✅ OK' if frontend_ok else '❌ FAIL' if frontend_running else '⚠️ Not running'}")
        
        # 6. 成功评估
        success = True
        if backend_running:
            success = success and (api_results['passed'] == api_results['total'])
        if frontend_running:
            success = success and frontend_ok
        
        if success:
            print("\n🎉 All tests passed!")
            print("\n📋 Next Steps:")
            print("   1. Open frontend and manually test pages")
            print("   2. Verify data loading and saving")
            print("   3. Check browser console for errors")
            print("   4. Test form submissions")
            
            print(f"\n🌐 Quick Access URLs:")
            if frontend_running:
                print(f"   Frontend: {self.frontend_url}")
            if backend_running:
                print(f"   Backend:  {self.backend_url}")
                print(f"   API Docs: {self.backend_url}/docs")
        else:
            print("\n❌ Some tests failed!")
            print("Please review the errors above and fix them.")
        
        return success

def main():
    """主函数"""
    tester = QuickIntegrationTest()
    
    try:
        success = tester.run_quick_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print(f"\n⏸️ Press Enter to exit...")
    try:
        input()
    except KeyboardInterrupt:
        pass
    
    sys.exit(exit_code)
