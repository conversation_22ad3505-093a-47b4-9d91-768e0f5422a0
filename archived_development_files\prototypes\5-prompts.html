<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit故事视频生成器 - 提示词管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: all 0.3s ease;
        }

        /* 主题变量定义 */
        :root {
            /* 蓝色主题（默认） */
            --theme-primary: #2563eb;
            --theme-primary-hover: #1d4ed8;
            --theme-primary-light: #eff6ff;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f9fafb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #374151;
            --border-primary: #e5e7eb;
            --border-secondary: #d1d5db;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
        }

        /* 绿色主题 */
        [data-theme="green"] {
            --theme-primary: #059669;
            --theme-primary-hover: #047857;
            --theme-primary-light: #ecfdf5;
        }

        /* 紫色主题 */
        [data-theme="purple"] {
            --theme-primary: #7c3aed;
            --theme-primary-hover: #6d28d9;
            --theme-primary-light: #f3f4f6;
        }

        /* 橙色主题 */
        [data-theme="orange"] {
            --theme-primary: #ea580c;
            --theme-primary-hover: #dc2626;
            --theme-primary-light: #fff7ed;
        }

        /* 红色主题 */
        [data-theme="red"] {
            --theme-primary: #dc2626;
            --theme-primary-hover: #b91c1c;
            --theme-primary-light: #fef2f2;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 32px;
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .page-description {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .btn-primary:hover {
            background: var(--theme-primary-hover);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-tertiary);
            border-color: var(--border-secondary);
        }

        .btn-secondary:hover {
            border-color: var(--theme-primary);
            color: var(--theme-primary);
        }

        .btn-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--theme-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 分类标签 */
        .category-tabs {
            display: flex;
            gap: 2px;
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
        }

        .category-tab {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            background: transparent;
            color: var(--text-secondary);
        }

        .category-tab.active {
            background: var(--theme-primary);
            color: white;
        }

        .category-tab:not(.active):hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        /* 工具栏 */
        .toolbar {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 8px 36px 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            color: var(--text-secondary);
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 提示词网格 */
        .prompts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .prompt-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-primary);
            overflow: hidden;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .prompt-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .prompt-header {
            padding: 16px 16px 0;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .prompt-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            flex: 1;
        }

        .prompt-type-badge {
            background: var(--theme-primary);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        .system-badge {
            background: var(--theme-primary);
        }

        .story-badge {
            background: var(--success-color);
        }

        .narration-badge {
            background: var(--warning-color);
        }

        .description-badge {
            background: var(--error-color);
        }

        .prompt-content {
            padding: 0 16px 16px;
        }

        .prompt-text {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .prompt-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-bottom: 12px;
        }

        .prompt-tag {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }

        .prompt-meta {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 12px;
        }

        .prompt-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .action-btn-test {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .action-btn-test:hover {
            background: var(--theme-primary-hover);
        }

        .action-btn-edit {
            background: transparent;
            color: var(--text-secondary);
            border-color: var(--border-secondary);
        }

        .action-btn-edit:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .action-btn-delete {
            background: transparent;
            color: var(--error-color);
            border-color: var(--error-color);
        }

        .action-btn-delete:hover {
            background: var(--error-color);
            color: white;
        }

        .action-icon {
            width: 12px;
            height: 12px;
            fill: currentColor;
        }

        /* 编辑模态框 */
        .edit-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .edit-content {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .edit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .edit-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .edit-close {
            width: 24px;
            height: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            border: none;
            background: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 6px;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: border-color 0.2s;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .tag-input-container {
            position: relative;
        }

        .tag-input {
            padding-right: 80px;
        }

        .tag-add-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--theme-primary);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .tags-display {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .tag-item {
            background: var(--theme-primary-light);
            color: var(--theme-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .tag-remove {
            cursor: pointer;
            font-weight: bold;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        .btn-cancel {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-secondary);
        }

        .btn-cancel:hover {
            background: var(--bg-tertiary);
        }

        /* 测试模态框 */
        .test-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .test-content {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 24px;
            max-width: 700px;
            width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .test-prompt-display {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            color: var(--text-primary);
        }

        .test-result {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 16px;
            min-height: 120px;
            font-size: 14px;
            line-height: 1.6;
            color: var(--text-primary);
        }

        .test-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-style: italic;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            color: var(--text-secondary);
        }

        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .toolbar-left {
                justify-content: space-between;
            }

            .search-input {
                width: 100%;
            }

            .category-tabs {
                flex-wrap: wrap;
            }

            .prompts-grid {
                grid-template-columns: 1fr;
            }

            .edit-content,
            .test-content {
                margin: 16px;
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title-section">
                <h1 class="page-title">提示词管理</h1>
                <p class="page-description">管理和优化AI生成内容的提示词模板，提升视频生成质量</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-secondary">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    导出模板
                </button>
                <button class="btn btn-primary" id="createBtn">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    新建提示词
                </button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">提示词总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0/0/0/0</div>
                <div class="stat-label">系统/故事/旁白/描述</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">标签分类</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">测试次数</div>
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="category-tabs">
            <button class="category-tab active" data-category="all">全部提示词</button>
            <button class="category-tab" data-category="system">系统提示词</button>
            <button class="category-tab" data-category="story">故事生成</button>
            <button class="category-tab" data-category="narration">旁白生成</button>
            <button class="category-tab" data-category="description">场景描述</button>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索提示词标题或内容...">
                    <svg class="search-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <select class="filter-select">
                    <option value="all">全部标签</option>
                    <option value="reddit">Reddit</option>
                    <option value="story">故事</option>
                    <option value="humor">幽默</option>
                    <option value="drama">戏剧</option>
                </select>
            </div>
            <div class="toolbar-right">
                <select class="filter-select">
                    <option value="updated">最近更新</option>
                    <option value="created">创建时间</option>
                    <option value="name">名称排序</option>
                    <option value="tests">测试次数</option>
                </select>
            </div>
        </div>

        <!-- 提示词网格 -->
        <div class="prompts-grid" id="promptsGrid">
            <!-- 空状态 -->
            <div class="empty-state" style="grid-column: 1 / -1;">
                <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <div class="empty-title">还没有提示词模板</div>
                <div class="empty-description">
                    点击"新建提示词"按钮开始创建您的第一个提示词模板
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="edit-modal" id="editModal">
        <div class="edit-content">
            <div class="edit-header">
                <div class="edit-title">编辑提示词</div>
                <button class="edit-close">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
            
            <form id="promptForm">
                <div class="form-group">
                    <label class="form-label" for="promptTitle">提示词标题</label>
                    <input type="text" id="promptTitle" class="form-input" placeholder="请输入提示词标题" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="promptType">类型</label>
                    <select id="promptType" class="form-select" required>
                        <option value="">请选择类型</option>
                        <option value="system">系统提示词</option>
                        <option value="story">故事生成</option>
                        <option value="narration">旁白生成</option>
                        <option value="description">场景描述</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="promptContent">提示词内容</label>
                    <textarea id="promptContent" class="form-textarea" 
                        placeholder="请输入提示词内容，可以使用变量如 {story_text}、{context} 等" required></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="promptDescription">描述说明</label>
                    <textarea id="promptDescription" class="form-textarea" 
                        placeholder="请简要描述此提示词的用途和使用场景"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">标签</label>
                    <div class="tag-input-container">
                        <input type="text" id="tagInput" class="form-input tag-input" placeholder="输入标签">
                        <button type="button" class="tag-add-btn" id="addTagBtn">添加</button>
                    </div>
                    <div class="tags-display" id="tagsDisplay">
                        <!-- 动态生成标签 -->
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-cancel" id="cancelBtn">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 测试模态框 -->
    <div class="test-modal" id="testModal">
        <div class="test-content">
            <div class="edit-header">
                <div class="edit-title">测试提示词</div>
                <button class="edit-close">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
            
            <div class="form-group">
                <label class="form-label">提示词预览</label>
                <div class="test-prompt-display" id="testPromptDisplay">
                    <!-- 动态显示提示词内容 -->
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">测试结果</label>
                <div class="test-result" id="testResult">
                    <div class="test-loading">点击"开始测试"按钮来测试此提示词...</div>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="startTestBtn">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                    </svg>
                    开始测试
                </button>
                <button type="button" class="btn btn-primary" id="closeTestBtn">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // 示例提示词数据
        const samplePrompts = [
            {
                id: 1,
                title: 'Reddit故事基础提示词',
                type: 'story',
                content: '请基于以下Reddit帖子生成一个引人入胜的故事：\n\n{story_text}\n\n要求：\n1. 保持故事的真实性和情感深度\n2. 突出故事的关键转折点\n3. 语言要生动有趣，适合年轻观众\n4. 控制在300字以内',
                description: '用于将Reddit原始帖子转换为适合视频的故事内容',
                tags: ['reddit', 'story', 'basic'],
                createdAt: '2024-01-15',
                updatedAt: '2024-01-20',
                testCount: 15
            },
            {
                id: 2,
                title: '旁白风格控制',
                type: 'narration',
                content: '请为以下故事内容生成专业的旁白文本：\n\n{story_content}\n\n旁白风格要求：\n- 语调：{tone} (可选：严肃、轻松、幽默、悬疑)\n- 节奏：适中，留有停顿空间\n- 情感：与故事内容匹配\n- 长度：适合30-60秒音频',
                description: '为视频内容生成高质量的旁白文本，支持多种风格',
                tags: ['narration', 'tone', 'audio'],
                createdAt: '2024-01-10',
                updatedAt: '2024-01-18',
                testCount: 23
            },
            {
                id: 3,
                title: '视觉场景描述生成器',
                type: 'description',
                content: '基于故事内容，生成详细的视觉场景描述：\n\n故事片段：{story_segment}\n\n请生成：\n1. 场景设置（时间、地点、环境）\n2. 人物外观和动作\n3. 关键道具和细节\n4. 情绪氛围描述\n\n输出格式：简洁的视觉指导，适合配图或视频制作',
                description: '为故事情节生成视觉化的场景描述，用于指导素材选择',
                tags: ['visual', 'scene', 'description'],
                createdAt: '2024-01-08',
                updatedAt: '2024-01-22',
                testCount: 8
            }
        ];

        let promptsList = [];
        let currentCategory = 'all';
        let editingPromptId = null;
        let currentTags = [];

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 恢复保存的主题
            const savedTheme = localStorage.getItem('theme') || 'blue';
            if (savedTheme !== 'blue') {
                document.documentElement.setAttribute('data-theme', savedTheme);
            }

            // 加载示例数据（实际使用时会从数据库加载）
            promptsList = [...samplePrompts];
            renderPromptsGrid();
            updateStats();
        });

        // 新建提示词
        document.getElementById('createBtn').addEventListener('click', function() {
            editingPromptId = null;
            resetForm();
            document.querySelector('.edit-title').textContent = '新建提示词';
            document.getElementById('editModal').style.display = 'flex';
        });

        // 分类切换
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 更新按钮状态
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                currentCategory = this.dataset.category;
                renderPromptsGrid();
                
                console.log('切换到分类:', currentCategory);
            });
        });

        // 渲染提示词网格
        function renderPromptsGrid() {
            const grid = document.getElementById('promptsGrid');
            
            // 根据当前分类过滤
            const filteredPrompts = currentCategory === 'all' 
                ? promptsList 
                : promptsList.filter(prompt => prompt.type === currentCategory);
            
            if (filteredPrompts.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        <div class="empty-title">还没有${getCategoryName(currentCategory)}</div>
                        <div class="empty-description">
                            点击"新建提示词"按钮开始创建您的第一个提示词模板
                        </div>
                    </div>
                `;
                return;
            }

            grid.innerHTML = filteredPrompts.map(prompt => `
                <div class="prompt-card">
                    <div class="prompt-header">
                        <div class="prompt-title">${prompt.title}</div>
                        <div class="prompt-type-badge ${prompt.type}-badge">${getTypeName(prompt.type)}</div>
                    </div>
                    <div class="prompt-content">
                        <div class="prompt-text">${prompt.content}</div>
                        <div class="prompt-tags">
                            ${prompt.tags.map(tag => `<span class="prompt-tag">${tag}</span>`).join('')}
                        </div>
                        <div class="prompt-meta">
                            创建时间：${prompt.createdAt} | 更新时间：${prompt.updatedAt} | 测试：${prompt.testCount}次
                        </div>
                        <div class="prompt-actions">
                            <button class="action-btn action-btn-test" onclick="testPrompt('${prompt.id}')">
                                <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                </svg>
                                测试
                            </button>
                            <button class="action-btn action-btn-edit" onclick="editPrompt('${prompt.id}')">
                                <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                                </svg>
                                编辑
                            </button>
                            <button class="action-btn action-btn-delete" onclick="deletePrompt('${prompt.id}')">
                                <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 112 0v4a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V8z" clip-rule="evenodd"/>
                                </svg>
                                删除
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 更新统计信息
        function updateStats() {
            const stats = document.querySelectorAll('.stat-value');
            
            // 总数
            stats[0].textContent = promptsList.length;
            
            // 类型统计
            const systemCount = promptsList.filter(p => p.type === 'system').length;
            const storyCount = promptsList.filter(p => p.type === 'story').length;
            const narrationCount = promptsList.filter(p => p.type === 'narration').length;
            const descriptionCount = promptsList.filter(p => p.type === 'description').length;
            stats[1].textContent = `${systemCount}/${storyCount}/${narrationCount}/${descriptionCount}`;
            
            // 标签数量
            const allTags = new Set();
            promptsList.forEach(p => p.tags.forEach(tag => allTags.add(tag)));
            stats[2].textContent = allTags.size;
            
            // 总测试次数
            const totalTests = promptsList.reduce((sum, p) => sum + p.testCount, 0);
            stats[3].textContent = totalTests;
        }

        // 编辑提示词
        function editPrompt(id) {
            const prompt = promptsList.find(p => p.id == id);
            if (!prompt) return;
            
            editingPromptId = id;
            
            // 填充表单
            document.getElementById('promptTitle').value = prompt.title;
            document.getElementById('promptType').value = prompt.type;
            document.getElementById('promptContent').value = prompt.content;
            document.getElementById('promptDescription').value = prompt.description || '';
            
            // 设置标签
            currentTags = [...prompt.tags];
            renderTags();
            
            document.querySelector('.edit-title').textContent = '编辑提示词';
            document.getElementById('editModal').style.display = 'flex';
        }

        // 测试提示词
        function testPrompt(id) {
            const prompt = promptsList.find(p => p.id == id);
            if (!prompt) return;
            
            // 显示提示词内容
            document.getElementById('testPromptDisplay').textContent = prompt.content;
            
            // 重置测试结果
            document.getElementById('testResult').innerHTML = 
                '<div class="test-loading">点击"开始测试"按钮来测试此提示词...</div>';
            
            document.getElementById('testModal').style.display = 'flex';
            
            // 绑定测试按钮事件
            document.getElementById('startTestBtn').onclick = function() {
                startTest(prompt);
            };
        }

        // 开始测试
        function startTest(prompt) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div class="test-loading">正在测试中，请稍候...</div>';
            
            // 模拟API调用
            setTimeout(() => {
                const sampleResult = generateSampleResult(prompt.type);
                resultDiv.innerHTML = sampleResult;
                
                // 更新测试次数
                prompt.testCount++;
                updateStats();
                renderPromptsGrid();
            }, 2000);
        }

        // 生成示例结果
        function generateSampleResult(type) {
            const results = {
                'story': '这是一个关于意外发现的感人故事。主人公在整理已故祖母的遗物时，意外发现了一封从未寄出的情书。这封信揭示了祖母年轻时一段不为人知的浪漫经历，也让主人公重新认识了这位他以为非常了解的亲人。通过这个发现，主人公不仅了解了家族历史，也学会了珍惜当下的情感...',
                'narration': '在这个充满惊喜的故事中，我们跟随主人公踏上一段意想不到的旅程。当他轻轻翻开那本泛黄的相册时，一封信静静地躺在那里，仿佛在等待着这一刻的到来。这个发现，将彻底改变他对家族历史的认知...',
                'description': '场景：温暖的午后阳光透过百叶窗洒进房间，灰尘在光束中轻舞。一位年轻男子坐在木地板上，周围散落着各种旧物件——老式相框、复古首饰盒、手写信件。他的表情从最初的怀念转为惊讶，手中拿着一封泛黄的信件，眼中闪烁着好奇的光芒。',
                'system': '系统提示词测试完成。该提示词结构清晰，变量使用规范，能够有效指导AI生成符合要求的内容。建议在实际使用时根据具体需求调整参数设置。'
            };
            
            return results[type] || '测试完成，生成内容正常。';
        }

        // 删除提示词
        function deletePrompt(id) {
            if (confirm('确定要删除这个提示词吗？此操作不可撤销。')) {
                promptsList = promptsList.filter(prompt => prompt.id != id);
                renderPromptsGrid();
                updateStats();
            }
        }

        // 标签管理
        document.getElementById('addTagBtn').addEventListener('click', addTag);
        document.getElementById('tagInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addTag();
            }
        });

        function addTag() {
            const input = document.getElementById('tagInput');
            const tag = input.value.trim();
            
            if (tag && !currentTags.includes(tag)) {
                currentTags.push(tag);
                input.value = '';
                renderTags();
            }
        }

        function removeTag(tag) {
            currentTags = currentTags.filter(t => t !== tag);
            renderTags();
        }

        function renderTags() {
            const container = document.getElementById('tagsDisplay');
            container.innerHTML = currentTags.map(tag => `
                <span class="tag-item">
                    ${tag}
                    <span class="tag-remove" onclick="removeTag('${tag}')">&times;</span>
                </span>
            `).join('');
        }

        // 表单提交
        document.getElementById('promptForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                title: document.getElementById('promptTitle').value,
                type: document.getElementById('promptType').value,
                content: document.getElementById('promptContent').value,
                description: document.getElementById('promptDescription').value,
                tags: [...currentTags]
            };
            
            if (editingPromptId) {
                // 编辑现有提示词
                const prompt = promptsList.find(p => p.id == editingPromptId);
                if (prompt) {
                    Object.assign(prompt, formData);
                    prompt.updatedAt = new Date().toISOString().split('T')[0];
                }
            } else {
                // 新建提示词
                const newPrompt = {
                    id: Date.now(),
                    ...formData,
                    createdAt: new Date().toISOString().split('T')[0],
                    updatedAt: new Date().toISOString().split('T')[0],
                    testCount: 0
                };
                promptsList.push(newPrompt);
            }
            
            document.getElementById('editModal').style.display = 'none';
            renderPromptsGrid();
            updateStats();
        });

        // 重置表单
        function resetForm() {
            document.getElementById('promptForm').reset();
            currentTags = [];
            renderTags();
        }

        // 模态框关闭事件
        document.querySelectorAll('.edit-close').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.edit-modal, .test-modal').style.display = 'none';
            });
        });

        document.getElementById('cancelBtn').addEventListener('click', function() {
            document.getElementById('editModal').style.display = 'none';
        });

        document.getElementById('closeTestBtn').addEventListener('click', function() {
            document.getElementById('testModal').style.display = 'none';
        });

        // 点击模态框背景关闭
        document.querySelectorAll('.edit-modal, .test-modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.style.display = 'none';
                }
            });
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            // 这里将来会实现实际的搜索过滤
            console.log('搜索:', query);
        });

        // 辅助函数
        function getCategoryName(category) {
            const names = {
                'all': '提示词',
                'system': '系统提示词',
                'story': '故事生成提示词',
                'narration': '旁白生成提示词',
                'description': '场景描述提示词'
            };
            return names[category] || '提示词';
        }

        function getTypeName(type) {
            const names = {
                'system': '系统',
                'story': '故事',
                'narration': '旁白',
                'description': '描述'
            };
            return names[type] || type;
        }
    </script>
</body>
</html>
