@echo off
REM Test Frontend Only - Reddit Story Video Generator

echo 🔧 Testing Frontend Only...

REM 检查 Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo 📦 Setting up frontend...

cd frontend

REM 安装依赖
echo Cleaning and installing npm dependencies...
if exist "node_modules" (
    echo Removing existing node_modules...
    rmdir /s /q node_modules
)
if exist "package-lock.json" (
    echo Removing package-lock.json...
    del package-lock.json
)

@echo off
chcp 65001 >nul
REM Test Frontend Only - Reddit Story Video Generator

echo ================================
echo  Reddit Story Video Generator
echo  Frontend Test & Hydration Fix
echo ================================
echo.

REM 检查 Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo 📦 Setting up frontend...

cd frontend

REM 清理和安装依赖
echo 🧹 Cleaning and installing npm dependencies...
if exist "node_modules" (
    echo   → Removing existing node_modules...
    rmdir /s /q node_modules
)
if exist "package-lock.json" (
    echo   → Removing package-lock.json...
    del package-lock.json
)
if exist ".next" (
    echo   → Removing .next build cache...
    rmdir /s /q .next
)

echo 📥 Installing fresh dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies ready!

REM 启动前端
echo.
echo ================================
echo 🚀 Starting frontend dev server
echo ================================
echo.
echo 📍 URLs to test:
echo   • Main page:      http://localhost:3000
echo   • Test page:      http://localhost:3000/test  
echo   • Diagnostics:    http://localhost:3000/diagnostics
echo   • Extension test: http://localhost:3000/extension-test
echo.
echo 📝 Hydration Fix Applied:
echo   • Pure client-side rendering
echo   • Extension interference protection
echo   • Simplified Next.js config  
echo   • Added loading states
echo   • suppressHydrationWarning flags
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev

cd ..
pause
