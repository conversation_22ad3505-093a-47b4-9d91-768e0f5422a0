"""
最小化的后端服务器 - 仅用于测试视频上传API
"""

import sys
import os
from pathlib import Path
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import uuid

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# 创建FastAPI应用
app = FastAPI(
    title="Video Upload Test API",
    description="最小化的视频上传测试API",
    version="0.1.0",
    docs_url="/docs"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 支持的视频文件类型
SUPPORTED_VIDEO_TYPES = {
    '.mp4': 'video/mp4',
    '.mov': 'video/quicktime',
    '.avi': 'video/x-msvideo',
    '.webm': 'video/webm',
    '.mkv': 'video/x-matroska'
}

# 创建上传目录
UPLOAD_DIR = Path("uploads/video_materials")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "video-upload-test"}

@app.get("/api/health")
async def api_health():
    """API健康检查"""
    return {"status": "healthy", "service": "video-upload-test-api"}

@app.get("/api/video-materials/")
async def get_video_materials():
    """获取视频素材列表 - 测试用"""
    return []

@app.post("/api/video-materials/upload")
async def upload_video_file(
    file: UploadFile = File(...),
    category: str = "general",
    tags: str = ""
):
    """上传视频文件 - 测试版本"""
    try:
        # 验证文件类型
        file_ext = os.path.splitext(file.filename or "")[1].lower()
        if file_ext not in SUPPORTED_VIDEO_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_ext}"
            )
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}_{file.filename}"
        file_path = UPLOAD_DIR / unique_filename
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        file_size = len(content)
        
        # 返回模拟的上传结果
        return {
            "success": True,
            "message": "文件上传成功",
            "data": {
                "id": str(uuid.uuid4()),
                "filePath": str(file_path),
                "thumbnailPath": None,
                "duration": 30.0,  # 模拟时长
                "resolution": "1920x1080",
                "fileSize": file_size,
                "format": "MP4",
                "frameRate": 30.0,
                "bitrate": 5000000,
                "width": 1920,
                "height": 1080
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@app.post("/api/video-materials/upload/bulk")
async def bulk_upload_video_files(
    files: list[UploadFile] = File(...),
    category: str = "general"
):
    """批量上传视频文件 - 测试版本"""
    try:
        success_materials = []
        failed_uploads = []
        
        for file in files:
            try:
                # 验证文件类型
                file_ext = os.path.splitext(file.filename or "")[1].lower()
                if file_ext not in SUPPORTED_VIDEO_TYPES:
                    failed_uploads.append({
                        "name": file.filename or "unknown",
                        "error": f"不支持的文件类型: {file_ext}"
                    })
                    continue
                
                # 生成唯一文件名
                unique_filename = f"{uuid.uuid4().hex}_{file.filename}"
                file_path = UPLOAD_DIR / unique_filename
                
                # 保存文件
                with open(file_path, "wb") as buffer:
                    content = await file.read()
                    buffer.write(content)
                
                file_size = len(content)
                
                # 添加到成功列表
                success_materials.append({
                    "id": str(uuid.uuid4()),
                    "name": file.filename,
                    "type": "video",
                    "format": "MP4",
                    "size": f"{file_size / 1024:.1f} KB",
                    "duration": "0:30",
                    "dimensions": {"width": 1920, "height": 1080},
                    "aspectRatio": "16:9",
                    "path": str(file_path),
                    "url": str(file_path),
                    "category": category,
                    "tags": [],
                    "isBuiltIn": False,
                    "createdAt": "2025-06-28T00:00:00Z",
                    "lastModified": "2025-06-28T00:00:00Z"
                })
                
            except Exception as e:
                failed_uploads.append({
                    "name": file.filename or "unknown",
                    "error": str(e)
                })
        
        return {
            "success": success_materials,
            "failed": failed_uploads,
            "total": len(files),
            "success_count": len(success_materials),
            "failed_count": len(failed_uploads)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量上传失败: {str(e)}")

if __name__ == "__main__":
    print("启动最小化视频上传测试服务器...")
    print("服务器地址: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")
    print("视频上传测试: http://localhost:8000/docs#/default/upload_video_file_api_video_materials_upload_post")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
