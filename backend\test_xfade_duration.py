#!/usr/bin/env python3
"""
测试xfade的实际时长行为
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import subprocess

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_xfade_duration_behavior():
    """测试xfade对视频时长的实际影响"""
    
    logger.info("🔍 测试xfade对视频时长的实际影响")
    
    try:
        # 创建两个5秒的测试视频
        logger.info("创建测试视频...")
        
        # 红色视频 5秒
        (
            ffmpeg
            .input('color=red:size=640x480:duration=5:rate=30', f='lavfi')
            .output('test_red_5s.mp4', vcodec='libx264', pix_fmt='yuv420p')
            .overwrite_output()
            .run(quiet=True)
        )
        
        # 绿色视频 5秒
        (
            ffmpeg
            .input('color=green:size=640x480:duration=5:rate=30', f='lavfi')
            .output('test_green_5s.mp4', vcodec='libx264', pix_fmt='yuv420p')
            .overwrite_output()
            .run(quiet=True)
        )
        
        logger.info("✅ 测试视频创建完成")
        
        # 测试1: 简单concat（无转场）
        logger.info("\n📊 测试1: 简单concat（无转场）")
        cmd1 = [
            'ffmpeg',
            '-i', 'test_red_5s.mp4',
            '-i', 'test_green_5s.mp4',
            '-filter_complex', '[0:v][1:v]concat=n=2:v=1:a=0',
            '-c:v', 'libx264',
            '-y', 'concat_result.mp4'
        ]
        
        result1 = subprocess.run(cmd1, capture_output=True, text=True)
        if result1.returncode == 0:
            # 获取时长
            probe1 = ffmpeg.probe('concat_result.mp4')
            duration1 = float(probe1['format']['duration'])
            logger.info(f"简单concat结果时长: {duration1}s (预期: 10s)")
        else:
            logger.error(f"简单concat失败: {result1.stderr}")
            return False
        
        # 测试2: xfade转场 1秒
        logger.info("\n📊 测试2: xfade转场 1秒")
        cmd2 = [
            'ffmpeg',
            '-i', 'test_red_5s.mp4',
            '-i', 'test_green_5s.mp4',
            '-filter_complex', '[0:v][1:v]xfade=transition=fade:duration=1:offset=4',
            '-c:v', 'libx264',
            '-y', 'xfade_result.mp4'
        ]
        
        result2 = subprocess.run(cmd2, capture_output=True, text=True)
        if result2.returncode == 0:
            # 获取时长
            probe2 = ffmpeg.probe('xfade_result.mp4')
            duration2 = float(probe2['format']['duration'])
            logger.info(f"xfade转场结果时长: {duration2}s")
            
            # 分析结果
            if abs(duration2 - 10.0) < 0.1:
                logger.info("✅ xfade不减少总时长，只是重叠播放")
            elif abs(duration2 - 9.0) < 0.1:
                logger.info("✅ xfade减少总时长，重叠部分被移除")
            else:
                logger.info(f"⚠️ xfade结果时长异常: {duration2}s")
        else:
            logger.error(f"xfade转场失败: {result2.stderr}")
            return False
        
        # 测试3: 三个视频的xfade
        logger.info("\n📊 测试3: 三个视频的xfade")
        
        # 创建蓝色视频 5秒
        (
            ffmpeg
            .input('color=blue:size=640x480:duration=5:rate=30', f='lavfi')
            .output('test_blue_5s.mp4', vcodec='libx264', pix_fmt='yuv420p')
            .overwrite_output()
            .run(quiet=True)
        )
        
        cmd3 = [
            'ffmpeg',
            '-i', 'test_red_5s.mp4',
            '-i', 'test_green_5s.mp4',
            '-i', 'test_blue_5s.mp4',
            '-filter_complex', 
            '[0:v][1:v]xfade=transition=fade:duration=1:offset=4[v01];[v01][2:v]xfade=transition=fade:duration=1:offset=8',
            '-c:v', 'libx264',
            '-y', 'triple_xfade_result.mp4'
        ]
        
        result3 = subprocess.run(cmd3, capture_output=True, text=True)
        if result3.returncode == 0:
            # 获取时长
            probe3 = ffmpeg.probe('triple_xfade_result.mp4')
            duration3 = float(probe3['format']['duration'])
            logger.info(f"三段xfade结果时长: {duration3}s")
            
            # 分析结果
            logger.info("分析:")
            logger.info(f"  原始总时长: 15s (5+5+5)")
            logger.info(f"  如果重叠减少: 13s (15-2)")
            logger.info(f"  如果不减少: 15s")
            logger.info(f"  实际结果: {duration3}s")
            
            if abs(duration3 - 15.0) < 0.1:
                logger.info("✅ xfade不减少总时长")
            elif abs(duration3 - 13.0) < 0.1:
                logger.info("✅ xfade减少总时长")
            else:
                logger.info(f"⚠️ 结果异常")
        else:
            logger.error(f"三段xfade失败: {result3.stderr}")
            return False
        
        logger.info("\n📋 总结:")
        logger.info(f"简单concat: {duration1}s")
        logger.info(f"两段xfade: {duration2}s")
        logger.info(f"三段xfade: {duration3}s")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        test_files = [
            'test_red_5s.mp4', 'test_green_5s.mp4', 'test_blue_5s.mp4',
            'concat_result.mp4', 'xfade_result.mp4', 'triple_xfade_result.mp4'
        ]
        for file_path in test_files:
            if Path(file_path).exists():
                Path(file_path).unlink()

if __name__ == "__main__":
    logger.info("🚀 开始测试xfade时长行为")
    
    success = test_xfade_duration_behavior()
    
    if success:
        logger.info("\n🎉 xfade时长测试完成!")
    else:
        logger.error("\n❌ xfade时长测试失败")
        sys.exit(1)
