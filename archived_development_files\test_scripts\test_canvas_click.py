#!/usr/bin/env python3
"""
测试画布点击功能的脚本
使用Playwright直接控制浏览器并点击画布区域
"""

import asyncio
from playwright.async_api import async_playwright

async def test_canvas_click():
    """测试画布点击添加元素功能"""
    async with async_playwright() as p:
        # 连接到现有的浏览器实例
        try:
            browser = await p.chromium.connect_over_cdp("http://localhost:9222")
            print("已连接到现有浏览器实例")
        except Exception as e:
            print(f"连接失败，启动新的浏览器实例: {e}")
            browser = await p.chromium.launch(headless=False)
        
        # 创建新的页面或获取现有页面
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 导航到封面模板页面
            print("导航到封面模板页面...")
            await page.goto('http://localhost:3000/covers')
            await page.wait_for_load_state('networkidle')
            
            # 等待页面完全加载
            await page.wait_for_timeout(2000)
            
            # 检查是否有模板存在，如果没有则创建新模板
            print("检查模板列表...")
            
            # 查找并点击一个现有模板
            template_elements = await page.query_selector_all('.template-card, [data-testid="template-item"]')
            if not template_elements:
                print("没有找到现有模板，尝试创建新模板...")
                # 点击新建模板按钮
                create_btn = await page.query_selector('button:has-text("新建模板")')
                if create_btn:
                    await create_btn.click()
                    await page.wait_for_timeout(1000)
                    
                    # 填写模板名称
                    name_input = await page.query_selector('input[placeholder*="模板名称"]')
                    if name_input:
                        await name_input.fill('测试画布功能')
                        
                    # 点击创建按钮
                    create_submit = await page.query_selector('button:has-text("创建模板")')
                    if create_submit:
                        await create_submit.click()
                        await page.wait_for_timeout(2000)
            
            # 选择模板进入编辑模式
            print("选择模板进入编辑模式...")
            template_cards = await page.query_selector_all('[class*="template"]')
            for card in template_cards:
                try:
                    await card.click()
                    break
                except:
                    continue
            
            await page.wait_for_timeout(2000)
            
            # 点击文本工具按钮
            print("点击文本工具按钮...")
            text_btn = await page.query_selector('button:has-text("文本")')
            if text_btn:
                await text_btn.click()
                print("已点击文本按钮")
                await page.wait_for_timeout(1000)
            else:
                print("未找到文本按钮")
            
            # 查找画布元素
            print("查找画布元素...")
            canvas = await page.query_selector('[class*="canvas"], [data-testid="canvas"], div[style*="background"]')
            
            if canvas:
                print("找到画布元素，获取位置信息...")
                box = await canvas.bounding_box()
                if box:
                    # 在画布中心位置点击
                    center_x = box['x'] + box['width'] / 2
                    center_y = box['y'] + box['height'] / 2
                    print(f"画布位置: x={box['x']}, y={box['y']}, width={box['width']}, height={box['height']}")
                    print(f"点击中心位置: x={center_x}, y={center_y}")
                    
                    await page.mouse.click(center_x, center_y)
                    print("已在画布中心点击")
                    
                    # 等待元素添加
                    await page.wait_for_timeout(2000)
                    
                    # 检查是否成功添加文本元素
                    print("检查是否成功添加文本元素...")
                    text_elements = await page.query_selector_all('[class*="text"], [data-testid="text-element"]')
                    if text_elements:
                        print(f"成功！找到 {len(text_elements)} 个文本元素")
                        return True
                    else:
                        print("未找到新添加的文本元素")
                        
                        # 截图保存状态
                        await page.screenshot(path='canvas_after_click.png')
                        print("已保存点击后的截图: canvas_after_click.png")
                        return False
                else:
                    print("无法获取画布位置信息")
                    return False
            else:
                print("未找到画布元素")
                # 尝试通过CSS选择器查找
                potential_canvas = await page.query_selector('div[style*="320px"][style*="180px"]')
                if potential_canvas:
                    print("通过尺寸找到了可能的画布元素")
                    box = await potential_canvas.bounding_box()
                    if box:
                        center_x = box['x'] + box['width'] / 2
                        center_y = box['y'] + box['height'] / 2
                        await page.mouse.click(center_x, center_y)
                        print("已在可能的画布位置点击")
                        await page.wait_for_timeout(2000)
                        return True
                
                return False
                
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            await page.screenshot(path='error_screenshot.png')
            return False
        
        finally:
            await context.close()

async def main():
    """主函数"""
    print("开始测试画布点击功能...")
    result = await test_canvas_click()
    
    if result:
        print("✅ 测试成功：画布点击功能正常工作")
    else:
        print("❌ 测试失败：画布点击功能存在问题")
    
    return result

if __name__ == '__main__':
    asyncio.run(main())
