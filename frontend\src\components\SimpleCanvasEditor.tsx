'use client';

import { useState, useRef, useEffect } from 'react';
import { DirectHttpClient } from '@/lib/api/directHttpClient';
import {
  CursorArrowRaysIcon,
  RectangleStackIcon,
  Square3Stack3DIcon,
  PhotoIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  CheckIcon,
  LinkIcon
} from '@heroicons/react/24/outline';

// 工具类型
type ToolType = 'select' | 'text' | 'shape' | 'image';

// 变量绑定类型
interface VariableBinding {
  enabled: boolean;
  variableName: string;
  propertyPath: string;
}

// 元素类型
interface CanvasElement {
  id: string;
  type: 'text' | 'shape' | 'image';
  x: number;
  y: number;
  width: number;
  height: number;
  content?: string;
  fontSize?: number;
  color?: string;
  textAlign?: 'left' | 'center' | 'right';
  backgroundColor?: string;
  shapeType?: 'rectangle' | 'square' | 'circle' | 'triangle' | 'star';
  imageType?: 'rectangle' | 'square' | 'circle';
  borderColor?: string;
  borderWidth?: number;
  imageUrl?: string;
  variableBinding?: VariableBinding;
}

// 背景配置类型
interface BackgroundConfig {
  type: 'solid' | 'gradient';
  value: string;
}

// 工具定义
const TOOLS = [
  { id: 'select' as ToolType, name: '选择', icon: CursorArrowRaysIcon },
  { id: 'text' as ToolType, name: '文本', icon: RectangleStackIcon },
  { id: 'shape' as ToolType, name: '形状', icon: Square3Stack3DIcon },
  { id: 'image' as ToolType, name: '图片', icon: PhotoIcon }
];

// 形状类型选项
const SHAPE_TYPES = [
  { value: 'rectangle', label: '矩形' },
  { value: 'square', label: '正方形' },
  { value: 'circle', label: '圆形' },
  { value: 'triangle', label: '三角形' },
  { value: 'star', label: '星形' }
];

// 渐变预设
const GRADIENT_PRESETS = {
  'blue-purple': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  'sunset': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  'ocean': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  'forest': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  'fire': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
};

// 可用变量
const AVAILABLE_VARIABLES = [
  { id: 'author', name: '账号名称', type: 'text' },
  { id: 'title', name: '视频标题', type: 'text' },
  { id: 'avatar', name: '账号头像', type: 'image' }
];

// 模板数据类型
interface TemplateData {
  id?: string;
  name: string;
  elements: CanvasElement[];
  background: BackgroundConfig;
  updatedAt: string;
}

interface SimpleCanvasEditorProps {
  templateId?: string;
  templateName?: string;
  onSave?: (templateData: TemplateData) => void;
  isNewTemplate?: boolean; // 新增：标识是否为新建模板
}

export default function SimpleCanvasEditor({ 
  templateId, 
  templateName = '未命名模板',
  onSave,
  isNewTemplate = false
}: SimpleCanvasEditorProps) {
  const [currentTool, setCurrentTool] = useState<ToolType>('select');
  const [elements, setElements] = useState<CanvasElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<CanvasElement | null>(null);
  const [elementCounter, setElementCounter] = useState(0);
  const [background, setBackground] = useState<BackgroundConfig>({
    type: 'gradient',
    value: GRADIENT_PRESETS['blue-purple']
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, elemX: 0, elemY: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);

  // 从后端加载模板数据
  const loadTemplate = async (id: string) => {
    setIsLoading(true);
    console.log(`开始加载模板，ID: ${id}`);
    
    try {
      const client = new DirectHttpClient('/api/cover-templates');
      const responseData = await client.get(`/${id}`);
      console.log('API响应数据:', responseData);
      
      // 从后端响应中提取模板数据
      let templateData = responseData;
      if ((responseData as any).data) {
        templateData = (responseData as any).data;
      }
      
      console.log('提取的模板数据:', templateData);
        
        // 设置元素数据
        const elementsData = (templateData as any).elements || [];
        console.log('元素数据:', elementsData, '数量:', elementsData.length);
        setElements(elementsData);
        
        // 设置背景数据
        const backgroundData = (templateData as any).background || {
          type: 'gradient',
          value: GRADIENT_PRESETS['blue-purple']
        };
        console.log('背景数据:', backgroundData);
        setBackground(backgroundData);
        
        // 更新元素计数器，确保新添加的元素不会重复ID
        if (elementsData.length > 0) {
          const maxCounter = elementsData.length;
          setElementCounter(maxCounter);
          console.log(`设置元素计数器为: ${maxCounter}`);
        }
        
        console.log('模板加载成功，元素已还原到画布');
    } catch (error) {
      console.error('加载模板出错:', error);
      // 如果网络错误，尝试从localStorage加载
      console.log('网络错误，尝试从本地缓存加载...');
      loadFromLocalStorage();
    } finally {
      setIsLoading(false);
    }
  };

  // 从localStorage加载（兜底方案）
  const loadFromLocalStorage = () => {
    try {
      const savedData = localStorage.getItem(`template_${templateId}`);
      console.log(`尝试从localStorage加载模板: template_${templateId}`);
      
      if (savedData) {
        const templateData = JSON.parse(savedData);
        console.log('本地缓存中的模板数据:', templateData);
        
        // 设置元素数据
        const elementsData = templateData.elements || [];
        console.log('从缓存加载的元素数据:', elementsData, '数量:', elementsData.length);
        setElements(elementsData);
        
        // 设置背景数据
        const backgroundData = templateData.background || {
          type: 'gradient',
          value: GRADIENT_PRESETS['blue-purple']
        };
        console.log('从缓存加载的背景数据:', backgroundData);
        setBackground(backgroundData);
        
        // 更新元素计数器
        if (elementsData.length > 0) {
          const maxCounter = elementsData.length;
          setElementCounter(maxCounter);
          console.log(`从缓存设置元素计数器为: ${maxCounter}`);
        }
        
        console.log('从本地缓存加载模板成功');
      } else {
        console.log('本地缓存中没有找到模板数据');
      }
    } catch (error) {
      console.error('从本地缓存加载失败:', error);
    }
  };

  // 组件加载时自动加载模板
  useEffect(() => {
    if (templateId) {
      loadTemplate(templateId);
    }
  }, [templateId]);

  // 切换工具
  const switchTool = (tool: ToolType) => {
    setCurrentTool(tool);
    console.log('切换到工具:', tool);
  };

  // 开始拖拽
  const handleMouseDown = (e: React.MouseEvent, element: CanvasElement) => {
    e.stopPropagation();
    setSelectedElement(element);
    setIsDragging(true);
    setDragStart({
      x: e.clientX,
      y: e.clientY,
      elemX: element.x,
      elemY: element.y
    });
  };

  // 拖拽功能
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging && selectedElement) {
        const deltaX = e.clientX - dragStart.x;
        const deltaY = e.clientY - dragStart.y;
        const newX = Math.max(0, Math.min(300, dragStart.elemX + deltaX));
        const newY = Math.max(0, Math.min(170, dragStart.elemY + deltaY));
        
        updateElement(selectedElement.id, { x: newX, y: newY });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, selectedElement, dragStart]);

  // 画布点击处理
  const handleCanvasClick = (e: React.MouseEvent) => {
    if (currentTool === 'select') {
      setSelectedElement(null);
      return;
    }

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left - 50;
    const y = e.clientY - rect.top - 10;

    const newId = `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newCounter = elementCounter + 1;

    let newElement: CanvasElement;

    if (currentTool === 'text') {
      newElement = {
        id: newId,
        type: 'text',
        x: Math.max(0, x),
        y: Math.max(0, y),
        width: 200,
        height: 20,
        content: `新文本 ${newCounter}`,
        fontSize: 16,
        color: '#ffffff',
        textAlign: 'left',
        variableBinding: {
          enabled: false,
          variableName: '',
          propertyPath: ''
        }
      };
    } else if (currentTool === 'shape') {
      newElement = {
        id: newId,
        type: 'shape',
        x: Math.max(0, x),
        y: Math.max(0, y),
        width: 50,
        height: 50,
        backgroundColor: '#ffffff',
        shapeType: 'rectangle',
        borderColor: '#000000',
        borderWidth: 0
      };
    } else if (currentTool === 'image') {
      newElement = {
        id: newId,
        type: 'image',
        x: Math.max(0, x),
        y: Math.max(0, y),
        width: 80,
        height: 60,
        imageType: 'rectangle',
        variableBinding: {
          enabled: false,
          variableName: '',
          propertyPath: ''
        }
      };
    } else {
      return;
    }

    setElements(prev => [...prev, newElement]);
    setElementCounter(newCounter);
    setSelectedElement(newElement);
    setCurrentTool('select'); // 添加元素后自动切换到选择工具
  };

  // 选择元素
  const selectElement = (element: CanvasElement) => {
    setSelectedElement(element);
    setCurrentTool('select');
  };

  // 保存模板功能
  // 保存模板功能
  const handleSave = async () => {
    if (isSaving) return;
    
    setIsSaving(true);
    
    const templateData: TemplateData = {
      id: templateId,
      name: templateName,
      elements: elements,
      background: background,
      updatedAt: new Date().toISOString()
    };
    
    try {
      // 尝试保存到后端
      const client = new DirectHttpClient('/api/cover-templates');
      
      let savedTemplate;
      if (templateId) {
        savedTemplate = await client.put(`/${templateId}`, templateData);
      } else {
        savedTemplate = await client.post('/', templateData);
      }
      
      console.log('模板保存成功:', savedTemplate);
      
      // 同时保存到localStorage作为缓存
      localStorage.setItem(`template_${templateId || (savedTemplate as any).id}`, JSON.stringify(templateData));
      
      // 调用回调函数通知父组件 - 传递标准化的数据格式
      if (onSave) {
        // 从后端响应中提取数据，确保格式正确
        const backendData = (savedTemplate as any).data || savedTemplate;
        const callbackData = {
          id: (backendData as any).id || templateId,
          name: (backendData as any).name || templateName,
          templateName: (backendData as any).name || templateName, // 兼容字段
          elements: templateData.elements,
          background: templateData.background,
          updatedAt: templateData.updatedAt
        };
        console.log('传递给父组件的回调数据:', callbackData);
        onSave(callbackData);
      }
      
      alert('模板保存成功！');
    } catch (error) {
      console.error('保存到后端失败:', error);
      
      // 如果后端保存失败，保存到localStorage作为兜底
      localStorage.setItem(`template_${templateId || 'temp'}`, JSON.stringify(templateData));
      
      alert('网络错误，模板已暂存到本地缓存');
    } finally {
      setIsSaving(false);
    }
  };

  // 更新元素
  const updateElement = (elementId: string, updates: Partial<CanvasElement>) => {
    setElements(prev => prev.map(el => 
      el.id === elementId ? { ...el, ...updates } : el
    ));
    
    if (selectedElement?.id === elementId) {
      setSelectedElement(prev => prev ? { ...prev, ...updates } : null);
    }
  };

  // 删除元素
  const deleteElement = (elementId: string) => {
    setElements(prev => prev.filter(el => el.id !== elementId));
    if (selectedElement?.id === elementId) {
      setSelectedElement(null);
    }
  };

  // 键盘删除处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' && selectedElement) {
        deleteElement(selectedElement.id);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedElement]);

  // 形状样式应用
  const getShapeStyle = (element: CanvasElement) => {
    const baseStyle: React.CSSProperties = {
      backgroundColor: element.backgroundColor || '#ffffff',
      borderColor: element.borderColor || '#000000',
      borderWidth: element.borderWidth || 0,
      borderStyle: element.borderWidth && element.borderWidth > 0 ? 'solid' : 'none'
    };

    switch (element.shapeType) {
      case 'circle':
        return { ...baseStyle, borderRadius: '50%' };
      case 'triangle':
        return { 
          ...baseStyle, 
          backgroundColor: 'transparent',
          borderStyle: 'solid',
          borderWidth: `0 ${element.width / 2}px ${element.height}px ${element.width / 2}px`,
          borderColor: `transparent transparent ${element.backgroundColor} transparent`,
          width: 0,
          height: 0
        };
      case 'star':
        return { 
          ...baseStyle, 
          borderRadius: '0',
          clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)'
        };
      default:
        return { ...baseStyle, borderRadius: '4px' };
    }
  };

  // RGB转HEX
  const rgbToHex = (rgb: string) => {
    if (!rgb) return '#ffffff';
    if (rgb.indexOf('#') === 0) return rgb;
    
    const result = rgb.match(/\d+/g);
    if (!result || result.length < 3) return '#ffffff';
    
    return "#" + ((1 << 24) + (parseInt(result[0]) << 16) + (parseInt(result[1]) << 8) + parseInt(result[2])).toString(16).slice(1);
  };

  return (
    <div className="flex h-full">
      {/* 左侧 - 编辑器 */}
      <div className="flex-1 bg-white rounded-lg shadow-sm border">
        {/* 工具栏 */}
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              {TOOLS.map(tool => (
                <button
                  key={tool.id}
                  onClick={() => switchTool(tool.id)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentTool === tool.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <tool.icon className="w-4 h-4 inline-block mr-1" />
                  {tool.name}
                </button>
              ))}
            </div>
            
            <div className="flex gap-2">
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <ArrowUturnLeftIcon className="w-4 h-4" />
              </button>
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <ArrowUturnRightIcon className="w-4 h-4" />
              </button>
              <button 
                onClick={handleSave}
                disabled={isSaving}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  isSaving 
                    ? 'bg-gray-400 text-white cursor-not-allowed' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                <CheckIcon className="w-4 h-4 inline-block mr-1" />
                {isSaving ? '保存中...' : '保存'}
              </button>
            </div>
          </div>
        </div>

        {/* 画布容器 */}
        <div className="p-8 flex justify-center">
          <div className="relative">
            <div
              ref={canvasRef}
              className={`relative w-80 h-48 rounded-lg overflow-hidden border-2 border-gray-200 ${
                currentTool === 'select' ? 'cursor-default' : 'cursor-crosshair'
              }`}
              style={{
                background: background.type === 'solid' ? background.value : background.value
              }}
              onClick={handleCanvasClick}
            >
              {/* 加载状态覆盖层 */}
              {isLoading && (
                <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-10">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-sm text-gray-600">加载模板中...</p>
                  </div>
                </div>
              )}

              {/* 渲染元素 */}
              {elements.map(element => (
                <div
                  key={element.id}
                  className={`absolute border-2 transition-all ${
                    selectedElement?.id === element.id 
                      ? 'border-blue-500 border-dashed cursor-move' 
                      : 'border-transparent hover:border-blue-300 hover:border-dashed cursor-pointer'
                  }`}
                  style={{
                    left: element.x,
                    top: element.y,
                    width: element.type === 'shape' && element.shapeType === 'triangle' ? 'auto' : element.width,
                    height: element.type === 'shape' && element.shapeType === 'triangle' ? 'auto' : element.height,
                    ...(element.type === 'text' ? {
                      color: element.color,
                      fontSize: element.fontSize,
                      textAlign: element.textAlign,
                      fontWeight: 'bold',
                      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)',
                      padding: '4px 8px',
                      userSelect: 'none',
                      wordWrap: 'break-word',
                      lineHeight: '1.2'
                    } : element.type === 'shape' ? getShapeStyle(element) : {
                      backgroundColor: '#f3f4f6',
                      border: '2px dashed #9ca3af',
                      borderRadius: element.imageType === 'circle' ? '50%' : '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '12px',
                      color: '#6b7280',
                      ...(element.imageUrl ? {
                        backgroundImage: `url(${element.imageUrl})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        border: 'none'
                      } : {})
                    })
                  }}
                  onMouseDown={(e) => handleMouseDown(e, element)}
                  onClick={(e) => {
                    e.stopPropagation();
                    selectElement(element);
                  }}
                >
                  {element.type === 'text' && (
                    element.variableBinding?.enabled ? 
                      `{${element.variableBinding.variableName}}` : 
                      element.content
                  )}
                  {element.type === 'image' && !element.imageUrl && (
                    element.variableBinding?.enabled ? 
                      `{${element.variableBinding.variableName}}` : 
                      '图片'
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 右侧 - 属性面板 */}
      <div className="w-80 bg-white border-l p-4 overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">属性面板</h3>
        
        {selectedElement ? (
          <div className="space-y-4">
            {/* 位置属性 */}
            <div>
              <h4 className="font-medium mb-2">位置和尺寸</h4>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">X</label>
                  <input
                    type="number"
                    value={selectedElement.x}
                    onChange={(e) => updateElement(selectedElement.id, { x: Math.max(0, parseInt(e.target.value) || 0) })}
                    className="w-full px-2 py-1 border rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Y</label>
                  <input
                    type="number"
                    value={selectedElement.y}
                    onChange={(e) => updateElement(selectedElement.id, { y: Math.max(0, parseInt(e.target.value) || 0) })}
                    className="w-full px-2 py-1 border rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">宽度</label>
                  <input
                    type="number"
                    value={selectedElement.width}
                    onChange={(e) => updateElement(selectedElement.id, { width: Math.max(10, parseInt(e.target.value) || 10) })}
                    className="w-full px-2 py-1 border rounded text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">高度</label>
                  <input
                    type="number"
                    value={selectedElement.height}
                    onChange={(e) => updateElement(selectedElement.id, { height: Math.max(10, parseInt(e.target.value) || 10) })}
                    className="w-full px-2 py-1 border rounded text-sm"
                  />
                </div>
              </div>
            </div>

            {/* 文本属性 */}
            {selectedElement.type === 'text' && (
              <div>
                <h4 className="font-medium mb-2">文本属性</h4>
                <div className="space-y-2">
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">内容</label>
                    <input
                      type="text"
                      value={selectedElement.content || ''}
                      onChange={(e) => updateElement(selectedElement.id, { content: e.target.value })}
                      className="w-full px-2 py-1 border rounded text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">字体大小</label>
                    <input
                      type="number"
                      value={selectedElement.fontSize || 16}
                      onChange={(e) => updateElement(selectedElement.id, { fontSize: parseInt(e.target.value) || 16 })}
                      className="w-full px-2 py-1 border rounded text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">颜色</label>
                    <input
                      type="color"
                      value={selectedElement.color || '#ffffff'}
                      onChange={(e) => updateElement(selectedElement.id, { color: e.target.value })}
                      className="w-full h-8 border rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">对齐</label>
                    <select
                      value={selectedElement.textAlign || 'left'}
                      onChange={(e) => updateElement(selectedElement.id, { textAlign: e.target.value as any })}
                      className="w-full px-2 py-1 border rounded text-sm"
                    >
                      <option value="left">左对齐</option>
                      <option value="center">居中</option>
                      <option value="right">右对齐</option>
                    </select>
                  </div>
                  
                  {/* 变量绑定 */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <input
                        type="checkbox"
                        checked={selectedElement.variableBinding?.enabled || false}
                        onChange={(e) => {
                          const binding = selectedElement.variableBinding || { enabled: false, variableName: '', propertyPath: '' };
                          updateElement(selectedElement.id, { 
                            variableBinding: { ...binding, enabled: e.target.checked }
                          });
                        }}
                        className="w-4 h-4"
                      />
                      <label className="text-sm text-gray-600">
                        <LinkIcon className="w-4 h-4 inline mr-1" />
                        绑定变量
                      </label>
                    </div>
                    {selectedElement.variableBinding?.enabled && (
                      <select
                        value={selectedElement.variableBinding?.variableName || ''}
                        onChange={(e) => {
                          const binding = selectedElement.variableBinding || { enabled: true, variableName: '', propertyPath: '' };
                          updateElement(selectedElement.id, { 
                            variableBinding: { ...binding, variableName: e.target.value, propertyPath: 'content' }
                          });
                        }}
                        className="w-full px-2 py-1 border rounded text-sm"
                      >
                        <option value="">选择变量</option>
                        {AVAILABLE_VARIABLES.filter(v => v.type === 'text').map(variable => (
                          <option key={variable.id} value={variable.id}>{variable.name}</option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 形状属性 */}
            {selectedElement.type === 'shape' && (
              <div>
                <h4 className="font-medium mb-2">形状属性</h4>
                <div className="space-y-2">
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">形状类型</label>
                    <select
                      value={selectedElement.shapeType || 'rectangle'}
                      onChange={(e) => updateElement(selectedElement.id, { shapeType: e.target.value as any })}
                      className="w-full px-2 py-1 border rounded text-sm"
                    >
                      {SHAPE_TYPES.map(shape => (
                        <option key={shape.value} value={shape.value}>{shape.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">填充颜色</label>
                    <input
                      type="color"
                      value={selectedElement.backgroundColor || '#ffffff'}
                      onChange={(e) => updateElement(selectedElement.id, { backgroundColor: e.target.value })}
                      className="w-full h-8 border rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">边框颜色</label>
                    <input
                      type="color"
                      value={selectedElement.borderColor || '#000000'}
                      onChange={(e) => updateElement(selectedElement.id, { borderColor: e.target.value })}
                      className="w-full h-8 border rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">边框宽度</label>
                    <input
                      type="number"
                      min="0"
                      value={selectedElement.borderWidth || 0}
                      onChange={(e) => updateElement(selectedElement.id, { borderWidth: parseInt(e.target.value) || 0 })}
                      className="w-full px-2 py-1 border rounded text-sm"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* 图片属性 */}
            {selectedElement.type === 'image' && (
              <div>
                <h4 className="font-medium mb-2">图片属性</h4>
                <div className="space-y-2">
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">形状</label>
                    <select
                      value={selectedElement.imageType || 'rectangle'}
                      onChange={(e) => updateElement(selectedElement.id, { imageType: e.target.value as any })}
                      className="w-full px-2 py-1 border rounded text-sm"
                    >
                      <option value="rectangle">矩形</option>
                      <option value="square">正方形</option>
                      <option value="circle">圆形</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">上传图片</label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = (e) => {
                            updateElement(selectedElement.id, { imageUrl: e.target?.result as string });
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                      className="w-full px-2 py-1 border rounded text-sm"
                    />
                  </div>
                  
                  {/* 变量绑定 */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <input
                        type="checkbox"
                        checked={selectedElement.variableBinding?.enabled || false}
                        onChange={(e) => {
                          const binding = selectedElement.variableBinding || { enabled: false, variableName: '', propertyPath: '' };
                          updateElement(selectedElement.id, { 
                            variableBinding: { ...binding, enabled: e.target.checked }
                          });
                        }}
                        className="w-4 h-4"
                      />
                      <label className="text-sm text-gray-600">
                        <LinkIcon className="w-4 h-4 inline mr-1" />
                        绑定变量
                      </label>
                    </div>
                    {selectedElement.variableBinding?.enabled && (
                      <select
                        value={selectedElement.variableBinding?.variableName || ''}
                        onChange={(e) => {
                          const binding = selectedElement.variableBinding || { enabled: true, variableName: '', propertyPath: '' };
                          updateElement(selectedElement.id, { 
                            variableBinding: { ...binding, variableName: e.target.value, propertyPath: 'imageUrl' }
                          });
                        }}
                        className="w-full px-2 py-1 border rounded text-sm"
                      >
                        <option value="">选择变量</option>
                        {AVAILABLE_VARIABLES.filter(v => v.type === 'image').map(variable => (
                          <option key={variable.id} value={variable.id}>{variable.name}</option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 删除按钮 */}
            <button
              onClick={() => deleteElement(selectedElement.id)}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700"
            >
              删除元素
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-gray-500 text-sm mb-4">
              选择一个元素以编辑其属性
            </div>
            
            {/* 背景设置 */}
            <div>
              <h4 className="font-medium mb-2">画布背景</h4>
              <div className="space-y-2">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">背景类型</label>
                  <select
                    value={background.type}
                    onChange={(e) => setBackground({ 
                      type: e.target.value as 'solid' | 'gradient',
                      value: e.target.value === 'solid' ? '#667eea' : GRADIENT_PRESETS['blue-purple']
                    })}
                    className="w-full px-2 py-1 border rounded text-sm"
                  >
                    <option value="solid">纯色</option>
                    <option value="gradient">渐变</option>
                  </select>
                </div>
                
                {background.type === 'solid' ? (
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">背景颜色</label>
                    <input
                      type="color"
                      value={background.value}
                      onChange={(e) => setBackground({ type: 'solid', value: e.target.value })}
                      className="w-full h-8 border rounded"
                    />
                  </div>
                ) : (
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">渐变样式</label>
                    <select
                      value={Object.keys(GRADIENT_PRESETS).find(key => GRADIENT_PRESETS[key as keyof typeof GRADIENT_PRESETS] === background.value) || 'blue-purple'}
                      onChange={(e) => setBackground({ 
                        type: 'gradient', 
                        value: GRADIENT_PRESETS[e.target.value as keyof typeof GRADIENT_PRESETS]
                      })}
                      className="w-full px-2 py-1 border rounded text-sm"
                    >
                      <option value="blue-purple">蓝紫渐变</option>
                      <option value="sunset">日落渐变</option>
                      <option value="ocean">海洋渐变</option>
                      <option value="forest">森林渐变</option>
                      <option value="fire">火焰渐变</option>
                    </select>
                    <div 
                      className="w-full h-8 border rounded mt-2"
                      style={{ background: background.value }}
                    ></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
