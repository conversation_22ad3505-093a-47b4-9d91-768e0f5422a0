@echo off
echo ===============================================
echo 视频素材管理页面 - 集成测试（使用最小化后端）
echo ===============================================

cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator"

echo 1. 启动最小化后端服务器...
cd backend
start "Minimal Backend" python minimal_server.py
cd ..

echo 2. 等待后端启动...
timeout /t 5 /nobreak > nul

echo 3. 启动前端开发服务器...
cd frontend
start "Frontend Server" npm run dev
cd ..

echo.
echo ===============================================
echo 服务器已启动！
echo ===============================================
echo 后端API (最小化): http://localhost:8000
echo 前端页面: http://localhost:3000
echo 视频素材管理页面: http://localhost:3000/videos
echo API文档: http://localhost:8000/docs
echo.
echo ===============================================
echo 测试步骤：
echo ===============================================
echo 1. 在浏览器中打开 http://localhost:3000/videos
echo 2. 测试单文件上传功能（点击"上传视频"）
echo 3. 测试批量上传功能（点击"批量导入"）
echo 4. 测试拖拽上传功能
echo 5. 检查上传进度和错误处理
echo.
echo 注意：此为最小化后端，仅用于测试上传API
echo 不包含数据库功能，上传的文件不会持久化显示
echo.
echo 按任意键关闭所有服务器...
pause > nul

echo 关闭服务器...
taskkill /f /im node.exe 2>nul
taskkill /f /im python.exe 2>nul
echo 所有服务器已关闭
