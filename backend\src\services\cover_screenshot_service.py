"""
封面截图服务
实现网页截图功能，将封面模板渲染的HTML以"reddit-cover"为根元素进行截图
"""

import os
import asyncio
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional
from uuid import uuid4
import logging
import json
import traceback

from sqlalchemy.orm import Session

from ..models.resources import CoverTemplate
from ..models.accounts import Account
from .template_import_service import template_import_service

logger = logging.getLogger(__name__)

class CoverScreenshotService:
    """封面截图服务 - 使用subprocess避免Windows异步问题"""
    
    def __init__(self):
        pass
        
    def _create_screenshot_script(self, html_content: str, output_path: str) -> str:
        """创建独立的截图脚本"""
        script_content = f'''
import sys
import os
from playwright.sync_api import sync_playwright
from pathlib import Path
import base64

# 确保输出使用UTF-8编码
if os.name == 'nt':  # Windows系统
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def main():
    html_content = """{html_content}"""
    output_path = r"{output_path}"
    
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox', 
                    '--disable-setuid-sandbox', 
                    '--disable-dev-shm-usage',
                    '--force-device-scale-factor=2',  # 提高截图质量
                    '--disable-web-security',  # 允许本地文件访问
                    '--allow-running-insecure-content',
                    '--disable-features=VizDisplayCompositor',  # 改善渲染性能
                    '--allow-file-access-from-files',  # 允许文件协议访问
                    '--disable-universal-access-from-file-urls'  # 禁用文件URL的通用访问限制
                ]
            )
            page = browser.new_page()
            
            # 监听网络请求失败
            def handle_request_failed(request):
                print(f"网络请求失败: {{request.url}}", file=sys.stderr)
            
            page.on("requestfailed", handle_request_failed)
            
            # 设置更高的分辨率，适配视频尺寸
            page.set_viewport_size({{"width": 1080, "height": 1920}})
            
            # 加载HTML内容 - 等待所有资源加载完成
            try:
                # 首先设置页面内容
                page.set_content(html_content, wait_until="domcontentloaded", timeout=15000)
                
                # 预加载所有图片资源
                print("预加载图片资源...", file=sys.stderr)
                page.evaluate("""
                    () => {{
                        const images = Array.from(document.querySelectorAll('img'));
                        images.forEach(img => {{
                            // 强制触发图片加载
                            if (img.src && !img.complete) {{
                                const newImg = new Image();
                                newImg.src = img.src;
                            }}
                        }});
                    }}
                """)
                
                # 等待所有图片加载完成，包括Base64图片和file://协议
                print("等待所有图片加载完成...", file=sys.stderr)
                page.wait_for_function("""
                    () => {{
                        const images = Array.from(document.querySelectorAll('img'));
                        if (images.length === 0) {{
                            console.log('没有找到图片元素');
                            return true;
                        }}
                        
                        let loadedCount = 0;
                        let totalCount = images.length;
                        
                        images.forEach((img, index) => {{
                            if (img.complete && img.naturalWidth > 0) {{
                                loadedCount++;
                            }} else if (img.src.startsWith('data:')) {{
                                // Base64图片应该立即可用
                                loadedCount++;
                            }} else if (img.src.startsWith('file://')) {{
                                // File协议图片，检查是否有尺寸
                                if (img.naturalWidth > 0 || img.complete) {{
                                    loadedCount++;
                                }} else {{
                                    console.log(`File协议图片 ${{index}} 未完成加载: ${{img.src}}`);
                                }}
                            }} else {{
                                console.log(`图片 ${{index}} 未完成加载: ${{img.src}}`);
                            }}
                        }});
                        
                        console.log(`图片加载进度: ${{loadedCount}}/${{totalCount}}`);
                        return loadedCount === totalCount;
                    }}
                """, timeout=30000)  # 增加超时时间到30秒
                
                # 等待字体和其他资源渲染
                page.wait_for_timeout(3000)
                print("所有资源加载完成", file=sys.stderr)
                
            except Exception as e:
                print(f"WARNING: 等待资源加载超时: {{e}}", file=sys.stderr)
                # 即使超时也继续，但给更多时间
                page.wait_for_timeout(5000)
            
            # 查找reddit-cover元素并截图
            try:
                # 首先检查页面中的图片状态
                image_info = page.evaluate("""
                    () => {{
                        const images = Array.from(document.querySelectorAll('img'));
                        return images.map((img, index) => ({{
                            index: index,
                            src: img.src.substring(0, 100) + (img.src.length > 100 ? '...' : ''),
                            complete: img.complete,
                            naturalWidth: img.naturalWidth,
                            naturalHeight: img.naturalHeight,
                            width: img.width,
                            height: img.height
                        }}));
                    }}
                """)
                print(f"页面图片状态: {{image_info}}", file=sys.stderr)
                
                cover_element = page.query_selector("#reddit-cover")
                if not cover_element:
                    # 如果找不到reddit-cover元素，尝试截图整个页面
                    print("WARNING: 未找到id为'reddit-cover'的元素，截图整个页面", file=sys.stderr)
                    # 创建输出目录
                    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                    page.screenshot(
                        path=output_path,
                        type='png',
                        full_page=True
                    )
                else:
                    # 创建输出目录
                    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                    
                    # 高质量截图
                    cover_element.screenshot(
                        path=output_path,
                        type='png',
                        omit_background=False,
                        animations='disabled'
                    )
                
                browser.close()
                print(f"SUCCESS: 成功生成封面截图: {{output_path}}")
                sys.exit(0)
                
            except Exception as screenshot_error:
                print(f"ERROR: 截图操作失败: {{screenshot_error}}", file=sys.stderr)
                browser.close()
                sys.exit(1)
            
    except Exception as e:
        print(f"ERROR: 截图失败: {{e}}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        
        # 创建临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(script_content)
            return f.name
    
    async def generate_cover_screenshot(
        self,
        template_id: str,
        account: Account,
        title: str,
        output_path: str,
        db: Session,
        additional_variables: Optional[Dict[str, str]] = None
    ) -> bool:
        """
        生成封面截图 - 使用独立进程避免Windows异步问题
        
        Args:
            template_id: 模板ID
            account: 账号信息
            title: 标题文本
            output_path: 输出路径
            db: 数据库会话
            additional_variables: 额外的变量
            
        Returns:
            bool: 是否成功生成
        """
        script_path = None
        try:
            logger.info(f"开始生成封面截图: template_id={template_id}, account={account.name}")
            
            # 准备头像路径 - 处理为本地文件的file:// URL
            avatar_path = self._get_avatar_path(account)
            
            # 准备变量数据
            variables = {
                'avatar': avatar_path,
                'account_name': account.name,
                'title': title,
                'description': title[:100] + '...' if len(title) > 100 else title,
            }
            
            # 添加额外变量
            if additional_variables:
                variables.update(additional_variables)
            
            # 渲染模板
            rendered_html = template_import_service.render_template(
                template_id=template_id,
                variables=variables,
                db=db,
                base_url="http://localhost:8000"  # 截图服务使用固定的本地URL
            )
            
            # 手动转换localhost URL为Base64 data URL以确保图片可以加载
            rendered_html = self._convert_localhost_urls_to_base64(rendered_html, template_id)
            
            # 转义HTML内容中的引号
            escaped_html = rendered_html.replace('"""', '\\"\\"\\"').replace('\\', '\\\\')
            
            # 创建截图脚本
            script_path = self._create_screenshot_script(escaped_html, output_path)
            
            # 使用同步subprocess避免Windows异步问题
            import sys
            import subprocess
            
            logger.info(f"执行截图脚本: {script_path}")
            
            def run_screenshot_script():
                """在线程中运行截图脚本"""
                try:
                    # 使用同步subprocess.run避免Windows异步问题
                    process = subprocess.run(
                        [sys.executable, script_path],
                        capture_output=True,
                        text=True,
                        timeout=60,  # 60秒超时
                        encoding='utf-8',
                        errors='ignore'
                    )
                    
                    return process.returncode, process.stdout, process.stderr
                    
                except subprocess.TimeoutExpired:
                    logger.error(f"截图脚本执行超时 (60秒)")
                    return -1, "", "执行超时"
                except Exception as e:
                    logger.error(f"subprocess执行失败: {e}")
                    return -1, "", str(e)
            
            # 在线程池中运行以避免阻塞事件循环
            returncode, stdout_text, stderr_text = await asyncio.to_thread(run_screenshot_script)
            
            if returncode == 0:
                logger.info(f"成功生成封面截图: {output_path}")
                return True
            else:
                logger.error(f"截图脚本执行失败 (退出码: {returncode})")
                logger.error(f"模板ID: {template_id}, 账号: {account.name}")
                logger.error(f"输出路径: {output_path}")
                logger.error(f"标准错误输出: {stderr_text}")
                if stdout_text:
                    logger.error(f"标准输出: {stdout_text}")
                
                # 检查输出文件是否存在
                if os.path.exists(output_path):
                    logger.warning(f"虽然退出码非0，但输出文件已存在: {output_path}")
                    return True
                
                return False
            
        except asyncio.TimeoutError:
            logger.error("截图脚本执行超时")
            logger.error(traceback.format_exc())
            return False
        except Exception as e:
            logger.error(f"生成封面截图失败: {e}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # 清理临时脚本文件
            if script_path and os.path.exists(script_path):
                try:
                    os.remove(script_path)
                except:
                    pass
    
    async def generate_cover_for_video_task(
        self,
        task,
        template_id: str,
        account: Account,
        title: str,
        output_path: str,
        db: Session
    ) -> bool:
        """
        为视频任务生成封面
        这是专门为视频生成流程设计的封装方法
        
        Args:
            task: 视频生成任务对象
            template_id: 模板ID
            account: 账号信息
            title: 标题文本（通常是故事的第一句话）
            output_path: 输出路径
            db: 数据库会话
            
        Returns:
            bool: 是否成功生成
        """
        additional_variables = {
            'timestamp': '2小时前',  # 可以根据需求动态生成
            'subreddit': 'r/stories',  # 可以从配置中获取
        }
        
        return await self.generate_cover_screenshot(
            template_id=template_id,
            account=account,
            title=title,
            output_path=output_path,
            db=db,
            additional_variables=additional_variables
        )
    
    async def cleanup(self):
        """清理资源"""
        # 使用subprocess方式不需要特殊清理
        pass

    def _get_avatar_path(self, account) -> str:
        """
        获取头像的Base64编码或默认头像URL
        
        Args:
            account: 账号对象
            
        Returns:
            str: 头像的Base64 data URL或默认头像URL
        """
        try:
            if hasattr(account, 'avatar_file_path') and account.avatar_file_path:
                # 构建完整的文件路径
                from pathlib import Path
                import base64
                import mimetypes
                
                backend_dir = Path(__file__).parent.parent.parent
                avatar_file_path = backend_dir / account.avatar_file_path
                
                # 检查文件是否存在且可读
                if avatar_file_path.exists() and avatar_file_path.is_file():
                    try:
                        # 检查文件大小，避免处理过大的文件
                        file_size = avatar_file_path.stat().st_size
                        if file_size > 5 * 1024 * 1024:  # 5MB限制
                            logger.warning(f"头像文件过大: {file_size} bytes, 使用默认头像")
                            return self._get_default_avatar()
                        
                        # 读取文件并转换为base64
                        with open(avatar_file_path, 'rb') as f:
                            file_data = f.read()
                        
                        # 获取MIME类型
                        mime_type, _ = mimetypes.guess_type(str(avatar_file_path))
                        if not mime_type or not mime_type.startswith('image/'):
                            mime_type = 'image/png'  # 默认PNG
                        
                        # 创建base64 data URL
                        base64_data = base64.b64encode(file_data).decode('utf-8')
                        data_url = f"data:{mime_type};base64,{base64_data}"
                        
                        logger.info(f"使用账号头像文件转换为Base64: {avatar_file_path}")
                        return data_url
                        
                    except Exception as e:
                        logger.error(f"读取头像文件失败: {e}")
                        return self._get_default_avatar()
                else:
                    logger.warning(f"账号头像文件不存在: {avatar_file_path}")
                    return self._get_default_avatar()
            
            # 如果没有文件路径，使用默认头像
            return self._get_default_avatar()
            
        except Exception as e:
            logger.error(f"处理头像路径时出错: {e}")
            return self._get_default_avatar()
    
    def _get_default_avatar(self) -> str:
        """获取默认头像"""
        return 'https://via.placeholder.com/50/666666/FFFFFF?text=U'
    
    def _convert_localhost_urls_to_base64(self, html_content: str, template_id: str) -> str:
        """将localhost URL转换为Base64编码的data URL"""
        import re
        import base64
        import mimetypes
        from pathlib import Path
        
        # 获取模板目录
        backend_dir = Path(__file__).parent.parent.parent
        templates_dir = backend_dir / "templates"
        
        # 匹配http://localhost:8000/templates/模式
        pattern = r'http://localhost:8000/templates/([^"\'\s]+)'
        
        def replace_with_base64_url(match):
            relative_path = match.group(1)
            local_file_path = templates_dir / relative_path
            
            if local_file_path.exists():
                try:
                    # 检查文件大小，避免处理过大的文件
                    file_size = local_file_path.stat().st_size
                    if file_size > 10 * 1024 * 1024:  # 10MB限制
                        logger.warning(f"模板图片文件过大: {file_size} bytes, 跳过转换: {relative_path}")
                        return match.group(0)  # 保持原样
                    
                    # 读取文件并转换为base64
                    with open(local_file_path, 'rb') as f:
                        file_data = f.read()
                    
                    # 获取MIME类型
                    mime_type, _ = mimetypes.guess_type(str(local_file_path))
                    if not mime_type or not mime_type.startswith('image/'):
                        mime_type = 'image/png'  # 默认PNG
                    
                    # 创建base64 data URL
                    base64_data = base64.b64encode(file_data).decode('utf-8')
                    data_url = f"data:{mime_type};base64,{base64_data}"
                    
                    logger.debug(f"转换图片为Base64: {relative_path} -> data:{mime_type};base64,{len(base64_data)} chars")
                    return data_url
                    
                except Exception as e:
                    logger.error(f"转换图片为Base64失败 {relative_path}: {e}")
                    return match.group(0)  # 保持原样
            else:
                logger.warning(f"本地图片文件不存在: {local_file_path}")
                return match.group(0)  # 保持原样
        
        return re.sub(pattern, replace_with_base64_url, html_content)

# 创建全局实例
cover_screenshot_service = CoverScreenshotService()

# 确保程序退出时清理资源
import atexit

def cleanup_screenshot_service():
    """清理截图服务"""
    try:
        asyncio.run(cover_screenshot_service.cleanup())
    except:
        pass

atexit.register(cleanup_screenshot_service)
