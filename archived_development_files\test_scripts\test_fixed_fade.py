#!/usr/bin/env python3
"""
测试修复后的淡入淡出效果 - 使用正确的FFmpeg fade语法
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_fixed_fade_effects():
    """测试修复后的淡入淡出效果"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试修复后的淡入淡出效果...")
    
    # 测试配置
    test_configs = [
        {
            'name': '修复后淡入效果',
            'animation': 'fade_in',
            'duration': 5.0,
            'animation_duration': 1.5,
            'output': 'fixed_fade_in.mp4'
        },
        {
            'name': '修复后淡出效果', 
            'animation': 'fade_out',
            'duration': 5.0,
            'animation_duration': 1.5,
            'output': 'fixed_fade_out.mp4'
        },
        {
            'name': '修复后淡入淡出效果',
            'animation': 'fade_in_out',
            'duration': 6.0,
            'animation_duration': 1.5,
            'output': 'fixed_fade_in_out.mp4'
        },
        {
            'name': '修复后无动画效果',
            'animation': 'none',
            'duration': 4.0,
            'animation_duration': 0.0,
            'output': 'fixed_no_animation.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    # 应用封面叠加效果（使用修复后的方法）
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=8  # 生成8秒测试视频，确保能看到完整效果
                    ).overwrite_output()
                    
                    logger.info("开始执行FFmpeg命令...")
                    
                    # 显示FFmpeg命令（简化版）
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
                    
                    # 执行
                    ffmpeg.run(out, quiet=True)  # 静默执行，减少输出
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功! 文件大小: {file_size} bytes")
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
        
        logger.info(f"\n=== 修复后测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count == len(test_configs):
            logger.info("🎉 所有修复后的淡入淡出效果测试通过!")
            return True
        else:
            logger.warning(f"⚠️ 部分测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False

def test_direct_ffmpeg_fade():
    """直接测试FFmpeg fade滤镜语法"""
    
    logger.info("\n🔄 直接测试FFmpeg fade滤镜语法...")
    
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    if not Path(video_path).exists() or not Path(cover_path).exists():
        logger.error("测试文件不存在")
        return False
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            rounded_cover_path = temp_path / "rounded_cover_direct.png"
            
            # 创建圆角封面
            success = VideoCompositionService._create_rounded_cover_image(
                cover_path, cover_width, int(cover_width * 0.06), str(rounded_cover_path)
            )
            
            if not success:
                logger.error("圆角封面创建失败")
                return False
            
            # 直接测试FFmpeg fade语法
            video_input = ffmpeg.input(video_path)
            cover_input = ffmpeg.input(str(rounded_cover_path))
            
            # 测试1: 淡入效果
            logger.info("测试1: 直接fade淡入效果")
            cover_fade_in = cover_input.filter('fade', t='in', st=0, d=1.5)
            output1 = video_input.overlay(
                cover_fade_in,
                x='(main_w-overlay_w)/2',
                y='(main_h-overlay_h)/2',
                enable='between(t,0,5)'
            )
            
            out1 = ffmpeg.output(
                output1,
                'direct_fade_in.mp4',
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p',
                t=6
            ).overwrite_output()
            
            ffmpeg.run(out1, quiet=True)
            
            # 测试2: 淡出效果
            logger.info("测试2: 直接fade淡出效果")
            cover_fade_out = cover_input.filter('fade', t='out', st=3.5, d=1.5)
            output2 = video_input.overlay(
                cover_fade_out,
                x='(main_w-overlay_w)/2',
                y='(main_h-overlay_h)/2',
                enable='between(t,0,5)'
            )
            
            out2 = ffmpeg.output(
                output2,
                'direct_fade_out.mp4',
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p',
                t=6
            ).overwrite_output()
            
            ffmpeg.run(out2, quiet=True)
            
            # 测试3: 淡入淡出效果
            logger.info("测试3: 直接fade淡入淡出效果")
            cover_fade_both = cover_input.filter('fade', t='in', st=0, d=1.5).filter('fade', t='out', st=4.5, d=1.5)
            output3 = video_input.overlay(
                cover_fade_both,
                x='(main_w-overlay_w)/2',
                y='(main_h-overlay_h)/2',
                enable='between(t,0,6)'
            )
            
            out3 = ffmpeg.output(
                output3,
                'direct_fade_both.mp4',
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p',
                t=7
            ).overwrite_output()
            
            ffmpeg.run(out3, quiet=True)
            
            # 检查结果
            results = []
            for filename in ['direct_fade_in.mp4', 'direct_fade_out.mp4', 'direct_fade_both.mp4']:
                if Path(filename).exists():
                    file_size = Path(filename).stat().st_size
                    logger.info(f"✅ {filename} 生成成功, 大小: {file_size} bytes")
                    results.append(True)
                else:
                    logger.error(f"❌ {filename} 生成失败")
                    results.append(False)
            
            return all(results)
            
    except Exception as e:
        logger.error(f"❌ 直接测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始修复后的淡入淡出效果测试")
    
    # 测试1: 使用修复后的方法
    success1 = test_fixed_fade_effects()
    
    # 测试2: 直接测试FFmpeg语法
    success2 = test_direct_ffmpeg_fade()
    
    if success1 and success2:
        logger.info("\n📋 修复后测试结果文件:")
        logger.info("=== 使用修复后方法 ===")
        logger.info("- fixed_fade_in.mp4 (修复后淡入效果)")
        logger.info("- fixed_fade_out.mp4 (修复后淡出效果)")
        logger.info("- fixed_fade_in_out.mp4 (修复后淡入淡出效果)")
        logger.info("- fixed_no_animation.mp4 (修复后无动画效果)")
        
        logger.info("\n=== 直接FFmpeg语法测试 ===")
        logger.info("- direct_fade_in.mp4 (直接淡入效果)")
        logger.info("- direct_fade_out.mp4 (直接淡出效果)")
        logger.info("- direct_fade_both.mp4 (直接淡入淡出效果)")
        
        logger.info("\n🎬 如何验证修复效果:")
        logger.info("1. 播放 fixed_* 文件，观察是否有平滑的淡入淡出效果")
        logger.info("2. 播放 direct_* 文件，对比效果是否一致")
        logger.info("3. 如果修复成功，应该能看到明显的透明度变化动画")
        
        logger.info("\n🎉 所有修复测试完成!")
    else:
        logger.error("\n❌ 修复测试失败")
        if not success1:
            logger.error("- 修复后方法测试失败")
        if not success2:
            logger.error("- 直接FFmpeg语法测试失败")
        sys.exit(1)
