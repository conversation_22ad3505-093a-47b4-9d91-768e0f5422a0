#!/usr/bin/env python
"""
修复数据库中的端点地址
"""

import sqlite3
from pathlib import Path

DATABASE_PATH = "D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"

def fix_endpoints():
    """修复数据库中的端点地址"""
    print("🔧 修复数据库中的端点地址...")
    
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 检查当前的端点地址
        cursor.execute("SELECT tts_endpoint, llm_base_url FROM settings LIMIT 1;")
        current = cursor.fetchone()
        
        if current:
            tts_endpoint, llm_base_url = current
            print(f"当前TTS端点: {tts_endpoint}")
            print(f"当前LLM端点: {llm_base_url}")
            
            # 更新正确的端点地址
            cursor.execute("""
                UPDATE settings 
                SET tts_endpoint = ?, 
                    llm_base_url = ?,
                    updated_at = datetime('now')
                WHERE id = (SELECT id FROM settings LIMIT 1)
            """, (
                "https://api.coze.cn/v1/workflow/run",
                "https://yunwu.ai/v1/chat/completions"
            ))
            
            conn.commit()
            
            # 验证更新
            cursor.execute("SELECT tts_endpoint, llm_base_url FROM settings LIMIT 1;")
            updated = cursor.fetchone()
            
            if updated:
                new_tts_endpoint, new_llm_base_url = updated
                print(f"\n✅ 更新后TTS端点: {new_tts_endpoint}")
                print(f"✅ 更新后LLM端点: {new_llm_base_url}")
                
                if (new_tts_endpoint == "https://api.coze.cn/v1/workflow/run" and 
                    new_llm_base_url == "https://yunwu.ai/v1/chat/completions"):
                    print("✅ 端点地址修复成功！")
                else:
                    print("❌ 端点地址修复失败")
            else:
                print("❌ 无法验证更新结果")
        else:
            print("❌ 数据库中没有设置记录")
            
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
    finally:
        if conn:
            conn.close()

def main():
    print("=" * 60)
    print("🔧 修复数据库端点地址工具")
    print("=" * 60)
    
    fix_endpoints()
    
    print("\n" + "=" * 60)
    print("✅ 修复完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
