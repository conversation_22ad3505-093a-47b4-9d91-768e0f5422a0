@echo off
echo ===============================================
echo 封面模板功能完整测试脚本
echo ===============================================

echo.
echo 步骤 1: 初始化数据库模板数据...
python init_template_data.py
if %ERRORLEVEL% NEQ 0 (
    echo 数据库初始化失败！
    pause
    exit /b 1
)

echo.
echo 步骤 2: 启动后端服务...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend"
start "Backend Server" python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

echo 等待后端服务启动...
timeout /t 10

echo.
echo 步骤 3: 测试后端API...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator"
python debug_cover_templates_api.py

echo.
echo 步骤 4: 启动前端开发服务器...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\frontend"
start "Frontend Server" npm run dev

echo.
echo 等待前端服务启动...
timeout /t 15

echo.
echo ===============================================
echo 所有服务已启动！请访问以下地址进行测试：
echo.
echo 1. 封面模板管理页面:
echo    http://localhost:3000/covers
echo.
echo 2. API调试工具:
echo    http://localhost:3000/api-test.html
echo.
echo 3. 后端API文档:
echo    http://localhost:8000/docs
echo.
echo 4. 直接API测试:
echo    http://localhost:8000/api/cover-templates
echo.
echo ===============================================
echo 测试建议：
echo 1. 先打开API调试工具测试数据获取
echo 2. 然后访问封面模板管理页面查看界面
echo 3. 尝试创建、编辑、删除模板
echo 4. 检查控制台是否有错误信息
echo ===============================================

pause
