/**
 * 资源管理状态
 * 管理背景音乐、视频素材、提示词、账户和封面模板
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import {
  ResourceState,
  BackgroundMusic,
  VideoMaterial,
  Prompt,
  ResourceAccount,
  CoverTemplate
} from '../types/store'
import DirectHttpClient from '../lib/api/directHttpClient'

// 默认资源数据
const defaultBackgroundMusic: BackgroundMusic[] = [
  {
    id: 'bg-music-1',
    name: '舒缓钢琴',
    filePath: '/assets/music/piano-calm.mp3',
    duration: 180,
    category: '舒缓',
    tags: ['钢琴', '舒缓', '背景'],
    isBuiltIn: true
  },
  {
    id: 'bg-music-2',
    name: '悬疑氛围',
    filePath: '/assets/music/suspense-ambient.mp3',
    duration: 200,
    category: '悬疑',
    tags: ['悬疑', '氛围', '紧张'],
    isBuiltIn: true
  }
]

const defaultVideoMaterials: VideoMaterial[] = [
  {
    id: 'video-1',
    name: '城市夜景',
    filePath: '/assets/videos/city-night.mp4',
    duration: 30,
    category: '城市',
    resolution: '1920x1080',
    tags: ['城市', '夜景', '灯光'],
    isBuiltIn: true
  },
  {
    id: 'video-2',
    name: '自然风光',
    filePath: '/assets/videos/nature-landscape.mp4',
    duration: 45,
    category: '自然',
    resolution: '1920x1080',
    tags: ['自然', '风光', '宁静'],
    isBuiltIn: true
  }
]

const defaultPrompts: Prompt[] = [
  {
    id: 'prompt-1',
    name: 'Reddit故事改写',
    content: '请将以下Reddit故事改写为更适合视频内容的版本，保持故事的核心情节，但让语言更加生动和引人入胜：\n\n{originalStory}',
    category: '故事改写',
    variables: ['originalStory'],
    isBuiltIn: true
  },
  {
    id: 'prompt-2',
    name: '生成故事标题',
    content: '基于以下故事内容，生成3-5个吸引人的标题选项：\n\n{storyContent}\n\n要求：\n1. 标题要有吸引力\n2. 不超过50个字符\n3. 体现故事的核心冲突或情感',
    category: '标题生成',
    variables: ['storyContent'],
    isBuiltIn: true
  }
]

const defaultAccounts: ResourceAccount[] = [
  {
    id: 'account-1',
    name: '主账户',
    platform: 'youtube',
    isActive: true
  }
]

const defaultCoverTemplates: CoverTemplate[] = [
  {
    id: 'cover-1',
    name: '简约风格',
    previewPath: '/assets/covers/preview-simple.jpg',
    templatePath: '/assets/covers/template-simple.json',
    variables: ['title', 'subtitle'],
    isBuiltIn: true
  },
  {
    id: 'cover-2',
    name: '科技风格',
    previewPath: '/assets/covers/preview-tech.jpg',
    templatePath: '/assets/covers/template-tech.json',
    variables: ['title', 'subtitle', 'accent'],
    isBuiltIn: true
  }
]

export const useResourceStore = create<ResourceState>()(
  persist(
    (set, get) => ({
      // 初始状态
      backgroundMusic: defaultBackgroundMusic,
      videoMaterials: defaultVideoMaterials,
      prompts: defaultPrompts,
      accounts: defaultAccounts,
      coverTemplates: defaultCoverTemplates,
      
      // 选中状态
      selectedBackgroundMusic: null,
      selectedVideoMaterials: [],
      selectedPrompt: null,
      selectedAccount: null,
      selectedCoverTemplate: null,

      // 背景音乐操作
      addBackgroundMusic: (music: Omit<BackgroundMusic, 'id'>) => {
        const newMusic: BackgroundMusic = {
          ...music,
          id: `bg-music-${Date.now()}`,
          isBuiltIn: false
        }
        
        set((state: ResourceState) => ({
          backgroundMusic: [...state.backgroundMusic, newMusic]
        }))
        
        return newMusic.id
      },

      updateBackgroundMusic: (id: string, updates: Partial<BackgroundMusic>) => {
        set((state: ResourceState) => ({
          backgroundMusic: state.backgroundMusic.map(music =>
            music.id === id ? { ...music, ...updates } : music
          )
        }))
      },

      removeBackgroundMusic: (id: string) => {
        set((state: ResourceState) => {
          const music = state.backgroundMusic.find(m => m.id === id)
          if (music?.isBuiltIn) {
            console.warn('Cannot remove built-in background music')
            return state
          }

          return {
            backgroundMusic: state.backgroundMusic.filter(music => music.id !== id),
            selectedBackgroundMusic: state.selectedBackgroundMusic === id ? null : state.selectedBackgroundMusic
          }
        })
      },

      selectBackgroundMusic: (id: string | null) => {
        set({ selectedBackgroundMusic: id })
      },

      // 视频素材操作
      addVideoMaterial: (material: Omit<VideoMaterial, 'id'>) => {
        const newMaterial: VideoMaterial = {
          ...material,
          id: `video-${Date.now()}`,
          isBuiltIn: false
        }
        
        set((state: ResourceState) => ({
          videoMaterials: [...state.videoMaterials, newMaterial]
        }))

        return newMaterial.id
      },

      updateVideoMaterial: (id: string, updates: Partial<VideoMaterial>) => {
        set((state: ResourceState) => ({
          videoMaterials: state.videoMaterials.map(material =>
            material.id === id ? { ...material, ...updates } : material
          )
        }))
      },

      removeVideoMaterial: (id: string) => {
        set((state: ResourceState) => {
          const material = state.videoMaterials.find(m => m.id === id)
          if (material?.isBuiltIn) {
            console.warn('Cannot remove built-in video material')
            return state
          }

          return {
            videoMaterials: state.videoMaterials.filter(material => material.id !== id),
            selectedVideoMaterials: state.selectedVideoMaterials.filter(materialId => materialId !== id)
          }
        })
      },

      selectVideoMaterials: (ids: string[]) => {
        set({ selectedVideoMaterials: ids })
      },

      // 提示词操作
      addPrompt: (prompt: Omit<Prompt, 'id'>) => {
        const newPrompt: Prompt = {
          ...prompt,
          id: `prompt-${Date.now()}`,
          isBuiltIn: false
        }
        
        set((state: ResourceState) => ({
          prompts: [...state.prompts, newPrompt]
        }))

        return newPrompt.id
      },

      updatePrompt: (id: string, updates: Partial<Prompt>) => {
        set((state: ResourceState) => ({
          prompts: state.prompts.map(prompt =>
            prompt.id === id ? { ...prompt, ...updates } : prompt
          )
        }))
      },

      removePrompt: (id: string) => {
        set((state: ResourceState) => {
          const prompt = state.prompts.find(p => p.id === id)
          if (prompt?.isBuiltIn) {
            console.warn('Cannot remove built-in prompt')
            return state
          }

          return {
            prompts: state.prompts.filter(prompt => prompt.id !== id),
            selectedPrompt: state.selectedPrompt === id ? null : state.selectedPrompt
          }
        })
      },

      selectPrompt: (id: string | null) => {
        set({ selectedPrompt: id })
      },

      // 账户操作
      addAccount: (account: Omit<ResourceAccount, 'id'>) => {
        const newAccount: ResourceAccount = {
          ...account,
          id: `account-${Date.now()}`
        }

        set((state: ResourceState) => ({
          accounts: [...state.accounts, newAccount]
        }))

        return newAccount.id
      },

      updateAccount: (id: string, updates: Partial<ResourceAccount>) => {
        set((state: ResourceState) => ({
          accounts: state.accounts.map(account =>
            account.id === id ? { ...account, ...updates } : account
          )
        }))
      },

      removeAccount: (id: string) => {
        set((state: ResourceState) => ({
          accounts: state.accounts.filter(account => account.id !== id),
          selectedAccount: state.selectedAccount === id ? null : state.selectedAccount
        }))
      },

      selectAccount: (id: string | null) => {
        set({ selectedAccount: id })
      },

      // 封面模板操作
      addCoverTemplate: (template: Omit<CoverTemplate, 'id'>) => {
        const newTemplate: CoverTemplate = {
          ...template,
          id: `cover-${Date.now()}`,
          isBuiltIn: false
        }
        
        set((state: ResourceState) => ({
          coverTemplates: [...state.coverTemplates, newTemplate]
        }))

        return newTemplate.id
      },

      updateCoverTemplate: (id: string, updates: Partial<CoverTemplate>) => {
        set((state: ResourceState) => ({
          coverTemplates: state.coverTemplates.map(template =>
            template.id === id ? { ...template, ...updates } : template
          )
        }))
      },

      removeCoverTemplate: (id: string) => {
        set((state: ResourceState) => {
          const template = state.coverTemplates.find(t => t.id === id)
          if (template?.isBuiltIn) {
            console.warn('Cannot remove built-in cover template')
            return state
          }

          return {
            coverTemplates: state.coverTemplates.filter(template => template.id !== id),
            selectedCoverTemplate: state.selectedCoverTemplate === id ? null : state.selectedCoverTemplate
          }
        })
      },

      selectCoverTemplate: (id: string | null) => {
        set({ selectedCoverTemplate: id })
      },

      // 重置资源
      resetResources: () => {
        set({
          backgroundMusic: defaultBackgroundMusic,
          videoMaterials: defaultVideoMaterials,
          prompts: defaultPrompts,
          accounts: defaultAccounts,
          coverTemplates: defaultCoverTemplates,
          selectedBackgroundMusic: null,
          selectedVideoMaterials: [],
          selectedPrompt: null,
          selectedAccount: null,
          selectedCoverTemplate: null,
        })
      },
    }),
    {
      name: 'reddit-story-generator-resources',
      storage: createJSONStorage(() => localStorage),
      
      // 部分持久化：保存资源数据和选中状态
      partialize: (state: ResourceState) => ({
        backgroundMusic: state.backgroundMusic.filter((m: BackgroundMusic) => !m.isBuiltIn), // 只保存用户添加的资源
        videoMaterials: state.videoMaterials.filter((m: VideoMaterial) => !m.isBuiltIn),
        prompts: state.prompts.filter((p: Prompt) => !p.isBuiltIn),
        accounts: state.accounts,
        coverTemplates: state.coverTemplates.filter((t: CoverTemplate) => !t.isBuiltIn),
        selectedBackgroundMusic: state.selectedBackgroundMusic,
        selectedVideoMaterials: state.selectedVideoMaterials,
        selectedPrompt: state.selectedPrompt,
        selectedAccount: state.selectedAccount,
        selectedCoverTemplate: state.selectedCoverTemplate,
      }),
      
      version: 1,
      
      // 版本迁移
      migrate: (persistedState: any, version: number) => {
        if (version === 0) {
          return {
            ...persistedState,
            selectedVideoMaterials: persistedState.selectedVideoMaterials || [],
          }
        }
        return persistedState
      },
      
      // 合并持久化数据和默认数据
      merge: (persistedState: any, currentState: any) => {
        return {
          ...currentState,
          // 合并用户资源和内置资源
          backgroundMusic: [
            ...defaultBackgroundMusic,
            ...(persistedState.backgroundMusic || [])
          ],
          videoMaterials: [
            ...defaultVideoMaterials,
            ...(persistedState.videoMaterials || [])
          ],
          prompts: [
            ...defaultPrompts,
            ...(persistedState.prompts || [])
          ],
          accounts: persistedState.accounts || defaultAccounts,
          coverTemplates: [
            ...defaultCoverTemplates,
            ...(persistedState.coverTemplates || [])
          ],
          selectedBackgroundMusic: persistedState.selectedBackgroundMusic || null,
          selectedVideoMaterials: persistedState.selectedVideoMaterials || [],
          selectedPrompt: persistedState.selectedPrompt || null,
          selectedAccount: persistedState.selectedAccount || null,
          selectedCoverTemplate: persistedState.selectedCoverTemplate || null,
        }
      }
    }
  )
)

// 资源管理工具函数
export const resourceUtils = {
  // 根据类别筛选背景音乐
  filterMusicByCategory: (music: BackgroundMusic[], category: string) => {
    return music.filter(m => m.category === category)
  },

  // 根据标签搜索资源
  searchResourcesByTags: <T extends { tags: string[]; name: string }>(
    resources: T[], 
    searchTerm: string
  ) => {
    const term = searchTerm.toLowerCase()
    return resources.filter(resource => 
      resource.name.toLowerCase().includes(term) ||
      resource.tags.some(tag => tag.toLowerCase().includes(term))
    )
  },

  // 验证资源文件
  validateResourceFile: async (filePath: string, type: 'audio' | 'video'): Promise<{
    isValid: boolean
    duration?: number
    resolution?: string
    error?: string
  }> => {
    try {
      const resourceClient = new DirectHttpClient('/api/resources')
      const result = await resourceClient.post<{
        isValid: boolean
        duration?: number
        resolution?: string
        error?: string
      }>('/validate', { filePath, type })
      
      return result
    } catch (error) {
      return {
        isValid: false,
        error: 'Network error: ' + (error as Error).message
      }
    }
  },

  // 获取所有可用的类别
  getAllCategories: (resources: { category: string }[]) => {
    return Array.from(new Set(resources.map(r => r.category)))
  },

  // 获取所有可用的标签
  getAllTags: (resources: { tags: string[] }[]) => {
    const allTags = resources.flatMap(r => r.tags)
    return Array.from(new Set(allTags))
  }
}

export type { ResourceState, BackgroundMusic, VideoMaterial, Prompt, ResourceAccount, CoverTemplate }
