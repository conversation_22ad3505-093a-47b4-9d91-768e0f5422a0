#!/usr/bin/env python3
"""
测试封面模板保存功能的脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/cover-templates"

def test_template_save_workflow():
    print("=== 测试封面模板保存工作流程 ===\n")
    
    # 1. 创建一个新模板
    print("1. 创建新模板...")
    new_template = {
        "name": "保存测试模板",
        "category": "测试",
        "description": "用于测试保存功能的模板",
        "variables": [],
        "elements": [
            {
                "id": "test-text",
                "type": "text",
                "x": 100,
                "y": 100,
                "width": 200,
                "height": 50,
                "content": "初始文本",
                "fontSize": 24,
                "color": "#000000"
            }
        ],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        },
        "is_built_in": False,
        "width": 1920,
        "height": 1080,
        "format": "png"
    }
    
    try:
        create_response = requests.post(BASE_URL, json=new_template)
        if create_response.status_code == 200:
            create_data = create_response.json()
            print(f"创建成功: {json.dumps(create_data, indent=2, ensure_ascii=False)}")
            
            # 提取模板ID
            template_id = None
            if isinstance(create_data, dict) and 'data' in create_data:
                template_id = create_data['data'].get('id')
            elif isinstance(create_data, dict) and 'id' in create_data:
                template_id = create_data['id']
            
            if not template_id:
                print("无法获取模板ID，测试中止")
                return
            
            print(f"模板ID: {template_id}")
            
            # 2. 更新模板（模拟编辑器保存）
            print(f"\n2. 更新模板 (ID: {template_id})...")
            update_data = {
                "name": "更新后的保存测试模板",
                "elements": [
                    {
                        "id": "test-text",
                        "type": "text",
                        "x": 150,
                        "y": 150,
                        "width": 250,
                        "height": 60,
                        "content": "更新后的文本",
                        "fontSize": 28,
                        "color": "#ff0000"
                    },
                    {
                        "id": "new-shape",
                        "type": "shape",
                        "x": 50,
                        "y": 50,
                        "width": 100,
                        "height": 100,
                        "shapeType": "circle",
                        "backgroundColor": "#00ff00"
                    }
                ],
                "background": {
                    "type": "solid",
                    "value": "#ffffff"
                }
            }
            
            update_response = requests.put(f"{BASE_URL}/{template_id}", json=update_data)
            if update_response.status_code == 200:
                update_result = update_response.json()
                print(f"更新成功: {json.dumps(update_result, indent=2, ensure_ascii=False)}")
                
                # 检查回调数据格式
                print("\n=== 分析回调数据格式 ===")
                callback_data = update_result.get('data', update_result)
                print(f"回调数据类型: {type(callback_data)}")
                if isinstance(callback_data, dict):
                    print(f"回调数据字段: {list(callback_data.keys())}")
                    print(f"name字段: {callback_data.get('name')} (类型: {type(callback_data.get('name'))})")
                    print(f"id字段: {callback_data.get('id')} (类型: {type(callback_data.get('id'))})")
                
            else:
                print(f"更新失败: {update_response.text}")
                
            # 3. 获取模板验证
            print(f"\n3. 获取模板验证 (ID: {template_id})...")
            get_response = requests.get(f"{BASE_URL}/{template_id}")
            if get_response.status_code == 200:
                get_data = get_response.json()
                print(f"获取成功: {json.dumps(get_data, indent=2, ensure_ascii=False)}")
            else:
                print(f"获取失败: {get_response.text}")
                
        else:
            print(f"创建失败: {create_response.text}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_template_save_workflow()
