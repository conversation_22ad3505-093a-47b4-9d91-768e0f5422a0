@echo off
REM Reddit Story Video Generator - Complete EXE Build Script
REM This script builds both frontend and backend into a standalone executable

echo ============================================================
echo Reddit Story Video Generator - EXE Build Script
echo ============================================================
echo.

REM Check if we're in the correct directory
if not exist "backend\main.py" (
    echo ERROR: Please run this script from the project root directory
    echo Expected structure: backend\main.py, frontend\package.json
    pause
    exit /b 1
)

REM Step 1: Check dependencies
echo Step 1: Checking dependencies...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to PATH
    pause
    exit /b 1
)
echo ✅ Python is available

REM Check Node.js
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js/npm is not installed or not in PATH
    echo Please install Node.js 16+ and add it to PATH
    pause
    exit /b 1
)
echo ✅ Node.js/npm is available

REM Check if virtual environment is active
python -c "import sys; exit(0 if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) else 1)" >nul 2>&1
if errorlevel 1 (
    echo WARNING: No virtual environment detected
    echo It's recommended to use a virtual environment
    echo Continue anyway? (y/n)
    set /p continue=
    if not "!continue!"=="y" if not "!continue!"=="Y" (
        echo Build cancelled
        pause
        exit /b 1
    )
) else (
    echo ✅ Virtual environment is active
)

echo.

REM Step 2: Install Python dependencies
echo Step 2: Installing Python dependencies...
echo.

cd backend
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install Python dependencies
    pause
    exit /b 1
)
echo ✅ Python dependencies installed

cd ..

REM Step 3: Install Node.js dependencies
echo.
echo Step 3: Installing Node.js dependencies...
echo.

cd frontend
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install Node.js dependencies
    pause
    exit /b 1
)
echo ✅ Node.js dependencies installed

REM Step 4: Build frontend
echo.
echo Step 4: Building frontend for production...
echo.

call npm run build:production
if errorlevel 1 (
    echo ERROR: Frontend build failed
    pause
    exit /b 1
)
echo ✅ Frontend build completed

REM Step 5: Copy frontend to backend
echo.
echo Step 5: Copying frontend files to backend...
echo.

if exist "..\backend\frontend_dist" (
    rmdir /s /q "..\backend\frontend_dist"
)
mkdir "..\backend\frontend_dist"

xcopy /E /I /Y "out\*" "..\backend\frontend_dist\"
if errorlevel 1 (
    echo ERROR: Failed to copy frontend files
    pause
    exit /b 1
)
echo ✅ Frontend files copied to backend

cd ..

REM Step 6: Create tools directory and check FFmpeg
echo.
echo Step 6: Checking FFmpeg...
echo.

if not exist "backend\tools" (
    mkdir "backend\tools"
    echo Created backend\tools directory
)

if not exist "backend\tools\ffmpeg.exe" (
    echo WARNING: FFmpeg not found in backend\tools\ffmpeg.exe
    echo Please download FFmpeg and place ffmpeg.exe in backend\tools\
    echo You can continue without FFmpeg, but video processing will not work
    echo.
    echo Continue anyway? (y/n)
    set /p continue=
    if not "!continue!"=="y" if not "!continue!"=="Y" (
        echo Build cancelled
        pause
        exit /b 1
    )
) else (
    echo ✅ FFmpeg found
)

REM Step 7: Build EXE with PyInstaller
echo.
echo Step 7: Building EXE with PyInstaller...
echo.

cd backend

REM Clean previous builds
if exist "dist" (
    rmdir /s /q "dist"
)
if exist "build" (
    rmdir /s /q "build"
)

REM Run PyInstaller
pyinstaller build.spec --clean --noconfirm
if errorlevel 1 (
    echo ERROR: PyInstaller build failed
    echo Check the output above for details
    pause
    exit /b 1
)

echo ✅ EXE build completed

cd ..

REM Step 8: Verify build
echo.
echo Step 8: Verifying build...
echo.

if exist "backend\dist\RedditStoryVideoGenerator.exe" (
    echo ✅ EXE file created successfully
    
    REM Get file size
    for %%I in ("backend\dist\RedditStoryVideoGenerator.exe") do set size=%%~zI
    set /a sizeMB=%size%/1024/1024
    echo    File size: %sizeMB% MB
    echo    Location: backend\dist\RedditStoryVideoGenerator.exe
) else (
    echo ERROR: EXE file was not created
    echo Check the PyInstaller output for errors
    pause
    exit /b 1
)

echo.
echo ============================================================
echo Build completed successfully!
echo ============================================================
echo.
echo Your executable is ready at:
echo   backend\dist\RedditStoryVideoGenerator.exe
echo.
echo To test the EXE:
echo   1. Copy the EXE file to a test directory
echo   2. Run it and open http://localhost:8000 in your browser
echo.
echo To deploy:
echo   1. Copy RedditStoryVideoGenerator.exe to the target machine
echo   2. Ensure the target machine has required Visual C++ Redistributables
echo   3. Run the EXE and access the web interface
echo.
echo For detailed instructions, see EXE_DEPLOYMENT_GUIDE.md
echo.
pause
pip install pyinstaller
if errorlevel 1 (
    echo ERROR: Failed to install PyInstaller
    pause
    exit /b 1
)

echo.
echo [3/5] Building frontend...
cd ..\frontend

REM Install Node.js dependencies
echo Installing Node.js dependencies...
npm install
if errorlevel 1 (
    echo ERROR: Failed to install Node.js dependencies
    pause
    exit /b 1
)

REM Build and export frontend for production
echo Building frontend for production...
npm run build:production
if errorlevel 1 (
    echo ERROR: Failed to build frontend
    pause
    exit /b 1
)

REM Copy frontend build to backend
echo Copying frontend files to backend...
if exist "..\backend\frontend_dist" (
    rmdir /s /q "..\backend\frontend_dist"
)
mkdir "..\backend\frontend_dist"
xcopy /E /I /Y "out\*" "..\backend\frontend_dist\"
if errorlevel 1 (
    echo ERROR: Failed to copy frontend files
    pause
    exit /b 1
)

echo.
echo [4/5] Preparing backend for packaging...
cd ..\backend

REM Set environment to production
set ENVIRONMENT=production

REM Create tools directory and download FFmpeg if needed
if not exist "tools" mkdir tools
if not exist "tools\ffmpeg.exe" (
    echo.
    echo WARNING: FFmpeg not found in tools\ffmpeg.exe
    echo Please download FFmpeg and place ffmpeg.exe in backend\tools\
    echo You can download it from: https://ffmpeg.org/download.html
    echo.
    echo The build will continue, but video generation will not work without FFmpeg
    pause
)

echo.
echo [5/5] Building executable with PyInstaller...
pyinstaller build.spec --clean --noconfirm
if errorlevel 1 (
    echo ERROR: Failed to build executable
    pause
    exit /b 1
)

echo.
echo ================================================
echo Build completed successfully!
echo ================================================
echo.
echo The executable has been created in: backend\dist\
echo.
echo To run the application:
echo 1. Navigate to backend\dist\
echo 2. Run RedditStoryVideoGenerator.exe
echo 3. Open your browser to http://localhost:8000
echo.
echo Note: Make sure to configure your API keys in the .env.production file
echo before distributing the executable.
echo.
pause
