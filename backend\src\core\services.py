"""
依赖注入 - 管理全局服务实例
确保视频生成服务在整个应用生命周期中使用同一个实例
"""

from typing import Optional
from sqlalchemy.orm import sessionmaker
from ..services.video_generation_service import VideoGenerationService

# 全局服务实例
_video_generation_service: Optional[VideoGenerationService] = None

def get_video_generation_service(session_maker: Optional[sessionmaker] = None) -> VideoGenerationService:
    """
    获取视频生成服务的全局实例
    确保整个应用使用同一个实例，包括任务队列
    """
    global _video_generation_service
    
    if _video_generation_service is None:
        if session_maker is None:
            from .database import get_session_maker
            session_maker = get_session_maker()
        
        _video_generation_service = VideoGenerationService(session_maker)
    
    return _video_generation_service

def set_video_generation_service(service: VideoGenerationService):
    """设置视频生成服务实例（用于启动时初始化）"""
    global _video_generation_service
    _video_generation_service = service

def reset_video_generation_service():
    """重置服务实例（用于测试或重启）"""
    global _video_generation_service
    _video_generation_service = None
