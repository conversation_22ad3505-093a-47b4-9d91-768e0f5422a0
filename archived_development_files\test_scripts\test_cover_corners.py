#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
封面圆角测试脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 切换到backend目录
backend_dir = Path(__file__).parent / "backend"
os.chdir(backend_dir)
sys.path.insert(0, str(backend_dir))

from src.database.database import SessionLocal
from src.database.models import Account
from src.services.cover_screenshot_service import cover_screenshot_service

async def test_cover_corners():
    """测试封面圆角效果"""
    print("🔍 开始测试封面圆角效果...")
    
    try:
        # 获取数据库会话
        with SessionLocal() as db:
            # 查找测试账号
            account = db.query(Account).first()
            
            if not account:
                print("❌ 未找到测试账号")
                return False
            
            print(f"✅ 使用账号: {account.username}")
            
            # 测试参数
            template_id = "1b88fddc-fb2c-4e8d-96e4-c1e2a74f7227"  # 当前打开的模板
            title = "测试封面圆角效果，这是第一句话。这是第二句话，用来测试标题分割。"
            output_path = "test_corners.png"
            
            print(f"📝 模板ID: {template_id}")
            print(f"📝 标题: {title}")
            print(f"📝 输出: {output_path}")
            
            # 生成封面
            success = await cover_screenshot_service.generate_cover_screenshot(
                template_id=template_id,
                account=account,
                title=title,
                output_path=output_path,
                db=db,
                additional_variables={'timestamp': '2小时前', 'subreddit': 'r/stories'}
            )
            
            if success:
                print(f"✅ 封面生成成功！请检查 {output_path} 的圆角效果")
                return True
            else:
                print("❌ 封面生成失败")
                return False
                
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_cover_corners())
