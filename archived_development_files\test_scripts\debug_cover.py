#!/usr/bin/env python3
"""
封面问题诊断脚本
"""

import sys
import os
from pathlib import Path
import ffmpeg
from loguru import logger
from PIL import Image

def diagnose_cover_file(cover_path: str):
    """诊断封面文件"""
    logger.info(f"\n=== 诊断封面文件: {cover_path} ===")
    
    if not Path(cover_path).exists():
        logger.error("❌ 封面文件不存在")
        return False
    
    # 文件基本信息
    file_size = Path(cover_path).stat().st_size
    logger.info(f"文件大小: {file_size} bytes")
    
    # 尝试用PIL打开
    try:
        with Image.open(cover_path) as img:
            logger.info(f"PIL读取成功: {img.format}, {img.mode}, {img.size}")
            
            # 检查是否有透明度
            if img.mode in ('RGBA', 'LA') or 'transparency' in img.info:
                logger.warning("⚠️ 图片包含透明度信息，可能导致显示问题")
                
                # 转换为RGB
                if img.mode != 'RGB':
                    rgb_img = img.convert('RGB')
                    test_path = cover_path.replace('.', '_rgb.')
                    rgb_img.save(test_path)
                    logger.info(f"已转换为RGB格式: {test_path}")
                    return test_path
            
    except Exception as e:
        logger.error(f"❌ PIL无法读取图片: {e}")
        return False
    
    # 尝试用FFmpeg probe
    try:
        probe = ffmpeg.probe(cover_path)
        for stream in probe['streams']:
            if stream['codec_type'] == 'video':
                logger.info(f"FFmpeg读取成功: {stream['codec_name']}, {stream['width']}x{stream['height']}")
                
                # 检查像素格式
                pix_fmt = stream.get('pix_fmt', 'unknown')
                logger.info(f"像素格式: {pix_fmt}")
                
                if pix_fmt in ['rgba', 'yuva420p', 'yuva444p']:
                    logger.warning("⚠️ 图片使用了带透明度的像素格式")
                
                break
    except Exception as e:
        logger.error(f"❌ FFmpeg无法读取图片: {e}")
        return False
    
    return cover_path

def test_cover_formats(video_path: str, cover_path: str):
    """测试不同的封面格式处理方式"""
    logger.info(f"\n=== 测试不同封面处理方式 ===")
    
    # 获取视频信息
    probe = ffmpeg.probe(video_path)
    video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
    width = int(video_info['width'])
    height = int(video_info['height'])
    
    cover_width = int(width * 0.8)
    
    tests = [
        {
            'name': '直接使用原图',
            'process': lambda cover_input: cover_input.filter('scale', cover_width, -1)
        },
        {
            'name': '强制RGB格式',
            'process': lambda cover_input: cover_input.filter('format', 'rgb24').filter('scale', cover_width, -1)
        },
        {
            'name': '强制YUV格式',
            'process': lambda cover_input: cover_input.filter('format', 'yuv420p').filter('scale', cover_width, -1)
        },
        {
            'name': '添加背景色',
            'process': lambda cover_input: cover_input.filter('scale', cover_width, -1).filter('format', 'rgba').filter('colorchannelmixer', aa=1.0)
        }
    ]
    
    for i, test in enumerate(tests):
        try:
            logger.info(f"\n--- 测试 {i+1}: {test['name']} ---")
            
            video_input = ffmpeg.input(video_path)
            cover_input = ffmpeg.input(cover_path)
            
            # 处理封面
            cover_processed = test['process'](cover_input)
            
            # 叠加
            output = video_input.overlay(
                cover_processed,
                x='(main_w-overlay_w)/2',
                y='(main_h-overlay_h)/2',
                enable='between(t,0,3)'
            )
            
            # 输出
            test_output = f"debug_test_{i+1}_{test['name'].replace(' ', '_')}.mp4"
            out = ffmpeg.output(
                output,
                test_output,
                vcodec='libx264',
                preset='ultrafast',
                pix_fmt='yuv420p',
                t=5  # 只生成5秒
            ).overwrite_output()
            
            # 显示命令
            cmd = ffmpeg.compile(out)
            logger.info(f"FFmpeg命令: {' '.join(cmd)}")
            
            # 执行
            ffmpeg.run(out, quiet=True)
            
            if Path(test_output).exists():
                logger.info(f"✅ 测试 {i+1} 成功: {test_output}")
            else:
                logger.error(f"❌ 测试 {i+1} 失败")
                
        except Exception as e:
            logger.error(f"❌ 测试 {i+1} 异常: {e}")

def test_simple_overlay(video_path: str, cover_path: str):
    """最简单的叠加测试"""
    logger.info(f"\n=== 最简单叠加测试 ===")
    
    try:
        # 不做任何处理，直接叠加
        video_input = ffmpeg.input(video_path)
        cover_input = ffmpeg.input(cover_path)
        
        output = video_input.overlay(cover_input, x=100, y=100, enable='between(t,0,3)')
        
        out = ffmpeg.output(
            output,
            'debug_simple.mp4',
            vcodec='libx264',
            preset='ultrafast',
            t=5
        ).overwrite_output()
        
        cmd = ffmpeg.compile(out)
        logger.info(f"简单测试命令: {' '.join(cmd)}")
        
        ffmpeg.run(out, quiet=True)
        
        if Path('debug_simple.mp4').exists():
            logger.info("✅ 简单测试成功: debug_simple.mp4")
        else:
            logger.error("❌ 简单测试失败")
            
    except Exception as e:
        logger.error(f"❌ 简单测试异常: {e}")

def create_test_cover():
    """创建一个测试封面图片"""
    logger.info("\n=== 创建测试封面 ===")
    
    try:
        # 创建一个简单的测试图片
        img = Image.new('RGB', (400, 300), color='red')
        
        # 添加一些文字
        try:
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(img)
            draw.text((50, 100), "TEST COVER", fill='white')
            draw.rectangle([20, 20, 380, 280], outline='white', width=5)
        except:
            pass
        
        test_cover_path = 'test_cover.jpg'
        img.save(test_cover_path, 'JPEG', quality=95)
        
        logger.info(f"✅ 测试封面已创建: {test_cover_path}")
        return test_cover_path
        
    except Exception as e:
        logger.error(f"❌ 创建测试封面失败: {e}")
        return None

def main():
    if len(sys.argv) != 3:
        print("用法: python debug_cover.py <视频文件> <封面文件>")
        print("示例: python debug_cover.py test.mp4 cover.jpg")
        sys.exit(1)
    
    video_path = sys.argv[1]
    cover_path = sys.argv[2]
    
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")
    
    logger.info("🔍 开始封面问题诊断...")
    
    # 检查文件
    if not Path(video_path).exists():
        logger.error(f"❌ 视频文件不存在: {video_path}")
        return
    
    # 诊断封面文件
    processed_cover = diagnose_cover_file(cover_path)
    if not processed_cover:
        logger.error("封面文件诊断失败，创建测试封面...")
        processed_cover = create_test_cover()
        if not processed_cover:
            return
    
    # 运行测试
    test_simple_overlay(video_path, processed_cover)
    test_cover_formats(video_path, processed_cover)
    
    logger.info("\n🎉 诊断完成! 请检查生成的测试文件:")
    logger.info("- debug_simple.mp4 (最简单测试)")
    logger.info("- debug_test_*.mp4 (各种格式测试)")

if __name__ == "__main__":
    main()
