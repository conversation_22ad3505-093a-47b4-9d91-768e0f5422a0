"""
视频生成相关的Pydantic Schema
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from datetime import datetime


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

    # 新增：分阶段处理状态
    AUDIO_PENDING = "audio_pending"       # 等待语音生成
    AUDIO_COMPLETED = "audio_completed"   # 语音生成完成，等待视频合成
    VIDEO_PENDING = "video_pending"       # 等待视频合成


class MaterialSelectionType(str, Enum):
    """素材选择类型"""
    RANDOM = "random"
    MANUAL = "manual"


class MusicSelectionType(str, Enum):
    """音乐选择类型"""
    RANDOM = "random"
    SPECIFIC = "specific"


class SubtitlePosition(str, Enum):
    """字幕位置"""
    TOP = "top"
    CENTER = "center"
    BOTTOM = "bottom"


class CoverPosition(str, Enum):
    """封面位置"""
    TOP = "top"
    CENTER = "center"
    BOTTOM = "bottom"
    CUSTOM = "custom"


class CoverAnimation(str, Enum):
    """封面动画效果"""
    NONE = "none"
    FADE_IN = "fade_in"
    FADE_OUT = "fade_out"
    FADE_IN_OUT = "fade_in_out"
    SLIDE_IN_LEFT = "slide_in_left"
    SLIDE_IN_RIGHT = "slide_in_right"
    SLIDE_IN_TOP = "slide_in_top"
    SLIDE_IN_BOTTOM = "slide_in_bottom"
    SLIDE_OUT_LEFT = "slide_out_left"
    SLIDE_OUT_RIGHT = "slide_out_right"
    SLIDE_OUT_TOP = "slide_out_top"
    SLIDE_OUT_BOTTOM = "slide_out_bottom"
    ZOOM_IN = "zoom_in"
    ZOOM_OUT = "zoom_out"
    ZOOM_IN_OUT = "zoom_in_out"


class TransitionType(str, Enum):
    """转场效果类型"""
    NONE = "none"
    FADE = "fade"
    DISSOLVE = "dissolve"
    SLIDE_LEFT = "slide_left"
    SLIDE_RIGHT = "slide_right"
    SLIDE_UP = "slide_up"
    SLIDE_DOWN = "slide_down"
    WIPE_LEFT = "wipe_left"
    WIPE_RIGHT = "wipe_right"
    WIPE_UP = "wipe_up"
    WIPE_DOWN = "wipe_down"
    CIRCLE_OPEN = "circle_open"
    CIRCLE_CLOSE = "circle_close"
    RADIAL = "radial"
    SMOOTH_LEFT = "smooth_left"
    SMOOTH_RIGHT = "smooth_right"
    SMOOTH_UP = "smooth_up"
    SMOOTH_DOWN = "smooth_down"


class VoiceSettings(BaseModel):
    """音色设置"""
    voice: str = Field(..., description="音色名称")
    speed: float = Field(default=1.0, ge=0.5, le=2.0, description="语速倍率")


class AudioSettings(BaseModel):
    """音频设置"""
    speech_volume: float = Field(default=1.0, ge=0.0, le=1.0, description="语音音量")
    background_music_volume: float = Field(default=0.15, ge=0.0, le=1.0, description="背景音乐音量")
    enable_background_music: bool = Field(default=True, description="是否启用背景音乐")


class CoverSettings(BaseModel):
    """封面设置"""
    position: CoverPosition = Field(default=CoverPosition.CENTER, description="封面位置")
    animation: CoverAnimation = Field(default=CoverAnimation.FADE_IN_OUT, description="封面动画效果")
    custom_x: Optional[str] = Field(default=None, description="自定义X位置表达式")
    custom_y: Optional[str] = Field(default=None, description="自定义Y位置表达式")
    animation_duration: float = Field(default=0.5, ge=0.1, le=2.0, description="动画持续时间（秒）")


class SubtitleSettings(BaseModel):
    """字幕设置"""
    font_family: str = Field(default="Arial", description="字体")
    font_size: int = Field(default=24, ge=12, le=48, description="字体大小")
    font_color: str = Field(default="#FFFFFF", description="字体颜色")
    position: SubtitlePosition = Field(default=SubtitlePosition.BOTTOM, description="字幕位置")
    words_per_screen: int = Field(default=1, ge=1, le=10, description="每屏单词数")
    stroke_thickness: int = Field(default=2, ge=0, le=10, description="描边厚度")
    stroke_color: str = Field(default="#000000", description="描边颜色")
    enabled: bool = Field(default=True, description="是否启用字幕")
    include_all_words: bool = Field(default=True, description="是否包含所有词语（包括第一句话）")


class TransitionSettings(BaseModel):
    """转场设置"""
    enabled: bool = Field(default=False, description="是否启用转场效果")
    transition_type: TransitionType = Field(default=TransitionType.FADE, description="转场类型")
    duration: float = Field(default=0.5, ge=0.1, le=2.0, description="转场时长（秒）")


class VideoSettings(BaseModel):
    """视频设置"""
    resolution: str = Field(default="1080x1920", description="分辨率")
    fps: int = Field(default=30, description="帧率")
    format: str = Field(default="mp4", description="格式")


class AccountConfig(BaseModel):
    """账号配置"""
    account_id: str = Field(..., description="账号ID")
    video_count: int = Field(..., ge=1, le=100, description="视频数量")


class VideoGenerationJobConfig(BaseModel):
    """视频生成作业配置"""
    video_material_group: str = Field(..., description="视频素材分组ID")
    material_selection: MaterialSelectionType = Field(..., description="素材选择方式")
    selected_materials: Optional[List[str]] = Field(default=None, description="手动选择的素材ID列表")
    
    prompt_group: str = Field(..., description="提示词分组ID")
    prompt_id: str = Field(..., description="选择的提示词ID")
    
    voice_settings: VoiceSettings = Field(..., description="音色设置")
    
    background_music_group: str = Field(..., description="背景音乐分组ID")
    music_selection: MusicSelectionType = Field(..., description="音乐选择方式")
    music_id: Optional[str] = Field(default=None, description="指定的音乐ID")
    
    audio_settings: AudioSettings = Field(default_factory=AudioSettings, description="音频设置")
    
    cover_template_id: str = Field(..., description="封面模板ID")
    cover_settings: CoverSettings = Field(default_factory=CoverSettings, description="封面设置")

    subtitle_config: SubtitleSettings = Field(default_factory=SubtitleSettings, description="字幕设置")
    video_settings: VideoSettings = Field(default_factory=VideoSettings, description="视频设置")
    transition_settings: TransitionSettings = Field(default_factory=TransitionSettings, description="转场设置")


class CreateVideoGenerationJobRequest(BaseModel):
    """创建视频生成作业请求"""
    name: str = Field(..., min_length=1, max_length=255, description="作业名称")
    description: Optional[str] = Field(default=None, description="作业描述")
    config: VideoGenerationJobConfig = Field(..., description="作业配置")
    account_configs: List[AccountConfig] = Field(..., description="账号配置列表")
    
    @validator('account_configs')
    def validate_account_configs(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要配置一个账号')
        return v


class WordTimestamp(BaseModel):
    """单词时间戳"""
    word: str = Field(..., description="单词")
    start: float = Field(..., description="开始时间(秒)")
    end: float = Field(..., description="结束时间(秒)")


class AudioAnalysis(BaseModel):
    """音频分析结果"""
    total_duration: float = Field(..., description="总时长(秒)")
    first_sentence_duration: float = Field(..., description="第一句话时长(秒)")
    word_timestamps: List[WordTimestamp] = Field(..., description="单词时间戳列表")


class VideoGenerationTaskResponse(BaseModel):
    """视频生成任务响应"""
    id: str
    job_id: str
    task_name: str
    account_id: str
    status: TaskStatus
    progress: float
    current_step: Optional[str] = None
    
    generated_story: Optional[str] = None
    first_sentence: Optional[str] = None
    custom_title: Optional[str] = None
    audio_file_path: Optional[str] = None
    subtitle_file_path: Optional[str] = None
    cover_image_path: Optional[str] = None
    final_video_path: Optional[str] = None
    
    used_materials: Optional[List[str]] = None
    used_music_id: Optional[str] = None
    
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[int] = None
    
    audio_analysis: Optional[AudioAnalysis] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    
    created_at: datetime
    updated_at: datetime


class VideoGenerationJobResponse(BaseModel):
    """视频生成作业响应"""
    id: str
    name: str
    description: Optional[str] = None
    config: Dict[str, Any]
    account_configs: List[Dict[str, Any]]
    
    status: TaskStatus
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    
    error_message: Optional[str] = None
    
    created_at: datetime
    updated_at: datetime
    
    # 可选包含任务列表
    tasks: Optional[List[VideoGenerationTaskResponse]] = None


class VideoGenerationJobListResponse(BaseModel):
    """视频生成作业列表响应"""
    jobs: List[VideoGenerationJobResponse]
    total: int
    page: int
    limit: int


class JobControlRequest(BaseModel):
    """作业控制请求"""
    action: str = Field(..., description="操作: start|pause|resume|cancel")
    
    @validator('action')
    def validate_action(cls, v):
        if v not in ['start', 'pause', 'resume', 'cancel']:
            raise ValueError('action must be one of: start, pause, resume, cancel')
        return v


class TaskControlRequest(BaseModel):
    """任务控制请求"""
    action: str = Field(..., description="操作: retry|cancel")
    
    @validator('action')
    def validate_action(cls, v):
        if v not in ['retry', 'cancel']:
            raise ValueError('action must be one of: retry, cancel')
        return v


class TaskLogResponse(BaseModel):
    """任务日志响应"""
    id: str
    task_id: str
    level: str
    step: Optional[str] = None
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime
    created_at: datetime


class JobProgressResponse(BaseModel):
    """作业进度响应"""
    job_id: str
    status: TaskStatus
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    running_tasks: int
    pending_tasks: int
    progress_percentage: float
    estimated_remaining_time: Optional[int] = None
    current_tasks: List[VideoGenerationTaskResponse]


class AvailableVoicesResponse(BaseModel):
    """可用音色响应"""
    voices: List[str] = Field(..., description="可用的音色列表")


class VideoPreviewRequest(BaseModel):
    """视频预览请求"""
    account_id: str = Field(..., description="账号ID")
    cover_template_id: str = Field(..., description="封面模板ID")
    sample_title: str = Field(..., description="示例标题")


class VideoPreviewResponse(BaseModel):
    """视频预览响应"""
    cover_preview_url: str = Field(..., description="封面预览图片URL")
    account_name: str = Field(..., description="账号名称")
    sample_title: str = Field(..., description="示例标题")


class BatchVideoGenerationJobConfig(BaseModel):
    """批量视频生成作业配置（基于文案列表）"""
    # 视频素材配置
    material_selection: str = Field(default="random", description="素材选择方式: random|manual")
    video_material_group: Optional[str] = Field(default=None, description="视频素材分组")
    selected_materials: Optional[List[str]] = Field(default=None, description="手动选择的素材ID列表")

    # 语音配置
    voice_settings: VoiceSettings = Field(default_factory=VoiceSettings, description="语音设置")

    # 音频配置
    audio_settings: AudioSettings = Field(default_factory=AudioSettings, description="音频设置")

    # 背景音乐配置
    music_selection: str = Field(default="random", description="音乐选择方式: random|specific")
    background_music_group: Optional[str] = Field(default=None, description="背景音乐分组")
    music_id: Optional[str] = Field(default=None, description="指定音乐ID")

    cover_template_id: str = Field(..., description="封面模板ID")
    cover_settings: CoverSettings = Field(default_factory=CoverSettings, description="封面设置")

    subtitle_config: SubtitleSettings = Field(default_factory=SubtitleSettings, description="字幕设置")
    video_config: VideoSettings = Field(default_factory=VideoSettings, description="视频设置")
    transition_settings: TransitionSettings = Field(default_factory=TransitionSettings, description="转场设置")


class CreateBatchVideoGenerationJobRequest(BaseModel):
    """创建批量视频生成作业请求（基于文案列表）"""
    name: str = Field(..., min_length=1, max_length=255, description="作业名称")
    description: Optional[str] = Field(default=None, description="作业描述")
    config: BatchVideoGenerationJobConfig = Field(..., description="作业配置")
    account_ids: List[str] = Field(..., description="账号ID列表")
    stories: List[str] = Field(..., description="文案列表")
    titles: Optional[List[str]] = Field(default=None, description="标题列表（可选）")

    @validator('account_ids')
    def validate_account_ids(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要选择一个账号')
        return v

    @validator('stories')
    def validate_stories(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要提供一个文案')
        # 过滤空文案
        filtered_stories = [story.strip() for story in v if story.strip()]
        if not filtered_stories:
            raise ValueError('没有有效的文案内容')
        return filtered_stories


class ExcelUploadResponse(BaseModel):
    """Excel文件上传响应"""
    success: bool
    stories: List[str] = Field(default_factory=list, description="解析出的文案列表")
    titles: List[str] = Field(default_factory=list, description="解析出的标题列表")
    total_count: int = Field(default=0, description="文案总数")
    message: Optional[str] = Field(default=None, description="响应消息")
    error: Optional[str] = Field(default=None, description="错误信息")
