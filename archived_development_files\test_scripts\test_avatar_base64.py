#!/usr/bin/env python3
"""
简化测试：只测试头像Base64转换
"""

import sys
import os
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

def test_avatar_base64():
    """测试头像Base64转换"""
    try:
        from src.core.database import get_db_session
        from src.models.accounts import Account
        from src.services.cover_screenshot_service import CoverScreenshotService
        
        print("=== 测试头像Base64转换 ===")
        
        db = get_db_session()
        
        # 获取第一个账号
        account = db.query(Account).first()
        if not account:
            print("未找到账号")
            return
        
        print(f"账号: {account.name}")
        print(f"头像文件路径: {account.avatar_file_path}")
        
        # 测试头像处理
        service = CoverScreenshotService()
        avatar_data = service._get_avatar_path(account)
        
        print(f"处理结果类型: {'Base64' if avatar_data.startswith('data:') else 'URL'}")
        print(f"处理结果长度: {len(avatar_data)} 字符")
        print(f"前100个字符: {avatar_data[:100]}...")
        
        # 保存为HTML测试文件
        test_html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>头像测试</title>
</head>
<body>
    <h1>头像测试</h1>
    <p>账号: {account.name}</p>
    <img src="{avatar_data}" style="width: 100px; height: 100px; border-radius: 50%;" />
</body>
</html>
"""
        
        with open("avatar_test.html", "w", encoding="utf-8") as f:
            f.write(test_html)
        
        print(f"测试HTML文件已生成: avatar_test.html")
        print("可以在浏览器中打开查看头像效果")
        
        db.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_avatar_base64()
