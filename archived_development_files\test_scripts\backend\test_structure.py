#!/usr/bin/env python3
"""
前端数据结构验证脚本
验证后端API返回的数据结构与前端Zustand store期望的结构是否一致
"""

import requests
import json
from typing import Dict, Any, List

BASE_URL = "http://localhost:8000/api/v1"

# 前端期望的数据结构（基于Zustand store定义）
EXPECTED_STRUCTURES = {
    "BackgroundMusic": {
        "id": str,
        "name": str,
        "filePath": str,
        "duration": (int, float),
        "category": str,
        "tags": list,
        "isBuiltIn": bool,
        "metadata": {
            "fileSize": (int, type(None)),
            "format": (str, type(None)),
            "bitrate": (int, type(None)),
            "sampleRate": (int, type(None))
        },
        "createdAt": (str, type(None)),
        "updatedAt": (str, type(None))
    },
    "VideoMaterial": {
        "id": str,
        "name": str,
        "filePath": str,
        "duration": (int, float),
        "category": str,
        "resolution": str,
        "tags": list,
        "isBuiltIn": bool,
        "metadata": {
            "fileSize": (int, type(None)),
            "format": (str, type(None)),
            "frameRate": (int, float, type(None)),
            "bitrate": (int, type(None)),
            "thumbnailPath": (str, type(None))
        },
        "createdAt": (str, type(None)),
        "updatedAt": (str, type(None))
    },
    "Prompt": {
        "id": str,
        "name": str,
        "content": str,
        "category": str,
        "variables": list,
        "isBuiltIn": bool,
        "metadata": {
            "description": (str, type(None)),
            "exampleOutput": (str, type(None)),
            "usageCount": int
        },
        "createdAt": (str, type(None)),
        "updatedAt": (str, type(None))
    },
    "Account": {
        "id": str,
        "name": str,
        "platform": str,
        "isActive": bool,
        "config": dict,
        "metadata": {
            "videoCount": int,
            "lastUsedAt": (str, type(None))
        },
        "createdAt": (str, type(None)),
        "updatedAt": (str, type(None))
    },
    "Settings": {
        "tts": {
            "provider": str,
            "voice": str,
            "speed": (int, float),
            "apiKey": (str, type(None)),
            "endpoint": (str, type(None)),
            "model": (str, type(None)),
            "pitch": (int, float, type(None)),
            "volume": (int, float, type(None))
        },
        "llm": {
            "provider": str,
            "model": str,
            "apiKey": (str, type(None)),
            "endpoint": (str, type(None)),
            "temperature": (int, float),
            "maxTokens": int,
            "systemPrompt": (str, type(None))
        },
        "general": {
            "theme": str,
            "language": str,
            "autoSave": bool,
            "showTips": bool,
            "outputDirectory": str
        }
    }
}

class DataStructureValidator:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()

    def validate_structure(self, data: Any, expected: Dict, path: str = "") -> List[str]:
        """递归验证数据结构"""
        errors = []
        
        if isinstance(expected, dict):
            if not isinstance(data, dict):
                errors.append(f"{path}: Expected dict, got {type(data).__name__}")
                return errors
            
            for key, expected_type in expected.items():
                if key not in data:
                    errors.append(f"{path}.{key}: Missing required field")
                    continue
                
                field_path = f"{path}.{key}" if path else key
                errors.extend(self.validate_structure(data[key], expected_type, field_path))
        
        elif isinstance(expected, tuple):
            # 允许多种类型之一
            if not any(isinstance(data, t) for t in expected):
                type_names = " or ".join(t.__name__ for t in expected)
                errors.append(f"{path}: Expected {type_names}, got {type(data).__name__}")
        
        else:
            # 单一类型
            if not isinstance(data, expected):
                errors.append(f"{path}: Expected {expected.__name__}, got {type(data).__name__}")
        
        return errors

    def test_settings_structure(self) -> Dict[str, Any]:
        """测试设置数据结构"""
        try:
            response = self.session.get(f"{self.base_url}/settings")
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"API returned {response.status_code}",
                    "structure": "Settings"
                }
            
            data = response.json()
            settings_data = data.get("data", {})
            
            errors = self.validate_structure(settings_data, EXPECTED_STRUCTURES["Settings"])
            
            return {
                "success": len(errors) == 0,
                "errors": errors,
                "structure": "Settings",
                "sample_data": settings_data
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "structure": "Settings"
            }

    def test_background_music_structure(self) -> Dict[str, Any]:
        """测试背景音乐数据结构"""
        try:
            # 先创建一个测试音乐
            music_data = {
                "name": "Structure Test Music",
                "file_path": "/test/structure_test.mp3",
                "duration": 180.0,
                "category": "test",
                "tags": ["structure", "test"],
                "is_built_in": False,
                "file_size": 2048000,
                "format": "mp3",
                "bitrate": 320,
                "sample_rate": 44100
            }
            
            create_response = self.session.post(f"{self.base_url}/resources/background-music/", json=music_data)
            if create_response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Failed to create test music: {create_response.status_code}",
                    "structure": "BackgroundMusic"
                }
            
            created_data = create_response.json()
            music_response_data = created_data.get("data", {})
            
            errors = self.validate_structure(music_response_data, EXPECTED_STRUCTURES["BackgroundMusic"])
            
            return {
                "success": len(errors) == 0,
                "errors": errors,
                "structure": "BackgroundMusic",
                "sample_data": music_response_data
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "structure": "BackgroundMusic"
            }

    def test_video_material_structure(self) -> Dict[str, Any]:
        """测试视频素材数据结构"""
        try:
            video_data = {
                "name": "Structure Test Video",
                "file_path": "/test/structure_test.mp4",
                "duration": 300.0,
                "category": "test",
                "resolution": "1920x1080",
                "tags": ["structure", "test"],
                "is_built_in": False,
                "file_size": 50000000,
                "format": "mp4",
                "frame_rate": 30.0,
                "bitrate": 5000,
                "thumbnail_path": "/test/thumb.jpg"
            }
            
            create_response = self.session.post(f"{self.base_url}/resources/video-materials/", json=video_data)
            if create_response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Failed to create test video: {create_response.status_code}",
                    "structure": "VideoMaterial"
                }
            
            created_data = create_response.json()
            video_response_data = created_data.get("data", {})
            
            errors = self.validate_structure(video_response_data, EXPECTED_STRUCTURES["VideoMaterial"])
            
            return {
                "success": len(errors) == 0,
                "errors": errors,
                "structure": "VideoMaterial",
                "sample_data": video_response_data
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "structure": "VideoMaterial"
            }

    def run_all_validations(self) -> Dict[str, Any]:
        """运行所有数据结构验证"""
        print("🔍 Starting Frontend-Backend Data Structure Validation...\n")
        
        validations = [
            ("Settings", self.test_settings_structure),
            ("BackgroundMusic", self.test_background_music_structure),
            ("VideoMaterial", self.test_video_material_structure)
        ]
        
        results = []
        total_passed = 0
        total_failed = 0
        
        for name, test_func in validations:
            print(f"🧪 Testing {name} structure...")
            result = test_func()
            results.append(result)
            
            if result["success"]:
                print(f"   ✅ {name}: Structure matches frontend expectations")
                total_passed += 1
            else:
                print(f"   ❌ {name}: Structure validation failed")
                if "errors" in result:
                    for error in result["errors"]:
                        print(f"      - {error}")
                if "error" in result:
                    print(f"      - {result['error']}")
                total_failed += 1
            print()
        
        print("="*60)
        print(f"📊 Data Structure Validation Summary:")
        print(f"   Total Structures: {total_passed + total_failed}")
        print(f"   ✅ Valid: {total_passed}")
        print(f"   ❌ Invalid: {total_failed}")
        
        if total_failed == 0:
            print("\n🎉 All data structures match frontend expectations!")
            print("Frontend-backend data consistency is confirmed.")
        else:
            print(f"\n⚠️ {total_failed} structure(s) need adjustment.")
            print("Please check the error messages above.")
        
        return {
            "success": total_failed == 0,
            "total": total_passed + total_failed,
            "passed": total_passed,
            "failed": total_failed,
            "results": results
        }

def main():
    """主函数"""
    validator = DataStructureValidator()
    results = validator.run_all_validations()
    
    return 0 if results["success"] else 1

if __name__ == "__main__":
    exit(main())
