/**
 * 音乐文件状态管理
 * 管理背景音乐文件的上传、存储、播放等功能
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

export interface MusicFile {
  id: string
  name: string
  duration: number | string
  size: string
  format: string
  filePath: string
  url?: string
  category?: string
  tags?: string[]
  isBuiltIn?: boolean
  metadata?: {
    fileSize?: number
    bitrate?: number
    sampleRate?: number
  }
  createdAt: string
  updatedAt?: string
}

interface MusicState {
  // 状态
  musicFiles: MusicFile[]
  isLoading: boolean
  error: string | null
  currentPage: number
  pageSize: number
  total: number

  // 操作方法
  setMusicFiles: (files: MusicFile[]) => void
  addMusicFile: (file: MusicFile) => void
  addMusicFiles: (files: MusicFile[]) => void
  removeMusicFile: (id: string) => void
  updateMusicFile: (id: string, updates: Partial<MusicFile>) => void
  clearAllMusic: () => void
  
  // 查询方法
  getMusicById: (id: string) => MusicFile | undefined
  getMusicByFormat: (format: string) => MusicFile[]
  getMusicByCategory: (category: string) => MusicFile[]
  getTotalCount: () => number
  getTotalSize: () => string
  getFormatStats: () => Record<string, number>
  
  // 分页方法
  setPage: (page: number) => void
  setPageSize: (size: number) => void
  setTotal: (total: number) => void
  
  // 工具方法
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// 格式化文件大小的工具函数
const formatFileSize = (totalBytes: number): string => {
  if (totalBytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(totalBytes) / Math.log(k))
  return parseFloat((totalBytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 计算总文件大小（字节）
const calculateTotalBytes = (files: MusicFile[]): number => {
  return files.reduce((total, file) => {
    // 从size字符串中提取数字和单位
    const sizeStr = file.size
    const match = sizeStr.match(/^([\d.]+)\s*([KMGT]?)B?$/i)
    if (!match) return total
    
    const value = parseFloat(match[1])
    const unit = match[2].toUpperCase()
    
    const multipliers = { '': 1, 'K': 1024, 'M': 1024 ** 2, 'G': 1024 ** 3, 'T': 1024 ** 4 }
    const multiplier = multipliers[unit as keyof typeof multipliers] || 1
    
    return total + (value * multiplier)
  }, 0)
}

export const useMusicStore = create<MusicState>()(
  persist(
    (set, get) => ({
      // 初始状态
      musicFiles: [],
      isLoading: false,
      error: null,
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // 设置音乐文件列表
      setMusicFiles: (files: MusicFile[]) => {
        set({ musicFiles: files, error: null })
      },

      // 添加单个音乐文件
      addMusicFile: (file: MusicFile) => {
        set((state) => ({
          musicFiles: [...state.musicFiles, file],
          error: null
        }))
      },

      // 批量添加音乐文件
      addMusicFiles: (files: MusicFile[]) => {
        set((state) => ({
          musicFiles: [...state.musicFiles, ...files],
          error: null
        }))
      },

      // 删除音乐文件
      removeMusicFile: (id: string) => {
        set((state) => {
          const fileToRemove = state.musicFiles.find(f => f.id === id)
          if (fileToRemove?.url) {
            // 释放URL对象
            URL.revokeObjectURL(fileToRemove.url)
          }
          return {
            musicFiles: state.musicFiles.filter(file => file.id !== id),
            error: null
          }
        })
      },

      // 更新音乐文件信息
      updateMusicFile: (id: string, updates: Partial<MusicFile>) => {
        set((state) => ({
          musicFiles: state.musicFiles.map(file =>
            file.id === id ? { ...file, ...updates, lastModified: new Date().toISOString() } : file
          ),
          error: null
        }))
      },

      // 清空所有音乐文件
      clearAllMusic: () => {
        const { musicFiles } = get()
        // 释放所有URL对象
        musicFiles.forEach(file => {
          if (file.url) {
            URL.revokeObjectURL(file.url)
          }
        })
        set({
          musicFiles: [],
          error: null
        })
      },

      // 根据ID获取音乐文件
      getMusicById: (id: string) => {
        return get().musicFiles.find(file => file.id === id)
      },

      // 根据格式获取音乐文件
      getMusicByFormat: (format: string) => {
        return get().musicFiles.filter(file => 
          file.format.toLowerCase() === format.toLowerCase()
        )
      },

      // 根据分类获取音乐文件
      getMusicByCategory: (category: string) => {
        return get().musicFiles.filter(file => 
          file.category === category
        )
      },

      // 获取文件总数
      getTotalCount: () => {
        return get().musicFiles.length
      },

      // 获取总文件大小（格式化字符串）
      getTotalSize: () => {
        const { musicFiles } = get()
        const totalBytes = calculateTotalBytes(musicFiles)
        return formatFileSize(totalBytes)
      },

      // 获取格式统计
      getFormatStats: () => {
        const { musicFiles } = get()
        return musicFiles.reduce((stats, file) => {
          const format = file.format.toLowerCase()
          stats[format] = (stats[format] || 0) + 1
          return stats
        }, {} as Record<string, number>)
      },

      // 设置当前页码
      setPage: (page: number) => {
        set({ currentPage: page })
      },

      // 设置每页大小
      setPageSize: (size: number) => {
        set({ pageSize: size })
      },

      // 设置总数
      setTotal: (total: number) => {
        set({ total })
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error })
      },
    }),
    {
      name: 'music-storage',
      storage: createJSONStorage(() => localStorage),
      // 过滤掉不需要持久化的字段
      partialize: (state) => ({
        musicFiles: state.musicFiles.map(file => ({
          ...file,
          // 不持久化File对象和URL，重新打开时需要重新上传
          file: undefined,
          url: undefined
        }))
      }),
    }
  )
)

// 导出类型
export type { MusicState }
