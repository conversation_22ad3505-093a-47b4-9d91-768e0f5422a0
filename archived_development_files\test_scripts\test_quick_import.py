#!/usr/bin/env python
"""
快速测试后端API导入和基本功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """测试导入是否正常"""
    try:
        from src.api.video_generation import router, safe_convert_job_to_response
        print("✅ 视频生成API模块导入成功")
        
        # 测试安全转换函数是否可用
        print("✅ 安全转换函数已定义")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_fake_job_conversion():
    """测试使用虚假对象的转换"""
    try:
        from src.api.video_generation import safe_convert_job_to_response
        
        # 创建一个模拟的job对象
        class FakeJob:
            def __init__(self):
                self.id = "test-123"
                self.name = "Test Job"
                self.description = "Test Description"
                self.config = {}
                self.account_configs = []
                self.status = "pending"
                self.total_tasks = 5
                self.completed_tasks = 2
                self.failed_tasks = 0
                self.started_at = None
                self.completed_at = None
                self.estimated_duration = None
                self.error_message = None
                self.created_at = None
                self.updated_at = None
        
        fake_job = FakeJob()
        result = safe_convert_job_to_response(fake_job)
        print("✅ 安全转换函数工作正常")
        print(f"转换结果: {result}")
        return True
    except Exception as e:
        print(f"❌ 转换测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试...")
    
    if test_imports():
        test_fake_job_conversion()
    
    print("测试完成")
