#!/usr/bin/env python3
"""
测试video_generation API的依赖注入修复
"""

import sys
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from fastapi.testclient import TestClient
from src.api.video_generation import router
from fastapi import FastAPI

app = FastAPI()
app.include_router(router)

client = TestClient(app)

def test_api_endpoints():
    """测试API端点是否能正常启动"""
    
    # 测试获取作业列表 - 应该不会出现 'sessionmaker' object has no attribute 'query' 错误
    try:
        response = client.get("/video-generator/jobs")
        print(f"GET /jobs - Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"GET /jobs - Error: {e}")
    
    # 测试获取任务列表
    try:
        response = client.get("/video-generator/tasks")
        print(f"GET /tasks - Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"GET /tasks - Error: {e}")
    
    # 测试获取单个作业详情
    try:
        response = client.get("/video-generator/jobs/test-job-id")
        print(f"GET /jobs/test-job-id - Status: {response.status_code}")
        if response.status_code not in [200, 404]:  # 404 is expected for non-existent job
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"GET /jobs/test-job-id - Error: {e}")

if __name__ == "__main__":
    print("测试video_generation API依赖注入修复...")
    test_api_endpoints()
    print("测试完成")
