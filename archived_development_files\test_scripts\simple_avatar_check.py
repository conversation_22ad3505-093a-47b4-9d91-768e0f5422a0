#!/usr/bin/env python3
"""
简单检查账号头像数据
"""

import sys
import os

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

try:
    from src.core.database import get_db_session
    from src.models.accounts import Account
    
    print("=== 账号头像数据检查 ===")
    
    db = get_db_session()
    accounts = db.query(Account).all()
    
    print(f"找到 {len(accounts)} 个账号")
    
    for i, acc in enumerate(accounts, 1):
        print(f"\n账号 {i}:")
        print(f"  ID: {acc.id}")
        print(f"  名称: {acc.name}")
        print(f"  头像URL: {acc.avatar_url}")
        print(f"  头像URL类型: {type(acc.avatar_url)}")
        print(f"  是否为None: {acc.avatar_url is None}")
        print(f"  是否为空字符串: {acc.avatar_url == ''}")
        
        if acc.avatar_url:
            print(f"  头像URL长度: {len(acc.avatar_url)}")
    
    db.close()
    print("\n=== 检查完成 ===")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
