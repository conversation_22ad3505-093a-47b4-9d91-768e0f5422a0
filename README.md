# Reddit Story Video Generator

一个基于AI的Reddit故事视频自动生成工具，采用现代化的前后端分离架构。

## 项目简介

这是一个使用Next.js + Python FastAPI构建的Web应用程序，可以自动生成Reddit故事视频。支持文字转语音、视频素材管理、字幕生成等功能，并提供多种部署方案。

## 🚀 技术架构

### 前端技术栈
- **框架**: Next.js 15 (App Router, SSG支持)
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS 3.x
- **状态管理**: Zustand
- **UI组件**: Radix UI / Headless UI

### 后端技术栈
- **框架**: Python FastAPI 0.104+
- **语言**: Python 3.11+
- **数据库**: SQLAlchemy (适配器支持SQLite/MySQL/PostgreSQL)
- **缓存**: 适配器支持(Memory/Redis/SQLite)
- **视频处理**: FFmpeg + MoviePy
- **AI集成**: OpenAI SDK, Azure SDK等

### 核心特性
- **适配器模式**: 支持多种数据库和缓存方案
- **部署支持**: 单机打包
- **配置驱动**: 环境配置文件支持
- **异步处理**: 高并发任务处理

## 📋 功能特性

### 系统管理
- [x] 多环境配置系统
- [x] 适配器模式架构  
- [x] 设置管理API (完成)
- [ ] TTS服务配置（OpenAI/Azure/F5-TTS）
- [ ] 大模型服务配置（OpenAI/Claude/本地模型）

### 资源管理
- [x] 背景音乐管理 (API完成)
- [x] 视频素材管理 (API完成)
- [x] 提示词管理 (API完成)
- [x] 账号名称管理 (API完成)
- [x] 封面模板管理 (API完成)

### 视频生成
- [ ] 自动故事生成（AI驱动）
- [ ] 文字转语音合成
- [ ] 智能视频素材选择

## 🧪 前后端联调验证

### 快速验证
```bash
# 快速检查前后端集成状态
quick-integration-test.bat

# 或手动运行Python脚本
cd backend
python quick_integration_test.py
```

### 完整验证流程
```bash
# 1. 完整集成测试（自动启动服务器）
full-integration-test.bat

# 2. 手动测试指南
cd backend
python manual_test_guide.py

# 3. 数据结构验证（需要服务器运行）
python test_data_structure.py
```

### 验证清单
- ✅ 后端API服务器启动 (http://localhost:8000)
- ✅ 前端开发服务器启动 (http://localhost:3000)
- ✅ 所有API端点响应正常
- ✅ 前端页面加载无错误
- ✅ 数据持久化功能正常
- ✅ 错误处理机制有效

### 常见问题排查
```bash
# 检查后端依赖
cd backend
pip install -r requirements.txt

# 检查前端依赖
cd frontend
npm install

# 查看后端日志
cd backend
python start_server.py

# 查看前端日志
cd frontend
npm run dev
```

## 🛠 开发状态

**当前阶段**: 阶段4A - 项目基础架构搭建 ✅

### 已完成
- [x] Next.js 15 前端项目初始化
- [x] Python FastAPI 后端项目初始化  
- [x] 适配器模式核心架构
- [x] 多环境配置系统
- [x] 基础API路由结构
- [x] 开发环境配置

### 进行中
- [ ] 阶段4B: 核心组件和服务开发

详细开发计划请查看 [development-plan.md](docs/development-plan.md)

## 🚀 快速开始

### 环境要求
- Node.js 18+ (前端)
- Python 3.11+ (后端)
- FFmpeg (视频处理)

### 安装依赖

#### 前端
```bash
cd frontend
npm install
```

#### 后端
```bash
cd backend
pip install -r requirements.txt
# 或使用 Poetry
poetry install
```

### 启动开发环境

#### 前端开发服务器
```bash
cd frontend
npm run dev
# 访问 http://localhost:3000
```

#### 后端API服务器
```bash
cd backend
python main.py
# API文档: http://localhost:8000/docs
```

### 配置说明

1. 复制配置文件: `shared/config/config.yaml`
2. 根据环境修改配置参数
3. 设置环境变量: `ENVIRONMENT=development`


## 📁 项目结构

```
RedditStoryVideoGenerator/
├── frontend/          # Next.js 前端
├── backend/           # Python FastAPI 后端
├── shared/            # 共享配置和资源
├── docs/              # 项目文档
└── prototypes/        # 界面原型
```

## 🤝 贡献指南

请参考 [coding_rules.md](coding_rules.md) 了解编码规范和开发流程。

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请创建 Issue 或联系开发团队。
3. 设计SQLite数据库结构
4. 建立Electron IPC通信机制

### 阶段二：核心功能开发
1. 系统设置模块
2. 资源管理模块
3. 数据库操作封装

### 阶段三：视频生成引擎
1. 大模型文案生成
2. 文字转语音处理
3. 视频合成算法
4. 字幕生成和同步

### 阶段四：用户界面和体验优化
1. 响应式界面设计
2. 进度显示和状态管理
3. 错误处理和日志记录

## 更新日志
- 2025-06-24: 项目初始化，完成需求分析和架构设计
