"""
客户端测试脚本 - 演示FastAPI异步处理能力
同时发送多个请求，观察服务器的并发处理情况
"""

import asyncio
import aiohttp
import time
from datetime import datetime

async def send_request(session, url, request_name, delay=0):
    """发送HTTP请求"""
    if delay > 0:
        await asyncio.sleep(delay)
    
    start_time = time.time()
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] 📤 发送请求: {request_name}")
    
    try:
        async with session.get(url) as response:
            result = await response.json()
            end_time = time.time()
            duration = end_time - start_time
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            print(f"[{timestamp}] 📥 收到响应: {request_name} (耗时: {duration:.2f}秒)")
            return result
    except Exception as e:
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] ❌ 请求失败: {request_name} - {e}")
        return None

async def test_scenario_1():
    """测试场景1：慢请求期间的快速请求处理"""
    print("\n" + "="*60)
    print("📋 测试场景1：验证FastAPI异步调度机制")
    print("   发送一个慢请求(3秒)，然后立即发送多个快速请求")
    print("   预期结果：快速请求不会被慢请求阻塞")
    print("="*60)
    
    async with aiohttp.ClientSession() as session:
        # 同时发送请求
        tasks = [
            send_request(session, "http://localhost:8001/slow", "慢请求(3秒)", 0),
            send_request(session, "http://localhost:8001/quick", "快速请求1", 0.1),
            send_request(session, "http://localhost:8001/quick", "快速请求2", 0.2),
            send_request(session, "http://localhost:8001/status", "状态检查1", 0.3),
            send_request(session, "http://localhost:8001/status", "状态检查2", 0.4),
            send_request(session, "http://localhost:8001/quick", "快速请求3", 0.5),
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        print(f"\n✅ 场景1完成，处理了{len([r for r in results if r is not None])}个请求")

async def test_scenario_2():
    """测试场景2：多个视频生成请求的并发处理"""
    print("\n" + "="*60)
    print("📋 测试场景2：多个视频生成请求并发处理")
    print("   同时发送3个视频生成请求(每个7.5秒)")
    print("   预期结果：并发处理，总时间约7.5秒而不是22.5秒")
    print("="*60)
    
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        tasks = [
            send_request(session, "http://localhost:8001/video_simulation", "视频生成1", 0),
            send_request(session, "http://localhost:8001/video_simulation", "视频生成2", 0.1),
            send_request(session, "http://localhost:8001/video_simulation", "视频生成3", 0.2),
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        print(f"\n✅ 场景2完成，3个视频生成请求总耗时: {total_time:.2f}秒")
        print(f"   如果是同步处理，应该需要约22.5秒")
        print(f"   异步并发处理，实际只需要约7.5秒")

async def test_scenario_3():
    """测试场景3：视频生成期间的其他API调用"""
    print("\n" + "="*60)
    print("📋 测试场景3：视频生成期间的其他API调用")
    print("   启动视频生成的同时，持续调用状态检查API")
    print("   预期结果：状态检查不会被视频生成阻塞")
    print("="*60)
    
    async with aiohttp.ClientSession() as session:
        # 启动视频生成任务
        video_task = send_request(session, "http://localhost:8001/video_simulation", "后台视频生成", 0)
        
        # 在视频生成期间，每隔0.5秒调用状态检查
        status_tasks = []
        for i in range(10):  # 5秒内进行10次状态检查
            task = send_request(session, "http://localhost:8001/status", f"状态检查{i+1}", i * 0.5)
            status_tasks.append(task)
        
        # 等待所有任务完成
        all_tasks = [video_task] + status_tasks
        results = await asyncio.gather(*all_tasks, return_exceptions=True)
        
        print(f"\n✅ 场景3完成，视频生成期间成功处理了{len(status_tasks)}次状态检查")

async def test_scenario_4():
    """测试场景4：并发能力测试"""
    print("\n" + "="*60)
    print("📋 测试场景4：内置并发能力测试")
    print("   调用服务器的并发测试API")
    print("   验证asyncio.gather的并发处理能力")
    print("="*60)
    
    async with aiohttp.ClientSession() as session:
        result = await send_request(session, "http://localhost:8001/test_concurrent", "并发测试", 0)
        if result:
            print(f"   并发测试结果: {result}")

async def main():
    """主测试函数"""
    print("🚀 FastAPI异步调度机制测试客户端")
    print("⚠️  请确保服务器已启动: python fastapi_async_execution_demo.py")
    
    # 检查服务器是否可用
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8001/status") as response:
                if response.status == 200:
                    print("✅ 服务器连接正常")
                else:
                    print("❌ 服务器响应异常")
                    return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("   请先运行: python fastapi_async_execution_demo.py")
        return
    
    # 运行所有测试场景
    await test_scenario_1()
    await asyncio.sleep(1)  # 间隔1秒
    
    await test_scenario_2()
    await asyncio.sleep(1)
    
    await test_scenario_3()
    await asyncio.sleep(1)
    
    await test_scenario_4()
    
    print("\n" + "="*60)
    print("🎉 所有测试场景完成！")
    print("📝 关键观察：")
    print("   1. 快速请求不会被慢请求阻塞")
    print("   2. 多个长时间任务可以并发执行")
    print("   3. 长时间任务不会阻塞其他API调用")
    print("   4. 所有请求都在同一线程中处理（事件循环）")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
