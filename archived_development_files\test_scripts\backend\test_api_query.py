#!/usr/bin/env python3
"""
直接测试后端API查询逻辑
"""

import sys
import os
sys.path.append('src')

from sqlalchemy import create_engine, or_
from sqlalchemy.orm import sessionmaker
from src.models.resources import BackgroundMusic

# 创建数据库连接
DATABASE_URL = "sqlite:///D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_api_query():
    """模拟API查询逻辑"""
    db = SessionLocal()
    try:
        # 模拟get_music_list的查询逻辑
        page = 1
        page_size = 20
        category = None
        search = None
        is_built_in = None
        
        print("=== 模拟API查询逻辑 ===")
        print(f"page: {page}, page_size: {page_size}")
        print(f"category: {category}, search: {search}, is_built_in: {is_built_in}")
        
        # 构建查询
        query = db.query(BackgroundMusic)
        print(f"\n初始查询: {query}")
        
        # 应用过滤条件
        if category:
            print(f"添加分类过滤: category == {category}")
            query = query.filter(BackgroundMusic.category == category)
        
        if is_built_in is not None:
            print(f"添加内置过滤: is_built_in == {is_built_in}")
            query = query.filter(BackgroundMusic.is_built_in == is_built_in)
        
        if search:
            print(f"添加搜索过滤: search == {search}")
            query = query.filter(
                or_(
                    BackgroundMusic.name.contains(search),
                    BackgroundMusic.category.contains(search)
                )
            )
        
        print(f"\n最终查询: {query}")
        
        # 计算总数
        total = query.count()
        print(f"查询总数: {total}")
        
        # 分页
        offset = (page - 1) * page_size
        items = query.offset(offset).limit(page_size).all()
        print(f"分页结果数量: {len(items)}")
        
        # 打印详细结果
        if items:
            print("\n查询结果:")
            for i, item in enumerate(items, 1):
                print(f"{i}. ID: {item.id}, Name: {item.name}, Category: {item.category}")
                # 测试to_frontend_format方法
                try:
                    frontend_data = item.to_frontend_format()
                    print(f"   Frontend format: {frontend_data}")
                except Exception as e:
                    print(f"   Frontend format ERROR: {e}")
        else:
            print("\n查询结果: 无数据")
        
        print("\n=== 检查是否缺少is_deleted过滤 ===")
        # 检查是否需要添加is_deleted过滤
        all_query = db.query(BackgroundMusic)
        all_count = all_query.count()
        non_deleted_query = db.query(BackgroundMusic).filter(BackgroundMusic.is_deleted == False)
        non_deleted_count = non_deleted_query.count()
        
        print(f"总记录数: {all_count}")
        print(f"未删除记录数: {non_deleted_count}")
        
        if all_count != non_deleted_count:
            print("⚠️  发现已删除的记录，需要添加is_deleted过滤条件")
        else:
            print("✅ 所有记录都未被删除")
        
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_api_query()
