# 界面原型设计计划

## 设计目标
为Reddit故事视频生成器创建直观、易用的界面原型，确保用户能够轻松完成所有功能操作。

## 设计原则
1. **简洁明了**: 界面布局清晰，功能分区明确
2. **用户友好**: 适合初中生用户使用，操作流程简单
3. **响应式设计**: 适配不同屏幕尺寸
4. **现代化风格**: 使用现代UI设计语言

## 原型设计顺序

### 第一阶段：主框架和导航
1. **主布局框架** (main-layout.html)
   - 顶部导航栏
   - 侧边菜单
   - 主内容区域
   - 状态栏
   - 主题色切换功能

### 第二阶段：系统设置模块
2. **系统设置页面** (settings.html)
   - TTS服务配置区域
   - 大模型配置区域
   - 配置测试功能
   - 保存/重置按钮

### 第三阶段：资源管理模块
3. **背景音乐管理** (background-music.html)
   - 文件批量导入区域
   - 音乐列表展示
   - 播放预览功能
   - 删除/编辑操作

4. **视频素材管理** (video-materials.html) ✅
   - 视频批量导入区域
   - 素材网格展示
   - 预览播放功能
   - 管理操作

5. **提示词管理** (prompts.html) ✅
   - 提示词创建/编辑表单
   - 提示词列表
   - 分类标签系统
   - 预览和测试功能

6. **账号名称管理** (accounts.html) ✅
   - 批量创建账号表单
   - 账号列表展示
   - 使用状态跟踪
   - 批量操作功能
   - 头像上传功能
   - 批量操作功能

7. **封面模板管理** (cover-templates.html) ✅
   - 模板创建/编辑器
   - 模板预览区域
   - 动态元素配置
   - 模板库展示

### 第四阶段：核心功能模块
8. **视频生成页面** (video-generator.html) ✅
   - 参数配置面板
   - 实时预览区域
   - 生成队列显示
   - 进度监控

9. **任务队列管理** (task-queue.html) ✅
   - 队列状态展示
   - 进度详情
   - 错误日志查看
   - 批量操作

### 第五阶段：辅助功能
10. **首页仪表板** (dashboard.html) ✅
    - 系统状态概览
    - 快速操作入口
    - 最近任务展示
    - 统计信息

## 设计规范

### 色彩方案
- **主色调**: #2563eb (蓝色)
- **辅助色**: #64748b (灰色)
- **成功色**: #059669 (绿色)
- **警告色**: #d97706 (橙色)
- **错误色**: #dc2626 (红色)
- **背景色**: #f8fafc (浅灰)

### 字体规范
- **主字体**: system-ui, -apple-system, sans-serif
- **标题**: 24px, 20px, 18px, 16px
- **正文**: 14px
- **小字**: 12px

### 间距规范
- **页面边距**: 24px
- **组件间距**: 16px
- **元素间距**: 8px
- **按钮内边距**: 8px 16px

### 组件规范
- **按钮**: 圆角 6px，阴影效果
- **输入框**: 边框 1px，圆角 4px
- **卡片**: 圆角 8px，轻微阴影
- **表格**: 斑马纹，悬停效果

## 交互设计要点

### 用户反馈
- 加载状态指示器
- 操作成功/失败提示
- 表单验证提示
- 进度条和百分比

### 便捷操作
- 拖拽上传文件
- 快捷键支持
- 批量选择操作
- 右键菜单

### 响应式适配
- 移动端菜单折叠
- 表格横向滚动
- 弹窗适配屏幕
- 图片自适应

## 原型确认流程
1. 每个页面先展示静态HTML原型
2. 用户确认布局和交互设计
3. 根据反馈进行调整
4. 确认后进入下一个页面
5. 全部完成后整体评审

## 更新日志
- 2025-06-24 10:00: 创建原型设计计划，定义设计规范和确认流程
- 2025-06-24 10:30: 完成主布局框架设计，添加主题色切换功能
  - 支持5种主题色：蓝色（默认）、绿色、紫色、橙色、红色
  - 使用CSS变量实现主题切换
  - 添加主题选择下拉菜单
  - 本地存储保存用户主题偏好
  - 平滑过渡动画效果
- 2025-06-24 11:00: 完成背景音乐管理页面设计
  - 修复列表视图切换功能，支持网格/列表两种显示模式
  - 添加真实音频时长获取功能，通过Audio API自动解析
  - 优化统计信息计算，基于实际文件大小和时长
  - 增强用户体验：拖拽上传、实时预览、音频播放器
- 2025-06-24 12:00: 完成视频生成页面重新设计
  - 根据需求说明重新设计界面，包含完整的10个输入配置项
  - 优化界面布局：配置面板 + 预览区域的双列布局
  - 支持语音服务、大模型、提示词、语音倍速、视频素材、背景音乐、封面模板、字幕、账号选择、输出目录配置
  - 添加实时预览功能：封面预览、字幕预览、队列预览
  - 统一界面风格，去除emoji图标，使用SVG图标
- 2025-06-24 12:30: 完成任务队列管理页面重新设计
  - 统一界面风格，与其他页面保持一致
  - 去除emoji图标，使用现代化SVG图标
  - 优化任务项布局：任务标题、状态、进度、操作按钮
  - 改进日志查看器：终端风格日志显示
  - 统计卡片使用图标化设计，提升视觉效果
- 2025-06-24 13:00: 完成首页仪表板页面设计
  - 创建10-dashboard.html，包含系统状态、快速操作入口、最近任务、统计信息等
  - 添加数据可视化图表：生成数量趋势、任务状态分布
  - 资源统计卡片：音乐、视频、提示词、账号等
  - 系统状态指示器：AI服务、TTS服务、存储状态等
  - 快速操作入口：新建视频、查看队列、设置等
  - 与其他页面保持一致的现代化风格
