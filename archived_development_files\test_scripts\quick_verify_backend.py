#!/usr/bin/env python3
"""
快速验证后端服务器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.src.api.routes import api_router
from backend.src.core.database import get_db
from backend.src.models.resources import VideoMaterial

def test_imports():
    """测试导入是否正常"""
    print("🧪 测试导入...")
    try:
        # 测试路由导入
        print(f"✅ API路由导入成功: {type(api_router)}")
        
        # 测试数据库导入
        print(f"✅ 数据库函数导入成功: {type(get_db)}")
        
        # 测试模型导入
        print(f"✅ VideoMaterial模型导入成功: {VideoMaterial}")
        
        # 测试模型方法
        material = VideoMaterial(
            id="test",
            name="测试素材",
            file_path="/test/path",
            duration=120.0,
            category="test",
            resolution="1920x1080",
            tags=["test"],
            is_built_in=False
        )
        
        frontend_data = material.to_frontend_format()
        print(f"✅ 模型转换方法正常: {type(frontend_data)}")
        print(f"   - 文件类型: {frontend_data.get('type')}")
        print(f"   - 时长: {frontend_data.get('duration')}")
        print(f"   - 分辨率: {frontend_data.get('dimensions')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    print("🚀 后端快速验证...")
    
    if test_imports():
        print("\n✅ 后端组件验证通过，可以启动服务器！")
        print("\n启动命令:")
        print("cd backend")
        print("python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000")
    else:
        print("\n❌ 后端验证失败，请检查代码")

if __name__ == "__main__":
    main()
