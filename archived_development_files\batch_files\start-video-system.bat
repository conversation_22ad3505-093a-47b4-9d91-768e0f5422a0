@echo off
REM 启动视频素材管理系统 - Windows版本

echo 🎥 启动视频素材管理系统
echo ========================

REM 1. 运行数据库迁移
echo 1. 执行数据库迁移...
cd backend
python migrate_video_categories.py
if %errorlevel% neq 0 (
    echo ✗ 数据库迁移失败
    pause
    exit /b 1
)
echo ✓ 数据库迁移完成

REM 2. 启动后端服务
echo.
echo 2. 启动后端服务...
echo 正在启动 FastAPI 服务器...
start "Backend Server" cmd /k "python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload"

REM 等待后端启动
timeout /t 3 /nobreak >nul

REM 3. 启动前端服务
echo.
echo 3. 启动前端服务...
cd ..\frontend
echo 正在启动 Next.js 开发服务器...
start "Frontend Server" cmd /k "npm run dev"

REM 等待前端启动
timeout /t 5 /nobreak >nul

echo.
echo 🎉 系统启动完成！
echo ========================
echo 后端服务: http://localhost:8000
echo 前端服务: http://localhost:3000
echo 视频管理: http://localhost:3000/videos
echo.
echo 功能特性:
echo ✓ 只支持视频文件上传
echo ✓ 真正的后端分类管理
echo ✓ 批量导入功能
echo ✓ 分类增删改查
echo.
echo 按任意键退出...
pause >nul
