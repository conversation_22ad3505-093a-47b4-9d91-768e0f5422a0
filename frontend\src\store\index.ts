/**
 * 全局状态管理入口
 * 统一导出所有状态管理hooks和工具函数
 */

// 导出各个状态管理模块
export { useSettingsStore } from './settingsStore'
export { useGenerationStore, generationUtils } from './generationStore'
export { useMusicStore } from './musicStore'
export { useVideoMaterialStore } from './videoMaterialStore'
export { usePromptStore } from './promptStore'
export { useAccountStore } from './accountStore'
export { useCoverTemplateStore } from './coverTemplateStore'

// 导出所有类型
export type {
  SettingsState,
  TTSConfig,
  LLMConfig,
  GeneralSettings,
  GenerationState,
  GenerationConfig,
  GenerationTask
} from '../types/store'
