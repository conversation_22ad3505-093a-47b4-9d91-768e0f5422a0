#!/usr/bin/env python3
"""
测试后端API直接访问功能
验证视频素材管理API是否正常工作，包括批量上传功能
"""

import requests
import json
import time
import os
from typing import Dict, Any

# 后端API基础URL
BASE_URL = "http://localhost:8000"

def test_api_endpoint(endpoint: str, method: str = "GET", data: Dict[Any, Any] = None, 
                     files: Dict[str, Any] = None) -> Dict[str, Any]:
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        print(f"🟢 测试 {method} {url}")
        
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            if files:
                response = requests.post(url, data=data, files=files, timeout=120)
            else:
                response = requests.post(url, json=data, timeout=120)
        elif method == "PUT":
            response = requests.put(url, json=data, timeout=30)
        elif method == "DELETE":
            response = requests.delete(url, timeout=30)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=2)[:200]}...")
                return {"success": True, "status": response.status_code, "data": result}
            except:
                print(f"   响应: {response.text[:200]}...")
                return {"success": True, "status": response.status_code, "data": response.text}
        else:
            print(f"   错误: {response.text}")
            return {"success": False, "status": response.status_code, "error": response.text}
            
    except Exception as e:
        print(f"🔴 请求失败: {str(e)}")
        return {"success": False, "error": str(e)}

def main():
    """主测试函数"""
    print("=" * 60)
    print("后端API直接访问测试")
    print("=" * 60)
    
    # 1. 测试基础健康检查
    print("\n1. 测试后端健康状态")
    health_result = test_api_endpoint("/health")
    if not health_result.get("success"):
        print("❌ 后端服务未启动或不可访问")
        return
    
    # 2. 测试视频素材相关API
    print("\n2. 测试视频素材API")
    
    # 获取所有视频素材
    materials_result = test_api_endpoint("/api/video-materials")
    
    # 获取视频分类
    categories_result = test_api_endpoint("/api/video-categories")
    
    # 3. 测试批量上传端点（不上传实际文件，只测试端点可访问性）
    print("\n3. 测试批量上传端点")
    upload_result = test_api_endpoint("/api/video-materials/upload/bulk", "POST", {
        "category_id": 1,
        "description": "测试批量上传"
    })
    
    # 4. 测试单个上传端点
    print("\n4. 测试单个上传端点")
    single_upload_result = test_api_endpoint("/api/video-materials/upload", "POST", {
        "category_id": 1,
        "description": "测试单个上传"
    })
    
    # 5. 测试其他CRUD操作
    print("\n5. 测试其他操作")
    
    # 创建分类
    create_category_result = test_api_endpoint("/api/video-categories", "POST", {
        "name": "测试分类",
        "description": "API测试用分类"
    })
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = [
        ("后端健康检查", health_result),
        ("获取视频素材", materials_result),
        ("获取视频分类", categories_result),
        ("批量上传端点", upload_result),
        ("单个上传端点", single_upload_result),
        ("创建分类", create_category_result),
    ]
    
    success_count = 0
    for name, result in results:
        status = "✅ 成功" if result.get("success") else "❌ 失败"
        print(f"{name}: {status}")
        if result.get("success"):
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个API测试通过")
    
    if success_count == len(results):
        print("🎉 所有API测试通过！后端服务正常运行。")
    else:
        print("⚠️  部分API测试失败，请检查后端服务状态。")

if __name__ == "__main__":
    main()
