import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

try:
    # 测试导入
    from src.services.template_import_service import template_import_service
    from src.services.app_init_service import app_init_service
    print('✓ 服务模块导入成功')
    
    # 测试目录初始化
    app_init_service.init_directories()
    print('✓ 目录初始化成功')
    
    # 检查HTML模板文件
    import os
    if os.path.exists('social_post_template.html'):
        print('✓ 社交媒体帖子模板文件存在')
        
        # 提取变量测试
        with open('social_post_template.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        variables = template_import_service.extract_variables_from_html(html_content)
        print(f'✓ 提取到变量: {variables}')
    else:
        print('✗ 社交媒体帖子模板文件不存在')
        
except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()
