"""
直接测试后端字幕样式API
"""

import requests
import json
import time

# 后端API地址
BASE_URL = "http://localhost:8000"

def test_subtitle_styles_api():
    """测试字幕样式API"""
    print("🧪 开始测试字幕样式API...")
    
    # 创建一个完整的视频生成作业请求
    create_job_data = {
        "name": "字幕样式测试作业",
        "description": "测试自定义字幕样式是否生效",
        "config": {
            "video_material_group": "nature",  # 假设有nature分组
            "material_selection": "random",
            "prompt_group": "story",  # 假设有story分组
            "prompt_id": "default_prompt",  # 假设有默认提示词
            "voice_settings": {
                "voice": "zh-CN-XiaoxiaoNeural",
                "speed": 1.0
            },
            "background_music_group": "ambient",  # 假设有ambient分组
            "music_selection": "random",
            "audio_settings": {
                "speech_volume": 1.0,
                "background_music_volume": 0.15,
                "enable_background_music": True
            },
            "cover_template_id": "default_template",  # 假设有默认模板
            "subtitle_config": {
                "font_family": "Microsoft YaHei",
                "font_size": 32,
                "font_color": "#FF0000",
                "position": "top",
                "enabled": True
            },
            "video_settings": {
                "resolution": "1080x1920",
                "fps": 30,
                "format": "mp4"
            }
        },
        "account_configs": [
            {
                "account_id": "test_account_1",
                "video_count": 1
            }
        ]
    }
    
    try:
        # 发送创建作业请求
        print(f"📤 发送测试请求到 {BASE_URL}/api/video-generator/jobs")
        print(f"📝 字幕配置: {json.dumps(create_job_data['config']['subtitle_config'], indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            f"{BASE_URL}/api/video-generator/jobs",
            json=create_job_data,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 作业创建成功")
            print(f"📋 作业ID: {result.get('id')}")
            
            # 检查响应中是否包含字幕配置
            if 'config' in result and 'subtitle_config' in result['config']:
                received_subtitle_config = result['config']['subtitle_config']
                print(f"📋 服务器接收到的字幕配置:")
                print(json.dumps(received_subtitle_config, indent=2, ensure_ascii=False))
                
                # 验证配置是否正确传递
                expected = create_job_data['config']['subtitle_config']
                mismatches = []
                
                for key, expected_value in expected.items():
                    received_value = received_subtitle_config.get(key)
                    if received_value != expected_value:
                        mismatches.append(f"  ❌ {key}: 期望 {expected_value}, 实际 {received_value}")
                    else:
                        print(f"  ✅ {key}: {received_value}")
                
                if mismatches:
                    print(f"❌ 配置传递存在问题:")
                    for mismatch in mismatches:
                        print(mismatch)
                else:
                    print(f"✅ 所有字幕配置正确传递!")
            else:
                print(f"❌ 响应中缺少字幕配置")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到后端服务 {BASE_URL}")
        print(f"请确保后端服务正在运行")
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n🎉 字幕样式API测试完成！")

if __name__ == "__main__":
    test_subtitle_styles_api()
