#!/usr/bin/env python3
"""
简化的API测试脚本 - 测试视频素材管理功能
"""

import requests
import json
import os
import time

BASE_URL = "http://127.0.0.1:8000"

def test_server_connection():
    """测试服务器连接"""
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        print(f"✅ 服务器连接正常: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def test_video_materials_endpoints():
    """测试视频素材相关接口"""
    
    # 1. 测试获取视频素材列表
    print("\n🧪 测试获取视频素材列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/video-materials/")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   返回数据类型: {type(data)}")
            print(f"   数据条数: {len(data) if isinstance(data, list) else 'N/A'}")
            print("   ✅ 获取列表成功")
        else:
            print(f"   ❌ 获取列表失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 2. 测试获取分类
    print("\n🧪 测试获取视频素材分类...")
    try:
        response = requests.get(f"{BASE_URL}/api/video-materials/categories")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            categories = response.json()
            print(f"   分类数量: {len(categories)}")
            print(f"   分类列表: {categories}")
            print("   ✅ 获取分类成功")
        else:
            print(f"   ❌ 获取分类失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 3. 测试统计信息
    print("\n🧪 测试获取统计信息...")
    try:
        response = requests.get(f"{BASE_URL}/api/video-materials/stats")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"   统计信息: {stats}")
            print("   ✅ 获取统计成功")
        else:
            print(f"   ❌ 获取统计失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")

def test_upload_endpoints():
    """测试上传相关接口"""
    
    print("\n🧪 测试上传接口...")
    try:
        # 测试上传接口是否可达（不上传实际文件）
        response = requests.options(f"{BASE_URL}/api/upload/single")
        print(f"   单文件上传接口状态: {response.status_code}")
        
        response = requests.options(f"{BASE_URL}/api/upload/batch")
        print(f"   批量上传接口状态: {response.status_code}")
        
        print("   ✅ 上传接口可达")
    except Exception as e:
        print(f"   ❌ 上传接口测试失败: {e}")

def main():
    print("🚀 开始简化API测试...")
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    if test_server_connection():
        test_video_materials_endpoints()
        test_upload_endpoints()
        print("\n✅ API测试完成！")
    else:
        print("\n❌ 无法连接到服务器，请确保后端已启动:")
        print("cd backend")
        print("python -m uvicorn main:app --reload --host 127.0.0.1 --port 8000")

if __name__ == "__main__":
    main()
