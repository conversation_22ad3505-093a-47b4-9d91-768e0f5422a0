# 前后端联调验证指南

## 🎯 目标
验证后端API与前端Zustand store的完全兼容性，确保数据结构一致、API调用正常。

## 📋 前期准备

### 1. 后端准备
```bash
# 进入后端目录
cd d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend

# 激活虚拟环境（如果有）
# .\venv\Scripts\activate

# 安装依赖（如果需要）
pip install requests

# 启动后端服务器
python start_server.py
```

### 2. 前端准备
```bash
# 进入前端目录
cd d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\frontend

# 安装依赖
npm install

# 启动前端开发服务器
npm run dev
```

## 🧪 测试步骤

### 步骤1: 启动后端服务器
```bash
cd backend
python start_server.py
```
服务器启动后访问：
- API文档: http://localhost:8000/docs
- 交互式API: http://localhost:8000/redoc
- 健康检查: http://localhost:8000/health

### 步骤2: 运行API集成测试
```bash
cd backend
python test_integration.py
```
这将测试所有API端点的基本功能。

### 步骤3: 运行数据结构验证
```bash
cd backend
python test_structure.py
```
这将验证API返回的数据结构与前端期望的结构是否一致。

### 步骤4: 启动前端并测试连接
```bash
cd frontend
npm run dev
```
在前端应用中测试：
1. 打开浏览器开发者工具
2. 导航到各个资源管理页面
3. 检查网络请求是否正常
4. 验证数据显示是否正确

## 📊 测试API端点列表

### Settings API
- ✅ GET `/api/v1/settings` - 获取设置
- ✅ PUT `/api/v1/settings` - 更新设置
- ✅ POST `/api/v1/settings/reset` - 重置设置
- ✅ GET `/api/v1/settings/validate` - 验证设置

### Background Music API
- ✅ GET `/api/v1/resources/background-music/` - 获取音乐列表
- ✅ POST `/api/v1/resources/background-music/` - 创建音乐
- ✅ GET `/api/v1/resources/background-music/{id}` - 获取单个音乐
- ✅ PUT `/api/v1/resources/background-music/{id}` - 更新音乐
- ✅ DELETE `/api/v1/resources/background-music/{id}` - 删除音乐
- ✅ POST `/api/v1/resources/background-music/batch` - 批量操作
- ✅ GET `/api/v1/resources/background-music/categories` - 获取分类

### Video Materials API
- ✅ GET `/api/v1/resources/video-materials/` - 获取视频列表
- ✅ POST `/api/v1/resources/video-materials/` - 创建视频
- ✅ GET `/api/v1/resources/video-materials/{id}` - 获取单个视频
- ✅ PUT `/api/v1/resources/video-materials/{id}` - 更新视频
- ✅ DELETE `/api/v1/resources/video-materials/{id}` - 删除视频
- ✅ POST `/api/v1/resources/video-materials/bulk` - 批量创建
- ✅ DELETE `/api/v1/resources/video-materials/bulk` - 批量删除
- ✅ GET `/api/v1/resources/video-materials/categories/list` - 获取分类

### Prompts API
- ✅ GET `/api/v1/resources/prompts/` - 获取提示词列表
- ✅ POST `/api/v1/resources/prompts/` - 创建提示词
- ✅ GET `/api/v1/resources/prompts/{id}` - 获取单个提示词
- ✅ PUT `/api/v1/resources/prompts/{id}` - 更新提示词
- ✅ DELETE `/api/v1/resources/prompts/{id}` - 删除提示词
- ✅ POST `/api/v1/resources/prompts/bulk` - 批量创建
- ✅ DELETE `/api/v1/resources/prompts/bulk` - 批量删除
- ✅ GET `/api/v1/resources/prompts/categories/list` - 获取分类
- ✅ POST `/api/v1/resources/prompts/{id}/use` - 使用提示词

### Accounts API
- ✅ GET `/api/v1/resources/accounts/` - 获取账户列表
- ✅ POST `/api/v1/resources/accounts/` - 创建账户
- ✅ GET `/api/v1/resources/accounts/{id}` - 获取单个账户
- ✅ PUT `/api/v1/resources/accounts/{id}` - 更新账户
- ✅ DELETE `/api/v1/resources/accounts/{id}` - 删除账户
- ✅ POST `/api/v1/resources/accounts/bulk` - 批量创建
- ✅ DELETE `/api/v1/resources/accounts/bulk` - 批量删除
- ✅ GET `/api/v1/resources/accounts/platforms/list` - 获取平台列表
- ✅ POST `/api/v1/resources/accounts/{id}/activate` - 激活账户
- ✅ POST `/api/v1/resources/accounts/{id}/deactivate` - 停用账户
- ✅ POST `/api/v1/resources/accounts/{id}/use` - 使用账户

### Cover Templates API
- ✅ GET `/api/v1/resources/cover-templates/` - 获取模板列表
- ✅ POST `/api/v1/resources/cover-templates/` - 创建模板
- ✅ GET `/api/v1/resources/cover-templates/{id}` - 获取单个模板
- ✅ PUT `/api/v1/resources/cover-templates/{id}` - 更新模板
- ✅ DELETE `/api/v1/resources/cover-templates/{id}` - 删除模板
- ✅ POST `/api/v1/resources/cover-templates/bulk` - 批量创建
- ✅ DELETE `/api/v1/resources/cover-templates/bulk` - 批量删除
- ✅ GET `/api/v1/resources/cover-templates/categories/list` - 获取分类
- ✅ POST `/api/v1/resources/cover-templates/{id}/use` - 使用模板

## 🔍 数据结构验证点

### 1. 字段命名一致性
- Create requests: snake_case (file_path, is_built_in)
- Response data: camelCase (filePath, isBuiltIn)

### 2. 数据类型一致性
- 时间戳格式: ISO 8601 字符串
- 布尔值: true/false
- 数字: 整数/浮点数
- 数组: 空数组默认值

### 3. 嵌套对象结构
- metadata 对象包含所有元数据字段
- config 对象包含平台特定配置

## 🐛 常见问题排查

### 后端问题
1. **服务器启动失败**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :8000
   # 杀死占用进程
   taskkill /PID <PID> /F
   ```

2. **数据库连接问题**
   - 检查数据库配置
   - 确认数据库文件权限

3. **API错误**
   - 查看服务器日志
   - 检查请求数据格式

### 前端问题
1. **CORS错误**
   - 确认后端CORS配置正确
   - 检查请求URL是否正确

2. **数据显示异常**
   - 检查前端store更新逻辑
   - 验证API响应数据结构

3. **网络请求失败**
   - 确认API端点URL正确
   - 检查请求头设置

## ✅ 验证清单

### 后端验证
- [ ] 服务器正常启动 (http://localhost:8000)
- [ ] API文档可访问 (http://localhost:8000/docs)
- [ ] 健康检查通过 (http://localhost:8000/health)
- [ ] 集成测试全部通过
- [ ] 数据结构验证全部通过

### 前端验证
- [ ] 前端应用正常启动
- [ ] 设置页面功能正常
- [ ] 资源管理页面功能正常
- [ ] 数据CRUD操作正常
- [ ] 网络请求无错误
- [ ] 状态管理正常

### 联调验证
- [ ] 前端能正常调用后端API
- [ ] 数据在前后端间正确传输
- [ ] 状态更新及时反映
- [ ] 错误处理机制正常
- [ ] 用户体验流畅

## 🎉 验证成功标志

当以下条件全部满足时，前后端联调验证成功：

1. ✅ 所有API端点返回正确状态码
2. ✅ 数据结构与前端期望完全一致
3. ✅ 前端页面功能完全正常
4. ✅ 数据流转无错误
5. ✅ 用户操作响应及时

完成这些验证后，项目就可以进入下一阶段的功能开发了！
