from gradio_client import Client, handle_file

print('准备测试...')

client = Client("http://application-e4ee63ebd100431995375a4a07ab41b320250817000003.ssh-hdc.xingluan.cn:1800/")

result = client.predict(
  ref_audio_input=handle_file('C:\\Users\\<USER>\\Desktop\\girl.mp3'),
  ref_text_input="对应girl.mp3朗读的文本内容",
  gen_text_input="要进行语音生成的文本内容",
  remove_silence=False,
  randomize_seed=True,
  seed_input=0,
  cross_fade_duration_slider=0.15,
  nfe_slider=32,
  speed_slider=1,
  api_name="/basic_tts"
)

print('client调用结束.')

audio_output = result[0]  # 生成的音频
spectrogram = result[1]   # 频谱图

print(f'生成的audio地址：{audio_output}') # 本地的音频文件地址