#!/usr/bin/env python
"""
启动FastAPI服务器脚本
"""
import uvicorn
import sys
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

if __name__ == "__main__":
    print("🚀 启动Reddit Story Video Generator后端服务...")
    print("📍 访问地址:")
    print("  - API文档: http://localhost:8000/docs")
    print("  - 健康检查: http://localhost:8000/health")
    print("  - 设置API: http://localhost:8000/api/v1/settings")
    print("🔄 按 Ctrl+C 停止服务\n")
    
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n✋ 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
