@echo off
echo 启动封面模板测试环境...
echo.

echo 1. 创建测试数据...
python create_cover_test_data.py
echo.

echo 2. 启动后端服务...
cd backend
start "Cover Template Backend" cmd /k "python main.py"
cd ..

echo 3. 等待后端启动...
timeout /t 3

echo 4. 测试API...
python test_api_simple.py
echo.

echo 5. 启动前端...
cd frontend
start "Cover Template Frontend" cmd /k "npm run dev"
cd ..

echo.
echo 服务启动完成!
echo 后端API: http://localhost:8001
echo 前端页面: http://localhost:3000
echo 封面管理: http://localhost:3000/covers
echo API文档: http://localhost:8001/docs
echo.
pause
