#!/usr/bin/env python3
"""测试字幕颜色转换"""

def html_color_to_ass(html_color):
    """将HTML颜色格式转换为ASS格式（BGR）"""
    if html_color.startswith('#'):
        html_color = html_color[1:]
    
    # HTML格式是RGB，ASS格式是BGR
    r = int(html_color[0:2], 16)
    g = int(html_color[2:4], 16) 
    b = int(html_color[4:6], 16)
    
    # ASS格式：&H00BBGGRR
    return f"&H00{b:02X}{g:02X}{r:02X}"

if __name__ == "__main__":
    print('颜色转换测试:')
    print('白色 #FFFFFF ->', html_color_to_ass('#FFFFFF'))
    print('红色 #FF0000 ->', html_color_to_ass('#FF0000'))
    print('绿色 #00FF00 ->', html_color_to_ass('#00FF00'))
    print('蓝色 #0000FF ->', html_color_to_ass('#0000FF'))
    print('黄色 #FFFF00 ->', html_color_to_ass('#FFFF00'))
    
    # 测试对齐方式
    positions = {
        'top': 10,
        'center': 6,
        'bottom': 2
    }
    
    print('\n位置对齐测试:')
    for pos, align in positions.items():
        print(f'{pos} -> Alignment={align}')
