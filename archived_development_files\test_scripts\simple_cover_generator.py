#!/usr/bin/env python3
"""
简化版批量生成封面脚本
根据账号名称、视频文案目录、封面模板名称，为每个文案生成封面图片
"""

import sys
import os
import asyncio
import re
from pathlib import Path
from typing import Optional, List

# 添加backend路径
backend_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
backend_src_path = os.path.abspath(os.path.join(backend_path, 'src'))

# 添加backend和backend/src到Python路径
if os.path.exists(backend_path):
    sys.path.insert(0, backend_path)
    print(f"✅ 添加backend路径: {backend_path}")
    
if os.path.exists(backend_src_path):
    sys.path.insert(0, backend_src_path)
    print(f"✅ 添加backend/src路径: {backend_src_path}")
else:
    print(f"❌ 未找到backend路径: {backend_src_path}")
    print("请确保脚本在正确的目录中运行")
    sys.exit(1)

class SimpleCoverGenerator:
    """简化的封面生成器"""
    
    def __init__(self):
        self.db = None
        
    async def init_services(self):
        """初始化服务"""
        try:
            # 直接连接到backend目录下的数据库文件
            import sqlite3
            from sqlalchemy import create_engine
            from sqlalchemy.orm import sessionmaker
            
            # 构建数据库文件路径
            db_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'reddit_story_generator.db'))
            print(f"🔄 连接数据库: {db_path}")
            
            if not os.path.exists(db_path):
                print(f"❌ 数据库文件不存在: {db_path}")
                return False
            
            # 创建数据库引擎和会话
            engine = create_engine(f'sqlite:///{db_path}')
            Session = sessionmaker(bind=engine)
            self.db = Session()
            
            # 导入模型类（确保在数据库连接之后）
            from src.models.accounts import Account
            from src.models.resources import CoverTemplate
            
            print("✅ 数据库连接成功")
            return True
            
        except Exception as e:
            print(f"❌ 服务初始化失败: {e}")
            print("请确保：")
            print("  1. 脚本在正确的目录中运行")
            print("  2. backend/reddit_story_generator.db文件存在")
            import traceback
            traceback.print_exc()
            return False
    
    def find_account_by_name(self, account_name: str):
        """根据账号名称查找账号"""
        try:
            from src.models.accounts import Account
            
            # 精确匹配
            account = self.db.query(Account).filter(Account.name == account_name).first()
            if account:
                return account
                
            # 模糊匹配
            account = self.db.query(Account).filter(Account.name.like(f"%{account_name}%")).first()
            if account:
                print(f"⚠️  通过模糊匹配找到账号: {account.name}")
                return account
                
            return None
            
        except Exception as e:
            print(f"❌ 查找账号失败: {e}")
            return None
    
    def find_template_by_name(self, template_name: str):
        """根据模板名称查找封面模板"""
        try:
            from src.models.resources import CoverTemplate
            
            # 精确匹配
            template = self.db.query(CoverTemplate).filter(CoverTemplate.name == template_name).first()
            if template:
                return template
                
            # 模糊匹配
            template = self.db.query(CoverTemplate).filter(CoverTemplate.name.like(f"%{template_name}%")).first()
            if template:
                print(f"⚠️  通过模糊匹配找到模板: {template.name}")
                return template
                
            return None
            
        except Exception as e:
            print(f"❌ 查找模板失败: {e}")
            return None
    
    def get_txt_files(self, directory: str) -> List[Path]:
        """获取目录中的所有txt文件"""
        try:
            directory_path = Path(directory)
            if not directory_path.exists():
                print(f"❌ 目录不存在: {directory}")
                return []
                
            if not directory_path.is_dir():
                print(f"❌ 不是有效目录: {directory}")
                return []
                
            txt_files = list(directory_path.glob("*.txt"))
            if not txt_files:
                print(f"⚠️  目录中没有找到txt文件: {directory}")
                return []
                
            print(f"✅ 找到 {len(txt_files)} 个txt文件")
            return txt_files
            
        except Exception as e:
            print(f"❌ 获取txt文件失败: {e}")
            return []
    
    def extract_first_sentence(self, file_path: Path) -> Optional[str]:
        """提取文件的第一句话"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            if not content:
                print(f"⚠️  文件为空: {file_path.name}")
                return None
                
            # 用正则表达式提取第一句话
            # 考虑中文和英文的句号、问号、感叹号
            sentence_pattern = r'[^.!?。！？]*[.!?。！？]'
            match = re.search(sentence_pattern, content)
            
            if match:
                first_sentence = match.group().strip()
                if first_sentence:
                    return first_sentence
                    
            # 如果没有找到句号，取前100个字符
            first_sentence = content[:100]
            if len(content) > 100:
                first_sentence += "..."
                
            return first_sentence
            
        except Exception as e:
            print(f"❌ 提取第一句话失败 {file_path.name}: {e}")
            return None
    
    async def generate_cover(self, account, template, title: str, output_path: Path) -> bool:
        """生成封面"""
        try:
            # 优先使用优化版本的封面生成服务
            try:
                from src.services.optimized_cover_screenshot_service import OptimizedCoverScreenshotService
                cover_service = OptimizedCoverScreenshotService()
                success = await cover_service.generate_cover_screenshot(
                    template_id=template.id,
                    account=account,
                    title=title,
                    output_path=str(output_path),
                    db=self.db,
                    additional_variables=None,
                    fast_mode=False
                )
            except ImportError:
                # 如果没有优化版本，使用原版本
                from src.services.cover_screenshot_service import cover_screenshot_service
                success = await cover_screenshot_service.generate_cover_screenshot(
                    template_id=template.id,
                    account=account,
                    title=title,
                    output_path=str(output_path),
                    db=self.db,
                    additional_variables=None,
                    fast_mode=False
                )
            
            if success and output_path.exists():
                file_size = output_path.stat().st_size
                print(f"✅ 封面生成成功: {output_path.name} ({file_size} bytes)")
                return True
            else:
                print(f"❌ 封面生成失败: {output_path.name}")
                return False
                
        except Exception as e:
            print(f"❌ 封面生成异常 {output_path.name}: {e}")
            return False
    
    async def batch_generate_covers(self, account_name: str, scripts_directory: str, template_name: str):
        """批量生成封面"""
        print(f"=== 批量生成封面 ===")
        print(f"账号名称: {account_name}")
        print(f"文案目录: {scripts_directory}")
        print(f"封面模板: {template_name}")
        print("=" * 50)
        
        # 初始化服务
        if not await self.init_services():
            return
            
        # 查找账号
        account = self.find_account_by_name(account_name)
        if not account:
            print(f"❌ 未找到账号: {account_name}")
            self.list_available_accounts()
            return
            
        # 查找模板
        template = self.find_template_by_name(template_name)
        if not template:
            print(f"❌ 未找到模板: {template_name}")
            self.list_available_templates()
            return
            
        # 获取txt文件列表
        txt_files = self.get_txt_files(scripts_directory)
        if not txt_files:
            return
            
        print(f"✅ 使用账号: {account.name}")
        print(f"✅ 使用模板: {template.name}")
        print(f"✅ 处理文件数: {len(txt_files)}")
        print("-" * 50)
        
        # 统计信息
        success_count = 0
        failed_count = 0
        
        # 处理每个txt文件
        for i, txt_file in enumerate(txt_files, 1):
            print(f"[{i}/{len(txt_files)}] 处理文件: {txt_file.name}")
            
            # 提取第一句话
            first_sentence = self.extract_first_sentence(txt_file)
            if not first_sentence:
                failed_count += 1
                continue
                
            print(f"  标题: {first_sentence[:50]}{'...' if len(first_sentence) > 50 else ''}")
            
            # 生成输出路径
            output_name = txt_file.stem + ".png"
            output_path = txt_file.parent / output_name
            
            # 检查文件是否已存在
            if output_path.exists():
                print(f"  ⚠️  文件已存在，跳过: {output_name}")
                continue
                
            # 生成封面
            success = await self.generate_cover(account, template, first_sentence, output_path)
            if success:
                success_count += 1
                
                # 同样将封面复制到exports目录
                try:
                    exports_dir = output_path.parent / "exports"
                    exports_dir.mkdir(exist_ok=True)
                    
                    # 使用与backend中相同的命名规则
                    export_cover_path = exports_dir / output_path.name
                    import shutil
                    shutil.copy2(output_path, export_cover_path)
                    print(f"  📤 已导出到exports目录: {export_cover_path.name}")
                    
                except Exception as e:
                    print(f"  ⚠️  导出到exports目录失败: {e}")
                    
            else:
                failed_count += 1
                
            print()  # 空行分隔
            
        # 打印统计结果
        print("=" * 50)
        print(f"📊 生成统计:")
        print(f"  成功: {success_count}")
        print(f"  失败: {failed_count}")
        print(f"  总计: {len(txt_files)}")
        if len(txt_files) > 0:
            print(f"  成功率: {success_count/len(txt_files)*100:.1f}%")
        
        # 关闭数据库连接
        if self.db:
            self.db.close()
    
    def list_available_accounts(self):
        """列出可用的账号"""
        try:
            from src.models.accounts import Account
            
            accounts = self.db.query(Account).all()
            if not accounts:
                print("❌ 没有找到任何账号")
                return
                
            print("\n📋 可用账号列表:")
            for account in accounts:
                print(f"  - {account.name} (ID: {account.id})")
                
        except Exception as e:
            print(f"❌ 获取账号列表失败: {e}")
    
    def list_available_templates(self):
        """列出可用的模板"""
        try:
            from src.models.resources import CoverTemplate
            
            templates = self.db.query(CoverTemplate).all()
            if not templates:
                print("❌ 没有找到任何模板")
                return
                
            print("\n📋 可用模板列表:")
            for template in templates:
                print(f"  - {template.name} (ID: {template.id})")
                
        except Exception as e:
            print(f"❌ 获取模板列表失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="批量生成封面图片")
    parser.add_argument("--account", "-a", help="账号名称")
    parser.add_argument("--directory", "-d", help="文案目录路径")
    parser.add_argument("--template", "-t", help="封面模板名称")
    parser.add_argument("--list-accounts", action="store_true", help="列出可用账号")
    parser.add_argument("--list-templates", action="store_true", help="列出可用模板")
    
    args = parser.parse_args()
    
    generator = SimpleCoverGenerator()
    
    # 如果需要列出账号或模板
    if args.list_accounts or args.list_templates:
        try:
            asyncio.run(generator.init_services())
            
            if args.list_accounts:
                generator.list_available_accounts()
            if args.list_templates:
                generator.list_available_templates()
                
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
        finally:
            if generator.db:
                generator.db.close()
        return
    
    # 检查必需参数
    if not args.account or not args.directory or not args.template:
        print("❌ 缺少必需参数")
        print("使用方法:")
        print("  python simple_cover_generator.py -a 账号名称 -d 文案目录 -t 模板名称")
        print("  python simple_cover_generator.py --list-accounts  # 列出可用账号")
        print("  python simple_cover_generator.py --list-templates  # 列出可用模板")
        return
    
    # 执行批量生成
    try:
        asyncio.run(generator.batch_generate_covers(
            account_name=args.account,
            scripts_directory=args.directory,
            template_name=args.template
        ))
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
