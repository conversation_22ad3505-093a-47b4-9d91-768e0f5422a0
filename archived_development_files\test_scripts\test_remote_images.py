#!/usr/bin/env python3
"""
测试模板导入服务的远程图片处理功能
"""

import os
import sys
from pathlib import Path
import tempfile

# 添加backend到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from src.services.template_import_service import TemplateImportService

def create_test_html_with_remote_images():
    """创建包含远程图片的测试HTML"""
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>测试模板</title>
</head>
<body>
    <div class="container">
        <h1>{{title}}</h1>
        <div class="user">
            <img src="{{avatar}}" alt="用户头像" />
            <span>{{username}}</span>
        </div>
        <div class="icons">
            <!-- 本地图片 -->
            <img src="images/local1.png" alt="本地图片1" />
            <!-- 远程图片 -->
            <img src="https://via.placeholder.com/32x32/FF0000/FFFFFF?text=1" alt="远程图片1" />
            <img src="https://via.placeholder.com/32x32/00FF00/FFFFFF?text=2" alt="远程图片2" />
            <img src="https://via.placeholder.com/32x32/0000FF/FFFFFF?text=3" alt="远程图片3" />
            <!-- 协议相对URL -->
            <img src="//via.placeholder.com/32x32/FFFF00/000000?text=4" alt="协议相对URL" />
            <!-- Data URI (应该被过滤) -->
            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="Data URI" />
        </div>
    </div>
</body>
</html>'''
    
    return html_content

def test_remote_image_processing():
    """测试远程图片处理功能"""
    print("=== 测试远程图片处理功能 ===")
    
    # 创建临时目录和HTML文件
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建本地图片目录和文件
        images_dir = temp_path / "images"
        images_dir.mkdir()
        
        # 创建一个简单的本地图片文件（1x1像素PNG）
        local_img_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\rIDATx\xdac\xf8\x0f\x00\x01\x01\x01\x00\x18\xdd\x8d\xb4\x00\x00\x00\x00IEND\xaeB`\x82'
        with open(images_dir / "local1.png", 'wb') as f:
            f.write(local_img_content)
        
        # 创建测试HTML文件
        html_file = temp_path / "test_template.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(create_test_html_with_remote_images())
        
        print(f"✅ 创建测试HTML文件: {html_file}")
        
        # 创建服务实例
        service = TemplateImportService()
        
        # 读取HTML内容
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 提取变量
        variables = service.extract_variables_from_html(html_content)
        print(f"📋 提取的变量: {variables}")
        
        # 提取图片路径
        image_paths = service.extract_image_paths_from_html(html_content)
        print(f"🖼️  提取的图片路径: {image_paths}")
        
        # 生成模板ID
        from uuid import uuid4
        template_id = str(uuid4())
        
        # 处理图片资源
        print("📥 开始处理图片资源...")
        path_mapping = service.copy_template_images(str(temp_path), template_id, image_paths)
        
        print(f"🔄 路径映射结果:")
        for old_path, new_path in path_mapping.items():
            print(f"   {old_path} -> {new_path}")
        
        # 更新HTML内容
        updated_html = service.update_html_image_paths(html_content, path_mapping)
        
        # 显示更新后的图片路径
        updated_image_paths = service.extract_image_paths_from_html(updated_html)
        print(f"🎯 更新后的图片路径: {updated_image_paths}")
        
        # 检查生成的文件
        template_images_dir = service.templates_dir / f"{template_id}_images"
        if template_images_dir.exists():
            print(f"📁 图片目录已创建: {template_images_dir}")
            
            # 遍历并显示所有复制/下载的文件
            for root, dirs, files in os.walk(template_images_dir):
                for file in files:
                    file_path = Path(root) / file
                    rel_path = file_path.relative_to(template_images_dir)
                    file_size = file_path.stat().st_size
                    print(f"   📷 {rel_path} ({file_size} bytes)")
        
        return True

def main():
    """主函数"""
    try:
        success = test_remote_image_processing()
        
        if success:
            print("\n🎉 远程图片处理测试成功！")
        else:
            print("\n💥 远程图片处理测试失败！")
            
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
