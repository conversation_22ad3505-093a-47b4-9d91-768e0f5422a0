@echo off
REM 快速验证 EXE 打包配置

echo ====================================
echo EXE 打包配置快速验证
echo ====================================
echo.

echo [1/4] 测试配置系统...
python test_config.py
if errorlevel 1 (
    echo 配置测试失败
    pause
    exit /b 1
)

echo.
echo [2/4] 尝试构建前端...
cd frontend
call npm run build:production
echo 检查前端构建输出...
if exist "out" (
    echo ✅ 前端构建成功，out 目录已创建
    dir out
) else (
    echo ❌ 前端构建可能失败，out 目录不存在
    echo 检查 .next 目录...
    if exist ".next" (
        echo .next 目录存在，可能需要检查配置
    )
)

cd ..

echo.
echo [3/4] 测试后端生产配置...
echo 启动后端（按 Ctrl+C 停止）...
cd backend
set ENVIRONMENT=production
start "Backend Test" cmd /c "python main.py & pause"

echo.
echo [4/4] 验证完成
echo.
echo 打开浏览器访问 http://localhost:8000 测试后端
echo 如果一切正常，可以运行 build_exe.bat 构建完整的 EXE
echo.
pause
