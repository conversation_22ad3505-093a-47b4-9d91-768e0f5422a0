#!/usr/bin/env python3
"""
前后端联调测试脚本
用于验证后端API与前端Zustand store的数据结构一致性
"""

import requests
import json
import time
from typing import Dict, Any, Optional

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

class APITester:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })

    def test_health_check(self) -> bool:
        """测试API健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/../health")
            return response.status_code == 200
        except:
            return False

    def test_settings_api(self) -> Dict[str, Any]:
        """测试设置API"""
        results = {
            "name": "Settings API",
            "passed": 0,
            "failed": 0,
            "tests": []
        }

        # 1. 获取设置
        try:
            response = self.session.get(f"{self.base_url}/settings")
            if response.status_code == 200:
                data = response.json()
                results["tests"].append({"name": "GET /settings", "status": "✅ PASS", "data": data})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "GET /settings", "status": f"❌ FAIL ({response.status_code})"})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "GET /settings", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        # 2. 更新设置
        try:
            update_data = {
                "general": {
                    "theme": "dark",
                    "language": "zh"
                },
                "tts": {
                    "provider": "openai",
                    "voice": "alloy"
                }
            }
            response = self.session.put(f"{self.base_url}/settings", json=update_data)
            if response.status_code == 200:
                results["tests"].append({"name": "PUT /settings", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "PUT /settings", "status": f"❌ FAIL ({response.status_code})"})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "PUT /settings", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        return results

    def test_background_music_api(self) -> Dict[str, Any]:
        """测试背景音乐API"""
        results = {
            "name": "Background Music API",
            "passed": 0,
            "failed": 0,
            "tests": []
        }

        # 1. 获取音乐列表
        try:
            response = self.session.get(f"{self.base_url}/resources/background-music/")
            if response.status_code == 200:
                results["tests"].append({"name": "GET /resources/background-music/", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "GET /resources/background-music/", "status": f"❌ FAIL ({response.status_code})"})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "GET /resources/background-music/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        # 2. 创建音乐
        try:
            music_data = {
                "name": "Test Music",
                "file_path": "/test/music.mp3",
                "duration": 120.5,
                "category": "test",
                "tags": ["test", "demo"],
                "is_built_in": False,
                "file_size": 1024000,
                "format": "mp3",
                "bitrate": 320,
                "sample_rate": 44100
            }
            response = self.session.post(f"{self.base_url}/resources/background-music/", json=music_data)
            if response.status_code == 200:
                created_music = response.json()
                results["tests"].append({"name": "POST /resources/background-music/", "status": "✅ PASS", "id": created_music.get("data", {}).get("id")})
                results["passed"] += 1
                results["created_id"] = created_music.get("data", {}).get("id")
            else:
                results["tests"].append({"name": "POST /resources/background-music/", "status": f"❌ FAIL ({response.status_code})", "error": response.text})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "POST /resources/background-music/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        # 3. 获取分类列表
        try:
            response = self.session.get(f"{self.base_url}/resources/background-music/categories")
            if response.status_code == 200:
                results["tests"].append({"name": "GET /resources/background-music/categories", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "GET /resources/background-music/categories", "status": f"❌ FAIL ({response.status_code})"})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "GET /resources/background-music/categories", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        return results

    def test_video_material_api(self) -> Dict[str, Any]:
        """测试视频素材API"""
        results = {
            "name": "Video Material API",
            "passed": 0,
            "failed": 0,
            "tests": []
        }

        # 1. 获取视频素材列表
        try:
            response = self.session.get(f"{self.base_url}/resources/video-materials/")
            if response.status_code == 200:
                results["tests"].append({"name": "GET /resources/video-materials/", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "GET /resources/video-materials/", "status": f"❌ FAIL ({response.status_code})"})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "GET /resources/video-materials/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        # 2. 创建视频素材
        try:
            video_data = {
                "name": "Test Video",
                "file_path": "/test/video.mp4",
                "duration": 300.0,
                "category": "test",
                "resolution": "1920x1080",
                "tags": ["test", "demo"],
                "is_built_in": False,
                "file_size": 50000000,
                "format": "mp4",
                "frame_rate": 30.0,
                "bitrate": 5000,
                "thumbnail_path": "/test/thumb.jpg"
            }
            response = self.session.post(f"{self.base_url}/resources/video-materials/", json=video_data)
            if response.status_code == 200:
                results["tests"].append({"name": "POST /resources/video-materials/", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "POST /resources/video-materials/", "status": f"❌ FAIL ({response.status_code})", "error": response.text})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "POST /resources/video-materials/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        return results

    def test_prompt_api(self) -> Dict[str, Any]:
        """测试提示词API"""
        results = {
            "name": "Prompt API",
            "passed": 0,
            "failed": 0,
            "tests": []
        }

        # 1. 获取提示词列表
        try:
            response = self.session.get(f"{self.base_url}/resources/prompts/")
            if response.status_code == 200:
                results["tests"].append({"name": "GET /resources/prompts/", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "GET /resources/prompts/", "status": f"❌ FAIL ({response.status_code})"})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "GET /resources/prompts/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        # 2. 创建提示词
        try:
            prompt_data = {
                "name": "Test Prompt",
                "content": "Generate a story about {topic}",
                "category": "test",
                "variables": ["topic"],
                "is_built_in": False,
                "description": "Test prompt for API validation"
            }
            response = self.session.post(f"{self.base_url}/resources/prompts/", json=prompt_data)
            if response.status_code == 200:
                results["tests"].append({"name": "POST /resources/prompts/", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "POST /resources/prompts/", "status": f"❌ FAIL ({response.status_code})", "error": response.text})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "POST /resources/prompts/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        return results

    def test_account_api(self) -> Dict[str, Any]:
        """测试账户API"""
        results = {
            "name": "Account API",
            "passed": 0,
            "failed": 0,
            "tests": []
        }

        # 1. 获取账户列表
        try:
            response = self.session.get(f"{self.base_url}/resources/accounts/")
            if response.status_code == 200:
                results["tests"].append({"name": "GET /resources/accounts/", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "GET /resources/accounts/", "status": f"❌ FAIL ({response.status_code})"})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "GET /resources/accounts/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        # 2. 创建账户
        try:
            account_data = {
                "name": "Test Account",
                "platform": "youtube",
                "is_active": True,
                "config": {
                    "channel_id": "test123",
                    "upload_defaults": {
                        "privacy": "private"
                    }
                }
            }
            response = self.session.post(f"{self.base_url}/resources/accounts/", json=account_data)
            if response.status_code == 200:
                results["tests"].append({"name": "POST /resources/accounts/", "status": "✅ PASS"})
                results["passed"] += 1
            else:
                results["tests"].append({"name": "POST /resources/accounts/", "status": f"❌ FAIL ({response.status_code})", "error": response.text})
                results["failed"] += 1
        except Exception as e:
            results["tests"].append({"name": "POST /resources/accounts/", "status": f"❌ ERROR: {e}"})
            results["failed"] += 1

        return results

    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有API测试"""
        print("🧪 Starting Frontend-Backend Integration Tests...\n")
        
        # 检查服务器是否运行
        if not self.test_health_check():
            print("❌ Backend server is not running at http://localhost:8000")
            print("Please start the server first with: python start_server.py")
            return {"success": False, "error": "Server not available"}

        print("✅ Backend server is running\n")

        # 运行各个API测试
        all_results = []
        
        test_functions = [
            self.test_settings_api,
            self.test_background_music_api,
            self.test_video_material_api,
            self.test_prompt_api,
            self.test_account_api
        ]

        total_passed = 0
        total_failed = 0

        for test_func in test_functions:
            result = test_func()
            all_results.append(result)
            total_passed += result["passed"]
            total_failed += result["failed"]
            
            # 打印结果
            print(f"📊 {result['name']}:")
            for test in result["tests"]:
                print(f"   {test['status']} {test['name']}")
            print(f"   Summary: {result['passed']} passed, {result['failed']} failed\n")

        # 总结
        print("="*50)
        print(f"🎯 Integration Test Summary:")
        print(f"   Total Tests: {total_passed + total_failed}")
        print(f"   ✅ Passed: {total_passed}")
        print(f"   ❌ Failed: {total_failed}")
        print(f"   Success Rate: {(total_passed/(total_passed+total_failed)*100):.1f}%" if (total_passed + total_failed) > 0 else "N/A")

        return {
            "success": total_failed == 0,
            "total_tests": total_passed + total_failed,
            "passed": total_passed,
            "failed": total_failed,
            "results": all_results
        }

def main():
    """主函数"""
    tester = APITester()
    results = tester.run_all_tests()
    
    if results.get("success"):
        print("\n🎉 All integration tests passed! Frontend-backend integration is ready.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please check the API implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
