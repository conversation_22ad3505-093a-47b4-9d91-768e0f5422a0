/**
 * 视频生成状态管理
 * 管理视频生成配置、任务队列和进度跟踪
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { 
  GenerationState, 
  GenerationConfig, 
  GenerationTask 
} from '../types/store'
import DirectHttpClient from '../lib/api/directHttpClient'

// 默认生成配置
const defaultGenerationConfig: GenerationConfig = {
  selectedPrompt: '',
  selectedAccount: '',
  selectedBackgroundMusic: '',
  selectedVideoMaterials: [],
  selectedCoverTemplate: '',
  
  // 视频设置
  videoDuration: 60,
  videoResolution: '1080p',
  frameRate: 30,
  
  // 音频设置
  backgroundMusicVolume: 0.3,
  voiceVolume: 1.0,
  
  // 字幕设置
  showSubtitles: true,
  subtitleStyle: {
    fontFamily: 'Arial',
    fontSize: 24,
    color: '#FFFFFF',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    position: 'bottom'
  }
}

export const useGenerationStore = create<GenerationState>()(
  persist(
    (set, get) => ({
      // 初始状态
      config: defaultGenerationConfig,
      tasks: [],
      currentTask: undefined,

      // 更新配置
      updateConfig: (configUpdates: Partial<GenerationConfig>) => {
        set((state) => ({
          config: { ...state.config, ...configUpdates }
        }))
      },

      // 创建新任务
      createTask: (config: GenerationConfig) => {
        const taskId = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        
        const newTask: GenerationTask = {
          id: taskId,
          config: { ...config },
          status: 'pending',
          progress: 0,
          currentStep: '准备开始...',
          startTime: new Date()
        }

        set((state) => ({
          tasks: [...state.tasks, newTask],
          currentTask: taskId
        }))

        // 开始生成过程
        get().startGeneration(taskId)
        
        return taskId
      },

      // 更新任务进度
      updateTaskProgress: (taskId: string, progress: number, step: string) => {
        set((state) => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? { ...task, progress: Math.min(100, Math.max(0, progress)), currentStep: step }
              : task
          )
        }))
      },

      // 更新任务状态
      updateTaskStatus: (
        taskId: string, 
        status: GenerationTask['status'], 
        error?: string
      ) => {
        set((state) => ({
          tasks: state.tasks.map(task =>
            task.id === taskId
              ? { 
                  ...task, 
                  status, 
                  error,
                  endTime: status === 'completed' || status === 'failed' ? new Date() : task.endTime,
                  progress: status === 'completed' ? 100 : task.progress
                }
              : task
          ),
          currentTask: status === 'completed' || status === 'failed' ? undefined : state.currentTask
        }))
      },

      // 移除任务
      removeTask: (taskId: string) => {
        set((state) => ({
          tasks: state.tasks.filter(task => task.id !== taskId),
          currentTask: state.currentTask === taskId ? undefined : state.currentTask
        }))
      },

      // 清除已完成的任务
      clearCompletedTasks: () => {
        set((state) => ({
          tasks: state.tasks.filter(task => 
            task.status !== 'completed' && task.status !== 'failed'
          )
        }))
      },

      // 开始生成过程（内部方法）
      startGeneration: async (taskId: string) => {
        const task = get().tasks.find(t => t.id === taskId)
        if (!task) return

        try {
          // 更新状态为处理中
          get().updateTaskStatus(taskId, 'processing')
          get().updateTaskProgress(taskId, 5, '验证配置...')

          // 验证配置
          const validation = generationUtils.validateConfig(task.config)
          if (!validation.isValid) {
            throw new Error(validation.errors.join(', '))
          }

          // 开始生成流程
          await generationUtils.generateVideo(taskId, task.config, {
            onProgress: (progress, step) => {
              get().updateTaskProgress(taskId, progress, step)
            }
          })

          // 完成
          get().updateTaskStatus(taskId, 'completed')
          
        } catch (error) {
          console.error('Generation failed:', error)
          get().updateTaskStatus(
            taskId, 
            'failed', 
            error instanceof Error ? error.message : 'Unknown error'
          )
        }
      }
    }),
    {
      name: 'reddit-story-generator-generation',
      storage: createJSONStorage(() => localStorage),
      
      // 部分持久化：保存配置和非处理中的任务
      partialize: (state) => ({
        config: state.config,
        tasks: state.tasks.filter(task => 
          task.status === 'pending' || task.status === 'completed' || task.status === 'failed'
        ),
        // 不保存currentTask，重启后需要重新开始
      }),
      
      version: 1,
      
      // 版本迁移
      migrate: (persistedState: any, version: number) => {
        if (version === 0) {
          return {
            ...persistedState,
            config: {
              ...defaultGenerationConfig,
              ...persistedState.config,
            }
          }
        }
        return persistedState
      },
    }
  )
)

// 视频生成工具函数
export const generationUtils = {
  // 验证生成配置
  validateConfig: (config: GenerationConfig): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (!config.selectedPrompt) {
      errors.push('请选择提示词模板')
    }

    if (!config.selectedAccount) {
      errors.push('请选择发布账户')
    }

    if (!config.selectedBackgroundMusic) {
      errors.push('请选择背景音乐')
    }

    if (config.selectedVideoMaterials.length === 0) {
      errors.push('请至少选择一个视频素材')
    }

    if (!config.selectedCoverTemplate) {
      errors.push('请选择封面模板')
    }

    if (config.videoDuration < 10 || config.videoDuration > 600) {
      errors.push('视频时长必须在10-600秒之间')
    }

    if (config.backgroundMusicVolume < 0 || config.backgroundMusicVolume > 1) {
      errors.push('背景音乐音量必须在0-1之间')
    }

    if (config.voiceVolume < 0 || config.voiceVolume > 1) {
      errors.push('语音音量必须在0-1之间')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  // 生成视频
  generateVideo: async (
    taskId: string, 
    config: GenerationConfig,
    callbacks: {
      onProgress: (progress: number, step: string) => void
    }
  ): Promise<void> => {
    const { onProgress } = callbacks
    const generationClient = new DirectHttpClient('/api/generation')

    try {
      // 步骤1: 生成故事内容 (10-20%)
      onProgress(10, '正在生成故事内容...')
      
      const storyData = await generationClient.post<any>('/story', {
        promptId: config.selectedPrompt,
        config: config
      })
      onProgress(20, '故事内容生成完成')

      // 步骤2: 生成语音 (20-40%)
      onProgress(25, '正在生成语音...')
      
      const audioData = await generationClient.post<any>('/audio', {
        text: storyData.content,
        config: config
      })
      onProgress(40, '语音生成完成')

      // 步骤3: 准备视频素材 (40-60%)
      onProgress(45, '正在准备视频素材...')
      
      const materialsData = await generationClient.post<any>('/prepare-materials', {
        materialIds: config.selectedVideoMaterials,
        duration: config.videoDuration,
        config: config
      })
      onProgress(60, '视频素材准备完成')

      // 步骤4: 合成视频 (60-90%)
      onProgress(65, '正在合成视频...')
      
      const videoResponse = await generationClient.postRaw('/compose', {
        taskId: taskId,
        storyData: storyData,
        audioData: audioData,
        materialsData: materialsData,
        config: config
      })

      // 监听合成进度
      const reader = videoResponse.data?.getReader?.()
      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const text = new TextDecoder().decode(value)
          const lines = text.split('\n').filter(line => line.startsWith('data: '))
          
          for (const line of lines) {
            try {
              const data = JSON.parse(line.substring(6))
              if (data.progress) {
                onProgress(60 + (data.progress * 0.3), data.step || '正在合成视频...')
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }

      onProgress(90, '视频合成完成')

      // 步骤5: 生成封面 (90-95%)
      onProgress(92, '正在生成封面...')
      
      await generationClient.post<any>('/cover', {
        templateId: config.selectedCoverTemplate,
        storyData: storyData,
        config: config
      })
      onProgress(95, '封面生成完成')

      // 步骤6: 完成处理 (95-100%)
      onProgress(98, '正在完成最后处理...')
      
      // 等待一小段时间确保所有处理完成
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      onProgress(100, '视频生成完成!')

    } catch (error) {
      console.error('Video generation error:', error)
      throw error
    }
  },

  // 取消生成任务
  cancelGeneration: async (taskId: string): Promise<void> => {
    try {
      const generationClient = new DirectHttpClient('/api/generation')
      await generationClient.post(`/cancel/${taskId}`)
    } catch (error) {
      console.error('Failed to cancel generation:', error)
    }
  },

  // 获取任务详情
  getTaskDetails: async (taskId: string): Promise<any> => {
    try {
      const generationClient = new DirectHttpClient('/api/generation')
      return await generationClient.get<any>(`/task/${taskId}`)
    } catch (error) {
      console.error('Failed to get task details:', error)
      return null
    }
  },

  // 格式化时间
  formatDuration: (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 计算预估完成时间
  estimateTimeRemaining: (task: GenerationTask): string => {
    if (!task.startTime || task.status !== 'processing') {
      return '--'
    }

    const elapsed = Date.now() - task.startTime.getTime()
    const progressRatio = task.progress / 100
    
    if (progressRatio <= 0) {
      return '--'
    }

    const totalEstimated = elapsed / progressRatio
    const remaining = totalEstimated - elapsed
    
    if (remaining <= 0) {
      return '即将完成'
    }

    const remainingMinutes = Math.ceil(remaining / (1000 * 60))
    return `约 ${remainingMinutes} 分钟`
  }
}

export type { GenerationState, GenerationConfig, GenerationTask }
