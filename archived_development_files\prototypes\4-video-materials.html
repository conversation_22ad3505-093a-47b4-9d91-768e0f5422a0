<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit故事视频生成器 - 视频素材管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: all 0.3s ease;
        }

        /* 主题变量定义 */
        :root {
            /* 蓝色主题（默认） */
            --theme-primary: #2563eb;
            --theme-primary-hover: #1d4ed8;
            --theme-primary-light: #eff6ff;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f9fafb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #374151;
            --border-primary: #e5e7eb;
            --border-secondary: #d1d5db;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
        }

        /* 绿色主题 */
        [data-theme="green"] {
            --theme-primary: #059669;
            --theme-primary-hover: #047857;
            --theme-primary-light: #ecfdf5;
        }

        /* 紫色主题 */
        [data-theme="purple"] {
            --theme-primary: #7c3aed;
            --theme-primary-hover: #6d28d9;
            --theme-primary-light: #f3f4f6;
        }

        /* 橙色主题 */
        [data-theme="orange"] {
            --theme-primary: #ea580c;
            --theme-primary-hover: #dc2626;
            --theme-primary-light: #fff7ed;
        }

        /* 红色主题 */
        [data-theme="red"] {
            --theme-primary: #dc2626;
            --theme-primary-hover: #b91c1c;
            --theme-primary-light: #fef2f2;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 32px;
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .page-description {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .btn-primary:hover {
            background: var(--theme-primary-hover);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-tertiary);
            border-color: var(--border-secondary);
        }

        .btn-secondary:hover {
            border-color: var(--theme-primary);
            color: var(--theme-primary);
        }

        .btn-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--theme-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 工具栏 */
        .toolbar {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 8px 36px 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            color: var(--text-secondary);
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 12px;
            background: var(--bg-secondary);
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .view-btn.active {
            background: var(--theme-primary);
            color: white;
        }

        .view-btn:not(.active):hover {
            background: var(--bg-tertiary);
        }

        /* 分类标签 */
        .category-tabs {
            display: flex;
            gap: 2px;
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
        }

        .category-tab {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            background: transparent;
            color: var(--text-secondary);
        }

        .category-tab.active {
            background: var(--theme-primary);
            color: white;
        }

        .category-tab:not(.active):hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        /* 拖拽上传区域 */
        .upload-zone {
            background: var(--bg-secondary);
            border: 2px dashed var(--border-secondary);
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 24px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .upload-zone:hover, .upload-zone.dragover {
            border-color: var(--theme-primary);
            background: var(--theme-primary-light);
        }

        .upload-icon {
            width: 48px;
            height: 48px;
            color: var(--text-secondary);
            margin: 0 auto 16px;
        }

        .upload-text {
            font-size: 16px;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 素材网格 */
        .materials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }

        /* 列表视图样式 */
        .materials-list {
            display: block;
        }

        .materials-list .material-card {
            display: flex;
            margin-bottom: 12px;
            height: 100px;
        }

        .materials-list .material-preview {
            width: 120px;
            height: 100px;
            flex-shrink: 0;
        }

        .materials-list .material-info {
            flex: 1;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .materials-list .material-details {
            flex: 1;
        }

        .materials-list .material-title {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .materials-list .material-meta {
            font-size: 14px;
            margin-bottom: 8px;
        }

        .materials-list .material-tags {
            margin-bottom: 0;
        }

        .materials-list .material-actions {
            flex-shrink: 0;
            margin-left: 16px;
        }

        .material-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-primary);
            overflow: hidden;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .material-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .material-preview {
            width: 100%;
            height: 180px;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-tertiary);
        }

        .material-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .material-type-icon {
            width: 48px;
            height: 48px;
            color: var(--text-secondary);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .material-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        .material-type-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: var(--theme-primary);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .video-badge {
            background: var(--theme-primary);
        }

        .image-badge {
            background: var(--success-color);
        }

        .gif-badge {
            background: var(--warning-color);
        }

        /* 在列表视图中隐藏时长和类型标识（因为已在meta信息中显示） */
        .materials-list .material-duration,
        .materials-list .material-type-badge {
            display: none;
        }

        .material-info {
            padding: 16px;
        }

        .material-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .material-meta {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .material-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-bottom: 12px;
        }

        .material-tag {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }

        .material-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .action-btn-preview {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .action-btn-preview:hover {
            background: var(--theme-primary-hover);
        }

        .action-btn-edit {
            background: transparent;
            color: var(--text-secondary);
            border-color: var(--border-secondary);
        }

        .action-btn-edit:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .action-btn-delete {
            background: transparent;
            color: var(--error-color);
            border-color: var(--error-color);
        }

        .action-btn-delete:hover {
            background: var(--error-color);
            color: white;
        }

        .action-icon {
            width: 12px;
            height: 12px;
            fill: currentColor;
        }

        /* 预览模态框 */
        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .preview-content {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 24px;
            max-width: 90vw;
            max-height: 90vh;
            position: relative;
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .preview-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .preview-close {
            width: 24px;
            height: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            border: none;
            background: none;
        }

        .preview-media {
            max-width: 100%;
            max-height: 60vh;
            border-radius: 6px;
        }

        .preview-info {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid var(--border-primary);
        }

        .preview-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            font-size: 14px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
        }

        .meta-label {
            color: var(--text-secondary);
            margin-bottom: 2px;
        }

        .meta-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            color: var(--text-secondary);
        }

        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .toolbar-left,
            .toolbar-right {
                justify-content: space-between;
            }

            .search-input {
                width: 100%;
            }

            .category-tabs {
                flex-wrap: wrap;
            }

            .materials-grid {
                grid-template-columns: 1fr;
            }

            .preview-content {
                margin: 16px;
                padding: 16px;
            }

            .preview-meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title-section">
                <h1 class="page-title">视频素材管理</h1>
                <p class="page-description">管理视频生成所需的视觉素材，包括视频片段、图片和GIF动画</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-secondary">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    导出列表
                </button>
                <button class="btn btn-primary" id="importBtn">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                    </svg>
                    批量导入
                </button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">素材文件总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0 GB</div>
                <div class="stat-label">占用存储空间</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0/0/0</div>
                <div class="stat-label">视频/图片/GIF</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">MP4, JPG, GIF</div>
                <div class="stat-label">支持格式</div>
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="category-tabs">
            <button class="category-tab active" data-category="all">全部素材</button>
            <button class="category-tab" data-category="video">视频素材</button>
            <button class="category-tab" data-category="image">图片素材</button>
            <button class="category-tab" data-category="gif">GIF动画</button>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索素材文件名称或标签...">
                    <svg class="search-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <select class="filter-select">
                    <option value="all">全部尺寸</option>
                    <option value="vertical">竖屏 (9:16)</option>
                    <option value="square">方形 (1:1)</option>
                    <option value="horizontal">横屏 (16:9)</option>
                </select>
            </div>
            <div class="toolbar-right">
                <span style="font-size: 14px; color: var(--text-secondary);">显示方式:</span>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid">
                        <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                        </svg>
                    </button>
                    <button class="view-btn" data-view="list">
                        <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 拖拽上传区域 -->
        <div class="upload-zone" id="uploadZone">
            <svg class="upload-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <div class="upload-text">拖拽素材文件到这里，或点击选择文件夹</div>
            <div class="upload-hint">支持 MP4、MOV、JPG、PNG、GIF 格式，可批量上传</div>
        </div>

        <!-- 素材网格 -->
        <div class="materials-grid" id="materialsGrid">
            <!-- 空状态 -->
            <div class="empty-state" style="grid-column: 1 / -1;">
                <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
                <div class="empty-title">还没有视频素材</div>
                <div class="empty-description">
                    拖拽素材文件到上方区域，或点击"批量导入"按钮开始添加视频素材
                </div>
            </div>
        </div>
    </div>

    <!-- 预览模态框 -->
    <div class="preview-modal" id="previewModal">
        <div class="preview-content">
            <div class="preview-header">
                <div class="preview-title">素材预览</div>
                <button class="preview-close">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
            <div id="previewMediaContainer">
                <!-- 动态插入媒体元素 -->
            </div>
            <div class="preview-info">
                <div class="preview-meta" id="previewMeta">
                    <!-- 动态插入元数据 -->
                </div>
            </div>
        </div>
    </div>

    <input type="file" id="fileInput" multiple accept=".mp4,.mov,.avi,.jpg,.jpeg,.png,.gif" style="display: none;">

    <script>
        // 示例素材数据
        const sampleMaterials = [
            {
                id: 1,
                name: '城市夜景延时摄影.mp4',
                type: 'video',
                duration: '0:15',
                size: '25.6 MB',
                resolution: '1920x1080',
                aspectRatio: '16:9',
                format: 'MP4',
                tags: ['城市', '夜景', '延时'],
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDI4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyODAiIGhlaWdodD0iMTgwIiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0xNDAgODBMMTIwIDEwMEgxNjBMMTQwIDgwWiIgZmlsbD0iIzZCNzI4MCIvPgo8cGF0aCBkPSJNMTQwIDEwMEwxMjAgMTIwSDE2MEwxNDAgMTAwWiIgZmlsbD0iIzZCNzI4MCIvPgo8L3N2Zz4=',
                path: '/materials/sample1.mp4'
            },
            {
                id: 2,
                name: '抽象几何背景.jpg',
                type: 'image',
                size: '2.8 MB',
                resolution: '1080x1920',
                aspectRatio: '9:16',
                format: 'JPG',
                tags: ['抽象', '几何', '背景'],
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDI4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyODAiIGhlaWdodD0iMTgwIiBmaWxsPSIjMDU5NjY5Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjMDQ3ODU3Ii8+CjxyZWN0IHg9IjE4MCIgeT0iODAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iIzA0Nzg1NyIvPgo8L3N2Zz4=',
                path: '/materials/sample2.jpg'
            },
            {
                id: 3,
                name: '加载动画.gif',
                type: 'gif',
                duration: '2.5s',
                size: '856 KB',
                resolution: '400x400',
                aspectRatio: '1:1',
                format: 'GIF',
                tags: ['动画', '加载', '循环'],
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDI4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyODAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRUE1ODBDIi8+CjxjaXJjbGUgY3g9IjE0MCIgY3k9IjkwIiByPSIzMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjREMyNjI2IiBzdHJva2Utd2lkdGg9IjQiLz4KPHN2Zz4=',
                path: '/materials/sample3.gif'
            }
        ];

        let materialsList = [];
        let currentCategory = 'all';

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 恢复保存的主题
            const savedTheme = localStorage.getItem('theme') || 'blue';
            if (savedTheme !== 'blue') {
                document.documentElement.setAttribute('data-theme', savedTheme);
            }

            // 加载示例数据（实际使用时会从数据库加载）
            materialsList = [...sampleMaterials];
            renderMaterialsGrid();
            updateStats();
        });

        // 文件导入处理
        document.getElementById('importBtn').addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });

        // 拖拽上传
        const uploadZone = document.getElementById('uploadZone');

        uploadZone.addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });

        uploadZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        // 文件处理函数
        function handleFiles(files) {
            const validFiles = files.filter(file => {
                const ext = file.name.split('.').pop().toLowerCase();
                return ['mp4', 'mov', 'avi', 'jpg', 'jpeg', 'png', 'gif'].includes(ext);
            });

            if (validFiles.length === 0) {
                alert('请选择有效的素材文件 (MP4, MOV, AVI, JPG, PNG, GIF)');
                return;
            }

            // 处理每个文件
            validFiles.forEach(file => {
                const ext = file.name.split('.').pop().toLowerCase();
                let type = 'image';
                if (['mp4', 'mov', 'avi'].includes(ext)) {
                    type = 'video';
                } else if (ext === 'gif') {
                    type = 'gif';
                }

                // 创建URL用于预览
                const url = URL.createObjectURL(file);
                
                const materialItem = {
                    id: Date.now() + Math.random(),
                    name: file.name,
                    type: type,
                    size: formatFileSize(file.size),
                    format: ext.toUpperCase(),
                    tags: [],
                    thumbnail: type === 'image' || type === 'gif' ? url : generateVideoThumbnail(url),
                    path: url
                };

                // 如果是视频文件，尝试获取时长和分辨率
                if (type === 'video') {
                    const video = document.createElement('video');
                    video.addEventListener('loadedmetadata', function() {
                        materialItem.duration = formatDuration(video.duration);
                        materialItem.resolution = `${video.videoWidth}x${video.videoHeight}`;
                        materialItem.aspectRatio = getAspectRatio(video.videoWidth, video.videoHeight);
                        renderMaterialsGrid();
                        updateStats();
                    });
                    video.src = url;
                } else if (type === 'image' || type === 'gif') {
                    const img = new Image();
                    img.addEventListener('load', function() {
                        materialItem.resolution = `${img.width}x${img.height}`;
                        materialItem.aspectRatio = getAspectRatio(img.width, img.height);
                        if (type === 'gif') {
                            materialItem.duration = 'loop';
                        }
                        renderMaterialsGrid();
                        updateStats();
                    });
                    img.src = url;
                }
                
                materialsList.push(materialItem);
                renderMaterialsGrid();
                updateStats();
            });
        }

        // 渲染素材网格
        function renderMaterialsGrid() {
            const grid = document.getElementById('materialsGrid');
            const isListView = grid.classList.contains('materials-list');
            
            // 根据当前分类过滤
            const filteredMaterials = currentCategory === 'all' 
                ? materialsList 
                : materialsList.filter(material => material.type === currentCategory);
            
            if (filteredMaterials.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                        <div class="empty-title">还没有${getCategoryName(currentCategory)}</div>
                        <div class="empty-description">
                            拖拽素材文件到上方区域，或点击"批量导入"按钮开始添加视频素材
                        </div>
                    </div>
                `;
                return;
            }

            if (isListView) {
                // 列表视图模板
                grid.innerHTML = filteredMaterials.map(material => `
                    <div class="material-card">
                        <div class="material-preview">
                            ${generatePreviewElement(material)}
                        </div>
                        <div class="material-info">
                            <div class="material-details">
                                <div class="material-title" title="${material.name}">${material.name}</div>
                                <div class="material-meta">${material.format} • ${material.size} • ${material.resolution || 'Unknown'} ${material.duration ? '• ' + material.duration : ''}</div>
                                <div class="material-tags">
                                    ${material.tags.map(tag => `<span class="material-tag">${tag}</span>`).join('')}
                                </div>
                            </div>
                            <div class="material-actions">
                                <button class="action-btn action-btn-preview" onclick="previewMaterial('${material.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                    </svg>
                                    预览
                                </button>
                                <button class="action-btn action-btn-edit" onclick="editMaterial('${material.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                                    </svg>
                                    编辑
                                </button>
                                <button class="action-btn action-btn-delete" onclick="deleteMaterial('${material.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 112 0v4a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V8z" clip-rule="evenodd"/>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                // 网格视图模板
                grid.innerHTML = filteredMaterials.map(material => `
                    <div class="material-card">
                        <div class="material-preview">
                            ${generatePreviewElement(material)}
                            <div class="material-type-badge ${material.type}-badge">${getTypeName(material.type)}</div>
                            ${material.duration ? `<div class="material-duration">${material.duration}</div>` : ''}
                        </div>
                        <div class="material-info">
                            <div class="material-title" title="${material.name}">${material.name}</div>
                            <div class="material-meta">${material.format} • ${material.size} • ${material.resolution || 'Unknown'}</div>
                            <div class="material-tags">
                                ${material.tags.map(tag => `<span class="material-tag">${tag}</span>`).join('')}
                            </div>
                            <div class="material-actions">
                                <button class="action-btn action-btn-preview" onclick="previewMaterial('${material.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                    </svg>
                                    预览
                                </button>
                                <button class="action-btn action-btn-edit" onclick="editMaterial('${material.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                                    </svg>
                                    编辑
                                </button>
                                <button class="action-btn action-btn-delete" onclick="deleteMaterial('${material.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 112 0v4a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V8z" clip-rule="evenodd"/>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }

        // 生成预览元素
        function generatePreviewElement(material) {
            if (material.thumbnail && (material.type === 'image' || material.type === 'gif')) {
                return `<img src="${material.thumbnail}" alt="${material.name}" class="material-thumbnail">`;
            } else {
                // 使用图标作为默认预览
                const iconSvg = material.type === 'video' ? 
                    '<path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>' :
                    '<path d="M8 5v14l11-7z"/>';
                return `<svg class="material-type-icon" viewBox="0 0 20 20" fill="currentColor">${iconSvg}</svg>`;
            }
        }

        // 更新统计信息
        function updateStats() {
            const stats = document.querySelectorAll('.stat-value');
            
            // 总数
            stats[0].textContent = materialsList.length;
            
            // 总大小
            const totalBytes = materialsList.reduce((total, material) => {
                const sizeMatch = material.size.match(/(\d+\.?\d*)\s*(KB|MB|GB)/);
                if (sizeMatch) {
                    const value = parseFloat(sizeMatch[1]);
                    const unit = sizeMatch[2];
                    let bytes = value;
                    if (unit === 'KB') bytes *= 1024;
                    else if (unit === 'MB') bytes *= 1024 * 1024;
                    else if (unit === 'GB') bytes *= 1024 * 1024 * 1024;
                    return total + bytes;
                }
                return total;
            }, 0);
            
            if (totalBytes > 1024 * 1024 * 1024) {
                stats[1].textContent = `${(totalBytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
            } else if (totalBytes > 1024 * 1024) {
                stats[1].textContent = `${(totalBytes / (1024 * 1024)).toFixed(1)} MB`;
            } else {
                stats[1].textContent = `${(totalBytes / 1024).toFixed(1)} KB`;
            }
            
            // 类型统计
            const videoCount = materialsList.filter(m => m.type === 'video').length;
            const imageCount = materialsList.filter(m => m.type === 'image').length;
            const gifCount = materialsList.filter(m => m.type === 'gif').length;
            stats[2].textContent = `${videoCount}/${imageCount}/${gifCount}`;
        }

        // 分类切换
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 更新按钮状态
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                currentCategory = this.dataset.category;
                renderMaterialsGrid();
                
                console.log('切换到分类:', currentCategory);
            });
        });

        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const view = this.dataset.view;
                const grid = document.getElementById('materialsGrid');
                
                // 切换视图样式
                if (view === 'list') {
                    grid.classList.remove('materials-grid');
                    grid.classList.add('materials-list');
                } else {
                    grid.classList.remove('materials-list');
                    grid.classList.add('materials-grid');
                }
                
                // 重新渲染
                renderMaterialsGrid();
                
                console.log('切换到', view, '视图');
            });
        });

        // 预览素材
        function previewMaterial(id) {
            const material = materialsList.find(m => m.id == id);
            if (!material) return;
            
            const modal = document.getElementById('previewModal');
            const container = document.getElementById('previewMediaContainer');
            const meta = document.getElementById('previewMeta');
            
            // 清空容器
            container.innerHTML = '';
            
            // 生成预览媒体
            if (material.type === 'video') {
                const video = document.createElement('video');
                video.src = material.path;
                video.controls = true;
                video.className = 'preview-media';
                container.appendChild(video);
            } else {
                const img = document.createElement('img');
                img.src = material.thumbnail || material.path;
                img.alt = material.name;
                img.className = 'preview-media';
                container.appendChild(img);
            }
            
            // 生成元数据
            meta.innerHTML = `
                <div class="meta-item">
                    <div class="meta-label">文件名</div>
                    <div class="meta-value">${material.name}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">文件类型</div>
                    <div class="meta-value">${material.format}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">文件大小</div>
                    <div class="meta-value">${material.size}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">分辨率</div>
                    <div class="meta-value">${material.resolution || 'Unknown'}</div>
                </div>
                ${material.duration ? `
                <div class="meta-item">
                    <div class="meta-label">时长</div>
                    <div class="meta-value">${material.duration}</div>
                </div>
                ` : ''}
                <div class="meta-item">
                    <div class="meta-label">长宽比</div>
                    <div class="meta-value">${material.aspectRatio || 'Unknown'}</div>
                </div>
            `;
            
            modal.style.display = 'flex';
            document.querySelector('.preview-title').textContent = `素材预览 - ${material.name}`;
        }

        // 编辑素材
        function editMaterial(id) {
            // 这里将来会打开编辑对话框
            console.log('编辑素材:', id);
            alert('编辑功能开发中...');
        }

        // 删除素材
        function deleteMaterial(id) {
            if (confirm('确定要删除这个素材文件吗？')) {
                materialsList = materialsList.filter(material => material.id != id);
                renderMaterialsGrid();
                updateStats();
            }
        }

        // 关闭预览模态框
        document.querySelector('.preview-close').addEventListener('click', function() {
            document.getElementById('previewModal').style.display = 'none';
        });

        // 点击模态框背景关闭
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            // 这里将来会实现实际的搜索过滤
            console.log('搜索:', query);
        });

        // 辅助函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function formatDuration(seconds) {
            if (isNaN(seconds) || seconds < 0) return '--:--';
            
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        function getAspectRatio(width, height) {
            const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
            const divisor = gcd(width, height);
            return `${width/divisor}:${height/divisor}`;
        }

        function generateVideoThumbnail(videoUrl) {
            // 实际应用中会生成真正的视频缩略图
            return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDI4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyODAiIGhlaWdodD0iMTgwIiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0xNDAgODBMMTIwIDEwMEgxNjBMMTQwIDgwWiIgZmlsbD0iIzZCNzI4MCIvPgo8cGF0aCBkPSJNMTQwIDEwMEwxMjAgMTIwSDE2MEwxNDAgMTAwWiIgZmlsbD0iIzZCNzI4MCIvPgo8L3N2Zz4=';
        }

        function getCategoryName(category) {
            const names = {
                'all': '素材',
                'video': '视频素材',
                'image': '图片素材',
                'gif': 'GIF动画'
            };
            return names[category] || '素材';
        }

        function getTypeName(type) {
            const names = {
                'video': '视频',
                'image': '图片',
                'gif': 'GIF'
            };
            return names[type] || type;
        }
    </script>
</body>
</html>
