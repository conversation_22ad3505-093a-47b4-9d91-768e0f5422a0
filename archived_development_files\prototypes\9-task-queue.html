<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务队列管理 - Reddit故事视频生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stat-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stat-icon.total {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .stat-icon.running {
            background: #dcfce7;
            color: var(--success-color);
        }

        .stat-icon.pending {
            background: #fef3c7;
            color: var(--warning-color);
        }

        .stat-icon.failed {
            background: #fee2e2;
            color: var(--danger-color);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .controls-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .controls-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .search-container {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.625rem 2.5rem 0.625rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.875rem;
            background: var(--bg-secondary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .search-icon {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .filter-select {
            padding: 0.625rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.875rem;
            background: var(--bg-primary);
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .btn-group {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.625rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-primary);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .task-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .task-panel-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .task-panel-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .task-list {
            max-height: 60vh;
            overflow-y: auto;
        }

        .task-item {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s;
        }

        .task-item:hover {
            background: var(--bg-secondary);
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .task-header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .task-checkbox {
            width: 1rem;
            height: 1rem;
        }

        .task-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .task-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            background: #fef3c7;
            color: var(--warning-color);
        }

        .status-running {
            background: #dcfce7;
            color: var(--success-color);
        }

        .status-completed {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .status-failed {
            background: #fee2e2;
            color: var(--danger-color);
        }

        .task-meta {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .meta-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .task-progress {
            margin-bottom: 1rem;
        }

        .progress-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .progress-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .progress-percentage {
            color: var(--text-secondary);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-secondary);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-fill.success {
            background: var(--success-color);
        }

        .progress-fill.warning {
            background: var(--warning-color);
        }

        .progress-fill.danger {
            background: var(--danger-color);
        }

        .task-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem;
            border: none;
            border-radius: 4px;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .action-btn.danger:hover {
            color: var(--danger-color);
            background: #fee2e2;
        }

        .log-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .log-content {
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 900px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: var(--shadow-lg);
        }

        .log-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .log-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .close-btn {
            padding: 0.5rem;
            border: none;
            background: transparent;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .close-btn:hover {
            color: var(--text-primary);
        }

        .log-body {
            padding: 1.5rem;
            flex: 1;
            overflow-y: auto;
        }

        .log-viewer {
            background: #1f2937;
            color: #f3f4f6;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.8125rem;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            display: flex;
            gap: 0.5rem;
        }

        .log-time {
            color: #9ca3af;
            flex-shrink: 0;
        }

        .log-level {
            flex-shrink: 0;
            font-weight: 600;
        }

        .log-level.info { color: #3b82f6; }
        .log-level.warn { color: #f59e0b; }
        .log-level.error { color: #ef4444; }
        .log-level.success { color: #10b981; }

        .log-message {
            flex: 1;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--text-secondary);
        }

        .empty-icon {
            width: 4rem;
            height: 4rem;
            margin: 0 auto 1rem;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .empty-desc {
            font-size: 0.875rem;
        }

        @media (max-width: 1024px) {
            .task-meta {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .controls-row {
                flex-direction: column;
                align-items: stretch;
                gap: 0.75rem;
            }

            .search-container {
                min-width: unset;
            }

            .btn-group {
                justify-content: stretch;
            }

            .btn {
                flex: 1;
                justify-content: center;
            }

            .task-meta {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .log-content {
                width: 95%;
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">任务队列管理</h1>
            <p class="page-description">监控视频生成任务的执行状态，查看详细进度和处理异常情况</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总任务数</span>
                    <div class="stat-icon total">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4"/>
                            <path d="M11 19H5a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h6"/>
                            <path d="M15 11h4a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-4"/>
                            <path d="M13 19h6a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2h-6"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="totalTasks">12</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">执行中</span>
                    <div class="stat-icon running">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5,3 19,12 5,21"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="runningTasks">3</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">等待中</span>
                    <div class="stat-icon pending">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="pendingTasks">5</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">失败</span>
                    <div class="stat-icon failed">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="failedTasks">2</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls-panel">
            <div class="controls-row">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索任务名称或描述..." id="searchInput">
                    <div class="search-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                    </div>
                </div>

                <select class="filter-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="pending">等待中</option>
                    <option value="running">执行中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                </select>

                <div class="btn-group">
                    <button class="btn btn-secondary" id="pauseAllBtn">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="6" y="4" width="4" height="16"/>
                            <rect x="14" y="4" width="4" height="16"/>
                        </svg>
                        暂停全部
                    </button>
                    <button class="btn btn-primary" id="resumeAllBtn">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5,3 19,12 5,21"/>
                        </svg>
                        恢复全部
                    </button>
                    <button class="btn btn-danger" id="clearCompletedBtn">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"/>
                            <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                        </svg>
                        清理已完成
                    </button>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="task-panel">
            <div class="task-panel-header">
                <input type="checkbox" id="selectAll" class="task-checkbox">
                <span class="task-panel-title">任务列表</span>
            </div>

            <div class="task-list" id="taskList">
                <!-- 任务项 1 -->
                <div class="task-item">
                    <div class="task-header">
                        <div class="task-header-left">
                            <input type="checkbox" class="task-checkbox">
                            <h3 class="task-title">Reddit惊悚故事：午夜敲门声</h3>
                        </div>
                        <span class="task-status status-running">执行中</span>
                    </div>

                    <div class="task-meta">
                        <div class="meta-item">
                            <span class="meta-label">任务ID</span>
                            <span>task_001</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">视频配置</span>
                            <span>720p • 3分钟</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">账号名称</span>
                            <span>恐怖故事君</span>
                        </div>
                    </div>

                    <div class="task-progress">
                        <div class="progress-header">
                            <span class="progress-label">视频生成进度</span>
                            <span class="progress-percentage">75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 75%"></div>
                        </div>
                    </div>

                    <div class="task-actions">
                        <button class="action-btn" title="查看日志" onclick="showLog('task_001')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                                <polyline points="10,9 9,9 8,9"/>
                            </svg>
                        </button>
                        <button class="action-btn" title="暂停任务">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="6" y="4" width="4" height="16"/>
                                <rect x="14" y="4" width="4" height="16"/>
                            </svg>
                        </button>
                        <button class="action-btn danger" title="取消任务">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 任务项 2 -->
                <div class="task-item">
                    <div class="task-header">
                        <div class="task-header-left">
                            <input type="checkbox" class="task-checkbox">
                            <h3 class="task-title">Reddit恐怖故事合集：深夜回家</h3>
                        </div>
                        <span class="task-status status-completed">已完成</span>
                    </div>

                    <div class="task-meta">
                        <div class="meta-item">
                            <span class="meta-label">任务ID</span>
                            <span>task_002</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">视频配置</span>
                            <span>1080p • 5分钟</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">完成时间</span>
                            <span>13:45 (耗时: 1h 15m)</span>
                        </div>
                    </div>

                    <div class="task-progress">
                        <div class="progress-header">
                            <span class="progress-label">视频生成进度</span>
                            <span class="progress-percentage">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="task-actions">
                        <button class="action-btn" title="查看日志" onclick="showLog('task_002')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                                <polyline points="10,9 9,9 8,9"/>
                            </svg>
                        </button>
                        <button class="action-btn" title="下载视频">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                        </button>
                        <button class="action-btn danger" title="删除任务">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 任务项 3 -->
                <div class="task-item">
                    <div class="task-header">
                        <div class="task-header-left">
                            <input type="checkbox" class="task-checkbox">
                            <h3 class="task-title">Reddit情感故事：失恋的夜晚</h3>
                        </div>
                        <span class="task-status status-failed">失败</span>
                    </div>

                    <div class="task-meta">
                        <div class="meta-item">
                            <span class="meta-label">任务ID</span>
                            <span>task_004</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">视频配置</span>
                            <span>1080p • 4分钟</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">错误信息</span>
                            <span>TTS API限制</span>
                        </div>
                    </div>

                    <div class="task-progress">
                        <div class="progress-header">
                            <span class="progress-label">视频生成进度</span>
                            <span class="progress-percentage">25%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill danger" style="width: 25%"></div>
                        </div>
                    </div>

                    <div class="task-actions">
                        <button class="action-btn" title="查看错误日志" onclick="showLog('task_004')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                                <polyline points="10,9 9,9 8,9"/>
                            </svg>
                        </button>
                        <button class="action-btn" title="重试任务">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23,4 23,10 17,10"/>
                                <polyline points="1,20 1,14 7,14"/>
                                <path d="M20.49 9A9 9 0 0 0 5.64 5.64l1.27 1.27m4.18 4.18l1.27 1.27M12.01 2.05"/>
                            </svg>
                        </button>
                        <button class="action-btn danger" title="删除任务">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 任务项 4 -->
                <div class="task-item">
                    <div class="task-header">
                        <div class="task-header-left">
                            <input type="checkbox" class="task-checkbox">
                            <h3 class="task-title">Reddit奇怪故事：神秘邻居</h3>
                        </div>
                        <span class="task-status status-pending">等待中</span>
                    </div>

                    <div class="task-meta">
                        <div class="meta-item">
                            <span class="meta-label">任务ID</span>
                            <span>task_005</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">视频配置</span>
                            <span>720p • 6分钟</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">队列位置</span>
                            <span>第 3 位</span>
                        </div>
                    </div>

                    <div class="task-progress">
                        <div class="progress-header">
                            <span class="progress-label">视频生成进度</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>

                    <div class="task-actions">
                        <button class="action-btn" title="查看配置">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                        </button>
                        <button class="action-btn" title="优先执行">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="19" x2="12" y2="5"/>
                                <polyline points="5,12 12,5 19,12"/>
                            </svg>
                        </button>
                        <button class="action-btn danger" title="取消任务">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志查看模态框 -->
    <div class="log-modal" id="logModal">
        <div class="log-content">
            <div class="log-header">
                <h3 class="log-title" id="logTitle">任务日志</h3>
                <button class="close-btn" onclick="closeLog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="log-body">
                <div class="log-viewer" id="logViewer">
                    <!-- 日志内容将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const taskItems = document.querySelectorAll('.task-item');
            
            taskItems.forEach(item => {
                const title = item.querySelector('.task-title').textContent.toLowerCase();
                const taskId = item.querySelector('.meta-item span:last-child').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || taskId.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 状态筛选
        document.getElementById('statusFilter').addEventListener('change', function(e) {
            const filterStatus = e.target.value;
            const taskItems = document.querySelectorAll('.task-item');
            
            taskItems.forEach(item => {
                const statusElement = item.querySelector('.task-status');
                const taskStatus = statusElement.classList.contains('status-running') ? 'running' :
                                statusElement.classList.contains('status-completed') ? 'completed' :
                                statusElement.classList.contains('status-failed') ? 'failed' : 'pending';
                
                if (!filterStatus || taskStatus === filterStatus) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function(e) {
            const checkboxes = document.querySelectorAll('.task-checkbox:not(#selectAll)');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
        });

        // 批量操作按钮
        document.getElementById('pauseAllBtn').addEventListener('click', function() {
            alert('暂停所有运行中的任务');
        });

        document.getElementById('resumeAllBtn').addEventListener('click', function() {
            alert('恢复所有暂停的任务');
        });

        document.getElementById('clearCompletedBtn').addEventListener('click', function() {
            if (confirm('确定要清理所有已完成的任务吗？')) {
                alert('清理已完成的任务');
            }
        });

        // 日志查看功能
        function showLog(taskId) {
            const logModal = document.getElementById('logModal');
            const logTitle = document.getElementById('logTitle');
            const logViewer = document.getElementById('logViewer');
            
            logTitle.textContent = `任务日志 - ${taskId}`;
            
            // 模拟日志内容
            const logs = generateSampleLogs(taskId);
            logViewer.innerHTML = logs;
            
            logModal.style.display = 'flex';
        }

        function closeLog() {
            document.getElementById('logModal').style.display = 'none';
        }

        function generateSampleLogs(taskId) {
            const logEntries = [
                { time: '15:30:01', level: 'info', message: '任务开始执行' },
                { time: '15:30:02', level: 'info', message: '正在获取Reddit故事内容...' },
                { time: '15:30:05', level: 'success', message: '故事内容获取成功，共1,234字符' },
                { time: '15:30:06', level: 'info', message: '正在调用TTS服务生成音频...' },
                { time: '15:30:45', level: 'success', message: '音频生成完成，时长3分15秒' },
                { time: '15:30:46', level: 'info', message: '正在准备视频素材...' },
                { time: '15:30:50', level: 'info', message: '开始视频合成...' },
            ];

            if (taskId === 'task_004') {
                logEntries.push(
                    { time: '15:31:20', level: 'error', message: 'TTS API调用失败: 配额已用完' },
                    { time: '15:31:21', level: 'error', message: '任务执行失败，等待重试' }
                );
            } else {
                logEntries.push(
                    { time: '15:32:15', level: 'info', message: '视频合成进行中... 75%' },
                    { time: '15:32:45', level: 'info', message: '正在添加字幕和封面...' }
                );
            }

            return logEntries.map(entry => 
                `<div class="log-entry">
                    <span class="log-time">[${entry.time}]</span>
                    <span class="log-level ${entry.level}">[${entry.level.toUpperCase()}]</span>
                    <span class="log-message">${entry.message}</span>
                </div>`
            ).join('');
        }

        // 点击模态框背景关闭
        document.getElementById('logModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLog();
            }
        });

        // 模拟进度更新
        function updateProgress() {
            const progressBars = document.querySelectorAll('.progress-fill.success');
            progressBars.forEach(bar => {
                const taskItem = bar.closest('.task-item');
                const statusElement = taskItem.querySelector('.task-status');
                
                if (statusElement.classList.contains('status-running')) {
                    const currentWidth = parseInt(bar.style.width) || 0;
                    if (currentWidth < 100) {
                        const newWidth = Math.min(currentWidth + Math.random() * 3, 100);
                        bar.style.width = newWidth + '%';
                        const percentageElement = taskItem.querySelector('.progress-percentage');
                        percentageElement.textContent = Math.floor(newWidth) + '%';
                    }
                }
            });
        }

        // 定时更新进度（演示用）
        setInterval(updateProgress, 2000);

        // 实时更新统计信息
        function updateStats() {
            const taskItems = document.querySelectorAll('.task-item');
            let total = taskItems.length;
            let running = 0;
            let pending = 0;
            let failed = 0;

            taskItems.forEach(item => {
                const statusElement = item.querySelector('.task-status');
                if (statusElement.classList.contains('status-running')) running++;
                else if (statusElement.classList.contains('status-pending')) pending++;
                else if (statusElement.classList.contains('status-failed')) failed++;
            });

            document.getElementById('totalTasks').textContent = total;
            document.getElementById('runningTasks').textContent = running;
            document.getElementById('pendingTasks').textContent = pending;
            document.getElementById('failedTasks').textContent = failed;
        }

        // 初始化
        updateStats();
    </script>
</body>
</html>
