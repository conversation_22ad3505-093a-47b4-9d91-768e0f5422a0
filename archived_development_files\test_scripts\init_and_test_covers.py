"""
初始化数据库并测试封面模板功能
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

try:
    from src.core.database import init_db
    print("正在初始化数据库...")
    init_db()
    print("数据库初始化完成!")
    
    # 导入测试函数并运行
    from test_cover_template_complete import test_cover_template_complete
    test_cover_template_complete()
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
