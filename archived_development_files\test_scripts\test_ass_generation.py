"""
简单的ASS字幕生成测试
"""

import tempfile
import os

def html_color_to_ass(html_color):
    """将HTML颜色转换为ASS格式"""
    if html_color.startswith('#'):
        html_color = html_color[1:]
    r = int(html_color[0:2], 16)
    g = int(html_color[2:4], 16) 
    b = int(html_color[4:6], 16)
    return f"&H00{b:02X}{g:02X}{r:02X}"

def create_ass_subtitle_file(srt_path: str, ass_path: str, font_name: str, font_size: int, font_color: str, position: str):
    """
    将SRT文件转换为ASS文件，应用自定义样式
    """
    try:
        ass_color = html_color_to_ass(font_color)
        
        # 设置对齐方式
        if position == 'top':
            alignment = 8  # 上方居中
            margin_v = 30  # 上边距
        elif position == 'center':
            alignment = 5  # 中间居中  
            margin_v = 0
        else:  # bottom
            alignment = 2  # 下方居中
            margin_v = 30  # 下边距
        
        # 读取SRT文件
        with open(srt_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        # 创建ASS文件头部
        ass_content = f"""[Script Info]
Title: Generated Subtitle
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{ass_color},&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,{alignment},10,10,{margin_v},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # 解析SRT并转换为ASS格式
        srt_blocks = srt_content.strip().split('\n\n')
        for block in srt_blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 跳过序号
                time_line = lines[1]
                text_lines = lines[2:]
                
                # 转换时间格式 (SRT: 00:00:01,000 --> 00:00:05,000 to ASS: 0:00:01.00,0:00:05.00)
                if ' --> ' in time_line:
                    start_time, end_time = time_line.split(' --> ')
                    
                    # 转换SRT时间格式到ASS格式
                    def srt_time_to_ass(srt_time):
                        # SRT: 00:00:01,000 -> ASS: 0:00:01.00
                        time_part, ms_part = srt_time.split(',')
                        hours, minutes, seconds = time_part.split(':')
                        # ASS只需要两位毫秒
                        ms = ms_part[:2]  # 取前两位毫秒
                        # 格式化为ASS时间格式：H:MM:SS.CS (CS是百分之一秒)
                        return f"{int(hours)}:{minutes}:{seconds}.{ms}"
                    
                    start_time_ass = srt_time_to_ass(start_time.strip())
                    end_time_ass = srt_time_to_ass(end_time.strip())
                    
                    text = '\\N'.join(text_lines)  # ASS使用\N换行
                    
                    ass_content += f"Dialogue: 0,{start_time_ass},{end_time_ass},Default,,0,0,0,,{text}\n"
        
        # 写入ASS文件
        with open(ass_path, 'w', encoding='utf-8') as f:
            f.write(ass_content)
        
        print(f"✅ 成功创建ASS字幕文件: {ass_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建ASS字幕文件失败: {e}")
        return False

def test_subtitle_styles():
    """测试字幕样式配置"""
    print("🧪 开始测试字幕样式配置...")
    
    # 创建临时SRT文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
        srt_content = """1
00:00:01,000 --> 00:00:03,000
这是一个测试字幕

2
00:00:03,500 --> 00:00:06,000
用来验证字幕样式是否生效

3
00:00:06,500 --> 00:00:09,000
包括字体、大小、颜色和位置
"""
        f.write(srt_content)
        srt_file = f.name
    
    # 测试案例：自定义红色、大字体、顶部位置
    print(f"\n📝 测试自定义样式...")
    print(f"   字体: Microsoft YaHei")
    print(f"   大小: 32")
    print(f"   颜色: #FF0000 (红色)")
    print(f"   位置: top")
    
    # 生成ASS文件
    ass_file = srt_file.replace('.srt', '_custom.ass')
    
    result = create_ass_subtitle_file(
        srt_file,
        ass_file,
        'Microsoft YaHei',
        32,
        '#FF0000',
        'top'
    )
    
    if result:
        # 检查ASS文件内容
        with open(ass_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n📄 生成的ASS文件内容预览:")
        print("=" * 50)
        for i, line in enumerate(content.split('\n')):
            if i < 15 or line.startswith('Dialogue:'):  # 显示头部和对话行
                print(line)
            elif i == 15:
                print("... (省略中间部分) ...")
        print("=" * 50)
        
        # 验证关键配置
        checks = [
            ('Microsoft YaHei' in content, '字体名称'),
            ('32' in content, '字体大小'),
            ('&H000000FF' in content, '红色配置'),  # 红色的ASS格式
            ('Alignment' in content or '8' in content, '顶部对齐'),
        ]
        
        print(f"\n🔍 配置验证结果:")
        for check_passed, check_name in checks:
            status = "✅" if check_passed else "❌"
            print(f"   {status} {check_name}")
    
    # 清理临时文件
    try:
        os.unlink(srt_file)
        if os.path.exists(ass_file):
            os.unlink(ass_file)
    except:
        pass
    
    print("\n🎉 字幕样式测试完成！")

if __name__ == "__main__":
    test_subtitle_styles()
