<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit故事视频生成器 - 背景音乐管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: all 0.3s ease;
        }

        /* 主题变量定义 */
        :root {
            /* 蓝色主题（默认） */
            --theme-primary: #2563eb;
            --theme-primary-hover: #1d4ed8;
            --theme-primary-light: #eff6ff;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f9fafb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #374151;
            --border-primary: #e5e7eb;
            --border-secondary: #d1d5db;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
        }

        /* 绿色主题 */
        [data-theme="green"] {
            --theme-primary: #059669;
            --theme-primary-hover: #047857;
            --theme-primary-light: #ecfdf5;
        }

        /* 紫色主题 */
        [data-theme="purple"] {
            --theme-primary: #7c3aed;
            --theme-primary-hover: #6d28d9;
            --theme-primary-light: #f3f4f6;
        }

        /* 橙色主题 */
        [data-theme="orange"] {
            --theme-primary: #ea580c;
            --theme-primary-hover: #dc2626;
            --theme-primary-light: #fff7ed;
        }

        /* 红色主题 */
        [data-theme="red"] {
            --theme-primary: #dc2626;
            --theme-primary-hover: #b91c1c;
            --theme-primary-light: #fef2f2;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 32px;
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .page-description {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .btn-primary:hover {
            background: var(--theme-primary-hover);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-tertiary);
            border-color: var(--border-secondary);
        }

        .btn-secondary:hover {
            border-color: var(--theme-primary);
            color: var(--theme-primary);
        }

        .btn-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--border-primary);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--theme-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 工具栏 */
        .toolbar {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 8px 36px 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            color: var(--text-secondary);
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 12px;
            background: var(--bg-secondary);
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .view-btn.active {
            background: var(--theme-primary);
            color: white;
        }

        .view-btn:not(.active):hover {
            background: var(--bg-tertiary);
        }

        /* 拖拽上传区域 */
        .upload-zone {
            background: var(--bg-secondary);
            border: 2px dashed var(--border-secondary);
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 24px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .upload-zone:hover, .upload-zone.dragover {
            border-color: var(--theme-primary);
            background: var(--theme-primary-light);
        }

        .upload-icon {
            width: 48px;
            height: 48px;
            color: var(--text-secondary);
            margin: 0 auto 16px;
        }

        .upload-text {
            font-size: 16px;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: var(--text-secondary);
        }        /* 音乐列表 */
        .music-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }

        /* 列表视图样式 */
        .music-list {
            display: block;
        }

        .music-list .music-card {
            display: flex;
            margin-bottom: 12px;
            height: 80px;
        }

        .music-list .music-cover {
            width: 80px;
            height: 80px;
            flex-shrink: 0;
        }

        .music-list .music-info {
            flex: 1;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .music-list .music-details {
            flex: 1;
        }

        .music-list .music-title {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .music-list .music-meta {
            font-size: 14px;
            margin-bottom: 0;
        }

        .music-list .music-actions {
            flex-shrink: 0;
            margin-left: 16px;
        }

        .music-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-primary);
            overflow: hidden;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .music-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .music-cover {
            width: 100%;
            height: 160px;
            background: linear-gradient(135deg, var(--theme-primary), var(--theme-primary-hover));
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .music-icon {
            width: 48px;
            height: 48px;
            color: white;
            opacity: 0.8;
        }        .music-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 在列表视图中隐藏音频时长（因为已在meta信息中显示） */
        .music-list .music-duration {
            display: none;
        }

        .music-info {
            padding: 16px;
        }

        .music-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-meta {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 12px;
        }

        .music-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .action-btn-play {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .action-btn-play:hover {
            background: #047857;
        }

        .action-btn-delete {
            background: transparent;
            color: var(--error-color);
            border-color: var(--error-color);
        }

        .action-btn-delete:hover {
            background: var(--error-color);
            color: white;
        }

        .action-icon {
            width: 12px;
            height: 12px;
            fill: currentColor;
        }

        /* 音频播放器 */
        .audio-player {
            position: fixed;
            bottom: 24px;
            right: 24px;
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--border-primary);
            min-width: 300px;
            z-index: 1000;
            display: none;
        }

        .player-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .player-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .player-close {
            width: 20px;
            height: 20px;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .player-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .player-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--theme-primary);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .player-btn:hover {
            background: var(--theme-primary-hover);
        }

        .player-progress {
            flex: 1;
            height: 4px;
            background: var(--border-secondary);
            border-radius: 2px;
            overflow: hidden;
        }

        .player-progress-bar {
            height: 100%;
            background: var(--theme-primary);
            width: 0%;
            transition: width 0.1s;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            color: var(--text-secondary);
        }

        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .toolbar-left,
            .toolbar-right {
                justify-content: space-between;
            }

            .search-input {
                width: 100%;
            }

            .music-grid {
                grid-template-columns: 1fr;
            }

            .audio-player {
                left: 16px;
                right: 16px;
                bottom: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title-section">
                <h1 class="page-title">背景音乐管理</h1>
                <p class="page-description">管理视频生成所需的背景音乐文件，支持批量导入和预览播放</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-secondary">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    导出列表
                </button>
                <button class="btn btn-primary" id="importBtn">
                    <svg class="btn-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                    </svg>
                    批量导入
                </button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">音乐文件总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0 GB</div>
                <div class="stat-label">占用存储空间</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">0:00:00</div>
                <div class="stat-label">总播放时长</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">MP3, WAV</div>
                <div class="stat-label">支持格式</div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索音乐文件名称...">
                    <svg class="search-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <select class="filter-select">
                    <option value="all">全部格式</option>
                    <option value="mp3">MP3</option>
                    <option value="wav">WAV</option>
                    <option value="m4a">M4A</option>
                    <option value="flac">FLAC</option>
                </select>
            </div>
            <div class="toolbar-right">
                <span style="font-size: 14px; color: var(--text-secondary);">显示方式:</span>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid">
                        <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                        </svg>
                    </button>
                    <button class="view-btn" data-view="list">
                        <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 拖拽上传区域 -->
        <div class="upload-zone" id="uploadZone">
            <svg class="upload-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <div class="upload-text">拖拽音乐文件到这里，或点击选择文件夹</div>
            <div class="upload-hint">支持 MP3、WAV、M4A、FLAC 格式，可批量上传</div>
        </div>

        <!-- 音乐列表 -->
        <div class="music-grid" id="musicGrid">
            <!-- 空状态 -->
            <div class="empty-state" style="grid-column: 1 / -1;">
                <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                </svg>
                <div class="empty-title">还没有音乐文件</div>
                <div class="empty-description">
                    拖拽音乐文件到上方区域，或点击"批量导入"按钮开始添加背景音乐
                </div>
            </div>
        </div>
    </div>

    <!-- 音频播放器 -->
    <div class="audio-player" id="audioPlayer">
        <div class="player-header">
            <div class="player-title">正在播放: 示例音乐.mp3</div>
            <svg class="player-close" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
        </div>
        <div class="player-controls">
            <button class="player-btn">
                <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                </svg>
            </button>
            <div class="player-progress">
                <div class="player-progress-bar"></div>
            </div>
            <span style="font-size: 12px; color: var(--text-secondary);">0:30 / 3:45</span>
        </div>
    </div>

    <input type="file" id="fileInput" multiple accept=".mp3,.wav,.m4a,.flac" style="display: none;">

    <script>
        // 示例音乐数据
        const sampleMusic = [
            {
                id: 1,
                name: '轻松背景音乐.mp3',
                duration: '3:45',
                size: '8.2 MB',
                format: 'MP3',
                path: '/music/sample1.mp3'
            },
            {
                id: 2,
                name: '深度思考音乐.wav',
                duration: '5:20',
                size: '45.6 MB',
                format: 'WAV',
                path: '/music/sample2.wav'
            },
            {
                id: 3,
                name: '悬疑氛围音效.m4a',
                duration: '2:15',
                size: '5.1 MB',
                format: 'M4A',
                path: '/music/sample3.m4a'
            }
        ];

        let musicList = [];

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 恢复保存的主题
            const savedTheme = localStorage.getItem('theme') || 'blue';
            if (savedTheme !== 'blue') {
                document.documentElement.setAttribute('data-theme', savedTheme);
            }            // 加载示例数据（实际使用时会从数据库加载）
            musicList = [...sampleMusic];
            renderMusicGrid();
            updateStats();
        });

        // 文件导入处理
        document.getElementById('importBtn').addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });

        // 拖拽上传
        const uploadZone = document.getElementById('uploadZone');

        uploadZone.addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });

        uploadZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });        // 文件处理函数
        function handleFiles(files) {
            const audioFiles = files.filter(file => {
                const ext = file.name.split('.').pop().toLowerCase();
                return ['mp3', 'wav', 'm4a', 'flac'].includes(ext);
            });

            if (audioFiles.length === 0) {
                alert('请选择有效的音频文件 (MP3, WAV, M4A, FLAC)');
                return;
            }

            // 处理每个音频文件
            audioFiles.forEach(file => {
                // 创建音频元素获取时长
                const audio = new Audio();
                const url = URL.createObjectURL(file);
                
                audio.addEventListener('loadedmetadata', function() {
                    const duration = formatDuration(audio.duration);
                    
                    const musicItem = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        duration: duration,
                        size: formatFileSize(file.size),
                        format: file.name.split('.').pop().toUpperCase(),
                        path: url
                    };
                    
                    musicList.push(musicItem);
                    renderMusicGrid();
                    updateStats();
                    
                    // 清理临时URL
                    URL.revokeObjectURL(url);
                });

                audio.addEventListener('error', function() {
                    // 如果无法获取时长，使用默认值
                    const musicItem = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        duration: '--:--',
                        size: formatFileSize(file.size),
                        format: file.name.split('.').pop().toUpperCase(),
                        path: url
                    };
                    
                    musicList.push(musicItem);
                    renderMusicGrid();
                    updateStats();
                });

                audio.src = url;
            });
        }        // 渲染音乐网格
        function renderMusicGrid() {
            const grid = document.getElementById('musicGrid');
            const isListView = grid.classList.contains('music-list');
            
            if (musicList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                        </svg>
                        <div class="empty-title">还没有音乐文件</div>
                        <div class="empty-description">
                            拖拽音乐文件到上方区域，或点击"批量导入"按钮开始添加背景音乐
                        </div>
                    </div>
                `;
                return;
            }

            if (isListView) {
                // 列表视图模板
                grid.innerHTML = musicList.map(music => `
                    <div class="music-card">
                        <div class="music-cover">
                            <svg class="music-icon" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                            </svg>
                        </div>
                        <div class="music-info">
                            <div class="music-details">
                                <div class="music-title" title="${music.name}">${music.name}</div>
                                <div class="music-meta">${music.format} • ${music.size} • ${music.duration}</div>
                            </div>
                            <div class="music-actions">
                                <button class="action-btn action-btn-play" onclick="playMusic('${music.id}', '${music.name}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                    </svg>
                                    播放
                                </button>
                                <button class="action-btn action-btn-delete" onclick="deleteMusic('${music.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 112 0v4a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V8z" clip-rule="evenodd"/>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                // 网格视图模板
                grid.innerHTML = musicList.map(music => `
                    <div class="music-card">
                        <div class="music-cover">
                            <svg class="music-icon" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                            </svg>
                            <div class="music-duration">${music.duration}</div>
                        </div>
                        <div class="music-info">
                            <div class="music-title" title="${music.name}">${music.name}</div>
                            <div class="music-meta">${music.format} • ${music.size}</div>
                            <div class="music-actions">
                                <button class="action-btn action-btn-play" onclick="playMusic('${music.id}', '${music.name}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                    </svg>
                                    播放
                                </button>
                                <button class="action-btn action-btn-delete" onclick="deleteMusic('${music.id}')">
                                    <svg class="action-icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 112 0v4a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V8z" clip-rule="evenodd"/>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }        // 更新统计信息
        function updateStats() {
            const stats = document.querySelectorAll('.stat-value');
            stats[0].textContent = musicList.length;
            
            // 计算总大小（根据实际文件大小）
            const totalBytes = musicList.reduce((total, music) => {
                // 从大小字符串中提取数值（简化处理）
                const sizeMatch = music.size.match(/(\d+\.?\d*)\s*(KB|MB|GB)/);
                if (sizeMatch) {
                    const value = parseFloat(sizeMatch[1]);
                    const unit = sizeMatch[2];
                    let bytes = value;
                    if (unit === 'KB') bytes *= 1024;
                    else if (unit === 'MB') bytes *= 1024 * 1024;
                    else if (unit === 'GB') bytes *= 1024 * 1024 * 1024;
                    return total + bytes;
                }
                return total;
            }, 0);
            
            if (totalBytes > 1024 * 1024 * 1024) {
                stats[1].textContent = `${(totalBytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
            } else if (totalBytes > 1024 * 1024) {
                stats[1].textContent = `${(totalBytes / (1024 * 1024)).toFixed(1)} MB`;
            } else {
                stats[1].textContent = `${(totalBytes / 1024).toFixed(1)} KB`;
            }
            
            // 计算总时长（根据实际音频时长）
            const totalSeconds = musicList.reduce((total, music) => {
                if (music.duration && music.duration !== '--:--') {
                    const parts = music.duration.split(':');
                    const minutes = parseInt(parts[0]) || 0;
                    const seconds = parseInt(parts[1]) || 0;
                    return total + (minutes * 60) + seconds;
                }
                return total;
            }, 0);
            
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            if (hours > 0) {
                stats[2].textContent = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                stats[2].textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // 播放音乐
        function playMusic(id, name) {
            const player = document.getElementById('audioPlayer');
            const title = player.querySelector('.player-title');
            title.textContent = `正在播放: ${name}`;
            player.style.display = 'block';
            
            // 这里将来会实际播放音频文件
            console.log('播放音乐:', id, name);
        }

        // 删除音乐
        function deleteMusic(id) {
            if (confirm('确定要删除这个音乐文件吗？')) {
                musicList = musicList.filter(music => music.id != id);
                renderMusicGrid();
                updateStats();
            }
        }

        // 关闭播放器
        document.querySelector('.player-close').addEventListener('click', function() {
            document.getElementById('audioPlayer').style.display = 'none';
        });        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const view = this.dataset.view;
                const grid = document.getElementById('musicGrid');
                
                // 切换视图样式
                if (view === 'list') {
                    grid.classList.remove('music-grid');
                    grid.classList.add('music-list');
                } else {
                    grid.classList.remove('music-list');
                    grid.classList.add('music-grid');
                }
                
                // 重新渲染
                renderMusicGrid();
                
                console.log('切换到', view, '视图');
            });
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            // 这里将来会实现实际的搜索过滤
            console.log('搜索:', query);
        });        // 文件大小格式化
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 时长格式化
        function formatDuration(seconds) {
            if (isNaN(seconds) || seconds < 0) return '--:--';
            
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }
    </script>
</body>
</html>
