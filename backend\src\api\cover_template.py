"""
封面模板管理API
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status, File, UploadFile, Form, Request
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional, Dict, Any
from uuid import uuid4
from pydantic import BaseModel, Field
import os
import json
from pathlib import Path
from loguru import logger

from ..core.database import get_db
from ..core.responses import success_response, error_response
from ..models.resources import CoverTemplate
from ..schemas.resources import (
    CoverTemplateCreate,
    CoverTemplateUpdate,
    CoverTemplateResponse,
    CoverTemplateQuery,
    BulkCoverTemplateResponse,
    CoverTemplatePreviewRequest,
    CoverTemplatePreviewResponse
)
from ..services.template_import_service import template_import_service

router = APIRouter(prefix="/cover-templates", tags=["cover-templates"])

@router.get("/")
async def get_cover_templates(
    query: CoverTemplateQuery = Depends(),
    db: Session = Depends(get_db)
):
    """获取封面模板列表"""
    try:
        db_query = db.query(CoverTemplate)
        
        # 分类过滤
        if query.category:
            db_query = db_query.filter(CoverTemplate.category == query.category)
        
        # 搜索过滤
        if query.search:
            db_query = db_query.filter(
                CoverTemplate.name.contains(query.search)
            )
        
        # 分页
        if query.skip is not None:
            db_query = db_query.offset(query.skip)
        if query.limit is not None:
            db_query = db_query.limit(query.limit)
        
        templates = db_query.all()
        
        # 转换为前端格式
        result = []
        for template in templates:
            frontend_data = template.to_frontend_format()
            result.append(frontend_data)
        
        return success_response({
            "templates": result,
            "total": len(result)
        }, "获取封面模板列表成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取封面模板列表失败: {str(e)}"
        )

@router.post("/")
async def create_cover_template(
    template_data: CoverTemplateCreate,
    db: Session = Depends(get_db)
):
    """创建封面模板"""
    try:
        # 创建新封面模板
        db_template = CoverTemplate(
            id=str(uuid4()),
            name=template_data.name,
            preview_path=template_data.preview_path or f"covers/preview_{str(uuid4())[:8]}.png",
            template_path=template_data.template_path or f"covers/template_{str(uuid4())[:8]}.json",
            variables=template_data.variables or [],
            is_built_in=template_data.is_built_in,
            description=template_data.description,
            category=template_data.category,
            tags=template_data.tags or [],
            width=template_data.width or 1920,
            height=template_data.height or 1080,
            format=template_data.format or "png"
        )
        
        # 处理画布元素和背景数据
        elements_data = getattr(template_data, 'elements', []) or []
        background_data = getattr(template_data, 'background', None)
        
        # 如果是字典格式的数据（从HTTP请求），直接使用
        if hasattr(template_data, 'model_dump'):
            # Pydantic模型，获取所有数据
            all_data = template_data.model_dump()
            elements_data = all_data.get('elements', [])
            background_data = all_data.get('background', None)
        
        # 设置模板的elements和background字段
        setattr(db_template, 'elements', elements_data)
        setattr(db_template, 'background', background_data)
        
        db.add(db_template)
        db.commit()
        db.refresh(db_template)
        
        # 转换为前端格式
        frontend_data = db_template.to_frontend_format()
        return success_response(frontend_data, "创建模板成功")
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建封面模板失败: {str(e)}"
        )

@router.put("/{template_id}")
async def update_cover_template(
    template_id: str,
    template_data: CoverTemplateUpdate,
    db: Session = Depends(get_db)
):
    """更新封面模板"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
        
        # 更新字段
        update_data = template_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == "previewPath":
                setattr(db_template, "preview_path", value)
            elif field == "templatePath":
                setattr(db_template, "template_path", value)
            elif field == "isBuiltIn":
                setattr(db_template, "is_built_in", value)
            elif field == "elements":
                # 保存画布元素数据
                setattr(db_template, "elements", value)
            elif field == "background":
                # 保存背景配置数据
                setattr(db_template, "background", value)
            elif field == "isPublic":
                # 处理isPublic字段，转换为is_built_in的反值
                setattr(db_template, "is_built_in", not value)
            else:
                setattr(db_template, field, value)
        
        db.commit()
        db.refresh(db_template)
        
        # 转换为前端格式
        frontend_data = db_template.to_frontend_format()
        return success_response(frontend_data, "更新模板成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新封面模板失败: {str(e)}"
        )

@router.delete("/{template_id}")
async def delete_cover_template(
    template_id: str,
    db: Session = Depends(get_db)
):
    """删除封面模板"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
        
        db.delete(db_template)
        db.commit()
        
        return success_response(None, "封面模板删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除封面模板失败: {str(e)}"
        )

@router.post("/bulk", response_model=BulkCoverTemplateResponse)
async def bulk_create_cover_templates(
    templates_data: List[CoverTemplateCreate],
    db: Session = Depends(get_db)
):
    """批量创建封面模板"""
    try:
        created_templates = []
        failed_templates = []
        
        for template_data in templates_data:
            try:
                db_template = CoverTemplate(
                    id=str(uuid4()),
                    name=template_data.name,
                    preview_path=template_data.preview_path,
                    template_path=template_data.template_path,
                    variables=template_data.variables,
                    is_built_in=template_data.is_built_in,
                    description=template_data.description,
                    category=template_data.category,
                    tags=template_data.tags,
                    width=template_data.width,
                    height=template_data.height,
                    format=template_data.format
                )
                
                db.add(db_template)
                db.flush()  # 获取生成的ID但不提交
                
                frontend_data = db_template.to_frontend_format()
                created_templates.append(CoverTemplateResponse(**frontend_data))
                
            except Exception as e:
                failed_templates.append({
                    "name": template_data.name,
                    "error": str(e)
                })
        
        db.commit()
        
        return BulkCoverTemplateResponse(
            success=created_templates,
            failed=failed_templates,
            total=len(templates_data),
            success_count=len(created_templates),
            failed_count=len(failed_templates)
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建封面模板失败: {str(e)}"
        )

@router.delete("/bulk")
async def bulk_delete_cover_templates(
    template_ids: List[str] = Query(..., description="要删除的封面模板ID列表"),
    db: Session = Depends(get_db)
):
    """批量删除封面模板"""
    try:
        deleted_count = db.query(CoverTemplate).filter(
            CoverTemplate.id.in_(template_ids)
        ).delete(synchronize_session=False)
        
        db.commit()
        
        return success_response(None, f"成功删除{deleted_count}个封面模板")
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除封面模板失败: {str(e)}"
        )

@router.get("/categories/list")
async def get_cover_template_categories(db: Session = Depends(get_db)):
    """获取封面模板分类列表"""
    try:
        categories = db.query(CoverTemplate.category).distinct().all()
        category_list = [cat[0] for cat in categories if cat[0]]
        
        return success_response({"categories": category_list}, "获取分类列表成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分类列表失败: {str(e)}"
        )

@router.post("/{template_id}/use")
async def use_cover_template(
    template_id: str,
    db: Session = Depends(get_db)
):
    """使用封面模板（增加使用次数）"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
          # 增加使用次数
        db_template.usage_count += 1  # type: ignore
        db.commit()
        
        return success_response(None, "封面模板使用次数已更新")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新使用次数失败: {str(e)}"
        )

@router.get("/statistics")
async def get_cover_template_statistics(db: Session = Depends(get_db)):
    """获取封面模板统计信息"""
    try:
        total_count = db.query(func.count(CoverTemplate.id)).scalar()
        built_in_count = db.query(func.count(CoverTemplate.id)).filter(CoverTemplate.is_built_in == True).scalar()
        custom_count = db.query(func.count(CoverTemplate.id)).filter(CoverTemplate.is_built_in == False).scalar()
        
        return success_response({
            "total_count": total_count,
            "built_in_count": built_in_count,
            "custom_count": custom_count
        }, "获取统计信息成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

@router.get("/stats")
async def get_cover_template_stats(db: Session = Depends(get_db)):
    """获取封面模板统计信息"""
    try:
        # 基本统计
        total_count = db.query(CoverTemplate).count()
        built_in_count = db.query(CoverTemplate).filter(CoverTemplate.is_built_in == True).count()
        custom_count = total_count - built_in_count
        
        # 分类统计
        category_stats = db.query(
            CoverTemplate.category,
            func.count(CoverTemplate.id).label('count')
        ).group_by(CoverTemplate.category).all()
        
        # 使用次数统计
        usage_stats = db.query(
            func.sum(CoverTemplate.usage_count).label('total_usage')
        ).first()
        
        total_usage = 0
        if usage_stats and usage_stats.total_usage:
            total_usage = usage_stats.total_usage
        
        stats_data = {
            "total": total_count,
            "builtIn": built_in_count,
            "custom": custom_count,
            "categories": [{"name": cat[0], "count": cat[1]} for cat in category_stats],
            "totalUsage": total_usage
        }
        
        return success_response(stats_data, "获取统计信息成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

@router.get("/variables")
async def get_available_variables():
    """获取可用变量列表"""
    try:
        # 定义可用的变量
        variables = [
            {
                "name": "avatar",
                "type": "image",
                "label": "头像",
                "description": "用户头像图片",
                "defaultValue": None,
                "properties": ["imageUrl", "src"]
            },
            {
                "name": "nickname", 
                "type": "text",
                "label": "昵称",
                "description": "用户昵称",
                "defaultValue": "用户昵称",
                "properties": ["content", "text"]
            },
            {
                "name": "title",
                "type": "text", 
                "label": "标题",
                "description": "内容标题",
                "defaultValue": "标题文本",
                "properties": ["content", "text"]
            },
            {
                "name": "subtitle",
                "type": "text",
                "label": "副标题", 
                "description": "内容副标题",
                "defaultValue": "副标题",
                "properties": ["content", "text"]
            },
            {
                "name": "timestamp",
                "type": "text",
                "label": "时间戳",
                "description": "发布时间",
                "defaultValue": "2024年1月1日",
                "properties": ["content", "text"]
            }
        ]
        
        return success_response({"variables": variables}, "获取可用变量成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取可用变量失败: {str(e)}"
        )

@router.post("/{template_id}/preview", response_model=CoverTemplatePreviewResponse)
async def preview_template(
    template_id: str,
    preview_data: CoverTemplatePreviewRequest,
    db: Session = Depends(get_db)
):
    """预览模板"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
        
        # TODO: 实现模板预览生成逻辑
        # 这里应该根据模板和变量值生成预览图
        preview_url = f"/api/cover-templates/preview/{template_id}"
        
        return CoverTemplatePreviewResponse(
            success=True,
            previewUrl=preview_url,
            error=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成预览失败: {str(e)}"
        )

class GenerateCoverRequest(BaseModel):
    template_id: str = Field(..., description="模板ID")
    variables: Dict[str, Any] = Field(default={}, description="变量值")

@router.post("/generate")
async def generate_cover(
    request: GenerateCoverRequest,
    db: Session = Depends(get_db)
):
    """生成封面"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == request.template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
        
        # 增加使用次数
        current_usage = getattr(db_template, 'usage_count', 0) or 0
        setattr(db_template, 'usage_count', current_usage + 1)
        
        # TODO: 实现封面生成逻辑
        # 这里应该根据模板和变量值生成最终的封面图
        
        db.commit()
        
        # 返回生成的封面URL（暂时返回预览图URL）
        cover_url = f"/api/cover-templates/preview/{request.template_id}"
        
        return success_response({
            "coverUrl": cover_url,
            "templateId": request.template_id,
            "variables": request.variables
        }, "封面生成成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成封面失败: {str(e)}"
        )

@router.post("/upload")
async def upload_template(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传模板文件"""
    try:
        # 检查文件类型
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )
        
        # 检查文件扩展名
        allowed_extensions = ['.json', '.template']
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型: {file_ext}"
            )
        
        # 读取文件内容
        content = await file.read()
        
        try:
            # 尝试解析JSON格式的模板文件
            template_data = json.loads(content.decode('utf-8'))
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模板文件格式错误，必须是有效的JSON"
            )
        
        # 验证模板数据结构
        required_fields = ['name', 'width', 'height', 'elements']
        for field in required_fields:
            if field not in template_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"模板文件缺少必需字段: {field}"
                )
        
        # 创建模板目录（如果不存在）
        upload_dir = Path("uploads/templates")
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存模板文件
        template_filename = f"template_{uuid4().hex}.json"
        template_path = upload_dir / template_filename
        
        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)
        
        # 创建数据库记录
        db_template = CoverTemplate(
            id=str(uuid4()),
            name=template_data['name'],
            preview_path="",  # 需要生成预览图
            template_path=str(template_path),
            variables=template_data.get('variables', []),
            is_built_in=False,
            description=template_data.get('description', ''),
            category=template_data.get('category', 'custom'),
            tags=template_data.get('tags', []),
            width=template_data['width'],
            height=template_data['height'],
            format=template_data.get('format', 'png')
        )
        
        db.add(db_template)
        db.commit()
        db.refresh(db_template)
        
        # 转换为前端格式
        frontend_data = db_template.to_frontend_format()
        
        return success_response({
            "template": CoverTemplateResponse(**frontend_data),
            "message": "模板上传成功"
        }, "模板上传成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传模板失败: {str(e)}"
        )

@router.get("/thumbnail/{template_id}")
async def get_template_thumbnail(
    template_id: str,
    db: Session = Depends(get_db)
):
    """获取模板缩略图"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
        
        # 检查预览图是否存在
        preview_path = getattr(db_template, 'preview_path', None)
        if preview_path and os.path.exists(preview_path):
            return FileResponse(
                preview_path,
                media_type="image/png",
                filename=f"thumbnail_{template_id}.png"
            )
        else:
            # 返回默认缩略图或生成新的缩略图
            # TODO: 实现缩略图生成逻辑
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="缩略图未找到"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取缩略图失败: {str(e)}"
        )

@router.get("/preview/{template_id}")
async def get_template_preview(
    template_id: str,
    db: Session = Depends(get_db)
):
    """获取模板预览图"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
        
        # 检查预览图是否存在
        preview_path = getattr(db_template, 'preview_path', None)
        if preview_path and os.path.exists(preview_path):
            return FileResponse(
                preview_path,
                media_type="image/png",
                filename=f"preview_{template_id}.png"
            )
        else:
            # TODO: 实现预览图生成逻辑
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="预览图未找到"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取预览图失败: {str(e)}"
        )


@router.get("/{template_id}")
async def get_cover_template(
    template_id: str,
    db: Session = Depends(get_db)
):
    """获取单个封面模板"""
    try:
        db_template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not db_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="封面模板未找到"
            )
        
        # 转换为前端格式
        frontend_data = db_template.to_frontend_format()
        return success_response(frontend_data, "获取模板详情成功")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取封面模板失败: {str(e)}"
        )

@router.post("/import-html")
async def import_html_template(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: str = Form(""),
    category: str = Form("custom"),
    db: Session = Depends(get_db)
):
    """导入HTML模板文件"""
    import time
    start_time = time.time()
    
    # 添加最早的日志记录
    logger.info(f"🎯 import_html_template 函数被调用")
    
    try:
        logger.info(f"🚀 开始导入HTML模板")
        logger.info(f"📝 参数: name={name}, description={description}, category={category}")
        logger.info(f"📄 文件信息: filename={file.filename}, content_type={file.content_type}")
        
        # 验证文件类型
        if not file.filename or not file.filename.endswith('.html'):
            logger.error(f"❌ 文件类型验证失败: filename={file.filename}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持HTML文件格式"
            )
        
        # 保存临时文件
        temp_file_path = f"temp_{uuid4()}.html"
        logger.info(f"💾 保存临时文件: {temp_file_path}")
        
        try:
            with open(temp_file_path, "wb") as f:
                content = await file.read()
                f.write(content)
            
            logger.info(f"📊 开始处理模板文件，文件大小: {len(content)} 字节")
            
            # 导入模板
            db_template = template_import_service.import_html_template(
                html_file_path=temp_file_path,
                name=name,
                description=description,
                category=category,
                db=db
            )
            
            processing_time = time.time() - start_time
            logger.info(f"模板处理完成，耗时: {processing_time:.2f} 秒")
            
            # 转换为前端格式
            frontend_data = db_template.to_frontend_format()
            
            total_time = time.time() - start_time
            logger.info(f"HTML模板导入完成: {name}，总耗时: {total_time:.2f} 秒")
            
            return success_response(frontend_data, "HTML模板导入成功")
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
                
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导入HTML模板失败: {str(e)}"
        )

@router.post("/{template_id}/render")
async def render_template(
    template_id: str,
    request: Request,
    variables: Dict[str, str] = {},
    db: Session = Depends(get_db)
):
    """渲染模板"""
    try:
        # 构建base URL
        base_url = f"{request.url.scheme}://{request.url.netloc}"
        
        rendered_html = template_import_service.render_template(
            template_id=template_id,
            variables=variables,
            db=db,
            base_url=base_url
        )
        
        return success_response({
            "rendered_html": rendered_html,
            "template_id": template_id,
            "variables": variables
        }, "模板渲染成功")
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"模板渲染失败: {str(e)}"
        )

@router.get("/{template_id}/variables")
async def get_template_variables(
    template_id: str,
    db: Session = Depends(get_db)
):
    """获取模板变量列表"""
    try:
        variables = template_import_service.list_template_variables(
            template_id=template_id,
            db=db
        )
        
        return success_response({
            "template_id": template_id,
            "variables": variables
        }, "获取模板变量成功")
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模板变量失败: {str(e)}"
        )
