"""
测试封面模板管理API
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent.parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

import asyncio
import httpx
from fastapi.testclient import TestClient

def test_cover_template_api():
    """测试封面模板API"""
    try:
        from main import app
        
        client = TestClient(app)
        
        print("测试封面模板API...")
        
        # 测试健康检查
        response = client.get("/health")
        print(f"Health check: {response.status_code} - {response.json()}")
        
        # 测试API健康检查
        response = client.get("/api/health")
        print(f"API health check: {response.status_code} - {response.json()}")
        
        # 测试获取模板统计
        response = client.get("/api/cover-templates/stats")
        print(f"Template stats: {response.status_code}")
        if response.status_code == 200:
            print(f"Stats: {response.json()}")
        else:
            print(f"Error: {response.text}")
        
        # 测试获取可用变量
        response = client.get("/api/cover-templates/variables")
        print(f"Available variables: {response.status_code}")
        if response.status_code == 200:
            print(f"Variables: {response.json()}")
        else:
            print(f"Error: {response.text}")
        
        # 测试获取模板列表
        response = client.get("/api/cover-templates")
        print(f"Template list: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Templates count: {len(data.get('data', {}).get('templates', []))}")
        else:
            print(f"Error: {response.text}")
        
        print("封面模板API测试完成!")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cover_template_api()
