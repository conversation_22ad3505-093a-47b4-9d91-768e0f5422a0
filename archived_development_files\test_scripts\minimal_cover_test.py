"""
极简封面测试脚本 - 快速诊断版本
用于快速检测封面生成问题的最小化测试
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试必要的导入"""
    print("🔍 检查导入...")
    
    try:
        import playwright
        print("✅ Playwright 已安装")
    except ImportError:
        print("❌ Playwright 未安装")
        print("请运行: pip install playwright")
        return False
    
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright sync API 可用")
    except ImportError:
        print("❌ Playwright sync API 不可用")
        return False
    
    return True

def test_browser_basic():
    """基础浏览器测试 - 同步版本"""
    print("\n🔍 基础浏览器测试...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            print("  启动浏览器...")
            
            # 使用最简单的配置
            browser = p.chromium.launch(
                headless=True,
                args=['--no-sandbox']
            )
            
            print("  创建页面...")
            page = browser.new_page()
            
            print("  设置简单内容...")
            page.set_content("<html><body><h1 style='color: red;'>测试</h1></body></html>")
            
            print("  截图...")
            output_path = Path(__file__).parent / "minimal_test.png"
            page.screenshot(path=str(output_path))
            
            browser.close()
            
            if output_path.exists():
                file_size = output_path.stat().st_size
                print(f"✅ 基础浏览器测试成功!")
                print(f"   文件: {output_path}")
                print(f"   大小: {file_size} bytes")
                return True
            else:
                print("❌ 截图文件未生成")
                return False
                
    except Exception as e:
        print(f"❌ 基础浏览器测试失败: {str(e)}")
        
        # 给出具体的错误建议
        error_str = str(e).lower()
        if "executable" in error_str or "browser" in error_str:
            print("\n🔧 修复建议:")
            print("1. 安装浏览器: playwright install chromium")
            print("2. 或运行: setup_cover_test.bat")
        elif "timeout" in error_str:
            print("\n🔧 修复建议:")
            print("1. 检查网络连接")
            print("2. 关闭杀毒软件暂时测试")
            print("3. 重启计算机")
        
        return False

def check_project_structure():
    """检查项目结构"""
    print("\n🔍 检查项目结构...")
    
    project_root = Path(__file__).parent
    print(f"项目根目录: {project_root}")
    
    # 检查关键目录
    backend_dir = project_root / "backend"
    if backend_dir.exists():
        print("✅ backend 目录存在")
        
        # 检查模板
        template_dir = backend_dir / "templates"
        if template_dir.exists():
            templates = list(template_dir.glob("*.html"))
            print(f"✅ 找到 {len(templates)} 个模板文件")
            
            if templates:
                print("   模板文件:")
                for t in templates[:3]:  # 只显示前3个
                    print(f"   - {t.name}")
        else:
            print("❌ templates 目录不存在")
        
        # 检查服务文件
        service_file = backend_dir / "src" / "services" / "cover_screenshot_service.py"
        if service_file.exists():
            print("✅ 封面服务文件存在")
        else:
            print("❌ 封面服务文件不存在")
    else:
        print("❌ backend 目录不存在")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 极简封面测试 - 快速诊断")
    print("=" * 50)
    
    # 测试1: 导入检查
    if not test_imports():
        print("\n❌ 导入测试失败，请先安装依赖")
        return
    
    # 测试2: 项目结构检查
    check_project_structure()
    
    # 测试3: 基础浏览器测试
    browser_success = test_browser_basic()
    
    print(f"\n{'=' * 50}")
    print("📊 测试结果")
    print(f"{'=' * 50}")
    
    if browser_success:
        print("✅ 基础截图功能正常")
        print("🎉 可以继续使用完整的封面生成功能")
        print("\n下一步:")
        print("- 运行: python cover_test_independent.py (完整测试)")
        print("- 或者直接使用视频生成功能")
    else:
        print("❌ 基础截图功能异常")
        print("🔧 建议:")
        print("1. 运行: playwright install chromium")
        print("2. 检查防火墙/杀毒软件设置")
        print("3. 重启VS Code和终端")
        print("4. 联系技术支持")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
