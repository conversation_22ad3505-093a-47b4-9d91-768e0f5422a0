#!/usr/bin/env python
"""
测试系统设置前后端完整集成
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

API_BASE = "http://localhost:8000"

async def test_settings_integration():
    """测试设置系统前后端集成"""
    print("🔍 测试系统设置前后端集成...")
    
    async with aiohttp.ClientSession() as session:
        # 1. 获取当前设置
        print("\n📋 1. 获取当前设置...")
        async with session.get(f"{API_BASE}/api/settings") as response:
            if response.status == 200:
                settings_data = await response.json()
                if settings_data.get('success'):
                    print("✅ 获取设置成功")
                    current_settings = settings_data['data']
                    print(f"   TTS提供商: {current_settings['tts']['provider']}")
                    print(f"   LLM提供商: {current_settings['llm']['provider']}")
                    print(f"   主题: {current_settings['general']['theme']}")
                else:
                    print(f"❌ 获取设置失败: {settings_data.get('message')}")
                    return
            else:
                print(f"❌ API调用失败: {response.status}")
                return
        
        # 2. 更新设置
        print("\n📝 2. 更新设置...")
        
        # 准备新的设置数据
        new_settings = {
            "tts": {
                "provider": "coze",
                "apiKey": "test-token-123",
                "voice": "zh_male_wennuanahu_moon_bigtts",
                "model": "test-workflow-id",
                "speed": 1.2
            },
            "llm": {
                "provider": "yunwu",
                "apiKey": "test-llm-key-456",
                "endpoint": "https://api.yunwu.ai/v1",
                "model": "gpt-4",
                "temperature": 0.8,
                "maxTokens": 3000,
                "systemPrompt": "你是一个测试用的AI助手"
            },
            "general": {
                "theme": "dark",
                "language": "en-US",
                "autoSave": False,
                "outputDirectory": "/test/output"
            }
        }
        
        async with session.put(
            f"{API_BASE}/api/settings",
            json=new_settings,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status == 200:
                update_data = await response.json()
                if update_data.get('success'):
                    print("✅ 更新设置成功")
                    updated_settings = update_data['data']
                    print(f"   TTS提供商: {updated_settings['tts']['provider']}")
                    print(f"   TTS API密钥: {'已设置' if updated_settings['tts']['apiKey'] else '未设置'}")
                    print(f"   LLM提供商: {updated_settings['llm']['provider']}")
                    print(f"   LLM模型: {updated_settings['llm']['model']}")
                    print(f"   主题: {updated_settings['general']['theme']}")
                else:
                    print(f"❌ 更新设置失败: {update_data.get('message')}")
                    return
            else:
                response_text = await response.text()
                print(f"❌ 更新设置API调用失败: {response.status}")
                print(f"   响应内容: {response_text}")
                return
        
        # 3. 验证数据库中的更新
        print("\n🔍 3. 验证数据库中的更新...")
        async with session.get(f"{API_BASE}/api/settings") as response:
            if response.status == 200:
                verify_data = await response.json()
                if verify_data.get('success'):
                    verified_settings = verify_data['data']
                    
                    # 检查更新是否正确持久化
                    checks = [
                        ("TTS提供商", verified_settings['tts']['provider'], "coze"),
                        ("TTS语音", verified_settings['tts']['voice'], "zh_male_wennuanahu_moon_bigtts"),
                        ("LLM提供商", verified_settings['llm']['provider'], "yunwu"),
                        ("LLM模型", verified_settings['llm']['model'], "gpt-4"),
                        ("主题", verified_settings['general']['theme'], "dark"),
                        ("语言", verified_settings['general']['language'], "en-US"),
                    ]
                    
                    all_correct = True
                    for name, actual, expected in checks:
                        if actual == expected:
                            print(f"   ✅ {name}: {actual}")
                        else:
                            print(f"   ❌ {name}: 期望 {expected}, 实际 {actual}")
                            all_correct = False
                    
                    if all_correct:
                        print("✅ 所有设置更新都已正确持久化到数据库")
                    else:
                        print("❌ 部分设置更新未正确持久化")
                        
                else:
                    print(f"❌ 验证失败: {verify_data.get('message')}")
            else:
                print(f"❌ 验证API调用失败: {response.status}")
        
        # 4. 测试设置重置
        print("\n🔄 4. 测试设置重置...")
        async with session.post(f"{API_BASE}/api/settings/reset") as response:
            if response.status == 200:
                reset_data = await response.json()
                if reset_data.get('success'):
                    print("✅ 设置重置成功")
                    reset_settings = reset_data['data']
                    print(f"   TTS提供商: {reset_settings['tts']['provider']}")
                    print(f"   LLM提供商: {reset_settings['llm']['provider']}")
                    print(f"   主题: {reset_settings['general']['theme']}")
                else:
                    print(f"❌ 设置重置失败: {reset_data.get('message')}")
            else:
                print(f"❌ 重置API调用失败: {response.status}")

async def test_database_persistence():
    """测试数据库持久化"""
    print("\n🔍 5. 验证数据库持久化...")
    
    import sqlite3
    DATABASE_PATH = "D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"
    
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        cursor.execute("SELECT tts_provider, llm_provider, theme FROM settings ORDER BY updated_at DESC LIMIT 1;")
        record = cursor.fetchone()
        
        if record:
            tts_provider, llm_provider, theme = record
            print(f"   数据库中最新记录:")
            print(f"   TTS提供商: {tts_provider}")
            print(f"   LLM提供商: {llm_provider}")
            print(f"   主题: {theme}")
        else:
            print("   ❌ 未找到数据库记录")
            
    except Exception as e:
        print(f"   ❌ 数据库查询错误: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 系统设置前后端集成测试")
    print("=" * 60)
    
    try:
        await test_settings_integration()
        await test_database_persistence()
        
        print("\n" + "=" * 60)
        print("✅ 测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
