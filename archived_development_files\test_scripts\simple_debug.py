"""
简化的封面生成调试脚本
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

async def main():
    try:
        print("🔍 开始调试...")
        
        from src.core.database import get_session_maker
        print("✅ 数据库模块导入成功")
        
        from src.services.cover_screenshot_service import cover_screenshot_service
        print("✅ 封面截图服务导入成功")
        
        from src.models.accounts import Account
        from src.models.video_generation import VideoGenerationTask
        print("✅ 模型导入成功")
        
        session_maker = get_session_maker()
        db = session_maker()
        print("✅ 数据库连接成功")
        
        # 查找失败的任务
        failed_tasks = db.query(VideoGenerationTask).filter(
            VideoGenerationTask.status == 'failed'
        ).order_by(VideoGenerationTask.updated_at.desc()).limit(3).all()
        
        print(f"📋 找到 {len(failed_tasks)} 个失败任务")
        
        if failed_tasks:
            task = failed_tasks[0]
            print(f"   任务ID: {task.id}")
            print(f"   任务名: {task.task_name}")
            print(f"   状态: {task.status}")
            print(f"   当前步骤: {task.current_step}")
            print(f"   第一句话: {task.first_sentence}")
            
            # 获取账号
            account = db.query(Account).filter(Account.id == task.account_id).first()
            if account:
                print(f"   账号: {account.name}")
                print(f"   头像路径: {account.avatar_file_path}")
            else:
                print("   ❌ 找不到账号")
        
        db.close()
        print("✅ 调试完成")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
