"""
SQLite database adapter implementation
"""

import aiosqlite
import asyncio
from typing import Any, Dict, List, Optional
from pathlib import Path
from loguru import logger

from ..base import DatabaseAdapter


class SqliteAdapter(DatabaseAdapter):
    """SQLite database adapter implementation"""
    
    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._connection: Optional[aiosqlite.Connection] = None
        self._lock = asyncio.Lock()
    
    async def connect(self) -> None:
        """Establish database connection"""
        if self._connection is None:
            self._connection = await aiosqlite.connect(
                self.db_path,
                isolation_level=None  # Autocommit mode
            )
            # Enable WAL mode for better concurrent access
            await self._connection.execute("PRAGMA journal_mode=WAL")
            await self._connection.execute("PRAGMA foreign_keys=ON")
            logger.info(f"Connected to SQLite database: {self.db_path}")
    
    async def disconnect(self) -> None:
        """Close database connection"""
        if self._connection:
            await self._connection.close()
            self._connection = None
            logger.info("Disconnected from SQLite database")
    
    async def execute(self, query: str, params: Optional[Dict[str, Any]] = None) -> None:
        """Execute a query without returning results"""
        async with self._lock:
            await self.connect()
            if params:
                await self._connection.execute(query, params)
            else:
                await self._connection.execute(query)
            await self._connection.commit()
    
    async def fetch_one(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Fetch a single row"""
        async with self._lock:
            await self.connect()
            self._connection.row_factory = aiosqlite.Row
            cursor = await self._connection.execute(query, params or {})
            row = await cursor.fetchone()
            return dict(row) if row else None
    
    async def fetch_all(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Fetch all matching rows"""
        async with self._lock:
            await self.connect()
            self._connection.row_factory = aiosqlite.Row
            cursor = await self._connection.execute(query, params or {})
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def fetch_many(self, query: str, params: Optional[Dict[str, Any]] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch limited number of rows"""
        query_with_limit = f"{query} LIMIT {limit}"
        return await self.fetch_all(query_with_limit, params)
