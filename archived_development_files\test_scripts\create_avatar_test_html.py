#!/usr/bin/env python3
"""
创建HTML测试文件来验证头像显示效果
"""

import sys
import os
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

def create_avatar_test_html():
    """创建头像测试HTML"""
    try:
        from src.core.database import get_db_session
        from src.models.accounts import Account
        from src.models.resources import CoverTemplate
        from src.services.cover_screenshot_service import CoverScreenshotService
        from src.services.template_import_service import template_import_service
        
        print("=== 创建头像测试HTML ===")
        
        db = get_db_session()
        
        # 获取第一个账号
        account = db.query(Account).first()
        template = db.query(CoverTemplate).first()
        
        print(f"账号: {account.name}")
        print(f"模板: {template.name}")
        
        # 获取头像Base64数据
        service = CoverScreenshotService()
        avatar_data = service._get_avatar_path(account)
        
        print(f"头像数据类型: {'Base64' if avatar_data.startswith('data:') else 'URL'}")
        print(f"头像数据长度: {len(avatar_data)} 字符")
        
        # 准备变量
        variables = {
            'avatar': avatar_data,
            'account_name': account.name,
            'title': '这是测试头像显示效果的标题文本',
            'description': '测试描述...'
        }
        
        # 渲染模板
        rendered_html = template_import_service.render_template(
            template_id=template.id,
            variables=variables,
            db=db
        )
        
        # 保存为测试HTML文件
        with open("avatar_display_test.html", "w", encoding="utf-8") as f:
            f.write(rendered_html)
        
        print(f"\n✅ 测试HTML文件已生成: avatar_display_test.html")
        print("请在浏览器中打开此文件，检查头像是否正确显示为圆形图片")
        
        # 同时创建一个简化的对比测试
        simple_test_html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>头像显示对比测试</title>
    <style>
        .avatar-test {{
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            background: #f0f0f0;
            margin: 20px;
            border: 2px solid #ccc;
        }}
        .avatar-test img {{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }}
    </style>
</head>
<body>
    <h1>头像显示对比测试</h1>
    
    <h2>1. 简化版头像显示</h2>
    <div class="avatar-test">
        <img src="{avatar_data}" alt="头像" />
    </div>
    
    <h2>2. 原始Base64数据信息</h2>
    <p>数据类型: {'Base64' if avatar_data.startswith('data:') else 'URL'}</p>
    <p>数据长度: {len(avatar_data)} 字符</p>
    <p>前100字符: {avatar_data[:100]}...</p>
    
    <h2>3. 模板CSS样式测试</h2>
    <div style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 20px; position: relative; overflow: hidden; background: #f0f0f0; border: 2px solid #000;">
        <img src="{avatar_data}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;" />
    </div>
</body>
</html>
"""
        
        with open("avatar_comparison_test.html", "w", encoding="utf-8") as f:
            f.write(simple_test_html)
        
        print("✅ 对比测试HTML文件已生成: avatar_comparison_test.html")
        
        db.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_avatar_test_html()
