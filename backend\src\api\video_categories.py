"""
视频分类管理API
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from uuid import uuid4

from ..core.database import get_db
from ..core.responses import success_response, error_response
from ..models.resources import VideoCategory
from ..schemas.resources import (
    VideoCategoryCreate,
    VideoCategoryUpdate,
    VideoCategoryResponse
)

router = APIRouter(prefix="/video-categories", tags=["video-categories"])


@router.get("/", response_model=List[VideoCategoryResponse])
async def get_video_categories(db: Session = Depends(get_db)):
    """获取所有视频分类"""
    try:
        from ..models.resources import VideoMaterial
        
        categories = db.query(VideoCategory).all()
        
        # 为每个分类计算素材数量
        result = []
        for cat in categories:
            # 计算该分类下的素材数量
            material_count = db.query(VideoMaterial).filter(
                VideoMaterial.category == cat.name,
                VideoMaterial.is_deleted == False
            ).count()
            
            # 获取基础格式化数据
            cat_data = cat.to_frontend_format()
            # 添加素材数量
            cat_data["material_count"] = material_count
            result.append(cat_data)
        
        return success_response(result, "获取视频分类列表成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频分类列表失败: {str(e)}")


@router.post("/", response_model=VideoCategoryResponse)
async def create_video_category(
    category_data: VideoCategoryCreate,
    db: Session = Depends(get_db)
):
    """创建新的视频分类"""
    try:
        # 检查分类名称是否已存在
        existing = db.query(VideoCategory).filter(VideoCategory.name == category_data.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="分类名称已存在")
        
        # 创建新分类
        category = VideoCategory(
            id=str(uuid4()),
            name=category_data.name,
            description=category_data.description
        )
        
        db.add(category)
        db.commit()
        db.refresh(category)
        
        return success_response(category.to_frontend_format(), "创建视频分类成功")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建视频分类失败: {str(e)}")


@router.put("/{category_id}", response_model=VideoCategoryResponse)
async def update_video_category(
    category_id: str,
    category_data: VideoCategoryUpdate,
    db: Session = Depends(get_db)
):
    """更新视频分类"""
    try:
        category = db.query(VideoCategory).filter(VideoCategory.id == category_id).first()
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 如果更新名称，检查是否与其他分类重复
        if category_data.name and category_data.name != category.name:
            existing = db.query(VideoCategory).filter(
                VideoCategory.name == category_data.name,
                VideoCategory.id != category_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="分类名称已存在")
            setattr(category, 'name', category_data.name)
        
        if category_data.description is not None:
            setattr(category, 'description', category_data.description)
        
        db.commit()
        db.refresh(category)
        
        return success_response(category.to_frontend_format(), "更新视频分类成功")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新视频分类失败: {str(e)}")


@router.delete("/{category_id}")
async def delete_video_category(
    category_id: str,
    db: Session = Depends(get_db)
):
    """删除视频分类"""
    try:
        category = db.query(VideoCategory).filter(VideoCategory.id == category_id).first()
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 检查是否有视频使用该分类
        from ..models.resources import VideoMaterial
        videos_using_category = db.query(VideoMaterial).filter(VideoMaterial.category == category.name).count()
        if videos_using_category > 0:
            raise HTTPException(status_code=400, detail=f"无法删除分类，有 {videos_using_category} 个视频正在使用该分类")
        
        db.delete(category)
        db.commit()
        
        return success_response(None, "删除视频分类成功")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除视频分类失败: {str(e)}")


@router.get("/{category_id}", response_model=VideoCategoryResponse)
async def get_video_category(
    category_id: str,
    db: Session = Depends(get_db)
):
    """获取单个视频分类详情"""
    try:
        category = db.query(VideoCategory).filter(VideoCategory.id == category_id).first()
        if not category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        return success_response(category.to_frontend_format(), "获取视频分类详情成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频分类详情失败: {str(e)}")
