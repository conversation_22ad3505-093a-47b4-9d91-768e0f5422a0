import sqlite3
import os

db_path = 'backend/reddit_story_generator.db'
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

cursor.execute('SELECT id, name, avatar_url, avatar_file_path FROM accounts')
rows = cursor.fetchall()

print('=== 账号头像完整信息 ===')
print(f'共找到 {len(rows)} 个账号')

for row in rows:
    print(f'ID: {row[0]}')
    print(f'  名称: {row[1]}')
    print(f'  URL: {row[2]}')
    print(f'  文件路径: {row[3]}')
    
    # 检查文件是否存在
    if row[3]:
        exists = os.path.exists(row[3])
        print(f'  文件是否存在: {exists}')
        if exists:
            print(f'  文件大小: {os.path.getsize(row[3])} bytes')
    print()

conn.close()
