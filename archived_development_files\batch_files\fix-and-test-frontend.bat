@echo off
chcp 65001 >nul
echo ================================
echo Reddit Story Video Generator
echo Frontend Hydration Fix & Test
echo ================================
echo.

set "PROJECT_ROOT=%~dp0"
set "FRONTEND_DIR=%PROJECT_ROOT%frontend"

echo [1/6] 检查前端目录...
if not exist "%FRONTEND_DIR%" (
    echo ❌ Frontend 目录不存在: %FRONTEND_DIR%
    pause
    exit /b 1
)

echo [2/6] 进入前端目录...
cd /d "%FRONTEND_DIR%"

echo [3/6] 安装/更新依赖...
echo 安装 npm 依赖...
call npm install
if %ERRORLEVEL% neq 0 (
    echo ❌ npm install 失败
    pause
    exit /b 1
)

echo [4/6] 类型检查...
echo 运行 TypeScript 类型检查...
call npm run type-check
if %ERRORLEVEL% neq 0 (
    echo ⚠️ TypeScript 类型检查有警告，继续运行...
)

echo [5/6] 构建测试...
echo 尝试构建项目...
call npm run build
if %ERRORLEVEL% neq 0 (
    echo ❌ 构建失败，但继续启动开发服务器...
) else (
    echo ✅ 构建成功
)

echo [6/6] 启动开发服务器...
echo.
echo ================================
echo 🚀 启动开发服务器
echo 📍 http://localhost:3000
echo ================================
echo.
echo 📝 Hydration 修复说明:
echo - 使用动态导入禁用 SSR
echo - 实现 ClientOnly 组件
echo - 添加加载状态
echo - 优化 Next.js 配置
echo.
echo 按 Ctrl+C 停止服务器
echo.

call npm run dev
