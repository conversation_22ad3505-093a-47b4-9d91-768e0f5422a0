"""
测试封面80%宽度设置的效果
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

# 加载环境变量
load_dotenv(dotenv_path=backend_path / '.env')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate, VideoMaterial, BackgroundMusic, Prompt
from backend.src.services.video_generation_helpers import VideoCompositionService

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_cover_80_percent():
    """测试80%宽度的封面效果"""
    db = SessionLocal()
    try:
        print("=== 测试80%宽度封面设置 ===")
        
        # 获取测试资源
        account = db.query(Account).first()
        template = db.query(CoverTemplate).first()
        materials = db.query(VideoMaterial).limit(3).all()
        music = db.query(BackgroundMusic).first()
        
        if not all([account, template, materials, music]):
            print("❌ 缺少必要的测试资源")
            return
        
        print(f"✅ 使用账号: {account.name}")
        print(f"✅ 使用封面模板: {template.name}")
        print(f"✅ 使用视频素材: {len(materials)} 个")
        print(f"✅ 使用背景音乐: {music.name}")
        
        # 构建测试任务数据
        class MockTask:
            def __init__(self, account_id):
                self.id = "cover-80-percent-test"
                self.account_id = account_id
                self.audio_file_path = "uploads/audio/test_audio.mp3"  # 假设的音频文件
                
        task = MockTask(account.id)
        
        # 检查是否有现有的封面文件用于测试
        cover_file = backend_path / "uploads" / "covers" / "1_subprocess-test-task_cover.png"
        if not cover_file.exists():
            print("❌ 未找到测试封面文件，请先运行封面生成测试")
            return
            
        print(f"✅ 使用现有封面文件: {cover_file}")
        
        # 创建一个简单的音频文件用于测试（如果不存在）
        audio_file = backend_path / task.audio_file_path
        audio_file.parent.mkdir(parents=True, exist_ok=True)
        if not audio_file.exists():
            # 创建一个空的音频文件用于测试
            import subprocess
            subprocess.run([
                'ffmpeg', '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
                '-ac', '1', '-ar', '22050', str(audio_file), '-y'
            ], capture_output=True)
            
        # 创建字幕文件
        subtitle_file = backend_path / "uploads" / "subtitles" / f"{task.account_id}_{task.id}.srt"
        subtitle_file.parent.mkdir(parents=True, exist_ok=True)
        with open(subtitle_file, 'w', encoding='utf-8') as f:
            f.write("""1
00:00:00,000 --> 00:00:03,000
这是一个测试字幕

2
00:00:03,000 --> 00:00:05,000
验证80%宽度封面效果
""")
        
        output_video = backend_path / "uploads" / "videos" / f"test_80_percent_cover_{task.id}.mp4"
        output_video.parent.mkdir(parents=True, exist_ok=True)
        
        video_settings = {
            'resolution': '1080x1920',  # 竖屏
            'fps': 30,
            'format': 'mp4'
        }
        
        print(f"🔄 开始生成测试视频（80%宽度封面）...")
        
        # 调用视频合成
        success = VideoCompositionService.compose_video(
            task=task,
            materials=materials,
            background_music=music,
            audio_duration=5.0,  # 5秒测试视频
            cover_image_path=str(cover_file.relative_to(backend_path)),
            first_sentence_duration=3.0,  # 前3秒显示封面
            subtitle_file_path=str(subtitle_file.relative_to(backend_path)),
            output_path=str(output_video.relative_to(backend_path)),
            video_settings=video_settings
        )
        
        if success and output_video.exists():
            file_size = output_video.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ 测试视频生成成功!")
            print(f"   输出路径: {output_video}")
            print(f"   文件大小: {file_size:.2f} MB")
            print(f"   视频分辨率: {video_settings['resolution']}")
            print(f"   封面宽度: 80% of video width ({int(1080 * 0.8)}px)")
            print("✅ 请查看生成的视频，验证封面是否以80%宽度居中显示")
        else:
            print("❌ 测试视频生成失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_cover_80_percent())
