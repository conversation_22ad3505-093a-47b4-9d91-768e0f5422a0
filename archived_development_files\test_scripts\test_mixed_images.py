#!/usr/bin/env python3
"""
测试混合图片模板导入
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def test_mixed_images():
    try:
        from src.services.template_import_service import TemplateImportService
        from src.core.database import get_db_session
        from src.models.resources import CoverTemplate
        
        print("=== 测试混合图片模板导入 ===")
        
        service = TemplateImportService()
        db = get_db_session()
        
        # 模板路径
        template_path = Path("reddit-template/mixed_images_template.html")
        
        if not template_path.exists():
            print(f"❌ 模板文件不存在: {template_path}")
            return False
        
        print(f"✅ 找到模板文件: {template_path}")
        
        # 先读取HTML内容测试图片提取
        with open(template_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 提取图片路径
        image_paths = service.extract_image_paths_from_html(html_content)
        print(f"🖼️  提取的图片路径:")
        for i, path in enumerate(image_paths, 1):
            print(f"   {i}. {path}")
        
        # 清理已存在的模板
        existing = db.query(CoverTemplate).filter(CoverTemplate.name == "混合图片测试模板").first()
        if existing:
            # 删除相关文件
            old_template_file = service.templates_dir / f"{existing.id}.html"
            old_images_dir = service.templates_dir / f"{existing.id}_images"
            
            if old_template_file.exists():
                os.remove(old_template_file)
            if old_images_dir.exists():
                import shutil
                shutil.rmtree(old_images_dir)
            
            db.delete(existing)
            db.commit()
            print("🗑️  清理了已存在的模板和文件")
        
        # 导入模板
        print("📥 开始导入混合图片模板...")
        imported_template = service.import_html_template(
            html_file_path=str(template_path),
            name="混合图片测试模板",
            description="包含本地图片和远程图片的测试模板",
            category="测试",
            db=db
        )
        
        if imported_template:
            print("✅ 模板导入成功")
            print(f"   - 模板ID: {imported_template.id}")
            print(f"   - 模板名称: {imported_template.name}")
            print(f"   - 变量列表: {imported_template.variables}")
            
            # 检查文件结构
            template_file = service.templates_dir / f"{imported_template.id}.html"
            images_dir = service.templates_dir / f"{imported_template.id}_images"
            
            print(f"📁 生成的文件结构:")
            print(f"   📄 模板文件: {template_file}")
            
            if images_dir.exists():
                print(f"   📁 图片目录: {images_dir}")
                
                # 递归列出所有文件
                for root, dirs, files in os.walk(images_dir):
                    level = root.replace(str(images_dir), '').count(os.sep)
                    indent = '   ' + '  ' * (level + 1)
                    folder_name = os.path.basename(root) if level > 0 else "images"
                    print(f"{indent}📁 {folder_name}/")
                    
                    subindent = '   ' + '  ' * (level + 2)
                    for file in files:
                        file_path = Path(root) / file
                        file_size = file_path.stat().st_size
                        print(f"{subindent}📷 {file} ({file_size} bytes)")
            
            # 验证处理后的HTML
            if template_file.exists():
                with open(template_file, 'r', encoding='utf-8') as f:
                    processed_html = f.read()
                
                updated_paths = service.extract_image_paths_from_html(processed_html)
                print(f"🔄 处理后的图片路径:")
                for i, path in enumerate(updated_paths, 1):
                    print(f"   {i}. {path}")
            
            return True
        else:
            print("❌ 模板导入失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    success = test_mixed_images()
    print(f"\n{'🎉 测试成功！' if success else '💥 测试失败！'}")
