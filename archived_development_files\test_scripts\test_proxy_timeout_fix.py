#!/usr/bin/env python3
"""
测试代理超时修复
"""

import requests
import time
from pathlib import Path

def test_direct_api():
    """测试直接API访问"""
    print("🔧 测试直接API访问（绕过Next.js代理）...")
    
    backend_url = "http://localhost:8000"
    template_file = Path("reddit-template/social_post_template.html")
    
    if not template_file.exists():
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        # 测试健康检查
        print("🔍 测试后端直接连接...")
        health_response = requests.get(f"{backend_url}/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ 后端直接连接正常")
        else:
            print("❌ 后端连接异常")
            return False
        
        # 读取模板文件
        with open(template_file, 'rb') as f:
            file_content = f.read()
        
        # 准备请求数据
        files = {
            'file': (template_file.name, file_content, 'text/html')
        }
        data = {
            'name': 'Reddit模板-代理绕过测试',
            'description': '测试直接API连接，绕过Next.js代理',
            'category': '社交媒体'
        }
        
        print(f"🚀 发送导入请求到: {backend_url}/api/cover-templates/import-html")
        print("⏳ 正在处理，请等待（可能需要较长时间下载海外图片）...")
        
        start_time = time.time()
        
        # 使用较长的超时时间
        response = requests.post(
            f"{backend_url}/api/cover-templates/import-html",
            files=files,
            data=data,
            timeout=180  # 3分钟超时
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"⏱️ 处理时间: {processing_time:.2f} 秒")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print("✅ 直接API调用成功!")
                    print(f"📊 响应消息: {result.get('message')}")
                    
                    # 验证模板变量
                    if 'data' in result and 'variables' in result['data']:
                        variables = result['data']['variables']
                        print(f"📝 检测到的变量: {variables}")
                        expected_vars = ['avatar', 'account_name', 'title']
                        for var in expected_vars:
                            if var in variables:
                                print(f"✅ 变量 {{{{{var}}}}} 被正确保护")
                    
                    return True
                else:
                    print(f"❌ 导入失败: {result.get('message')}")
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text[:500]}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误详情: {response.text[:500]}")
        
        return False
        
    except requests.exceptions.Timeout:
        print("⏰ 请求超时（180秒）")
        print("💡 建议检查网络连接或减少模板中的海外图片")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_frontend_proxy():
    """测试前端代理（可选）"""
    print("\n🌐 测试Next.js代理（localhost:3000）...")
    
    try:
        # 简单测试代理是否能工作
        response = requests.get("http://localhost:3000/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ Next.js代理正常工作")
            return True
        else:
            print(f"❌ 代理异常: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("ℹ️ 前端服务未运行或代理配置问题（这是正常的）")
    except Exception as e:
        print(f"ℹ️ 代理测试异常: {e}")
    
    return False

if __name__ == "__main__":
    print("🔧 代理超时修复测试\n")
    
    # 测试直接API访问
    direct_ok = test_direct_api()
    
    # 测试前端代理
    proxy_ok = test_frontend_proxy()
    
    print(f"\n📋 测试结果总结:")
    print(f"  直接API访问: {'✅ 通过' if direct_ok else '❌ 失败'}")
    print(f"  前端代理测试: {'✅ 通过' if proxy_ok else 'ℹ️ 跳过'}")
    
    if direct_ok:
        print("\n🎉 直接API访问成功！")
        print("💡 修复说明:")
        print("  - 前端导入请求现在直接访问后端API（绕过Next.js代理）")
        print("  - 超时时间增加到3分钟，适合处理海外图片")
        print("  - 后端优化了图片下载逻辑，提高容错性")
        print("  - 模板变量 {{xxx}} 完全不受影响")
    else:
        print("\n⚠️ 需要进一步检查:")
        print("  - 确保后端服务正在运行")
        print("  - 检查网络连接")
        print("  - 考虑减少模板中的海外图片数量")
