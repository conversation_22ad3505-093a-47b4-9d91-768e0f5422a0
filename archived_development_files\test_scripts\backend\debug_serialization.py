#!/usr/bin/env python
"""
调试API响应序列化问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_serialization():
    print("🔍 调试API响应序列化...")
    
    try:
        from src.core.responses import ApiResponse
        from src.schemas.settings import SettingsResponse, TTSConfig, LLMConfig, GeneralSettings
        from src.models.settings import Settings
        
        # 创建Settings对象
        settings = Settings()
        frontend_data = settings.to_frontend_format()
        
        print(f"✅ frontend_data: {frontend_data}")
        
        # 创建SettingsResponse
        settings_response = SettingsResponse(
            tts=TTSConfig(**frontend_data["tts"]),
            llm=LLMConfig(**frontend_data["llm"]),
            general=GeneralSettings(**frontend_data["general"])
        )
        
        print(f"✅ settings_response类型: {type(settings_response)}")
        
        # 测试SettingsResponse序列化
        settings_dict = settings_response.model_dump()
        print(f"✅ settings_response序列化: {settings_dict}")
        
        # 创建ApiResponse
        api_response = ApiResponse.success(
            data=settings_response,
            message="获取设置成功"
        )
        
        print(f"✅ api_response类型: {type(api_response)}")
        print(f"✅ api_response成功: {api_response.success}")
        print(f"✅ api_response消息: {api_response.message}")
        
        # 测试ApiResponse序列化
        response_dict = api_response.model_dump()
        print(f"✅ api_response序列化: {response_dict}")
        
        print("\n🎉 序列化测试通过!")
        
    except Exception as e:
        print(f"❌ 序列化测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_serialization()
