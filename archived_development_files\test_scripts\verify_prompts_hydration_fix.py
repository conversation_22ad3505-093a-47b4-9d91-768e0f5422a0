#!/usr/bin/env python3
"""
提示词管理页面水合错误修复验证
"""

import os
import time
from pathlib import Path

def verify_hydration_fix():
    """验证水合错误修复"""
    print("🔧 验证提示词管理页面水合错误修复...")
    print("=" * 60)
    
    # 检查 ClientOnly 组件
    client_only_file = Path("frontend/src/components/ClientOnly.tsx")
    if client_only_file.exists():
        print("✅ ClientOnly 组件已创建")
        content = client_only_file.read_text(encoding='utf-8')
        if 'useEffect' in content and 'hasMounted' in content:
            print("✅ ClientOnly 组件实现正确")
        else:
            print("❌ ClientOnly 组件实现不完整")
    else:
        print("❌ ClientOnly 组件不存在")
        return False
    
    # 检查提示词页面修改
    prompts_page = Path("frontend/src/app/prompts/page.tsx")
    if prompts_page.exists():
        content = prompts_page.read_text(encoding='utf-8')
        
        if 'import ClientOnly from' in content:
            print("✅ 提示词页面已导入 ClientOnly 组件")
        else:
            print("❌ 提示词页面未导入 ClientOnly 组件")
            return False
            
        if 'PromptsPageContent' in content:
            print("✅ 提示词页面已分离为客户端组件")
        else:
            print("❌ 提示词页面未正确分离")
            return False
    else:
        print("❌ 提示词页面不存在")
        return False
    
    # 检查 promptStore 配置
    prompt_store = Path("frontend/src/store/promptStore.ts")
    if prompt_store.exists():
        content = prompt_store.read_text(encoding='utf-8')
        
        if 'skipHydration: true' in content:
            print("✅ promptStore 已配置 skipHydration")
        else:
            print("❌ promptStore 未配置 skipHydration")
            return False
            
        if 'partialize:' in content:
            print("✅ promptStore 已配置 partialize")
        else:
            print("❌ promptStore 未配置 partialize")
            return False
    else:
        print("❌ promptStore 不存在")
        return False
    
    print("\n✅ 水合错误修复验证完成！")
    print("\n🚀 修复内容总结:")
    print("1. 创建了 ClientOnly 组件防止 SSR 水合错误")
    print("2. 将提示词页面包装在 ClientOnly 中")
    print("3. 配置了 promptStore 的 persist 选项:")
    print("   - skipHydration: true")
    print("   - partialize: 只持久化 UI 状态")
    print("4. 移除了可能导致不一致的运行时状态")
    
    print("\n🧪 测试建议:")
    print("1. 启动开发服务器: npm run dev")
    print("2. 访问 http://localhost:3000/prompts")
    print("3. 检查浏览器控制台是否还有水合错误")
    print("4. 测试页面功能是否正常")
    
    return True

if __name__ == "__main__":
    success = verify_hydration_fix()
    if success:
        print("\n🎉 验证通过！可以启动应用测试。")
    else:
        print("\n❌ 验证失败！请检查修复内容。")
