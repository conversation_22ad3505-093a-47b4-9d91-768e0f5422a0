"""
字幕样式测试脚本
测试字幕配置参数是否正确应用到视频中
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
backend_path = project_root / "backend" / "src"
sys.path.append(str(backend_path))

from services.video_generation_helpers import VideoCompositionService

def test_subtitle_styles():
    """测试字幕样式配置"""
    print("🧪 开始测试字幕样式配置...")
    
    # 创建临时SRT文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
        srt_content = """1
00:00:01,000 --> 00:00:03,000
这是一个测试字幕

2
00:00:03,500 --> 00:00:06,000
用来验证字幕样式是否生效

3
00:00:06,500 --> 00:00:09,000
包括字体、大小、颜色和位置
"""
        f.write(srt_content)
        srt_file = f.name
    
    # 测试不同的样式配置
    test_cases = [
        {
            'name': '默认样式（应该使用SRT）',
            'font_name': 'Arial',
            'font_size': 24,
            'font_color': '#FFFFFF',
            'position': 'bottom'
        },
        {
            'name': '自定义字体（应该使用ASS）',
            'font_name': 'Microsoft YaHei',
            'font_size': 24,
            'font_color': '#FFFFFF',
            'position': 'bottom'
        },
        {
            'name': '自定义大小（应该使用ASS）',
            'font_name': 'Arial',
            'font_size': 32,
            'font_color': '#FFFFFF',
            'position': 'bottom'
        },
        {
            'name': '自定义颜色（应该使用ASS）',
            'font_name': 'Arial',
            'font_size': 24,
            'font_color': '#FF0000',
            'position': 'bottom'
        },
        {
            'name': '自定义位置（应该使用ASS）',
            'font_name': 'Arial',
            'font_size': 24,
            'font_color': '#FFFFFF',
            'position': 'top'
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📝 测试案例 {i+1}: {test_case['name']}")
        print(f"   字体: {test_case['font_name']}")
        print(f"   大小: {test_case['font_size']}")
        print(f"   颜色: {test_case['font_color']}")
        print(f"   位置: {test_case['position']}")
        
        # 生成ASS文件
        ass_file = srt_file.replace('.srt', f'_{i+1}.ass')
        
        try:
            result = VideoCompositionService._create_ass_subtitle_file(
                srt_file,
                ass_file,
                test_case['font_name'],
                test_case['font_size'],
                test_case['font_color'],
                test_case['position']
            )
            
            if result:
                print(f"   ✅ ASS文件生成成功: {ass_file}")
                
                # 检查ASS文件内容
                with open(ass_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 验证样式是否正确应用
                if test_case['font_name'] in content:
                    print(f"   ✅ 字体设置正确")
                else:
                    print(f"   ❌ 字体设置错误")
                
                if str(test_case['font_size']) in content:
                    print(f"   ✅ 字体大小设置正确")
                else:
                    print(f"   ❌ 字体大小设置错误")
                    
                # 显示ASS文件的Style行（便于检查）
                for line in content.split('\n'):
                    if line.startswith('Style: Default'):
                        print(f"   📄 样式行: {line}")
                        break
                        
            else:
                print(f"   ❌ ASS文件生成失败")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 清理临时文件
    try:
        os.unlink(srt_file)
        for i in range(len(test_cases)):
            ass_file = srt_file.replace('.srt', f'_{i+1}.ass')
            if os.path.exists(ass_file):
                os.unlink(ass_file)
    except:
        pass
    
    print("\n🎉 字幕样式测试完成！")

if __name__ == "__main__":
    test_subtitle_styles()
