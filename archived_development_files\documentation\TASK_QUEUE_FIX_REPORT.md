# 任务队列无法启动问题修复报告

## 问题诊断

### 发现的根本原因
通过分析发现，视频生成任务创建后一直处于`pending`状态，没有真正开始执行的根本原因是：

1. **服务实例不一致**：在FastAPI启动时创建了一个VideoGenerationService实例并启动了任务队列，但在API路由中每次都创建新的VideoGenerationService实例来处理请求
2. **任务队列隔离**：任务队列运行在启动时的服务实例中，但任务是通过API路由中的新实例创建的，导致两者完全隔离

### 具体问题位置

#### 启动时的代码（main.py）
```python
# 启动时创建服务实例A并启动任务队列
task_service = VideoGenerationService(session_maker)
await task_service.start_task_queue()  # 任务队列在实例A中运行
```

#### API路由中的代码（video_generation.py）
```python
# 每次API请求都创建新的服务实例B
service = VideoGenerationService(session_maker)
job = await service.create_job(request)  # 任务创建在实例B中
```

**结果**：实例A的任务队列永远看不到实例B创建的任务！

## 修复方案

### 1. 创建全局服务管理模块
创建了`src/core/services.py`来管理全局服务实例：

```python
# 全局服务实例
_video_generation_service: Optional[VideoGenerationService] = None

def get_video_generation_service(session_maker: Optional[sessionmaker] = None) -> VideoGenerationService:
    """获取视频生成服务的全局实例"""
    global _video_generation_service
    
    if _video_generation_service is None:
        if session_maker is None:
            from .database import get_session_maker
            session_maker = get_session_maker()
        
        _video_generation_service = VideoGenerationService(session_maker)
    
    return _video_generation_service
```

### 2. 修改启动逻辑
在`main.py`中设置全局服务实例：

```python
# 启动任务队列处理器
task_service = VideoGenerationService(session_maker)
await task_service.start_task_queue()

# 设置全局服务实例 - 关键修复
set_video_generation_service(task_service)
```

### 3. 修改API路由
在所有API端点中使用全局服务实例：

```python
# 修复前
service = VideoGenerationService(session_maker)  # 每次创建新实例

# 修复后
service = get_video_generation_service(session_maker)  # 使用全局实例
```

## 修复的文件

1. **新增文件**：
   - `backend/src/core/services.py` - 全局服务管理

2. **修改文件**：
   - `backend/main.py` - 设置全局服务实例
   - `backend/src/api/video_generation.py` - 使用全局服务实例

## 修复验证

### 预期结果
修复后，应该观察到：
1. 新创建的视频任务立即开始执行
2. 任务状态从`pending`变为`running`
3. 控制台出现视频生成处理日志
4. 任务进度正常更新

### 验证步骤
1. 重启后端服务以应用修改
2. 创建新的视频生成作业
3. 观察任务状态和日志输出
4. 确认任务正常进入视频生成流程

## 技术说明

### 为什么会出现这个问题？
- FastAPI的依赖注入系统每次都会创建新的服务实例
- 任务队列是基于内存的状态管理，不同实例之间无法共享状态
- 缺乏单例模式的服务管理

### 为什么这样修复？
- 使用全局实例确保整个应用只有一个VideoGenerationService
- 任务创建和任务执行在同一个服务实例中进行
- 保持任务队列的连续性和状态一致性

### 潜在风险和注意事项
- 全局实例需要线程安全（当前基于asyncio单线程，安全）
- 服务重启时需要重新初始化全局实例
- 测试时需要重置全局状态以避免测试间干扰

## 后续优化建议

1. **持久化任务队列**：考虑使用Redis或数据库来管理任务队列状态
2. **健康检查**：添加任务队列运行状态的健康检查端点
3. **监控日志**：增强任务处理的日志记录和监控
4. **异常恢复**：添加任务队列异常停止时的自动恢复机制

---

**修复完成时间**：2025-07-04
**修复人员**：AI Assistant
**影响范围**：视频生成任务处理系统
