#!/usr/bin/env python3
"""
测试批量生成的cover_settings修复
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.schemas.video_generation import BatchVideoGenerationJobConfig, CoverSettings

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_batch_cover_settings():
    """测试批量生成配置中的cover_settings"""
    
    logger.info("🔍 测试批量生成配置中的cover_settings...")
    
    try:
        # 创建测试配置
        config = BatchVideoGenerationJobConfig(
            material_selection="random",
            video_material_group="default",
            voice_settings={
                "voice": "zh_female_zhixingnvsheng_mars_bigtts",
                "speed": 1.0
            },
            audio_settings={
                "speech_volume": 1.0,
                "background_music_volume": 0.15,
                "enable_background_music": True
            },
            music_selection="random",
            background_music_group="default",
            cover_template_id="test_template",
            cover_settings={
                "position": "center",
                "animation": "slide_in_left",  # 测试slide_in_left
                "animation_duration": 1.5
            },
            subtitle_config={
                "font_family": "Arial",
                "font_size": 24,
                "font_color": "#FFFFFF",
                "position": "bottom",
                "enabled": True
            },
            video_config={
                "resolution": "1080x1920",
                "fps": 30,
                "format": "mp4"
            }
        )
        
        logger.info("✅ BatchVideoGenerationJobConfig 创建成功")
        
        # 转换为字典（模拟存储到数据库的过程）
        config_dict = config.dict()
        logger.info(f"配置字典: {config_dict}")
        
        # 检查cover_settings是否存在
        if 'cover_settings' in config_dict:
            cover_settings = config_dict['cover_settings']
            logger.info(f"✅ cover_settings 存在: {cover_settings}")
            
            # 检查animation值
            if cover_settings.get('animation') == 'slide_in_left':
                logger.info("✅ animation 值正确: slide_in_left")
            else:
                logger.error(f"❌ animation 值错误: {cover_settings.get('animation')}")
                return False
                
            # 检查其他字段
            if cover_settings.get('position') == 'center':
                logger.info("✅ position 值正确: center")
            else:
                logger.error(f"❌ position 值错误: {cover_settings.get('position')}")
                return False
                
            if cover_settings.get('animation_duration') == 1.5:
                logger.info("✅ animation_duration 值正确: 1.5")
            else:
                logger.error(f"❌ animation_duration 值错误: {cover_settings.get('animation_duration')}")
                return False
                
        else:
            logger.error("❌ cover_settings 不存在于配置中")
            return False
        
        # 测试从字典重新获取（模拟从数据库读取的过程）
        logger.info("\n🔍 测试从字典获取cover_settings...")
        retrieved_cover_settings = config_dict.get('cover_settings')
        
        if retrieved_cover_settings:
            logger.info(f"✅ 成功从字典获取cover_settings: {retrieved_cover_settings}")
            
            animation = retrieved_cover_settings.get('animation')
            if animation == 'slide_in_left':
                logger.info("✅ 从字典获取的animation值正确: slide_in_left")
            else:
                logger.error(f"❌ 从字典获取的animation值错误: {animation}")
                return False
        else:
            logger.error("❌ 无法从字典获取cover_settings")
            return False
        
        logger.info("\n🎉 所有测试通过！")
        logger.info("BatchVideoGenerationJobConfig 现在正确支持 cover_settings")
        logger.info("批量生成应该能正确传递动画设置了")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_cover_settings_schema():
    """测试CoverSettings模式"""
    
    logger.info("\n🔍 测试CoverSettings模式...")
    
    try:
        # 测试各种动画类型
        test_animations = [
            'none',
            'fade_in',
            'fade_out', 
            'fade_in_out',
            'slide_in_left',
            'slide_in_right',
            'slide_in_top',
            'slide_in_bottom',
            'slide_out_left',
            'slide_out_right',
            'slide_out_top',
            'slide_out_bottom',
            'zoom_in',
            'zoom_out',
            'zoom_in_out'
        ]
        
        for animation in test_animations:
            cover_settings = CoverSettings(
                position="center",
                animation=animation,
                animation_duration=1.5
            )
            logger.info(f"✅ {animation} 动画类型验证通过")
        
        logger.info("✅ 所有动画类型都支持")
        return True
        
    except Exception as e:
        logger.error(f"❌ CoverSettings模式测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始测试批量生成cover_settings修复")
    
    # 测试1：BatchVideoGenerationJobConfig
    success1 = test_batch_cover_settings()
    
    # 测试2：CoverSettings模式
    success2 = test_cover_settings_schema()
    
    if success1 and success2:
        logger.info("\n🎉 所有测试通过！")
        logger.info("修复完成：批量生成现在支持cover_settings了")
        logger.info("现在可以重新提交批量生成任务，应该能正确使用slide_in_left动画")
    else:
        logger.error("\n❌ 测试失败")
        sys.exit(1)
