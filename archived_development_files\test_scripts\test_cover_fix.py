#!/usr/bin/env python3
"""
测试封面修复 - 验证圆角封面的尺寸和位置是否正确
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_cover_fix():
    """测试封面修复"""

    # 测试文件路径 - 使用现有文件
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    output_path = "debug_cover_fix.mp4"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试封面修复...")
    logger.info(f"视频: {video_path}")
    logger.info(f"封面: {cover_path}")
    logger.info(f"输出: {output_path}")
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        duration = float(video_info['duration'])
        
        logger.info(f"视频信息: {width}x{height}, 时长: {duration}s")
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        cover_duration = min(3.0, duration)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px, 时长={cover_duration}s")
        
        # 创建临时目录
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        
        # 测试圆角封面创建
        rounded_cover_path = temp_dir / "test_rounded_cover.png"
        logger.info(f"创建圆角封面: {rounded_cover_path}")
        
        success = VideoCompositionService._create_rounded_cover_image(
            cover_path, cover_width, corner_radius, str(rounded_cover_path)
        )
        
        if not success or not rounded_cover_path.exists():
            logger.error("❌ 圆角封面创建失败")
            return False
            
        logger.info("✅ 圆角封面创建成功")
        
        # 创建视频流
        video_input = ffmpeg.input(video_path)
        cover_input = ffmpeg.input(str(rounded_cover_path))
        
        # 叠加封面（居中）
        output = video_input.overlay(
            cover_input,
            x='(main_w-overlay_w)/2',
            y='(main_h-overlay_h)/2',
            enable=f'between(t,0,{cover_duration})'
        )
        
        # 输出视频
        out = ffmpeg.output(
            output,
            output_path,
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p',
            t=5  # 只生成5秒测试视频
        ).overwrite_output()
        
        logger.info("开始执行FFmpeg命令...")
        
        # 显示FFmpeg命令
        cmd = ffmpeg.compile(out)
        logger.info(f"FFmpeg命令: {' '.join(cmd)}")
        
        # 执行
        ffmpeg.run(out, quiet=False)
        
        # 检查结果
        if Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            logger.info(f"✅ 测试成功! 输出文件: {output_path}, 大小: {file_size} bytes")
            
            # 获取输出视频信息验证
            try:
                output_probe = ffmpeg.probe(output_path)
                output_info = next(s for s in output_probe['streams'] if s['codec_type'] == 'video')
                output_width = int(output_info['width'])
                output_height = int(output_info['height'])
                logger.info(f"输出视频尺寸: {output_width}x{output_height}")
                
                if output_width == width and output_height == height:
                    logger.info("✅ 视频尺寸验证通过")
                else:
                    logger.warning(f"⚠️ 视频尺寸不匹配: 期望{width}x{height}, 实际{output_width}x{output_height}")
                    
            except Exception as e:
                logger.warning(f"无法验证输出视频信息: {e}")
            
            return True
        else:
            logger.error("❌ 测试失败: 输出文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False

def test_different_video_sizes():
    """测试不同视频尺寸的封面处理"""
    
    logger.info("\n🔄 开始测试不同视频尺寸...")
    
    # 模拟不同的视频尺寸
    test_sizes = [
        (1080, 1920, "1080x1920 (竖屏)"),
        (1920, 1080, "1920x1080 (横屏)"),
        (720, 1280, "720x1280 (小竖屏)"),
        (1440, 2560, "1440x2560 (2K竖屏)")
    ]
    
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"

    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    for width, height, desc in test_sizes:
        logger.info(f"\n--- 测试 {desc} ---")
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"视频尺寸: {width}x{height}")
        logger.info(f"封面宽度: {cover_width}px")
        logger.info(f"圆角半径: {corner_radius}px")
        
        # 创建临时目录
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        
        # 测试圆角封面创建
        rounded_cover_path = temp_dir / f"test_rounded_cover_{width}x{height}.png"
        
        success = VideoCompositionService._create_rounded_cover_image(
            cover_path, cover_width, corner_radius, str(rounded_cover_path)
        )
        
        if success and rounded_cover_path.exists():
            # 检查生成的图像尺寸
            try:
                from PIL import Image
                with Image.open(rounded_cover_path) as img:
                    actual_width, actual_height = img.size
                    logger.info(f"生成的封面尺寸: {actual_width}x{actual_height}")
                    
                    if actual_width == cover_width:
                        logger.info("✅ 封面宽度正确")
                    else:
                        logger.error(f"❌ 封面宽度错误: 期望{cover_width}, 实际{actual_width}")
                        
            except Exception as e:
                logger.error(f"❌ 无法检查生成的图像: {e}")
        else:
            logger.error(f"❌ 圆角封面创建失败: {desc}")
    
    logger.info("\n✅ 不同尺寸测试完成")
    return True

if __name__ == "__main__":
    logger.info("🚀 开始封面修复测试")
    
    # 测试1: 基本封面修复
    success1 = test_cover_fix()
    
    # 测试2: 不同视频尺寸
    success2 = test_different_video_sizes()
    
    if success1 and success2:
        logger.info("\n🎉 所有测试通过!")
    else:
        logger.error("\n❌ 部分测试失败")
        sys.exit(1)
