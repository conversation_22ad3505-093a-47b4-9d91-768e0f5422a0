/**
 * 视频分类管理 - 直接API客户端 (不使用fetch)
 * 所有API调用都直接访问后端，无超时限制
 */

import DirectHttpClient from './directHttpClient'
import { ApiResponse } from '@/types/store'

// 保持原有接口类型不变，确保兼容性
export interface VideoCategory {
  id: string
  name: string
  description?: string
  material_count: number
  createdAt?: string
  updatedAt?: string
}

export interface VideoCategoryCreateRequest {
  name: string
  description?: string
}

export interface VideoCategoryUpdateRequest {
  name?: string
  description?: string
}

export class DirectVideoCategoriesAPI {
  private client: DirectHttpClient
  private static instance: DirectVideoCategoriesAPI
  
  constructor() {
    this.client = new DirectHttpClient('/api/video-categories')
  }

  static getInstance(): DirectVideoCategoriesAPI {
    if (!DirectVideoCategoriesAPI.instance) {
      DirectVideoCategoriesAPI.instance = new DirectVideoCategoriesAPI()
    }
    return DirectVideoCategoriesAPI.instance
  }

  /**
   * 获取所有视频分类
   */
  async getCategories(): Promise<ApiResponse<VideoCategory[]>> {
    return this.client.get<ApiResponse<VideoCategory[]>>('/')
  }

  /**
   * 创建新的视频分类
   */
  async createCategory(data: VideoCategoryCreateRequest): Promise<ApiResponse<VideoCategory>> {
    return this.client.post<ApiResponse<VideoCategory>>('/', data)
  }

  /**
   * 更新视频分类
   */
  async updateCategory(id: string, data: VideoCategoryUpdateRequest): Promise<ApiResponse<VideoCategory>> {
    return this.client.put<ApiResponse<VideoCategory>>(`/${id}`, data)
  }

  /**
   * 删除视频分类
   */
  async deleteCategory(id: string): Promise<ApiResponse<null>> {
    return this.client.delete<ApiResponse<null>>(`/${id}`)
  }

  /**
   * 获取单个视频分类详情
   */
  async getCategory(id: string): Promise<ApiResponse<VideoCategory>> {
    return this.client.get<ApiResponse<VideoCategory>>(`/${id}`)
  }
}

export const directVideoCategoriesAPI = DirectVideoCategoriesAPI.getInstance()
