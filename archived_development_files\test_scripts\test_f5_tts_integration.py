#!/usr/bin/env python3
"""
F5-TTS集成测试脚本
"""

import sys
import os
sys.path.append('src')

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    
    try:
        # 测试基础模型导入
        from models.f5_tts_voices import F5TTSVoice
        print("✅ F5TTSVoice模型导入成功")
    except Exception as e:
        print(f"❌ F5TTSVoice模型导入失败: {e}")
        return False
    
    try:
        # 测试API导入
        from api.f5_tts_voices import router
        print("✅ F5-TTS API路由导入成功")
    except Exception as e:
        print(f"❌ F5-TTS API路由导入失败: {e}")
        return False
    
    try:
        # 测试gradio_client导入
        import gradio_client
        print("✅ gradio_client导入成功")
    except Exception as e:
        print(f"❌ gradio_client导入失败: {e}")
        print("请运行: pip install gradio_client")
        return False
    
    return True

def test_database_model():
    """测试数据库模型"""
    print("\n🗄️ 测试数据库模型...")
    
    try:
        from models.f5_tts_voices import F5TTSVoice
        
        # 创建模型实例
        voice = F5TTSVoice(
            name="测试音色",
            description="这是一个测试音色",
            language="zh-CN",
            gender="female",
            ref_audio_path="/path/to/test.wav",
            ref_text="这是测试文本"
        )
        
        # 测试to_frontend_format方法
        frontend_data = voice.to_frontend_format()
        
        required_fields = ['id', 'name', 'description', 'language', 'gender', 'provider', 'ref_audio_path', 'ref_text']
        for field in required_fields:
            if field not in frontend_data:
                print(f"❌ 缺少必需字段: {field}")
                return False
        
        print("✅ 数据库模型测试通过")
        print(f"   - 音色名称: {frontend_data['name']}")
        print(f"   - 提供商: {frontend_data['provider']}")
        return True
        
    except Exception as e:
        print(f"❌ 数据库模型测试失败: {e}")
        return False

def test_api_routes():
    """测试API路由"""
    print("\n🌐 测试API路由...")
    
    try:
        from fastapi import FastAPI
        from api.f5_tts_voices import router
        
        app = FastAPI()
        app.include_router(router)
        
        # 检查路由是否正确注册
        routes = [route.path for route in app.routes if hasattr(route, 'path')]
        expected_routes = [
            '/api/f5-tts-voices',
            '/api/f5-tts-voices/{voice_id}',
            '/api/f5-tts-voices/{voice_id}/test'
        ]
        
        for expected_route in expected_routes:
            # 检查是否有匹配的路由模式
            route_found = any(expected_route.replace('{voice_id}', '') in route for route in routes)
            if route_found:
                print(f"✅ 路由存在: {expected_route}")
            else:
                print(f"❌ 路由缺失: {expected_route}")
                return False
        
        print("✅ API路由测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API路由测试失败: {e}")
        return False

def test_tts_service():
    """测试TTS服务"""
    print("\n🎵 测试TTS服务...")
    
    try:
        from services.tts_service import TTSService
        from core.database import get_session_maker
        
        session_maker = get_session_maker()
        tts_service = TTSService(session_maker)
        
        # 测试获取可用音色
        voices = tts_service.get_available_voices()
        print(f"✅ TTS服务初始化成功，当前音色数量: {len(voices)}")
        
        return True
        
    except Exception as e:
        print(f"❌ TTS服务测试失败: {e}")
        return False

def test_settings_integration():
    """测试设置集成"""
    print("\n⚙️ 测试设置集成...")
    
    try:
        from models.settings import Settings
        
        # 创建设置实例
        settings = Settings()
        frontend_data = settings.to_frontend_format()
        
        # 检查是否包含F5-TTS配置
        tts_config = frontend_data.get('tts', {})
        if 'f5TtsEndpoint' in tts_config:
            print("✅ 设置模型包含F5-TTS配置")
        else:
            print("❌ 设置模型缺少F5-TTS配置")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 设置集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始F5-TTS集成测试\n")
    
    tests = [
        ("导入测试", test_imports),
        ("数据库模型测试", test_database_model),
        ("API路由测试", test_api_routes),
        ("TTS服务测试", test_tts_service),
        ("设置集成测试", test_settings_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！F5-TTS集成成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
