#!/usr/bin/env python3
"""
端到端测试视频生成流程，验证封面头像修复
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

async def test_video_generation_with_avatar():
    """测试带有正确头像的视频生成流程"""
    try:
        from src.core.database import get_db_session
        from src.models.accounts import Account
        from src.models.resources import CoverTemplate
        from src.services.cover_screenshot_service import cover_screenshot_service
        
        print("=== 端到端测试视频生成流程封面头像 ===")
        
        db = get_db_session()
        
        # 获取第一个账号
        account = db.query(Account).first()
        if not account:
            print("未找到账号")
            return
        
        # 获取第一个模板
        template = db.query(CoverTemplate).first()
        if not template:
            print("未找到封面模板")
            return
        
        print(f"账号: {account.name}")
        print(f"模板: {template.name}")
        
        # 模拟视频生成任务中的封面生成
        class MockTask:
            def __init__(self):
                self.id = "test-task"
                self.account_id = account.id
                self.first_sentence = "这是一个测试故事的第一句话，用来验证封面生成功能"
        
        task = MockTask()
        output_path = f"video_cover_test_{account.id}.png"
        
        print(f"\n生成视频封面...")
        print(f"输出路径: {output_path}")
        
        # 使用视频任务专用方法生成封面
        success = await cover_screenshot_service.generate_cover_for_video_task(
            task=task,
            template_id=template.id,
            account=account,
            title=task.first_sentence,
            output_path=output_path,
            db=db
        )
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"\n✅ 视频封面生成成功！")
            print(f"封面文件: {output_path}")
            print(f"文件大小: {file_size} bytes")
            
            # 验证头像是否已正确处理
            print(f"\n🔍 验证头像处理:")
            if account.avatar_file_path:
                backend_dir = Path(__file__).parent / 'backend'
                avatar_path = backend_dir / account.avatar_file_path
                if avatar_path.exists():
                    print(f"✅ 账号头像文件存在: {avatar_path}")
                    print(f"✅ 头像已转换为Base64并嵌入到封面中")
                else:
                    print(f"❌ 账号头像文件不存在: {avatar_path}")
            else:
                print(f"⚠️ 账号没有设置头像文件")
                
        else:
            print(f"\n❌ 视频封面生成失败")
        
        db.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_video_generation_with_avatar())
