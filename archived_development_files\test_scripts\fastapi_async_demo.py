"""
FastAPI异步处理机制演示
展示await如何让出控制权，允许处理其他请求
"""

import asyncio
import time
import threading
from fastapi import FastAPI
from loguru import logger

app = FastAPI()

# 记录当前线程ID
def get_thread_info():
    return f"线程ID: {threading.get_ident()}"

# 模拟耗时的异步操作（如ffmpeg、Whisper等）
async def simulate_heavy_async_work(task_name: str, duration: int):
    """模拟耗时异步操作"""
    logger.info(f"[{task_name}] 开始异步工作 - {get_thread_info()}")
    
    # 使用asyncio.sleep而不是time.sleep，这样会让出控制权
    await asyncio.sleep(duration)
    
    logger.info(f"[{task_name}] 异步工作完成 - {get_thread_info()}")
    return f"{task_name}完成"

# 模拟耗时的同步操作（阻塞版本）
def simulate_heavy_sync_work(task_name: str, duration: int):
    """模拟耗时同步操作（阻塞）"""
    logger.info(f"[{task_name}] 开始同步工作（阻塞） - {get_thread_info()}")
    
    # time.sleep会阻塞整个线程
    time.sleep(duration)
    
    logger.info(f"[{task_name}] 同步工作完成 - {get_thread_info()}")
    return f"{task_name}完成"

@app.get("/async-request/{request_id}")
async def async_request_handler(request_id: str):
    """异步请求处理器"""
    start_time = time.time()
    logger.info(f"🟢 [请求{request_id}] 开始处理 - {get_thread_info()}")
    
    # 模拟视频生成中的异步操作
    if request_id == "A":
        # 请求A：模拟长时间的ffmpeg操作
        result = await simulate_heavy_async_work(f"请求{request_id}-ffmpeg", 5)
    elif request_id == "B":
        # 请求B：快速操作
        result = f"请求{request_id}快速响应"
        await asyncio.sleep(0.1)  # 模拟很短的异步操作
    else:
        # 其他请求：中等时长操作
        result = await simulate_heavy_async_work(f"请求{request_id}-其他", 2)
    
    end_time = time.time()
    logger.info(f"✅ [请求{request_id}] 处理完成，耗时: {end_time - start_time:.2f}秒 - {get_thread_info()}")
    
    return {
        "request_id": request_id,
        "result": result,
        "duration": f"{end_time - start_time:.2f}秒",
        "thread_info": get_thread_info(),
        "timestamp": time.time()
    }

@app.get("/sync-request/{request_id}")
async def sync_request_handler(request_id: str):
    """同步请求处理器（阻塞版本）"""
    start_time = time.time()
    logger.info(f"🔴 [同步请求{request_id}] 开始处理 - {get_thread_info()}")
    
    # 使用同步阻塞操作
    if request_id == "A":
        result = simulate_heavy_sync_work(f"同步请求{request_id}", 5)
    else:
        result = simulate_heavy_sync_work(f"同步请求{request_id}", 2)
    
    end_time = time.time()
    logger.info(f"✅ [同步请求{request_id}] 处理完成，耗时: {end_time - start_time:.2f}秒 - {get_thread_info()}")
    
    return {
        "request_id": request_id,
        "result": result,
        "duration": f"{end_time - start_time:.2f}秒",
        "thread_info": get_thread_info(),
        "timestamp": time.time()
    }

@app.get("/status")
async def status():
    """状态检查接口"""
    return {
        "status": "running",
        "thread_info": get_thread_info(),
        "timestamp": time.time()
    }

if __name__ == "__main__":
    print("""
FastAPI异步机制演示

测试步骤：
1. 启动服务器：python fastapi_async_demo.py
2. 打开多个浏览器标签页，同时访问：
   - http://localhost:8000/async-request/A  (5秒异步操作)
   - http://localhost:8000/async-request/B  (0.1秒快速操作)
   - http://localhost:8000/async-request/C  (2秒异步操作)
   - http://localhost:8000/status         (状态检查)

观察现象：
✅ 异步版本：所有请求几乎同时响应，B和C不会被A阻塞
❌ 同步版本：后续请求必须等待前面的请求完成

对比测试同步版本：
   - http://localhost:8000/sync-request/A  (5秒同步操作，会阻塞)
   - http://localhost:8000/sync-request/B  (必须等A完成后才能处理)
""")
    
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
