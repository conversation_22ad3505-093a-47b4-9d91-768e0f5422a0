#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Account模型属性修复
"""

import sys
from pathlib import Path

# 添加backend路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def test_account_model():
    """测试Account模型属性"""
    try:
        print("🧪 测试Account模型属性...")
        
        # 导入模型
        from src.models.accounts import Account
        
        # 检查模型属性
        account_fields = [attr for attr in dir(Account) if not attr.startswith('_')]
        print(f"✅ Account模型字段: {account_fields}")
        
        # 检查关键字段
        required_fields = ['id', 'name', 'platform', 'status']
        for field in required_fields:
            if hasattr(Account, field):
                print(f"✅ 字段 '{field}' 存在")
            else:
                print(f"❌ 字段 '{field}' 不存在")
        
        # 验证没有username字段
        if hasattr(Account, 'username'):
            print("⚠️ Account模型仍然有username字段")
        else:
            print("✅ Account模型正确使用name字段（无username）")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 Account模型属性修复验证")
    print("=" * 50)
    
    success = test_account_model()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Account模型属性验证通过")
        print("💡 数据库模型属性错误已修复")
    else:
        print("❌ Account模型属性验证失败")
    print("=" * 50)

if __name__ == "__main__":
    main()
