"""
LLM测试API
用于测试提示词功能
"""

from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any, Optional
import aiohttp
import json
import asyncio
from loguru import logger

from ..core.database import get_db
from ..core.responses import success_response, error_response
from ..services.settings_service import get_current_llm_config
from ..schemas.settings import LLMConfig

router = APIRouter(prefix="/llm", tags=["llm"])

class TestPromptRequest(BaseModel):
    prompt: str
    llm_config: Optional[LLMConfig] = None  # 可选，如果不提供则使用系统设置
    variables: Dict[str, str] = {}

async def call_openai_compatible_api(config: LLMConfig, prompt: str) -> str:
    """调用OpenAI兼容的API"""
    
    # 构建请求
    headers = {
        "Content-Type": "application/json"
    }
    
    if config.apiKey:
        headers["Authorization"] = f"Bearer {config.apiKey}"
    
    # 构建消息
    messages = []
    if config.systemPrompt:
        messages.append({"role": "system", "content": config.systemPrompt})
    messages.append({"role": "user", "content": prompt})
    
    data = {
        "model": config.model,
        "messages": messages,
        "temperature": config.temperature,
        "max_tokens": config.maxTokens
    }
    
    # 确定API端点
    endpoint = config.endpoint or "https://api.openai.com/v1/chat/completions"
    if not endpoint.endswith('/chat/completions'):
        endpoint = endpoint.rstrip('/') + '/chat/completions'
    
    async with aiohttp.ClientSession() as session:
        async with session.post(endpoint, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"LLM API调用失败: {response.status} - {error_text}"
                )
            
            # 兼容处理：无论Content-Type是什么，都尝试解析为JSON
            response_text = await response.text()
            logger.info(f"LLM API调用返回信息: {response_text}")
            try:
                result = json.loads(response_text)
            except json.JSONDecodeError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"LLM API返回数据不是有效的JSON格式: {str(e)}"
                )
            
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="LLM返回格式异常"
                )

async def call_claude_api(config: LLMConfig, prompt: str) -> str:
    """调用Claude API"""
    
    headers = {
        "Content-Type": "application/json",
        "x-api-key": config.apiKey,
        "anthropic-version": "2023-06-01"
    }
    
    data = {
        "model": config.model,
        "max_tokens": config.maxTokens,
        "temperature": config.temperature,
        "messages": [{"role": "user", "content": prompt}]
    }
    
    if config.systemPrompt:
        data["system"] = config.systemPrompt
    
    endpoint = config.endpoint or "https://api.anthropic.com/v1/messages"
    
    async with aiohttp.ClientSession() as session:
        async with session.post(endpoint, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Claude API调用失败: {response.status} - {error_text}"
                )
            
            # 兼容处理：无论Content-Type是什么，都尝试解析为JSON
            response_text = await response.text()
            try:
                result = json.loads(response_text)
            except json.JSONDecodeError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Claude API返回数据不是有效的JSON格式: {str(e)}"
                )
            
            if 'content' in result and len(result['content']) > 0:
                return result['content'][0]['text']
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Claude返回格式异常"
                )

@router.post("/test-prompt")
async def test_prompt(request: TestPromptRequest, db: Session = Depends(get_db)):
    """测试提示词"""
    try:
        # 如果请求中没有提供LLM配置，则从系统设置中获取
        llm_config = request.llm_config
        if not llm_config:
            llm_config = get_current_llm_config(db)
            if not llm_config:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="未找到LLM配置，请先在系统设置中配置LLM服务"
                )
        
        # 检查必要的配置项
        if not llm_config.apiKey:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="LLM API密钥未配置，请先在系统设置中配置API密钥"
            )
        
        # 替换变量
        prompt = request.prompt
        for key, value in request.variables.items():
            prompt = prompt.replace(f"{{{key}}}", value)
        
        # 根据提供商调用不同的API
        if llm_config.provider in ["openai", "azure", "yunwu", "local"]:
            result = await call_openai_compatible_api(llm_config, prompt)
        elif llm_config.provider == "claude":
            result = await call_claude_api(llm_config, prompt)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的LLM提供商: {llm_config.provider}"
            )
        
        return success_response({"result": result}, "测试完成")
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试失败: {str(e)}"
        )
