@echo off
echo 🧹 Cleaning project dependencies...

echo Cleaning frontend...
cd frontend
if exist "node_modules" (
    echo Removing frontend node_modules...
    rmdir /s /q node_modules
)
if exist "package-lock.json" (
    del package-lock.json
)
if exist ".next" (
    echo Removing .next build cache...
    rmdir /s /q .next
)
cd ..

echo Cleaning backend...
cd backend
if exist "venv" (
    echo Removing Python virtual environment...
    rmdir /s /q venv
)
if exist "__pycache__" (
    rmdir /s /q __pycache__
)
if exist "src\__pycache__" (
    rmdir /s /q src\__pycache__
)
cd ..

echo ✅ Cleanup completed!
echo You can now run test-frontend.bat or test-backend.bat to reinstall dependencies
pause
