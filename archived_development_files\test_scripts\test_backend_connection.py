"""
简单的后端连接测试
"""

import requests
import sys

def test_backend_connection():
    """测试后端连接"""
    backend_url = "http://localhost:8000"
    
    try:
        print("🔌 测试后端连接...")
        response = requests.get(f"{backend_url}/api/cover-templates", timeout=5)
        print(f"✅ 后端连接成功: {response.status_code}")
        
        if response.status_code == 200:
            templates = response.json()
            print(f"📋 当前模板数量: {len(templates)}")
            return True
        else:
            print(f"⚠️ 后端响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 后端连接失败: 服务器未启动或端口不可用")
        return False
    except requests.exceptions.Timeout:
        print("❌ 后端连接超时")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

if __name__ == "__main__":
    if test_backend_connection():
        print("\n🎯 后端服务正常，可以继续测试新建模板修复")
    else:
        print("\n🚨 请确保后端服务正在运行:")
        print("   cd backend && python main.py")
        sys.exit(1)
