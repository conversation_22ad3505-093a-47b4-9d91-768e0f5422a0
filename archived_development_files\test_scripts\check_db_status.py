#!/usr/bin/env python3
"""
检查数据库中的作业和任务状态
"""
import sys
from pathlib import Path

# 添加backend路径
backend_dir = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_dir))

from src.core.database import get_session_maker
from src.models.video_generation import VideoGenerationJob, VideoGenerationTask

def check_database_status():
    """检查数据库状态"""
    session_maker = get_session_maker()
    db = session_maker()
    
    try:
        # 检查作业数量
        jobs_count = db.query(VideoGenerationJob).count()
        tasks_count = db.query(VideoGenerationTask).count()
        
        print(f'数据库中的作业数量: {jobs_count}')
        print(f'数据库中的任务数量: {tasks_count}')
        
        # 获取最近5个作业的状态
        recent_jobs = db.query(VideoGenerationJob).order_by(VideoGenerationJob.created_at.desc()).limit(5).all()
        
        print('\n最近5个作业状态:')
        for job in recent_jobs:
            print(f'  作业ID: {job.id}')
            print(f'  名称: {job.name}')
            print(f'  状态: {job.status}')
            print(f'  创建时间: {job.created_at}')
            print(f'  开始时间: {job.started_at}')
            print(f'  总任务数: {job.total_tasks}')
            print(f'  完成任务数: {job.completed_tasks}')
            print('---')
            
        # 获取最近5个任务的状态  
        recent_tasks = db.query(VideoGenerationTask).order_by(VideoGenerationTask.created_at.desc()).limit(5).all()
        
        print('\n最近5个任务状态:')
        for task in recent_tasks:
            print(f'  任务ID: {task.id}')
            print(f'  任务名称: {task.task_name}')
            print(f'  作业ID: {task.job_id}')
            print(f'  状态: {task.status}')
            print(f'  进度: {task.progress}%')
            print(f'  当前步骤: {task.current_step}')
            print(f'  创建时间: {task.created_at}')
            print(f'  开始时间: {task.started_at}')
            print('---')
            
    finally:
        db.close()

if __name__ == "__main__":
    check_database_status()
