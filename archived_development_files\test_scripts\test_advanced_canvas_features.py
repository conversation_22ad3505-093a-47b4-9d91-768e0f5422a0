#!/usr/bin/env python3
"""
封面模板管理高级功能自动化测试
测试撤销重做、元素缩放、层级调整、对齐等功能
"""

import asyncio
import sys
import time
import subprocess
import os
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext

class AdvancedCanvasTester:
    def __init__(self):
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.base_url = "http://localhost:3000"
        
    async def setup_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口
            slow_mo=1000,    # 每个操作间隔1秒
        )
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await self.context.new_page()
        
        # 等待页面加载
        await self.page.goto(f"{self.base_url}/covers")
        await self.page.wait_for_load_state('networkidle')
        
    async def cleanup(self):
        """清理资源"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
            
    async def test_create_template_and_add_elements(self):
        """测试创建模板并添加多个元素"""
        print("📝 测试创建模板并添加多个元素...")
        
        # 点击新建模板按钮
        await self.page.click('button:has-text("新建模板")')
        await asyncio.sleep(1)
        
        # 填写模板信息
        await self.page.fill('input[placeholder="请输入模板名称"]', '高级功能测试模板')
        await self.page.select_option('select:has-option[value="经典"]', '经典')
        await self.page.fill('textarea[placeholder="模板描述"]', '用于测试高级功能的模板')
        
        # 点击创建按钮
        await self.page.click('button:has-text("创建模板")')
        await asyncio.sleep(2)
        
        # 选择文本工具并添加文本元素
        await self.page.click('button:has-text("文本")')
        await asyncio.sleep(0.5)
        
        # 在画布上添加几个文本元素
        canvas = await self.page.query_selector('div[ref="canvasRef"]')
        if canvas:
            box = await canvas.bounding_box()
            # 添加第一个文本元素
            await self.page.click(f'div[ref="canvasRef"]', position={'x': 50, 'y': 30})
            await asyncio.sleep(1)
            
            # 添加第二个文本元素
            await self.page.click(f'div[ref="canvasRef"]', position={'x': 150, 'y': 80})
            await asyncio.sleep(1)
            
        # 选择形状工具并添加形状
        await self.page.click('button:has-text("形状")')
        await asyncio.sleep(0.5)
        
        # 在画布上添加形状元素
        if canvas:
            await self.page.click(f'div[ref="canvasRef"]', position={'x': 200, 'y': 50})
            await asyncio.sleep(1)
            
        print("✅ 成功创建模板并添加多个元素")
        return True
        
    async def test_element_selection_and_properties(self):
        """测试元素选择和属性编辑"""
        print("🎯 测试元素选择和属性编辑...")
        
        # 选择画布上的第一个元素
        elements = await self.page.query_selector_all('div[style*="position: absolute"]')
        if elements:
            await elements[0].click()
            await asyncio.sleep(1)
            
            # 检查属性面板是否显示
            properties_panel = await self.page.query_selector('text="属性设置"')
            if properties_panel:
                print("✅ 属性面板显示正确")
                
                # 尝试修改文本内容
                text_input = await self.page.query_selector('input[value*="文本"]')
                if text_input:
                    await text_input.clear()
                    await text_input.fill('修改后的文本')
                    await asyncio.sleep(1)
                    
                # 尝试修改颜色
                color_input = await self.page.query_selector('input[type="color"]')
                if color_input:
                    await color_input.fill('#ff0000')
                    await asyncio.sleep(1)
                    
        print("✅ 元素选择和属性编辑测试完成")
        return True
        
    async def test_undo_redo_functionality(self):
        """测试撤销重做功能"""
        print("↩️ 测试撤销重做功能...")
        
        # 检查撤销按钮是否可用
        undo_button = await self.page.query_selector('button:has-text("撤销")')
        if undo_button:
            is_disabled = await undo_button.get_attribute('disabled')
            if is_disabled is None:  # 不是disabled状态
                await undo_button.click()
                await asyncio.sleep(1)
                print("✅ 撤销操作执行成功")
                
                # 测试重做
                redo_button = await self.page.query_selector('button:has-text("重做")')
                if redo_button:
                    is_disabled = await redo_button.get_attribute('disabled')
                    if is_disabled is None:
                        await redo_button.click()
                        await asyncio.sleep(1)
                        print("✅ 重做操作执行成功")
                        
        # 测试快捷键
        await self.page.keyboard.press('Control+z')
        await asyncio.sleep(0.5)
        await self.page.keyboard.press('Control+y')
        await asyncio.sleep(0.5)
        
        print("✅ 撤销重做功能测试完成")
        return True
        
    async def test_layer_management(self):
        """测试层级管理功能"""
        print("📚 测试层级管理功能...")
        
        # 选择一个元素
        elements = await self.page.query_selector_all('div[style*="position: absolute"]')
        if elements and len(elements) > 1:
            # 选择第一个元素
            await elements[0].click()
            await asyncio.sleep(1)
            
            # 测试置于顶层
            top_button = await self.page.query_selector('button[title="置于顶层"]')
            if top_button:
                await top_button.click()
                await asyncio.sleep(1)
                print("✅ 置于顶层功能测试完成")
                
            # 测试上移一层
            up_button = await self.page.query_selector('button[title="上移一层"]')
            if up_button:
                await up_button.click()
                await asyncio.sleep(1)
                print("✅ 上移一层功能测试完成")
                
            # 测试下移一层
            down_button = await self.page.query_selector('button[title="下移一层"]')
            if down_button:
                await down_button.click()
                await asyncio.sleep(1)
                print("✅ 下移一层功能测试完成")
                
        print("✅ 层级管理功能测试完成")
        return True
        
    async def test_alignment_tools(self):
        """测试对齐工具"""
        print("📐 测试对齐工具...")
        
        # 选择一个元素
        elements = await self.page.query_selector_all('div[style*="position: absolute"]')
        if elements:
            await elements[0].click()
            await asyncio.sleep(1)
            
            # 测试左对齐
            align_left = await self.page.query_selector('button[title="左对齐"]')
            if align_left:
                await align_left.click()
                await asyncio.sleep(1)
                print("✅ 左对齐功能测试完成")
                
            # 测试水平居中
            align_center = await self.page.query_selector('button[title="水平居中"]')
            if align_center:
                await align_center.click()
                await asyncio.sleep(1)
                print("✅ 水平居中功能测试完成")
                
            # 测试垂直居中
            align_middle = await self.page.query_selector('button[title="垂直居中"]')
            if align_middle:
                await align_middle.click()
                await asyncio.sleep(1)
                print("✅ 垂直居中功能测试完成")
                
        print("✅ 对齐工具测试完成")
        return True
        
    async def test_element_resizing(self):
        """测试元素缩放功能"""
        print("🔄 测试元素缩放功能...")
        
        # 选择一个非文本元素
        elements = await self.page.query_selector_all('div[style*="position: absolute"]')
        for element in elements:
            # 检查是否是形状元素（通常有固定高度）
            style = await element.get_attribute('style')
            if 'height' in style and 'px' in style:
                await element.click()
                await asyncio.sleep(1)
                
                # 查找缩放手柄
                resize_handles = await self.page.query_selector_all('.cursor-se-resize, .cursor-nw-resize')
                if resize_handles:
                    print("✅ 找到缩放手柄")
                    # 可以在这里添加拖拽缩放的测试
                    break
                    
        print("✅ 元素缩放功能测试完成")
        return True
        
    async def test_save_and_export(self):
        """测试保存和导出功能"""
        print("💾 测试保存和导出功能...")
        
        # 测试保存模板
        save_button = await self.page.query_selector('button:has-text("保存")')
        if save_button:
            await save_button.click()
            await asyncio.sleep(2)
            print("✅ 保存功能测试完成")
            
        # 测试导出PNG
        export_button = await self.page.query_selector('button:has-text("导出PNG")')
        if export_button:
            await export_button.click()
            await asyncio.sleep(2)
            print("✅ 导出PNG功能测试完成")
            
        # 测试生成功能
        generate_button = await self.page.query_selector('button:has-text("测试生成")')
        if generate_button:
            await generate_button.click()
            await asyncio.sleep(2)
            print("✅ 测试生成功能测试完成")
            
        print("✅ 保存和导出功能测试完成")
        return True
        
    async def run_all_tests(self):
        """运行所有测试"""
        try:
            await self.setup_browser()
            
            print("🚀 开始高级功能自动化测试...")
            print("=" * 60)
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            # 运行测试套件
            tests = [
                self.test_create_template_and_add_elements,
                self.test_element_selection_and_properties,
                self.test_undo_redo_functionality,
                self.test_layer_management,
                self.test_alignment_tools,
                self.test_element_resizing,
                self.test_save_and_export
            ]
            
            passed = 0
            failed = 0
            
            for test in tests:
                try:
                    result = await test()
                    if result:
                        passed += 1
                    else:
                        failed += 1
                except Exception as e:
                    print(f"❌ 测试失败: {e}")
                    failed += 1
                    
                await asyncio.sleep(1)  # 测试间隔
                
            print("=" * 60)
            print(f"🎯 测试完成: {passed} 通过, {failed} 失败")
            
            if failed == 0:
                print("🎉 所有高级功能测试通过！")
                return True
            else:
                print("⚠️ 部分测试失败，请检查功能实现")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            return False
        finally:
            await self.cleanup()

def check_services():
    """检查必要的服务是否运行"""
    print("🔍 检查服务状态...")
    
    # 检查前端服务
    try:
        import requests
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务运行正常")
        else:
            print("❌ 前端服务访问异常")
            return False
    except:
        print("❌ 前端服务未启动，请运行: npm run dev")
        return False
        
    # 检查后端服务
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务运行正常")
        else:
            print("❌ 后端服务访问异常")
            return False
    except:
        print("❌ 后端服务未启动，请启动后端服务")
        return False
        
    return True

async def main():
    print("🎨 封面模板管理高级功能自动化测试")
    print("=" * 60)
    
    # 检查服务状态
    if not check_services():
        print("⚠️ 请先启动必要的服务")
        return False
        
    # 运行测试
    tester = AdvancedCanvasTester()
    result = await tester.run_all_tests()
    
    return result

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        sys.exit(1)
