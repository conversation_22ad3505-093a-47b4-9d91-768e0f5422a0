#!/usr/bin/env python3
"""
简单的远程图片导入测试
"""

import os
import sys
from pathlib import Path

# 添加backend到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from src.services.template_import_service import TemplateImportService
from src.core.database import get_db_session
from src.models.resources import CoverTemplate

def test_remote_template_import():
    """测试包含远程图片的模板导入"""
    print("=== 测试远程图片模板导入 ===")
    
    service = TemplateImportService()
    db = get_db_session()
    
    try:
        # 测试模板路径
        template_path = Path("reddit-template/remote_test_template.html")
        
        if not template_path.exists():
            print(f"❌ 测试模板文件不存在: {template_path}")
            return False
        
        print(f"✅ 找到测试模板: {template_path}")
        
        # 清理已存在的测试模板
        existing = db.query(CoverTemplate).filter(CoverTemplate.name == "远程图片测试模板").first()
        if existing:
            db.delete(existing)
            db.commit()
            print("🗑️  清理了已存在的测试模板")
        
        # 导入模板
        print("📥 开始导入包含远程图片的模板...")
        imported_template = service.import_html_template(
            html_file_path=str(template_path),
            name="远程图片测试模板",
            description="测试远程图片下载和本地化功能的模板",
            category="测试",
            db=db
        )
        
        if imported_template:
            print("✅ 模板导入成功")
            print(f"   - 模板ID: {imported_template.id}")
            print(f"   - 模板名称: {imported_template.name}")
            print(f"   - 变量列表: {imported_template.variables}")
            
            # 检查图片目录
            templates_dir = service.templates_dir
            images_dir = templates_dir / f"{imported_template.id}_images"
            
            if images_dir.exists():
                print(f"📁 图片目录已创建: {images_dir}")
                
                # 列出所有复制/下载的文件
                for root, dirs, files in os.walk(images_dir):
                    for file in files:
                        file_path = Path(root) / file
                        rel_path = file_path.relative_to(images_dir)
                        file_size = file_path.stat().st_size
                        print(f"   📷 {rel_path} ({file_size} bytes)")
            
            # 读取处理后的HTML
            template_file = templates_dir / f"{imported_template.id}.html"
            if template_file.exists():
                with open(template_file, 'r', encoding='utf-8') as f:
                    processed_html = f.read()
                
                # 检查图片路径
                updated_paths = service.extract_image_paths_from_html(processed_html)
                print(f"🔄 更新后的图片路径: {updated_paths}")
            
            return True
        else:
            print("❌ 模板导入失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    success = test_remote_template_import()
    print("\n🎉 测试成功！" if success else "\n💥 测试失败！")
