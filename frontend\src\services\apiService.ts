/**
 * 统一的API服务层
 * 管理所有与后端的API通信
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

// API响应通用接口
interface ApiResponse<T = any> {
  data?: T
  message?: string
  error?: string
}

// 通用API请求方法
async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    // 确保端点以斜杠结尾，避免重定向
    let finalEndpoint = endpoint;

    // Separate path from query string
    const queryIndex = finalEndpoint.indexOf('?');
    let path = finalEndpoint;
    let queryString = '';

    if (queryIndex !== -1) {
      path = finalEndpoint.substring(0, queryIndex);
      queryString = finalEndpoint.substring(queryIndex);
    }

    // Remove trailing slash from the path part
    // if (path.endsWith('/')) {
    //   path = path.slice(0, -1);
    // }

    finalEndpoint = path + queryString;

    const url = `${API_BASE_URL}${finalEndpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`)
    }

    const responseData = await response.json()
    
    // 适配后端响应格式 - 后端返回 { success, data, message, ... }
    if (responseData.success !== undefined && responseData.data !== undefined) {
      return { data: responseData.data }
    }
    
    // 如果后端直接返回数据
    return { data: responseData }
  } catch (error) {
    console.error(`API Error [${endpoint}]:`, error)
    return { error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// ============= 资源管理API =============

// 账号管理
export const accountsApi = {
  // 获取账号列表
  getAccounts: async (): Promise<ApiResponse<Account[]>> => {
    const response = await apiRequest<{accounts: Account[], total: number, page: number, page_size: number}>('/api/accounts/')
    if (response.data && response.data.accounts) {
      return { data: response.data.accounts, error: response.error }
    }
    return { data: [], error: response.error }
  },
  
  // 获取单个账号详情
  getAccount: (id: string) => apiRequest<Account>(`/api/accounts/${id}`),
}

// 视频素材管理 - 直接API调用版本
export const videoMaterialsApi = {
  // 获取素材列表（支持分类筛选）
  getMaterials: async (category?: string) => {
    const { directVideoMaterialApi } = await import('@/lib/api/directVideoMaterials')
    try {
      const materials = await directVideoMaterialApi.getVideoMaterials(category ? { category } : undefined)
      // 转换 VideoMaterialAPI 到 VideoMaterial 格式
      const convertedMaterials: VideoMaterial[] = materials.map(material => ({
        id: material.id,
        name: material.name,
        file_path: material.filePath || material.path,
        category: material.category,
        duration: material.duration ? parseFloat(material.duration.replace(/[^\d.]/g, '')) || 0 : 0,
        resolution: `${material.dimensions.width}x${material.dimensions.height}`,
        size: material.metadata.fileSize || 0,
        thumbnail_url: material.thumbnailUrl || undefined,
        created_at: material.createdAt || new Date().toISOString()
      }))
      return { data: convertedMaterials, error: null }
    } catch (error) {
      return { data: [], error: error instanceof Error ? error.message : '获取素材失败' }
    }
  },
  
  // 获取素材分类列表
  getCategories: async () => {
    const { directVideoCategoriesAPI } = await import('@/lib/api/directVideoCategories')
    try {
      const response = await directVideoCategoriesAPI.getCategories()
      return { data: response.data || [], error: null }
    } catch (error) {
      return { data: [], error: error instanceof Error ? error.message : '获取分类失败' }
    }
  },
  
  // 获取单个素材详情
  getMaterial: async (id: string) => {
    const { directVideoMaterialApi } = await import('@/lib/api/directVideoMaterials')
    try {
      const material = await directVideoMaterialApi.getVideoMaterial(id)
      return { data: material, error: null }
    } catch (error) {
      return { data: null, error: error instanceof Error ? error.message : '获取素材详情失败' }
    }
  },
}

// 背景音乐管理
export const backgroundMusicApi = {
  // 获取音乐列表
  getMusic: async (category?: string): Promise<ApiResponse<BackgroundMusic[]>> => {
    const params = category ? `?category=${encodeURIComponent(category)}` : ''
    const response = await apiRequest<{items: BackgroundMusic[], total: number, page: number, pageSize: number}>(`/api/background-music${params}`)
    if (response.data && response.data.items) {
      return { data: response.data.items, error: response.error }
    }
    return { data: [], error: response.error }
  },
  
  // 获取音乐分类列表
  getCategories: async (): Promise<ApiResponse<string[]>> => {
    const response = await apiRequest<{items: BackgroundMusic[], total: number, page: number, pageSize: number}>('/api/background-music')
    if (response.data && response.data.items) {
      // 从音乐列表中提取分类
      const categorySet = new Set(response.data.items.map(music => music.category))
      const categories = Array.from(categorySet)
      return { data: categories }
    }
    return { data: [] }
  },
  
  // 获取单个音乐详情
  getMusicItem: (id: string) => apiRequest<BackgroundMusic>(`/api/background-music/${id}`),
}

// 提示词管理
export const promptsApi = {
  // 获取提示词列表 - 直接返回所有提示词，不支持分类筛选
  getPrompts: async (category?: string) => {
    const response = await apiRequest<Prompt[]>('/api/prompts/')
    if (response.data && category) {
      // 在前端进行分类筛选
      const filteredData = response.data.filter(prompt => prompt.category === category)
      return { data: filteredData, error: response.error }
    }
    return response
  },
  
  // 获取提示词分类列表 - 从所有提示词中提取分类
  getCategories: async () => {
    const response = await apiRequest<Prompt[]>('/api/prompts/')
    if (response.data) {
      const categorySet = new Set(response.data.map(prompt => prompt.category))
      const categories = Array.from(categorySet)
      return { data: categories, error: null }
    }
    return { data: [], error: response.error }
  },
  
  // 获取单个提示词详情
  getPrompt: (id: string) => apiRequest<Prompt>(`/api/prompts/${id}`),
}

// 封面模板管理
export const coverTemplatesApi = {
  // 获取模板列表
  getTemplates: async (category?: string): Promise<ApiResponse<CoverTemplate[]>> => {
    const params = category ? `?category=${encodeURIComponent(category)}` : ''
    const response = await apiRequest<{templates: CoverTemplate[], total: number}>(`/api/cover-templates/${params}`)
    if (response.data && response.data.templates) {
      // 提取templates数组
      return { data: response.data.templates, error: response.error }
    }
    return { data: [], error: response.error }
  },
  
  // 获取模板分类列表
  getCategories: () => apiRequest<string[]>('/api/cover-templates/categories/list'),
  
  // 获取单个模板详情
  getTemplate: (id: string) => apiRequest<CoverTemplate>(`/api/cover-templates/${id}`),
  
  // 获取模板变量定义
  getVariables: () => apiRequest<TemplateVariable[]>('/api/cover-templates/variables'),
}

// 系统设置
export const settingsApi = {
  // 获取系统设置
  getSettings: async () => {
    const response = await apiRequest<any>('/settings')
    if (response.data) {
      // 后端返回的是具体配置，需要适配为前端期望的格式
      const settings = response.data
      
      // 根据当前TTS提供商创建音色列表
      const ttsVoices: TTSVoice[] = []
      
      if (settings.tts?.provider === 'coze') {
        // Coze音色选项
        ttsVoices.push(
          {
            id: 'zh_female_zhixingnvsheng_mars_bigtts',
            name: '智慧女声',
            language: 'zh-CN',
            gender: 'female',
            provider: 'coze'
          },
          {
            id: 'zh_male_zhinangnvsheng_mars_bigtts',
            name: '智囊男声',
            language: 'zh-CN', 
            gender: 'male',
            provider: 'coze'
          }
        )
      } else if (settings.tts?.provider === 'openai') {
        // OpenAI音色选项
        ttsVoices.push(
          {
            id: 'alloy',
            name: 'Alloy',
            language: 'en-US',
            gender: 'neutral',
            provider: 'openai'
          },
          {
            id: 'echo',
            name: 'Echo',
            language: 'en-US',
            gender: 'male',
            provider: 'openai'
          },
          {
            id: 'fable',
            name: 'Fable',
            language: 'en-US',
            gender: 'neutral',
            provider: 'openai'
          },
          {
            id: 'nova',
            name: 'Nova',
            language: 'en-US',
            gender: 'female',
            provider: 'openai'
          },
          {
            id: 'shimmer',
            name: 'Shimmer',
            language: 'en-US',
            gender: 'female',
            provider: 'openai'
          }
        )
      }
      
      // 如果当前设置中有voice配置，确保它在列表中
      if (settings.tts?.voice && !ttsVoices.find(v => v.id === settings.tts.voice)) {
        ttsVoices.unshift({
          id: settings.tts.voice,
          name: settings.tts.voice,
          language: 'zh-CN',
          gender: 'unknown',
          provider: settings.tts.provider || 'unknown'
        })
      }

      const adaptedData: SystemSettings = {
        tts_voices: ttsVoices,
        llm_models: [
          {
            id: settings.llm?.model || 'gpt-4o',
            name: settings.llm?.model || 'GPT-4O',
            provider: settings.llm?.provider || 'yunwu',
            max_tokens: settings.llm?.maxTokens || 10236
          }
        ],
        video_resolutions: ['1080x1920', '1920x1080', '720x1280'],
        default_video_fps: 30,
        max_concurrent_tasks: 5
      }
      
      return { data: adaptedData, error: response.error }
    }
    return response
  },
  
  // 更新系统设置
  updateSettings: (settings: Partial<SystemSettings>) => 
    apiRequest<SystemSettings>('/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    }),
}

// ============= 视频生成API =============

// 视频生成作业管理
export const videoGenerationApi = {
  // 创建视频生成作业
  createJob: (jobData: CreateVideoGenerationJobRequest) =>
    apiRequest<VideoGenerationJob>('/api/video-generator/jobs', {
      method: 'POST',
      body: JSON.stringify(jobData),
    }),
  
  // 获取作业列表
  getJobs: (page = 1, limit = 20) =>
    apiRequest<JobListResponse>(`/api/video-generator/jobs?page=${page}&limit=${limit}`),
  
  // 获取作业详情
  getJob: (jobId: string) =>
    apiRequest<VideoGenerationJob>(`/api/video-generator/jobs/${jobId}`),
  
  // 控制视频生成作业（启动、暂停、恢复、取消）
  controlJob: (jobId: string, action: 'start' | 'pause' | 'resume' | 'cancel') =>
    apiRequest(`/api/video-generator/jobs/${jobId}/control`, {
      method: 'POST',
      body: JSON.stringify({ action }),
    }),
  
  // 删除作业
  deleteJob: (jobId: string) =>
    apiRequest(`/api/video-generator/jobs/${jobId}`, { method: 'DELETE' }),

  // 获取作业进度
  getJobProgress: (jobId: string) =>
    apiRequest<JobProgress>(`/api/video-generator/jobs/${jobId}/progress`),

  // 上传Excel文件并解析文案
  uploadExcel: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return fetch(`${API_BASE_URL}/api/video-generator/upload-excel`, {
      method: 'POST',
      body: formData,
    }).then(response => response.json())
  },

  // 创建批量视频生成作业
  createBatchJob: (jobData: CreateBatchVideoGenerationJobRequest) =>
    apiRequest<VideoGenerationJob>('/api/video-generator/batch-jobs', {
      method: 'POST',
      body: JSON.stringify(jobData),
    }),
}

// 视频生成任务管理
export const videoGenerationTasksApi = {
  // 获取任务列表
  getTasks: (jobId?: string, status?: string) => {
    const params = new URLSearchParams()
    if (jobId) params.append('job_id', jobId)
    if (status) params.append('status', status)
    const queryString = params.toString() ? `?${params.toString()}` : ''
    return apiRequest<VideoGenerationTask[]>(`/api/video-generator/tasks${queryString}`)
  },
  
  // 获取任务详情
  getTask: (taskId: string) =>
    apiRequest<VideoGenerationTask>(`/api/video-generator/tasks/${taskId}`),
  
  // 重试任务
  retryTask: (taskId: string) =>
    apiRequest(`/api/video-generator/tasks/${taskId}/retry`, { method: 'POST' }),
  
  // 取消任务
  cancelTask: (taskId: string) =>
    apiRequest(`/api/video-generator/tasks/${taskId}/cancel`, { method: 'POST' }),

  // 获取任务日志
  getTaskLogs: (taskId: string) =>
    apiRequest<TaskLog[]>(`/api/video-generator/tasks/${taskId}/logs`),
}

// ============= 类型定义 =============

// 基础数据类型
export interface Account {
  id: string
  name: string
  platform: string
  avatar_url?: string
  status: string
  created_at: string
  updated_at: string
}

export interface VideoMaterial {
  id: string
  name: string
  file_path: string
  category: string
  duration: number
  resolution: string
  size: number
  thumbnail_url?: string
  created_at: string
}

export interface VideoCategory {
  id: string
  name: string
  description?: string
  material_count: number
}

export interface BackgroundMusic {
  id: string
  name: string
  file_path: string
  category: string
  duration: number
  artist?: string
  size: number
  created_at: string
}

export interface Prompt {
  id: string
  name: string
  content: string
  category: string
  variables: string[]
  description?: string
  created_at: string
}

export interface CoverTemplate {
  id: string
  name: string
  preview_url: string
  variables: string[]
  category: string
  description?: string
  created_at: string
}

export interface TemplateVariable {
  name: string
  type: string
  description: string
  required: boolean
  default_value?: any
}

export interface SystemSettings {
  tts_voices: TTSVoice[]
  llm_models: LLMModel[]
  video_resolutions: string[]
  default_video_fps: number
  max_concurrent_tasks: number
}

export interface TTSVoice {
  id: string
  name: string
  language: string
  gender: string
  provider: string
  // F5-TTS特有字段
  description?: string
  ref_audio_path?: string
  ref_text?: string
  remove_silence?: boolean
  cross_fade_duration?: number
  nfe_value?: number
  randomize_seed?: boolean
  is_active?: boolean
  is_built_in?: boolean
  usage_count?: number
  created_at?: string
  updated_at?: string
}

export interface LLMModel {
  id: string
  name: string
  provider: string
  max_tokens: number
}

// 视频生成相关类型
export interface CreateVideoGenerationJobRequest {
  name: string
  description?: string
  config: VideoGenerationJobConfig
  account_configs: AccountConfig[]
}

export interface VideoGenerationJobConfig {
  // 素材配置
  material_selection: 'random' | 'manual'
  video_material_group?: string
  selected_materials?: string[]
  
  // 文案配置
  prompt_group: string
  prompt_id: string
  
  // 音频配置
  voice_settings: {
    voice: string
    speed: number
  }
  
  // 音量配置
  audio_settings: {
    speech_volume: number      // 语音音量 (0-1)
    background_music_volume: number  // 背景音乐音量 (0-1)
    enable_background_music: boolean  // 是否启用背景音乐
  }
  
  // 音乐配置
  music_selection: 'random' | 'specific'
  background_music_group?: string
  music_id?: string
  
  // 封面配置
  cover_template_id: string
  
  // 字幕配置
  subtitle_config: SubtitleConfig
  
  // 视频配置
  video_config: VideoConfig
}

export interface AccountConfig {
  account_id: string
  video_count: number
}

export interface SubtitleConfig {
  font_family: string
  font_size: number
  font_color: string
  position: 'top' | 'center' | 'bottom'
  words_per_screen: number
  stroke_thickness: number
  stroke_color: string
  enabled: boolean
}

export interface VideoConfig {
  resolution: string
  fps: number
  output_format: string
}

export interface VideoGenerationJob {
  id: string
  name: string
  description?: string
  config: VideoGenerationJobConfig
  account_configs: AccountConfig[]
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled'
  created_at: string
  started_at?: string
  completed_at?: string
  error_message?: string
}

export interface VideoGenerationTask {
  id: string
  job_id: string
  task_name: string
  account_id: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'audio_pending' | 'audio_completed' | 'video_pending'
  progress: number
  current_step?: string
  error_message?: string
  
  // 生成内容
  generated_story?: string
  first_sentence?: string
  audio_file_path?: string
  subtitle_file_path?: string
  cover_image_path?: string
  final_video_path?: string
  
  // 使用的资源
  used_materials?: string[]
  used_music_id?: string
  
  // 时间信息
  created_at: string
  started_at?: string
  completed_at?: string
  
  // 其他信息
  retry_count?: number
  audio_analysis?: any
}

export interface JobListResponse {
  jobs: VideoGenerationJob[]
  total: number
  page: number
  limit: number
}

export interface JobProgress {
  job_id: string
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  running_tasks: number
  pending_tasks: number
  overall_progress: number
  current_step?: string
}

export interface TaskLog {
  id: string
  task_id: string
  level: 'info' | 'warning' | 'error'
  message: string
  created_at: string
}

// 批量视频生成相关接口
export interface CreateBatchVideoGenerationJobRequest {
  name: string
  description?: string
  config: {
    material_selection: 'random' | 'manual'
    video_material_group?: string
    selected_materials?: string[]
    voice_settings: {
      voice?: string
      male_voice?: string
      female_voice?: string
      speed: number
    }
    audio_settings: {
      speech_volume: number
      background_music_volume: number
      enable_background_music: boolean
    }
    music_selection: 'random' | 'specific'
    background_music_group?: string
    music_id?: string
    cover_template_id: string
    subtitle_config: {
      font_family: string
      font_size: number
      font_color: string
      position: string
      enabled: boolean
    }
    video_config: {
      resolution: string
      fps: number
      output_format: string
    }
  }
  account_ids: string[]
  stories: string[]
  titles?: string[]
  genders?: string[]
}

export interface ExcelUploadResponse {
  success: boolean
  stories: string[]
  titles: string[]
  genders: string[]
  total_count: number
  message?: string
  error?: string
}

// F5-TTS音色管理API
export const f5TtsVoicesApi = {
  // 获取F5-TTS音色列表
  getVoices: () => apiRequest<TTSVoice[]>('/api/f5-tts-voices'),

  // 获取单个音色详情
  getVoice: (voiceId: string) => apiRequest<TTSVoice>(`/api/f5-tts-voices/${voiceId}`),

  // 创建新音色
  createVoice: async (voiceData: FormData): Promise<ApiResponse<TTSVoice>> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/f5-tts-voices`, {
        method: 'POST',
        body: voiceData
      })
      return await response.json()
    } catch (error) {
      return { data: undefined, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  },

  // 更新音色
  updateVoice: async (voiceId: string, voiceData: FormData): Promise<ApiResponse<TTSVoice>> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/f5-tts-voices/${voiceId}`, {
        method: 'PUT',
        body: voiceData
      })
      return await response.json()
    } catch (error) {
      return { data: undefined, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  },

  // 删除音色
  deleteVoice: (voiceId: string) =>
    apiRequest<any>(`/api/f5-tts-voices/${voiceId}`, { method: 'DELETE' }),

  // 测试音色
  testVoice: async (voiceId: string, testText: string = "这是一个音色测试。"): Promise<ApiResponse<any>> => {
    try {
      const formData = new FormData()
      formData.append('test_text', testText)

      const response = await fetch(`${API_BASE_URL}/api/f5-tts-voices/${voiceId}/test`, {
        method: 'POST',
        body: formData
      })
      return await response.json()
    } catch (error) {
      return { data: undefined, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

export default {
  accounts: accountsApi,
  videoMaterials: videoMaterialsApi,
  backgroundMusic: backgroundMusicApi,
  prompts: promptsApi,
  coverTemplates: coverTemplatesApi,
  settings: settingsApi,
  videoGeneration: videoGenerationApi,
  videoGenerationTasks: videoGenerationTasksApi,
  f5TtsVoices: f5TtsVoicesApi,
}
