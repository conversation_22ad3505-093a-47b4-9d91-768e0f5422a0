#!/usr/bin/env python3
"""
测试trim修复 - 验证12秒定格问题是否彻底解决
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_trim_fix():
    """测试trim修复"""
    
    logger.info("🎬 测试trim修复 - 目标彻底解决12秒定格问题")
    
    try:
        # 创建4个测试视频，总时长约20秒，确保超过12秒
        video_configs = [
            ('red', '#FF0000', 5.0),      # 5秒红色
            ('green', '#00FF00', 6.0),    # 6秒绿色  
            ('blue', '#0000FF', 4.0),     # 4秒蓝色
            ('yellow', '#FFFF00', 5.0),   # 5秒黄色
        ]
        
        test_files = []
        durations = []
        
        for name, color, duration in video_configs:
            filename = f"trim_test_{name}.mp4"
            logger.info(f"创建 {filename} ({duration}s)")
            
            (
                ffmpeg
                .input(f'color={color}:size=640x480:duration={duration}:rate=30', f='lavfi')
                .output(filename, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
            test_files.append(filename)
            durations.append(duration)
        
        logger.info(f"视频文件: {test_files}")
        logger.info(f"时长: {durations}")
        logger.info(f"总原始时长: {sum(durations)}s")
        
        # 转场配置
        transition_duration = 1.0
        transition_count = len(durations) - 1
        transition_overlap = transition_count * transition_duration
        expected_video_duration = sum(durations) - transition_overlap
        
        logger.info(f"转场计算:")
        logger.info(f"  转场时长: {transition_duration}s")
        logger.info(f"  转场数量: {transition_count}")
        logger.info(f"  转场重叠: {transition_overlap}s")
        logger.info(f"  预期视频时长: {expected_video_duration}s")
        
        # 模拟音频时长（故意设置为12秒来测试修复）
        fake_audio_duration = 12.0
        logger.info(f"模拟音频时长: {fake_audio_duration}s")
        logger.info(f"关键测试: 视频应该播放{expected_video_duration}s，不应该被截断到{fake_audio_duration}s")
        
        # 创建视频流
        streams = []
        for video_path in test_files:
            stream = ffmpeg.input(video_path)
            streams.append(stream)
        
        logger.info("应用修复后的转场效果...")
        
        # 使用修复后的转场方法
        final_stream = VideoCompositionService._create_video_with_transitions(
            streams, 'fade', transition_duration, durations
        )
        
        # 手动应用fps和trim（模拟原来的逻辑）
        # 这里我们需要手动计算正确的时长
        total_material_duration = sum(durations)
        transition_count = len(durations) - 1
        transition_overlap = transition_count * transition_duration
        actual_video_duration = total_material_duration - transition_overlap
        
        # 使用实际视频时长和音频时长的较小值
        final_duration = min(actual_video_duration, fake_audio_duration)
        
        logger.info(f"最终trim时长: {final_duration}s")
        
        final_stream_with_trim = (
            final_stream
            .filter('fps', fps=30)
            .trim(duration=final_duration)
        )
        
        # 输出视频
        out = ffmpeg.output(
            final_stream_with_trim,
            'trim_fix_test.mp4',
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        # 执行命令
        logger.info("执行FFmpeg命令...")
        result = subprocess.run(ffmpeg.compile(out), capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            if Path('trim_fix_test.mp4').exists():
                file_size = Path('trim_fix_test.mp4').stat().st_size
                logger.info("✅ trim修复测试成功!")
                logger.info(f"文件大小: {file_size} bytes")
                logger.info(f"预期时长: {final_duration}s")
                
                logger.info("\n🎬 预期播放效果:")
                logger.info("0-5s: 红色")
                logger.info("5-11s: 绿色 (有1s转场重叠)")
                logger.info("11-15s: 蓝色 (有1s转场重叠)")
                logger.info("15-19s: 黄色 (有1s转场重叠)")
                
                logger.info("\n🔍 关键验证点:")
                logger.info(f"- 视频应该播放{final_duration}秒")
                logger.info("- 12秒后应该继续正常播放，不定格")
                logger.info("- 15秒后应该显示黄色")
                logger.info("- 字幕应该正常显示到结束")
                
                if final_duration > 12:
                    logger.info("✅ 修复成功：视频时长超过12秒，应该解决定格问题")
                else:
                    logger.warning("⚠️ 视频时长仍被限制在12秒以内")
                
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ FFmpeg执行失败，返回码: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 清理测试文件
        for name, _, _ in video_configs:
            filename = f"trim_test_{name}.mp4"
            if Path(filename).exists():
                Path(filename).unlink()

if __name__ == "__main__":
    logger.info("🚀 开始trim修复测试")
    logger.info("目标: 彻底解决12秒定格问题")
    
    success = test_trim_fix()
    
    if success:
        logger.info("\n🎉 trim修复测试成功!")
        logger.info("12秒定格问题应该彻底解决！")
        logger.info("请播放 trim_fix_test.mp4 验证效果")
    else:
        logger.error("\n❌ trim修复测试失败")
        sys.exit(1)
