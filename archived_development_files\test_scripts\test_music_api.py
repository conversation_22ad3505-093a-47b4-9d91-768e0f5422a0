#!/usr/bin/env python3
"""
快速测试音乐管理API
"""

import sys
import os
from pathlib import Path
import requests
import json

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

API_BASE = "http://localhost:8000/api"

def test_music_apis():
    """测试音乐相关的API端点"""
    
    print("🎵 音乐管理API测试")
    print("=" * 50)
    
    # 测试获取分类列表
    print("\n1. 测试获取分类列表")
    try:
        response = requests.get(f"{API_BASE}/background-music/categories/list")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except requests.exceptions.ConnectionError:
        print("   ❌ 无法连接到后端服务，请确保后端正在运行")
        return
    except Exception as e:
        print(f"   错误: {str(e)}")
    
    # 测试获取音乐列表
    print("\n2. 测试获取音乐列表")
    try:
        response = requests.get(f"{API_BASE}/background-music")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   错误: {str(e)}")
    
    # 测试添加分类
    print("\n3. 测试添加分类")
    try:
        test_category = "test_category"
        form_data = {"category": test_category}
        response = requests.post(f"{API_BASE}/background-music/categories", data=form_data)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   错误: {str(e)}")
    
    # 测试删除分类  
    print("\n4. 测试删除分类")
    try:
        test_category = "test_category"
        response = requests.delete(f"{API_BASE}/background-music/categories/{test_category}")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   错误: {str(e)}")

if __name__ == "__main__":
    test_music_apis()
