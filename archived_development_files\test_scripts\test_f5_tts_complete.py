#!/usr/bin/env python3
"""
完整的F5-TTS功能测试
测试从创建音色到获取音色列表的完整流程
"""

import requests
import json
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_f5_tts_complete_workflow():
    """测试F5-TTS完整工作流程"""
    
    print("🚀 开始F5-TTS完整功能测试...")
    
    # 1. 测试获取空的F5-TTS音色列表
    print("\n1. 测试获取F5-TTS音色列表（应该为空）...")
    response = requests.get(f"{BASE_URL}/api/settings/tts-voices?provider=f5-tts")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 成功获取音色列表: {len(data['data'])} 个音色")
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 获取音色列表失败: {response.text}")
        return False
    
    # 2. 测试获取Coze音色列表
    print("\n2. 测试获取Coze音色列表...")
    response = requests.get(f"{BASE_URL}/api/settings/tts-voices?provider=coze")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 成功获取Coze音色列表: {len(data['data'])} 个音色")
        for voice in data['data']:
            print(f"   - {voice['name']} ({voice['id']})")
    else:
        print(f"❌ 获取Coze音色列表失败: {response.text}")
        return False
    
    # 3. 测试F5-TTS音色管理API
    print("\n3. 测试F5-TTS音色管理API...")
    response = requests.get(f"{BASE_URL}/api/f5-tts-voices")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 成功访问F5-TTS音色管理API: {len(data['data'])} 个音色")
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 访问F5-TTS音色管理API失败: {response.text}")
        return False
    
    # 4. 测试设置API中的F5-TTS配置
    print("\n4. 测试获取设置（检查F5-TTS配置）...")
    response = requests.get(f"{BASE_URL}/api/settings")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        tts_config = data['data']['tts']
        print(f"✅ 成功获取设置")
        print(f"TTS提供商: {tts_config['provider']}")
        print(f"F5-TTS端点: {tts_config.get('f5TtsEndpoint', '未设置')}")
        
        # 检查是否包含F5-TTS字段
        if 'f5TtsEndpoint' in tts_config:
            print("✅ 设置中包含F5-TTS端点字段")
        else:
            print("❌ 设置中缺少F5-TTS端点字段")
            return False
    else:
        print(f"❌ 获取设置失败: {response.text}")
        return False
    
    # 5. 测试更新TTS设置为F5-TTS
    print("\n5. 测试更新TTS设置为F5-TTS...")
    update_data = {
        "tts": {
            "provider": "f5-tts",
            "f5TtsEndpoint": "http://localhost:7860"
        }
    }
    response = requests.put(
        f"{BASE_URL}/api/settings",
        json=update_data,
        headers={"Content-Type": "application/json"}
    )
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 成功更新TTS设置为F5-TTS")
        print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 更新TTS设置失败: {response.text}")
        return False
    
    # 6. 验证设置更新
    print("\n6. 验证设置更新...")
    response = requests.get(f"{BASE_URL}/api/settings")
    if response.status_code == 200:
        data = response.json()
        tts_config = data['data']['tts']
        if tts_config['provider'] == 'f5-tts' and tts_config['f5TtsEndpoint'] == 'http://localhost:7860':
            print("✅ 设置更新验证成功")
        else:
            print(f"❌ 设置更新验证失败: {tts_config}")
            return False
    else:
        print(f"❌ 验证设置失败: {response.text}")
        return False
    
    print("\n🎉 F5-TTS完整功能测试成功！")
    print("\n📋 测试总结:")
    print("✅ F5-TTS音色列表API正常")
    print("✅ Coze音色列表API正常") 
    print("✅ F5-TTS音色管理API正常")
    print("✅ 设置API包含F5-TTS配置")
    print("✅ 可以更新TTS设置为F5-TTS")
    print("✅ 设置更新验证成功")
    
    return True

if __name__ == "__main__":
    try:
        success = test_f5_tts_complete_workflow()
        if success:
            print("\n✅ 所有测试通过！F5-TTS集成完成。")
        else:
            print("\n❌ 测试失败！")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
