'use client';

import React, { useState, useEffect } from 'react';
import { usePromptStore, type Prompt, type PromptType } from '@/store/promptStore';
import { useSettingsStore } from '@/store/settingsStore';
import { testPrompt } from '@/lib/api/llm';
import ClientOnly from '@/components/ClientOnly';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PlayIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

// 类型名称映射
const typeNames: Record<PromptType, string> = {
  system: '系统',
  story: '故事',
  narration: '旁白',
  description: '描述'
};

const categoryNames: Record<'all' | PromptType, string> = {
  all: '全部提示词',
  system: '系统提示词',
  story: '故事生成',
  narration: '旁白生成',
  description: '场景描述'
};

// 提示词卡片组件
const PromptCard: React.FC<{
  prompt: Prompt;
  onEdit: (prompt: Prompt) => void;
  onTest: (prompt: Prompt) => void;
  onDelete: (id: string) => void;
}> = ({ prompt, onEdit, onTest, onDelete }) => {
  const getBadgeColor = (type: PromptType) => {
    switch (type) {
      case 'system': return 'bg-blue-500';
      case 'story': return 'bg-green-500';
      case 'narration': return 'bg-yellow-500';
      case 'description': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1">
      <div className="p-4 pb-0">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold text-gray-900 flex-1 mr-2">{prompt.title}</h3>
          <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getBadgeColor(prompt.type)}`}>
            {typeNames[prompt.type]}
          </span>
        </div>
      </div>
      
      <div className="px-4 pb-4">        <p className="text-sm text-gray-600 mb-3 overflow-hidden" style={{
          display: '-webkit-box',
          WebkitLineClamp: 3,
          WebkitBoxOrient: 'vertical' as const
        }}>
          {prompt.content}
        </p>
        
        <div className="flex flex-wrap gap-1 mb-3">
          {prompt.tags.map(tag => (
            <span key={tag} className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
              {tag}
            </span>
          ))}
        </div>
        
        <div className="text-xs text-gray-500 mb-3">
          创建时间：{prompt.createdAt} | 更新时间：{prompt.updatedAt} | 测试：{prompt.testCount}次
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={() => onTest(prompt)}
            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors flex items-center justify-center gap-1"
          >
            <PlayIcon className="w-3 h-3" />
            测试
          </button>
          <button
            onClick={() => onEdit(prompt)}
            className="flex-1 bg-transparent hover:bg-gray-50 text-gray-600 border border-gray-200 px-3 py-2 rounded text-sm font-medium transition-colors flex items-center justify-center gap-1"
          >
            <PencilIcon className="w-3 h-3" />
            编辑
          </button>
          <button
            onClick={() => onDelete(prompt.id)}
            className="flex-1 bg-transparent hover:bg-red-50 text-red-500 border border-red-200 px-3 py-2 rounded text-sm font-medium transition-colors flex items-center justify-center gap-1"
          >
            <TrashIcon className="w-3 h-3" />
            删除
          </button>
        </div>
      </div>
    </div>
  );
};

// 编辑模态框组件
const EditModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  prompt?: Prompt;
  onSave: (data: any) => void;
}> = ({ isOpen, onClose, prompt, onSave }) => {
  const [formData, setFormData] = useState({
    title: '',
    type: '' as PromptType | '',
    content: '',
    description: '',
    tags: [] as string[]
  });
  const [tagInput, setTagInput] = useState('');

  React.useEffect(() => {
    if (prompt) {
      setFormData({
        title: prompt.title,
        type: prompt.type,
        content: prompt.content,
        description: prompt.description || '',
        tags: [...prompt.tags]
      });
    } else {
      setFormData({
        title: '',
        type: '',
        content: '',
        description: '',
        tags: []
      });
    }
  }, [prompt, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title || !formData.type || !formData.content) return;
    
    onSave(formData);
    onClose();
  };

  const addTag = () => {
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            {prompt ? '编辑提示词' : '新建提示词'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              提示词标题
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入提示词标题"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              类型
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as PromptType }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">请选择类型</option>
              <option value="system">系统提示词</option>
              <option value="story">故事生成</option>
              <option value="narration">旁白生成</option>
              <option value="description">场景描述</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              提示词内容
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y"
              placeholder="请输入提示词内容，可以使用变量如 {story_text}、{context} 等"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              描述说明
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y"
              placeholder="请简要描述此提示词的用途和使用场景"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              标签
            </label>
            <div className="flex gap-2 mb-2">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="输入标签"
              />
              <button
                type="button"
                onClick={addTag}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                添加
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.tags.map(tag => (
                <span
                  key={tag}
                  className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm flex items-center gap-1"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md font-medium transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium transition-colors"
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// 测试模态框组件
const TestModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  prompt?: Prompt;
}> = ({ isOpen, onClose, prompt }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState('');
  const [error, setError] = useState('');
  const [variables, setVariables] = useState<Record<string, string>>({});
  const { incrementTestCount } = usePromptStore();
  const { llm } = useSettingsStore();

  // 提取提示词中的变量
  const extractVariables = (content: string) => {
    const variableRegex = /\{([^}]+)\}/g;
    const matches = content.match(variableRegex) || [];
    const vars: Record<string, string> = {};
    matches.forEach(match => {
      const varName = match.slice(1, -1);
      if (!vars[varName]) {
        vars[varName] = '';
      }
    });
    return vars;
  };

  React.useEffect(() => {
    if (isOpen && prompt) {
      setResult('');
      setError('');
      setIsLoading(false);
      const vars = extractVariables(prompt.content);
      setVariables(vars);
    }
  }, [isOpen, prompt]);

  const startTest = async () => {
    if (!prompt) return;
    
    // 检查LLM配置
    if (!llm.apiKey) {
      setError('请先在系统设置中配置LLM API密钥');
      return;
    }
    
    setIsLoading(true);
    setResult('');
    setError('');
    
    try {
      // 不传递LLM配置，让后端使用系统设置
      const response = await testPrompt({
        prompt: prompt.content,
        variables
      });
      
      if (response.success && response.result) {
        setResult(response.result);
        incrementTestCount(prompt.id);
      } else {
        setError(response.error || '测试失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '测试过程中发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  const updateVariable = (name: string, value: string) => {
    setVariables(prev => ({ ...prev, [name]: value }));
  };

  if (!isOpen || !prompt) return null;

  const hasVariables = Object.keys(variables).length > 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-3xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">测试提示词</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              提示词预览
            </label>
            <div className="bg-gray-50 border border-gray-200 rounded-md p-4 font-mono text-sm whitespace-pre-wrap">
              {prompt.content}
            </div>
          </div>

          {hasVariables && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                变量配置
              </label>
              <div className="space-y-2">
                {Object.keys(variables).map(varName => (
                  <div key={varName} className="flex items-center gap-2">
                    <label className="w-20 text-sm text-gray-600">{varName}:</label>
                    <input
                      type="text"
                      value={variables[varName]}
                      onChange={(e) => updateVariable(varName, e.target.value)}
                      className="flex-1 px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={`请输入 ${varName} 的值`}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              LLM配置状态
            </label>
            <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
              <div className="text-sm text-gray-600">
                提供商: {llm.provider} | 模型: {llm.model}
                {llm.apiKey ? 
                  <span className="text-green-600 ml-2">✓ API密钥已配置</span> : 
                  <span className="text-red-600 ml-2">✗ 未配置API密钥</span>
                }
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              测试结果
            </label>
            <div className="bg-gray-50 border border-gray-200 rounded-md p-4 min-h-[120px]">
              {isLoading ? (
                <div className="flex items-center justify-center text-gray-500 italic">
                  正在测试中，请稍候...
                </div>
              ) : error ? (
                <div className="text-sm text-red-600 leading-relaxed">
                  <div className="font-medium mb-1">测试失败:</div>
                  {error}
                </div>
              ) : result ? (
                <div className="text-sm text-gray-900 leading-relaxed">{result}</div>
              ) : (
                <div className="flex items-center justify-center text-gray-500 italic">
                  点击"开始测试"按钮来测试此提示词...
                </div>
              )}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={startTest}
              disabled={isLoading || !llm.apiKey}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white py-2 px-4 rounded-md font-medium transition-colors flex items-center gap-2"
            >
              <PlayIcon className="w-4 h-4" />
              开始测试
            </button>
            <button
              onClick={onClose}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md font-medium transition-colors"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// 主页面组件
export default function PromptsPage() {
  return (
    <ClientOnly 
      fallback={
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="text-center py-8">
            <div className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              正在初始化提示词管理...
            </div>
          </div>
        </div>
      }
    >
      <PromptsPageContent />
    </ClientOnly>
  );
}

// 提示词页面内容组件
function PromptsPageContent() {
  const {
    currentCategory,
    searchQuery,
    sortBy,
    isLoading,
    error,
    setCurrentCategory,
    setSearchQuery,
    setSortBy,
    fetchPrompts,
    addPrompt,
    updatePrompt,
    deletePrompt,
    getFilteredPrompts,
    getStats,
    getAllTags
  } = usePromptStore();

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isTestModalOpen, setIsTestModalOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<Prompt | undefined>();
  const [testingPrompt, setTestingPrompt] = useState<Prompt | undefined>();

  // 页面加载时获取数据
  useEffect(() => {
    fetchPrompts();
  }, [fetchPrompts]);

  const filteredPrompts = getFilteredPrompts();
  const stats = getStats();
  const allTags = getAllTags();

  const handleCreatePrompt = () => {
    setEditingPrompt(undefined);
    setIsEditModalOpen(true);
  };

  const handleEditPrompt = (prompt: Prompt) => {
    setEditingPrompt(prompt);
    setIsEditModalOpen(true);
  };

  const handleTestPrompt = (prompt: Prompt) => {
    setTestingPrompt(prompt);
    setIsTestModalOpen(true);
  };

  const handleDeletePrompt = async (id: string) => {
    if (confirm('确定要删除此提示词吗？此操作不可恢复。')) {
      try {
        await deletePrompt(id);
      } catch (error) {
        console.error('删除失败:', error);
      }
    }
  };

  const handleSavePrompt = async (promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'testCount'>) => {
    try {
      if (editingPrompt) {
        await updatePrompt(editingPrompt.id, promptData);
      } else {
        await addPrompt(promptData);
      }
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-6 py-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-end mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">提示词管理</h1>
          <p className="text-gray-600">管理和优化AI生成内容的提示词模板，提升视频生成质量</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={handleCreatePrompt}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2"
          >
            <PlusIcon className="w-4 h-4" />
            新建提示词
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="text-red-800 font-medium">错误</div>
          <div className="text-red-600 text-sm">{error}</div>
        </div>
      )}

      {/* 加载状态 */}
      {isLoading && (
        <div className="text-center py-8">
          <div className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            正在加载提示词...
          </div>
        </div>
      )}

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-blue-500 mb-1">{stats.total}</div>
          <div className="text-sm text-gray-600">提示词总数</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-blue-500 mb-1">
            {stats.systemCount}/{stats.storyCount}/{stats.narrationCount}/{stats.descriptionCount}
          </div>
          <div className="text-sm text-gray-600">系统/故事/旁白/描述</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-blue-500 mb-1">{stats.tagCount}</div>
          <div className="text-sm text-gray-600">标签分类</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-blue-500 mb-1">{stats.totalTestCount}</div>
          <div className="text-sm text-gray-600">测试次数</div>
        </div>
      </div>

      {/* 分类标签 */}
      <div className="bg-white rounded-lg border border-gray-200 p-1 mb-6 inline-flex">
        {Object.entries(categoryNames).map(([key, name]) => (
          <button
            key={key}
            onClick={() => setCurrentCategory(key as 'all' | PromptType)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              currentCategory === key
                ? 'bg-blue-500 text-white'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            {name}
          </button>
        ))}
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6 flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索提示词标题或内容..."
              className="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
          </div>
          
          <select className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white">
            <option value="all">全部标签</option>
            {allTags.map(tag => (
              <option key={tag} value={tag}>{tag}</option>
            ))}
          </select>
        </div>
        
        <div>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
          >
            <option value="updated">最近更新</option>
            <option value="created">创建时间</option>
            <option value="name">名称排序</option>
            <option value="tests">测试次数</option>
          </select>
        </div>
      </div>

      {/* 提示词网格 */}
      {filteredPrompts.length === 0 ? (
        <div className="text-center py-16 text-gray-500">
          <div className="w-16 h-16 mx-auto mb-4 text-gray-300">
            <svg viewBox="0 0 20 20" fill="currentColor" className="w-full h-full">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
            </svg>
          </div>
          <div className="text-lg font-medium mb-2">还没有{categoryNames[currentCategory]}</div>
          <div className="text-gray-400">
            点击"新建提示词"按钮开始创建您的第一个提示词模板
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredPrompts.map(prompt => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              onEdit={handleEditPrompt}
              onTest={handleTestPrompt}
              onDelete={handleDeletePrompt}
            />
          ))}
        </div>
      )}

      {/* 编辑模态框 */}
      <EditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        prompt={editingPrompt}
        onSave={handleSavePrompt}
      />

      {/* 测试模态框 */}
      <TestModal
        isOpen={isTestModalOpen}
        onClose={() => setIsTestModalOpen(false)}
        prompt={testingPrompt}
      />
    </div>
  );
}
