"""
快速验证封面生成流程
"""

import os
import sys
from pathlib import Path

# 设置路径
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def quick_check():
    """快速检查资源"""
    db = SessionLocal()
    try:
        print("=== 快速资源检查 ===")
        
        # 检查账号
        accounts = db.query(Account).all()
        print(f"账号数量: {len(accounts)}")
        if accounts:
            account = accounts[0]
            print(f"第一个账号: {account.name} (ID: {account.id})")
        
        # 检查模板
        templates = db.query(CoverTemplate).all()
        print(f"模板数量: {len(templates)}")
        if templates:
            template = templates[0]
            print(f"第一个模板: {template.name} (ID: {template.id})")
            print(f"模板变量: {template.variables}")
            
            # 检查模板文件
            template_file = Path("templates") / f"{template.id}.html"
            if template_file.exists():
                print(f"✅ 模板文件存在: {template_file}")
                file_size = template_file.stat().st_size
                print(f"   文件大小: {file_size} 字节")
            else:
                print(f"❌ 模板文件不存在: {template_file}")
        
        return accounts and templates
        
    finally:
        db.close()

if __name__ == "__main__":
    success = quick_check()
    if success:
        print("\n✅ 资源检查通过，可以继续集成测试")
    else:
        print("\n❌ 资源检查失败，请检查数据库和模板文件")
