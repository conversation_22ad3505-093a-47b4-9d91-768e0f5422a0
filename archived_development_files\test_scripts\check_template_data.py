#!/usr/bin/env python3
"""
简单的API测试脚本 - 直接测试API是否有数据
"""

import asyncio
import sys
import os

# 添加项目路径到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.join(current_dir, "backend")
sys.path.insert(0, backend_dir)

from sqlalchemy.orm import Session
from src.core.database import get_db_session
from src.models.resources import CoverTemplate
from src.api.cover_template import get_cover_templates
from src.schemas.resources import CoverTemplateQuery

async def test_templates():
    print("=== 检查封面模板数据 ===\n")
    
    # 获取数据库连接
    db = get_db_session()
    
    try:
        # 1. 直接查询数据库中的模板数量
        template_count = db.query(CoverTemplate).count()
        print(f"数据库中模板总数: {template_count}")
        
        # 2. 获取所有模板
        templates = db.query(CoverTemplate).all()
        print(f"查询到的模板:")
        
        for i, template in enumerate(templates, 1):
            print(f"  {i}. ID: {template.id}")
            print(f"     名称: {template.name}")
            print(f"     分类: {template.category}")
            print(f"     描述: {template.description}")
            print(f"     创建时间: {getattr(template, 'created_at', 'N/A')}")
            print(f"     前端格式: {template.to_frontend_format()}")
            print()
        
        # 3. 测试API函数
        print("测试API函数响应:")
        query = CoverTemplateQuery()
        api_response = await get_cover_templates(query, db)
        print(f"API响应: {api_response}")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_templates())
