#!/usr/bin/env python3
"""
修复视频时长不匹配问题的脚本
主要解决转场效果导致的视频时长缩短问题
"""

import os
import sys
import asyncio
import ffmpeg
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置路径
MATERIALS_DIR = Path("backend/uploads/video_materials")
OUTPUT_DIR = Path("test_outputs")
OUTPUT_DIR.mkdir(exist_ok=True)

async def run_ffmpeg_async(stream, description="FFmpeg操作"):
    """异步运行FFmpeg命令"""
    try:
        logger.info(f"开始执行: {description}")
        cmd = ffmpeg.compile(stream)
        logger.info(f"FFmpeg命令: {' '.join(cmd)}")
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            logger.info(f"✅ {description} 成功完成")
            return True
        else:
            logger.error(f"❌ {description} 失败")
            logger.error(f"错误输出: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ {description} 执行异常: {e}")
        return False

def get_video_info(video_path):
    """获取视频信息"""
    try:
        probe = ffmpeg.probe(str(video_path))
        video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
        audio_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'audio'), None)
        
        result = {}
        if video_stream:
            result.update({
                'video_duration': float(video_stream.get('duration', 0)),
                'width': int(video_stream.get('width', 0)),
                'height': int(video_stream.get('height', 0)),
                'fps': eval(video_stream.get('r_frame_rate', '30/1'))
            })
        
        if audio_stream:
            result['audio_duration'] = float(audio_stream.get('duration', 0))
        
        return result
    except Exception as e:
        logger.error(f"获取视频信息失败: {e}")
        return None

def calculate_transition_duration(segment_durations, transition_duration=0.5):
    """计算转场后的实际视频时长"""
    if len(segment_durations) <= 1:
        return sum(segment_durations)
    
    total_original = sum(segment_durations)
    transition_count = len(segment_durations) - 1
    total_overlap = transition_count * transition_duration
    actual_duration = total_original - total_overlap
    
    logger.info(f"转场时长计算:")
    logger.info(f"  原始总时长: {total_original}s")
    logger.info(f"  转场数量: {transition_count}")
    logger.info(f"  转场重叠: {total_overlap}s")
    logger.info(f"  实际时长: {actual_duration}s")
    
    return actual_duration

def create_transitions_with_duration_fix(input_streams, transition_duration=0.5, durations=None, target_audio_duration=None):
    """创建带转场效果的视频拼接，并修复时长不匹配问题"""
    if len(input_streams) < 2:
        return input_streams[0]
    
    if durations is None:
        durations = [5.0] * len(input_streams)
    
    # 计算转场后的实际视频时长
    actual_video_duration = calculate_transition_duration(durations, transition_duration)
    
    # 统一帧率
    normalized_streams = []
    for stream in input_streams:
        normalized = stream.filter('fps', fps=30)
        normalized_streams.append(normalized)
    
    # 逐步构建转场链
    result = normalized_streams[0]
    current_result_duration = durations[0]
    
    for i in range(1, len(normalized_streams)):
        next_segment_duration = durations[i]
        offset = max(0, current_result_duration - transition_duration)
        
        logger.info(f"转场 {i}: offset={offset:.2f}s, duration={transition_duration}s")
        
        # 应用转场
        result = ffmpeg.filter(
            [result, normalized_streams[i]],
            'xfade',
            transition='fade',
            duration=transition_duration,
            offset=offset
        )
        
        # 更新结果时长
        current_result_duration = offset + next_segment_duration
    
    # 如果需要匹配音频时长，延长视频
    if target_audio_duration and target_audio_duration > actual_video_duration:
        extension_needed = target_audio_duration - actual_video_duration
        logger.info(f"需要延长视频 {extension_needed:.2f}s 以匹配音频时长")
        
        # 方法1：冻结最后一帧
        # result = result.filter('tpad', stop_duration=extension_needed)
        
        # 方法2：直接冻结最后一帧（更简单可靠）
        logger.info(f"冻结最后一帧 {extension_needed:.2f}s")
        result = result.filter('tpad', stop_duration=extension_needed)
    
    return result

def select_test_videos(target_duration=35):
    """选择测试视频，确保总时长超过目标时长"""
    video_files = []
    total_duration = 0
    
    # 获取所有mp4文件
    for file_path in MATERIALS_DIR.glob("*.mp4"):
        if "thumb_" not in file_path.name:  # 排除缩略图
            info = get_video_info(file_path)
            if info and info.get('video_duration', 0) > 0:
                video_files.append({
                    'path': file_path,
                    'duration': info['video_duration'],
                    'info': info
                })
                total_duration += info['video_duration']
                logger.info(f"选择视频: {file_path.name}, 时长: {info['video_duration']:.2f}s")
                
                if total_duration >= target_duration:
                    break
    
    logger.info(f"总共选择 {len(video_files)} 个视频，总时长: {total_duration:.2f}s")
    return video_files

async def create_test_audio(duration, output_path):
    """创建测试音频文件"""
    logger.info(f"创建测试音频，时长: {duration}秒")
    
    try:
        # 使用ffmpeg生成测试音频（正弦波）
        audio_stream = ffmpeg.input(f'sine=frequency=440:duration={duration}', f='lavfi')
        
        output_stream = ffmpeg.output(audio_stream, str(output_path), acodec='aac', ar=44100)
        
        success = await run_ffmpeg_async(output_stream.overwrite_output(), "创建测试音频")
        return success
        
    except Exception as e:
        logger.error(f"创建测试音频失败: {e}")
        return False

async def test_duration_fix(target_duration=35):
    """测试时长修复方案"""
    logger.info("=== 测试时长修复方案 ===")
    
    try:
        # 1. 选择视频素材
        video_files = select_test_videos(target_duration)
        if not video_files:
            logger.error("没有找到合适的测试视频")
            return False
        
        # 2. 创建视频流（带转场和时长修复）
        input_streams = []
        durations = []
        
        for video_file in video_files:
            stream = ffmpeg.input(str(video_file['path']))
            # 统一分辨率和帧率
            stream = stream.filter('scale', 1080, 1920).filter('fps', fps=30)
            input_streams.append(stream)
            durations.append(video_file['duration'])
        
        # 3. 创建测试音频
        audio_path = OUTPUT_DIR / "test_audio_fixed.aac"
        audio_success = await create_test_audio(target_duration, audio_path)
        if not audio_success:
            logger.error("创建测试音频失败")
            return False
        
        # 4. 使用修复后的转场效果
        video_stream = create_transitions_with_duration_fix(
            input_streams, 0.5, durations, target_duration
        )
        
        # 5. 创建字幕
        srt_content = f"""1
00:00:00,000 --> 00:00:05,000
修复测试 - 开始

2
00:00:05,000 --> 00:00:10,000
修复测试 - 5秒

3
00:00:10,000 --> 00:00:15,000
修复测试 - 10秒

4
00:00:15,000 --> 00:00:20,000
修复测试 - 15秒

5
00:00:20,000 --> 00:00:25,000
修复测试 - 20秒

6
00:00:25,000 --> 00:00:30,000
修复测试 - 25秒

7
00:00:30,000 --> 00:00:35,000
修复测试 - 30秒
"""
        
        srt_path = OUTPUT_DIR / "test_fixed_subtitle.srt"
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        # 6. 添加字幕到视频
        video_with_subtitle = video_stream.filter('subtitles', filename=str(srt_path))
        
        # 7. 添加音频
        audio_stream = ffmpeg.input(str(audio_path))
        
        # 8. 合成最终视频
        output_path = OUTPUT_DIR / "test_duration_fixed.mp4"
        output_stream = ffmpeg.output(
            video_with_subtitle, audio_stream, 
            str(output_path), 
            vcodec='libx264', 
            acodec='aac', 
            preset='fast', 
            pix_fmt='yuv420p'
        )
        
        success = await run_ffmpeg_async(output_stream.overwrite_output(), "修复后视频合成")
        if not success:
            return False
        
        # 9. 检查输出文件
        if output_path.exists():
            info = get_video_info(output_path)
            if info:
                video_dur = info.get('video_duration', 0)
                audio_dur = info.get('audio_duration', 0)
                logger.info(f"✅ 修复后视频合成成功: {output_path.name}")
                logger.info(f"   视频时长: {video_dur:.2f}s")
                logger.info(f"   音频时长: {audio_dur:.2f}s")
                logger.info(f"   时长差异: {abs(video_dur - audio_dur):.2f}s")
                
                if abs(video_dur - audio_dur) < 0.5:
                    logger.info("🎉 时长匹配成功！")
                    return True
                else:
                    logger.warning("⚠️ 时长仍有较大差异")
                    return False
            else:
                logger.error("❌ 无法获取输出视频信息")
                return False
        else:
            logger.error("❌ 输出文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 时长修复测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始时长修复测试...")
    
    success = await test_duration_fix(35)
    
    if success:
        logger.info("🎉 时长修复测试成功！")
    else:
        logger.error("❌ 时长修复测试失败")

if __name__ == "__main__":
    asyncio.run(main())
