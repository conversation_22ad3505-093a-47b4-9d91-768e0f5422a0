#!/usr/bin/env python
"""
调试API响应问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.responses import ApiResponse
from src.schemas.settings import SettingsResponse, TTSConfig, LLMConfig, GeneralSettings
from src.models.settings import Settings
from src.core.database import get_db

def test_api_response():
    print("🔍 调试API响应...")
    
    # 测试1: 创建Settings对象
    print("\n1. 测试Settings对象:")
    try:
        settings = Settings()
        print("✅ Settings对象创建成功")
        
        # 测试to_frontend_format方法
        frontend_data = settings.to_frontend_format()
        print(f"✅ frontend_data: {frontend_data}")
        
    except Exception as e:
        print(f"❌ Settings对象测试失败: {e}")
        return
    
    # 测试2: 创建响应对象
    print("\n2. 测试创建响应对象:")
    try:
        response_data = SettingsResponse(
            tts=TTSConfig(**frontend_data["tts"]),
            llm=LLMConfig(**frontend_data["llm"]),
            general=GeneralSettings(**frontend_data["general"])
        )
        print("✅ SettingsResponse对象创建成功")
        
    except Exception as e:
        print(f"❌ SettingsResponse创建失败: {e}")
        return
    
    # 测试3: 创建ApiResponse
    print("\n3. 测试ApiResponse创建:")
    try:
        api_response = ApiResponse.success(
            data=response_data,
            message="获取设置成功"
        )
        print("✅ ApiResponse创建成功")
        print(f"Response type: {type(api_response)}")
        
        # 测试序列化
        response_dict = api_response.model_dump()
        print(f"✅ 序列化成功: {response_dict}")
        
    except Exception as e:
        print(f"❌ ApiResponse创建失败: {e}")
        return
    
    print("\n🎉 所有测试通过!")

if __name__ == "__main__":
    test_api_response()
