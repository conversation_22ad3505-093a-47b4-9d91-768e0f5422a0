# 硬编码地址统一修复清单

## 🎯 修复目标
将所有硬编码的 `http://localhost:8000` 统一替换为使用 `NEXT_PUBLIC_API_BASE_URL` 环境变量，确保地址配置的统一性和可维护性。

## 📋 发现的硬编码地址

### ✅ 已正确配置的文件
**文件**: `src/lib/api/directHttpClient.ts`  
**当前代码**: 
```typescript
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
```
**状态**: ✅ 正确 - 作为fallback是合理的

---

## 🔴 需要修复的文件

### 1. `src/services/apiService.ts` 
**问题**: 使用了错误的环境变量名
```typescript
// ❌ 当前
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

// ✅ 应该改为
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
```

### 2. `src/lib/api/videoMaterials.ts`
**问题**: 复杂的条件判断和错误的环境变量使用
```typescript
// ❌ 当前
const API_BASE = process.env.NODE_ENV === 'development' ? '' : (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000')

// ✅ 应该改为
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
```

### 3. `src/lib/api/videoCategories.ts`
**问题**: 同 videoMaterials.ts
```typescript
// ❌ 当前
const API_BASE_URL = process.env.NODE_ENV === 'development' ? '' : (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000')

// ✅ 应该改为  
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
```

### 4. `src/lib/api/prompts.ts`
**问题**: 使用错误的环境变量，且有重复的hardcode
```typescript
// ❌ 当前
const API_BASE = process.env.NEXT_PUBLIC_API_URL 
  ? 'http://localhost:8000' 
  : 'http://localhost:8000';

// ✅ 应该改为
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
```

### 5. `src/lib/api/llm.ts` 
**问题**: 环境变量逻辑错误，生产环境配置有误
```typescript
// ❌ 当前
const API_BASE = process.env.NODE_ENV === 'production'
  ? 'http://localhost:8000your-production-domain.com' 
  : 'http://localhost:8000';

// ✅ 应该改为
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
```

### 6. `src/lib/api/coverTemplates.ts`
**问题**: 硬编码地址且注释不准确
```typescript
// ❌ 当前
const API_BASE = process.env.NODE_ENV === 'production'
  ? 'http://localhost:8000' 
  : 'http://localhost:8000'; // 在开发环境使用相对路径，通过Next.js代理

// ✅ 应该改为
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
```

### 7. `src/hooks/useApi.ts`
**问题**: 使用错误的环境变量，且有多处硬编码
```typescript
// ❌ 当前 (第10行)
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

// ❌ 当前 (第533行)
return `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/background-music/${id}/play`

// ✅ 应该改为
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
return `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}/api/background-music/${id}/play`
```

### 8. `src/app/videos/page.tsx`
**问题**: 组件中直接硬编码后端地址
```typescript
// ❌ 当前 (第1434行)
src={`http://localhost:8000${material.thumbnailUrl}`}

// ❌ 当前 (第1547行) 
src={previewMaterial.url ? `http://localhost:8000${previewMaterial.url}` : previewMaterial.path}

// ❌ 当前 (第1552行)
console.log('尝试的URL:', previewMaterial.url ? `http://localhost:8000${previewMaterial.url}` : previewMaterial.path)

// ✅ 应该改为
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
src={`${API_BASE}${material.thumbnailUrl}`}
src={previewMaterial.url ? `${API_BASE}${previewMaterial.url}` : previewMaterial.path}
console.log('尝试的URL:', previewMaterial.url ? `${API_BASE}${previewMaterial.url}` : previewMaterial.path)
```

### 9. `src/app/test-api/page.tsx`
**问题**: 测试页面中的硬编码地址
```typescript
// ❌ 当前 (第41-42行)
console.log('测试2: 绝对路径 http://localhost:8000/api/cover-templates/');
const response2 = await fetch('http://localhost:8000/api/cover-templates/', {

// ✅ 应该改为
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
console.log(`测试2: 绝对路径 ${API_BASE}/api/cover-templates/`);
const response2 = await fetch(`${API_BASE}/api/cover-templates/`, {
```

---

## 🛠️ 修复执行计划

### 阶段一: API库文件修复 (优先级: 🔴 高)
1. `src/lib/api/prompts.ts` - 5分钟
2. `src/lib/api/llm.ts` - 5分钟  
3. `src/lib/api/coverTemplates.ts` - 5分钟
4. `src/lib/api/videoMaterials.ts` - 5分钟
5. `src/lib/api/videoCategories.ts` - 5分钟

### 阶段二: 服务和Hook文件修复 (优先级: 🟡 中)
6. `src/services/apiService.ts` - 5分钟
7. `src/hooks/useApi.ts` - 10分钟

### 阶段三: 组件文件修复 (优先级: 🟢 低)
8. `src/app/videos/page.tsx` - 10分钟
9. `src/app/test-api/page.tsx` - 5分钟

**总预计时间**: 55分钟

---

## ✅ 修复验证清单

每个文件修复后需要验证:
- [ ] 代码中不再包含硬编码的 `http://localhost:8000`
- [ ] 正确使用 `NEXT_PUBLIC_API_BASE_URL` 环境变量
- [ ] 保留合理的 fallback 值
- [ ] 相关功能测试通过

---

## 🔧 标准化模式

### 推荐的统一写法:
```typescript
// 对于API库文件
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

// 对于组件文件  
const getApiUrl = (path: string) => {
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
  return `${API_BASE}${path}`
}
```

### 要避免的写法:
```typescript
// ❌ 不要使用错误的环境变量名
process.env.NEXT_PUBLIC_API_URL

// ❌ 不要使用复杂的环境判断
process.env.NODE_ENV === 'development' ? '' : '...'

// ❌ 不要直接硬编码
'http://localhost:8000'
```

---

**创建时间**: 2025-07-08  
**预计完成**: 1小时内  
**状态**: 待执行
