#!/usr/bin/env python3
"""
简化的帧率修复测试 - 验证基本的帧率统一功能
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_framerate_normalization():
    """测试简单的帧率统一功能"""
    
    logger.info("🎬 开始测试简单的帧率统一功能...")
    
    try:
        # 创建两个不同帧率的简单视频
        logger.info("创建30fps红色视频")
        red_30fps = (
            ffmpeg
            .input('color=red:size=640x480:duration=2:rate=30', f='lavfi')
            .output('test_red_30fps.mp4', vcodec='libx264', pix_fmt='yuv420p', r=30)
            .overwrite_output()
        )
        ffmpeg.run(red_30fps, quiet=True)
        
        logger.info("创建60fps绿色视频")
        green_60fps = (
            ffmpeg
            .input('color=green:size=640x480:duration=2:rate=60', f='lavfi')
            .output('test_green_60fps.mp4', vcodec='libx264', pix_fmt='yuv420p', r=60)
            .overwrite_output()
        )
        ffmpeg.run(green_60fps, quiet=True)
        
        # 测试帧率统一
        logger.info("测试帧率统一...")
        
        # 读取视频流
        red_stream = ffmpeg.input('test_red_30fps.mp4')
        green_stream = ffmpeg.input('test_green_60fps.mp4')
        
        # 统一帧率为30fps
        red_normalized = red_stream.filter('fps', fps=30)
        green_normalized = green_stream.filter('fps', fps=30)
        
        # 简单连接测试（不使用xfade）
        logger.info("测试简单连接...")
        concat_stream = ffmpeg.concat(red_normalized, green_normalized, v=1, a=0)
        
        out = ffmpeg.output(
            concat_stream,
            'test_framerate_concat.mp4',
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        ffmpeg.run(out, quiet=True)
        
        if Path('test_framerate_concat.mp4').exists():
            logger.info("✅ 帧率统一和简单连接测试成功!")
            
            # 现在测试xfade转场
            logger.info("测试xfade转场...")
            
            # 重新创建流（因为之前的流已经被使用）
            red_stream2 = ffmpeg.input('test_red_30fps.mp4')
            green_stream2 = ffmpeg.input('test_green_60fps.mp4')
            
            # 统一帧率
            red_normalized2 = red_stream2.filter('fps', fps=30)
            green_normalized2 = green_stream2.filter('fps', fps=30)
            
            # 应用xfade转场
            xfade_stream = ffmpeg.filter(
                [red_normalized2, green_normalized2],
                'xfade',
                transition='fade',
                duration=0.5,
                offset=1.5  # 在1.5秒开始转场
            )
            
            out2 = ffmpeg.output(
                xfade_stream,
                'test_framerate_xfade.mp4',
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p'
            ).overwrite_output()
            
            ffmpeg.run(out2, quiet=True)
            
            if Path('test_framerate_xfade.mp4').exists():
                logger.info("✅ 帧率统一和xfade转场测试成功!")
                logger.info("📋 生成的测试文件:")
                logger.info("- test_framerate_concat.mp4 (简单连接)")
                logger.info("- test_framerate_xfade.mp4 (xfade转场)")
                logger.info("🎉 帧率不匹配问题修复成功!")
                return True
            else:
                logger.error("❌ xfade转场测试失败")
                return False
        else:
            logger.error("❌ 简单连接测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 清理测试文件
        test_files = [
            'test_red_30fps.mp4',
            'test_green_60fps.mp4'
        ]
        
        for file_path in test_files:
            if Path(file_path).exists():
                Path(file_path).unlink()
                logger.info(f"清理测试文件: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始简化的帧率修复测试")
    
    success = test_simple_framerate_normalization()
    
    if success:
        logger.info("\n🎉 简化帧率修复测试完成!")
        logger.info("帧率统一功能正常工作。")
        logger.info("现在可以处理混合帧率的视频转场了。")
    else:
        logger.error("\n❌ 简化帧率修复测试失败")
        sys.exit(1)
