#!/usr/bin/env python
"""
检查数据库中的设置数据
"""

import sqlite3
import json
import os
import sys
from pathlib import Path

# 数据库路径
DATABASE_PATH = "D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"

def check_database_file():
    """检查数据库文件是否存在"""
    print(f"🔍 检查数据库文件: {DATABASE_PATH}")
    
    if os.path.exists(DATABASE_PATH):
        file_size = os.path.getsize(DATABASE_PATH)
        print(f"✅ 数据库文件存在，大小: {file_size} 字节")
        return True
    else:
        print("❌ 数据库文件不存在")
        return False

def check_settings_table():
    """检查settings表及其数据"""
    print("\n🔍 检查数据库表结构...")
    
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 检查是否有settings表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings';")
        settings_table = cursor.fetchone()
        
        if settings_table:
            print("✅ settings表存在")
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(settings);")
            columns = cursor.fetchall()
            print(f"📋 表结构 ({len(columns)} 列):")
            for col in columns:
                print(f"   {col[1]} ({col[2]}) - {col[5] if col[5] else '无注释'}")
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM settings;")
            count = cursor.fetchone()[0]
            print(f"\n📊 记录总数: {count}")
            
            if count > 0:
                cursor.execute("SELECT * FROM settings LIMIT 1;")
                record = cursor.fetchone()
                if record:
                    print("📄 第一条记录:")
                    column_names = [description[0] for description in cursor.description]
                    for i, value in enumerate(record):
                        if value:  # 只显示非空值
                            print(f"   {column_names[i]}: {value}")
            else:
                print("⚠️ 表中没有数据")
        else:
            print("❌ settings表不存在")
            
            # 显示所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"数据库中的表: {[table[0] for table in tables]}")
    
    except Exception as e:
        print(f"❌ 数据库操作错误: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def check_settings_data():
    """详细检查设置数据"""
    print("\n🔍 详细检查设置数据...")
    
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 获取所有设置记录
        cursor.execute("SELECT * FROM settings;")
        records = cursor.fetchall()
        
        if not records:
            print("❌ 没有找到设置记录")
            return
            
        column_names = [description[0] for description in cursor.description]
        
        for i, record in enumerate(records):
            print(f"\n📋 记录 {i+1}:")
            
            # 分类显示设置
            tts_settings = {}
            llm_settings = {}
            general_settings = {}
            other_settings = {}
            
            for j, value in enumerate(record):
                col_name = column_names[j]
                if value is not None:
                    if col_name.startswith('tts_'):
                        tts_settings[col_name] = value
                    elif col_name.startswith('llm_'):
                        llm_settings[col_name] = value
                    elif col_name in ['theme', 'language', 'auto_save', 'show_tips', 'output_directory']:
                        general_settings[col_name] = value
                    else:
                        other_settings[col_name] = value
            
            if tts_settings:
                print("   🎤 TTS配置:")
                for k, v in tts_settings.items():
                    print(f"      {k}: {v}")
            
            if llm_settings:
                print("   🤖 LLM配置:")
                for k, v in llm_settings.items():
                    print(f"      {k}: {v}")
            
            if general_settings:
                print("   ⚙️ 通用设置:")
                for k, v in general_settings.items():
                    print(f"      {k}: {v}")
            
            if other_settings:
                print("   📄 其他字段:")
                for k, v in other_settings.items():
                    print(f"      {k}: {v}")
                    
    except Exception as e:
        print(f"❌ 检查设置数据错误: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def main():
    print("=" * 60)
    print("🔍 数据库设置数据检查工具")
    print("=" * 60)
    
    # 检查数据库文件
    if not check_database_file():
        print("\n💡 建议:")
        print("1. 确保后端已启动并初始化过数据库")
        print("2. 检查数据库路径配置是否正确")
        return
    
    # 检查表结构
    check_settings_table()
    
    # 检查设置数据
    check_settings_data()
    
    print("\n" + "=" * 60)
    print("✅ 检查完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
