import sys
from pathlib import Path

# 添加backend到Python路径
sys.path.insert(0, str(Path(__file__).parent / "backend"))

from src.services.template_import_service import TemplateImportService

def test_image_extraction():
    service = TemplateImportService()
    
    # 测试HTML内容
    html = '''<img src="{{avatar}}" />
<img src="images/local.png" />
<img src="https://example.com/remote.jpg" />
<img src="//example.com/protocol-relative.png" />
<img src="data:image/png;base64,abc123" />'''

    paths = service.extract_image_paths_from_html(html)
    print('提取的图片路径:', paths)
    
    expected = ['images/local.png', 'https://example.com/remote.jpg', '//example.com/protocol-relative.png']
    print('期望的路径:', expected)
    print('测试通过:', set(paths) == set(expected))

if __name__ == "__main__":
    test_image_extraction()
