"""
简单测试封面模板API的脚本
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def test_api_simple():
    try:
        from main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        print("API测试开始...")
        
        # 测试基本健康检查
        response = client.get("/health")
        print(f"Health: {response.status_code}")
        
        # 测试API健康检查
        response = client.get("/api/health") 
        print(f"API Health: {response.status_code}")
        
        # 测试统计
        response = client.get("/api/cover-templates/stats")
        print(f"Stats: {response.status_code}")
        if response.status_code == 200:
            print(f"Stats Data: {response.json()}")
        
        # 测试变量
        response = client.get("/api/cover-templates/variables")
        print(f"Variables: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Variables: {len(data.get('data', {}).get('variables', []))} found")
        
        # 测试模板列表
        response = client.get("/api/cover-templates")
        print(f"Templates: {response.status_code}")
        if response.status_code == 200:
            templates = response.json()
            print(f"Templates: {len(templates) if isinstance(templates, list) else 0} found")
        
        print("API测试完成!")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_simple()
