"""
F5-TTS音色管理API
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import Optional
from pathlib import Path
import uuid
import shutil
import traceback
from loguru import logger

from ..core.database import get_db
from ..models.f5_tts_voices import F5TTSVoice
from src.core.responses import ApiResponse

router = APIRouter(prefix="/f5-tts-voices", tags=["F5-TTS音色管理"])


@router.get("")
async def get_f5_tts_voices(db: Session = Depends(get_db)):
    """获取F5-TTS音色列表"""
    try:
        voices = db.query(F5TTSVoice).filter(F5TTSVoice.is_active == True).all()
        result = [voice.to_frontend_format() for voice in voices]
        
        return ApiResponse.create_success(
            data=result,
            message="获取F5-TTS音色列表成功"
        ).model_dump()
    except Exception as e:
        logger.error(f"获取音色列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取音色列表失败: {str(e)}")


@router.post("")
async def create_f5_tts_voice(
    name: str = Form(...),
    description: str = Form(""),
    language: str = Form("zh-CN"),
    gender: str = Form(...),
    ref_text: str = Form(...),
    ref_audio: UploadFile = File(...),
    remove_silence: bool = Form(False),
    cross_fade_duration: float = Form(0.15),
    nfe_value: int = Form(32),
    randomize_seed: bool = Form(True),
    db: Session = Depends(get_db)
):
    """创建新的F5-TTS音色"""
    try:
        # 验证音频文件
        if not ref_audio.content_type or not ref_audio.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="只支持音频文件")
        
        # 保存参考音频文件
        ref_audio_dir = Path("data/f5_tts_voices")
        ref_audio_dir.mkdir(parents=True, exist_ok=True)
        
        file_extension = Path(ref_audio.filename or "audio.wav").suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = ref_audio_dir / unique_filename
        
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(ref_audio.file, buffer)
        
        logger.info(f"参考音频文件已保存到: {file_path}")
        
        # 创建音色记录
        voice = F5TTSVoice(
            name=name,
            description=description,
            language=language,
            gender=gender,
            ref_audio_path=str(file_path),
            ref_text=ref_text,
            remove_silence=remove_silence,
            cross_fade_duration=cross_fade_duration,
            nfe_value=nfe_value,
            randomize_seed=randomize_seed
        )
        
        db.add(voice)
        db.commit()
        db.refresh(voice)
        
        logger.info(f"F5-TTS音色创建成功: {voice.name} (ID: {voice.id})")
        
        return ApiResponse.create_success(
            data=voice.to_frontend_format(),
            message="创建F5-TTS音色成功"
        ).model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建音色失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建音色失败: {str(e)}")


@router.get("/{voice_id}")
async def get_f5_tts_voice(voice_id: str, db: Session = Depends(get_db)):
    """获取单个F5-TTS音色详情"""
    try:
        voice = db.query(F5TTSVoice).filter(F5TTSVoice.id == voice_id).first()
        if not voice:
            raise HTTPException(status_code=404, detail="音色不存在")
        
        return ApiResponse.create_success(
            data=voice.to_frontend_format(),
            message="获取音色详情成功"
        ).model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取音色详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取音色详情失败: {str(e)}")


@router.put("/{voice_id}")
async def update_f5_tts_voice(
    voice_id: str,
    name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    language: Optional[str] = Form(None),
    gender: Optional[str] = Form(None),
    ref_text: Optional[str] = Form(None),
    ref_audio: Optional[UploadFile] = File(None),
    remove_silence: Optional[bool] = Form(None),
    cross_fade_duration: Optional[float] = Form(None),
    nfe_value: Optional[int] = Form(None),
    randomize_seed: Optional[bool] = Form(None),
    is_active: Optional[bool] = Form(None),
    db: Session = Depends(get_db)
):
    """更新F5-TTS音色"""
    try:
        voice = db.query(F5TTSVoice).filter(F5TTSVoice.id == voice_id).first()
        if not voice:
            raise HTTPException(status_code=404, detail="音色不存在")
        
        # 更新基本信息
        if name is not None:
            voice.name = name
        if description is not None:
            voice.description = description
        if language is not None:
            voice.language = language
        if gender is not None:
            voice.gender = gender
        if ref_text is not None:
            voice.ref_text = ref_text
        if remove_silence is not None:
            voice.remove_silence = remove_silence
        if cross_fade_duration is not None:
            voice.cross_fade_duration = cross_fade_duration
        if nfe_value is not None:
            voice.nfe_value = nfe_value
        if randomize_seed is not None:
            voice.randomize_seed = randomize_seed
        if is_active is not None:
            voice.is_active = is_active
        
        # 处理新的参考音频文件
        if ref_audio and ref_audio.filename:
            if not ref_audio.content_type or not ref_audio.content_type.startswith('audio/'):
                raise HTTPException(status_code=400, detail="只支持音频文件")
            
            # 删除旧文件
            old_file_path = Path(voice.ref_audio_path)
            if old_file_path.exists():
                old_file_path.unlink()
            
            # 保存新文件
            ref_audio_dir = Path("data/f5_tts_voices")
            ref_audio_dir.mkdir(parents=True, exist_ok=True)
            
            file_extension = Path(ref_audio.filename).suffix
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = ref_audio_dir / unique_filename
            
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(ref_audio.file, buffer)
            
            voice.ref_audio_path = str(file_path)
            logger.info(f"参考音频文件已更新: {file_path}")
        
        db.commit()
        db.refresh(voice)
        
        logger.info(f"F5-TTS音色更新成功: {voice.name} (ID: {voice.id})")
        
        return ApiResponse.create_success(
            data=voice.to_frontend_format(),
            message="更新F5-TTS音色成功"
        ).model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新音色失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新音色失败: {str(e)}")


@router.delete("/{voice_id}")
async def delete_f5_tts_voice(voice_id: str, db: Session = Depends(get_db)):
    """删除F5-TTS音色"""
    try:
        voice = db.query(F5TTSVoice).filter(F5TTSVoice.id == voice_id).first()
        if not voice:
            raise HTTPException(status_code=404, detail="音色不存在")
        
        # 删除音频文件
        file_path = Path(voice.ref_audio_path)
        if file_path.exists():
            file_path.unlink()
            logger.info(f"参考音频文件已删除: {file_path}")
        
        # 删除数据库记录
        db.delete(voice)
        db.commit()
        
        logger.info(f"F5-TTS音色删除成功: {voice.name} (ID: {voice.id})")
        
        return ApiResponse.create_success(
            data=None,
            message="删除F5-TTS音色成功"
        ).model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除音色失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除音色失败: {str(e)}")


@router.post("/{voice_id}/test")
async def test_f5_tts_voice(
    voice_id: str,
    test_text: str = Form("这是一个音色测试。"),
    db: Session = Depends(get_db)
):
    """测试F5-TTS音色"""
    try:
        voice = db.query(F5TTSVoice).filter(F5TTSVoice.id == voice_id).first()
        if not voice:
            raise HTTPException(status_code=404, detail="音色不存在")

        logger.info(f"开始测试F5-TTS音色: {voice.name} (ID: {voice.id})")

        # 获取F5-TTS配置
        from ..core.database import get_session_maker
        from ..models.settings import Settings

        session_maker = get_session_maker()
        session = session_maker()
        try:
            settings = session.query(Settings).first()
            if not settings or not settings.f5_tts_endpoint:
                raise HTTPException(status_code=400, detail="F5-TTS服务端点未配置")

            f5_tts_endpoint = settings.f5_tts_endpoint
        finally:
            session.close()

        # 调用F5-TTS服务进行实际测试
        from ..services.tts_service import TTSService
        from ..schemas.settings import TTSConfig
        from ..core.database import get_session_maker

        # 创建TTS配置
        tts_config = TTSConfig(
            provider="f5-tts",
            f5TtsEndpoint=f5_tts_endpoint
        )

        # 创建临时输出文件
        import tempfile
        import os
        from pathlib import Path

        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_output_path = temp_file.name

        try:
            # 创建TTS服务实例（需要传入session_maker）
            session_maker = get_session_maker()
            tts_service = TTSService(session_maker)

            logger.info(f"开始调用F5-TTS API，音色: {voice.name}, 文本: {test_text}")

            success = await tts_service._call_f5_tts_api(
                config=tts_config.model_dump(),  # 转换为字典
                text=test_text,
                voice=voice.id,
                speed=1.0,
                output_path=temp_output_path
            )

            logger.info(f"F5-TTS API调用结果: success={success}, 输出文件: {temp_output_path}")

            if success and os.path.exists(temp_output_path):
                # 检查生成的音频文件大小
                file_size = os.path.getsize(temp_output_path)

                logger.info(f"F5-TTS音色测试成功: {voice.name}, 生成音频大小: {file_size} bytes")

                return ApiResponse.create_success(
                    data={
                        "voice_id": voice_id,
                        "voice_name": voice.name,
                        "test_text": test_text,
                        "test_result": "success",
                        "audio_size": file_size,
                        "message": f"成功生成 {file_size} 字节的音频文件"
                    },
                    message="音色测试成功"
                ).model_dump()
            else:
                error_details = []
                if not success:
                    error_details.append("F5-TTS API调用返回失败")
                if not os.path.exists(temp_output_path):
                    error_details.append(f"输出文件不存在: {temp_output_path}")
                else:
                    file_size = os.path.getsize(temp_output_path)
                    error_details.append(f"输出文件大小: {file_size} 字节")

                error_msg = "F5-TTS服务调用失败: " + "; ".join(error_details)
                logger.error(error_msg)
                raise Exception(error_msg)

        finally:
            # 清理临时文件
            if os.path.exists(temp_output_path):
                try:
                    os.unlink(temp_output_path)
                except:
                    pass

    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e)
        logger.error(f"音色测试失败: {error_msg}")
        logger.error(traceback.format_exc())

        # 提供更友好的错误信息
        if "F5-TTS服务端点未配置" in error_msg:
            user_msg = "F5-TTS服务端点未配置，请在设置页面配置F5-TTS服务地址"
        elif "gradio_client" in error_msg:
            user_msg = "无法连接到F5-TTS服务，请检查服务是否正常运行"
        elif "未找到音色配置" in error_msg:
            user_msg = "音色配置不存在或已损坏"
        elif "参考音频文件不存在" in error_msg:
            user_msg = "音色的参考音频文件丢失，请重新上传"
        else:
            user_msg = f"音色测试失败: {error_msg}"

        return ApiResponse.create_error(
            message=user_msg
        ).model_dump()
