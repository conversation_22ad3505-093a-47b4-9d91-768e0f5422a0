#!/usr/bin/env python3
"""
测试模板图片路径转换功能
"""

import sys
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from src.services.template_import_service import TemplateImportService

def test_image_path_conversion():
    """测试图片路径转换功能"""
    print("=== 测试图片路径转换功能 ===")
    
    service = TemplateImportService()
    
    # 模拟的HTML内容
    template_id = "test-123-456"
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <title>测试模板</title>
</head>
<body>
    <div class="container">
        <img src="{template_id}_images/images/auth.png" alt="认证图标" />
        <img src="{template_id}_images/remote/image_12345.png" alt="远程图片" />
        <img src="{template_id}_images/images/icons/1.png" alt="图标1" />
        <img src="{{{{avatar}}}}" alt="用户头像" />
        <img src="https://external.com/image.jpg" alt="外部图片" />
    </div>
</body>
</html>'''
    
    base_url = "http://localhost:8000"
    
    print("原始HTML内容:")
    print(html_content)
    print("\n" + "="*50 + "\n")
    
    # 转换路径
    converted_html = service.convert_image_paths_to_urls(html_content, template_id, base_url)
    
    print("转换后的HTML内容:")
    print(converted_html)
    print("\n" + "="*50 + "\n")
    
    # 验证转换结果
    expected_conversions = [
        (f"{template_id}_images/images/auth.png", f"{base_url}/templates/{template_id}_images/images/auth.png"),
        (f"{template_id}_images/remote/image_12345.png", f"{base_url}/templates/{template_id}_images/remote/image_12345.png"),
        (f"{template_id}_images/images/icons/1.png", f"{base_url}/templates/{template_id}_images/images/icons/1.png")
    ]
    
    print("验证转换结果:")
    all_passed = True
    for original, expected in expected_conversions:
        if expected in converted_html:
            print(f"✅ {original} -> {expected}")
        else:
            print(f"❌ {original} -> {expected} (未找到)")
            all_passed = False
    
    # 验证不应该被转换的路径
    unchanged_paths = ["{{avatar}}", "https://external.com/image.jpg"]
    print("\n验证未改变的路径:")
    for path in unchanged_paths:
        if path in converted_html:
            print(f"✅ {path} (保持不变)")
        else:
            print(f"❌ {path} (意外改变)")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    success = test_image_path_conversion()
    print(f"\n{'🎉 测试通过！' if success else '💥 测试失败！'}")
