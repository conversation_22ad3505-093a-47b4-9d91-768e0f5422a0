'use client';

import React, { useState, useEffect } from 'react';
import { useAccountStore } from '@/store/accountStore';
import { type Account, type AccountCreate, type AccountUpdate, PlatformType, AccountStatus } from '@/types/store';
import { useNotificationStore } from '@/store/notificationStore';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon,
  Squares2X2Icon,
  ListBulletIcon,
  UserIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';

// 平台和状态的显示名称
const platformNames: Record<PlatformType, string> = {
  [PlatformType.REDDIT]: 'Reddit',
  [PlatformType.YOUTUBE]: 'YouTube',
  [PlatformType.TIKTOK]: 'TikTok',
  [PlatformType.TWITTER]: 'Twitter',
  [PlatformType.INSTAGRAM]: 'Instagram',
  [PlatformType.BILIBILI]: 'Bilibili'
};

// 转换头像URL为完整的后端URL
const getFullAvatarUrl = (avatarUrl?: string): string => {
  if (!avatarUrl) return '';
  
  // 如果已经是完整URL，直接返回
  if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    return avatarUrl;
  }
  
  // 如果是相对路径，加上后端基础URL
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
  return `${API_BASE}${avatarUrl}`;
};

const statusNames: Record<AccountStatus, string> = {
  [AccountStatus.UNUSED]: '未使用',
  [AccountStatus.USED]: '已使用',
  [AccountStatus.DISABLED]: '已禁用'
};

// 状态颜色类
const statusColors: Record<AccountStatus, string> = {
  [AccountStatus.UNUSED]: 'bg-blue-100 text-blue-800',
  [AccountStatus.USED]: 'bg-green-100 text-green-800',
  [AccountStatus.DISABLED]: 'bg-red-100 text-red-800'
};

// 平台颜色类
const platformColors: Record<PlatformType, string> = {
  [PlatformType.REDDIT]: 'bg-orange-100 text-orange-800',
  [PlatformType.YOUTUBE]: 'bg-red-100 text-red-800',
  [PlatformType.TIKTOK]: 'bg-black text-white',
  [PlatformType.TWITTER]: 'bg-blue-100 text-blue-800',
  [PlatformType.INSTAGRAM]: 'bg-pink-100 text-pink-800',
  [PlatformType.BILIBILI]: 'bg-cyan-100 text-cyan-800'
};

// 新建/编辑账号模态框
const AccountModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  account?: Account;
  onSave: (data: AccountCreate | AccountUpdate) => Promise<Account>;
  onAvatarUploaded?: () => void;
}> = ({ isOpen, onClose, account, onSave, onAvatarUploaded }) => {
  const { uploadAvatar } = useAccountStore();
  
  const [formData, setFormData] = useState<AccountCreate>({
    name: '',
    platform: PlatformType.REDDIT,
    description: '',
    brand_color: '#ff4500',
    font_style: '微软雅黑',
    content_style: '轻松幽默',
    platform_settings: {}
  });
  const [loading, setLoading] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string>('');
  const [uploadingAvatar, setUploadingAvatar] = useState(false);

  useEffect(() => {
    if (account) {
      setFormData({
        name: account.name,
        platform: account.platform,
        description: account.description || '',
        brand_color: account.brand_color,
        font_style: account.font_style,
        content_style: account.content_style,
        platform_settings: account.platform_settings || {}
      });
      // 为头像URL添加版本参数以破坏缓存
      setAvatarPreview(account.avatar_url ? 
        `${getFullAvatarUrl(account.avatar_url)}?v=${new Date(account.updated_at || account.created_at).getTime()}` : 
        ''
      );
    } else {
      setFormData({
        name: '',
        platform: PlatformType.REDDIT,
        description: '',
        brand_color: '#ff4500',
        font_style: '微软雅黑',
        content_style: '轻松幽默',
        platform_settings: {}
      });
      setAvatarPreview('');
    }
    setAvatarFile(null);
  }, [account, isOpen]);

  // 处理头像文件选择
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        alert('请选择有效的图片文件 (JPEG, PNG, GIF, WebP)');
        return;
      }
      
      // 验证文件大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('文件大小不能超过 5MB');
        return;
      }
      
      setAvatarFile(file);
      
      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 移除头像
  const handleRemoveAvatar = () => {
    setAvatarFile(null);
    setAvatarPreview('');
    // 注意：这里只是清除本地预览，实际删除服务器头像需要在保存时处理
    // 或者可以添加一个立即删除服务器头像的API调用
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;
    
    setLoading(true);
    try {
      // 先保存账号基本信息
      const savedAccount = await onSave(formData);
      
      // 如果有新的头像文件，上传头像
      if (avatarFile) {
        setUploadingAvatar(true);
        try {
          // 如果是编辑模式，使用现有账号ID；如果是新建模式，使用返回的账号ID
          const accountId = account ? account.id : savedAccount.id;
          const newAvatarUrl = await uploadAvatar(accountId, avatarFile);
          
          // 更新头像预览为新的头像URL（带版本参数）
          if (newAvatarUrl) {
            setAvatarPreview(`${newAvatarUrl}?v=${Date.now()}`);
          }
          
          // 通知父组件头像已上传，需要刷新列表
          onAvatarUploaded?.();
        } catch (error) {
          console.error('上传头像失败:', error);
          // 即使头像上传失败，也不阻止保存账号信息
        } finally {
          setUploadingAvatar(false);
        }
      }
      
      onClose();
    } catch (error) {
      console.error('保存账号失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {account ? '编辑账号' : '新建账号'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 头像上传 */}
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden border-2 border-gray-300">
                {avatarPreview ? (
                  <img
                    src={avatarPreview}
                    alt="头像预览"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <UserIcon className="w-10 h-10 text-gray-400" />
                )}
              </div>
              
              {/* 上传按钮 */}
              <label className="absolute bottom-0 right-0 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-1.5 cursor-pointer transition-colors">
                <PhotoIcon className="w-4 h-4" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </label>
              
              {/* 删除按钮 */}
              {avatarPreview && (
                <button
                  type="button"
                  onClick={handleRemoveAvatar}
                  className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 transition-colors"
                >
                  <XMarkIcon className="w-3 h-3" />
                </button>
              )}
            </div>
          </div>
          
          {uploadingAvatar && (
            <div className="text-center text-sm text-blue-600">
              正在上传头像...
            </div>
          )}

          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                账号名称 *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入账号名称"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                平台类型 *
              </label>
              <select
                value={formData.platform}
                onChange={(e) => setFormData(prev => ({ ...prev, platform: e.target.value as PlatformType }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                {Object.entries(platformNames).map(([value, label]) => (
                  <option key={value} value={value}>{label}</option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入账号描述"
              rows={3}
            />
          </div>

          {/* 个性化设置 */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-medium mb-3">个性化设置</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  品牌色彩
                </label>
                <input
                  type="color"
                  value={formData.brand_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, brand_color: e.target.value }))}
                  className="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  字体风格
                </label>
                <select
                  value={formData.font_style}
                  onChange={(e) => setFormData(prev => ({ ...prev, font_style: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="微软雅黑">微软雅黑</option>
                  <option value="苹方">苹方</option>
                  <option value="思源黑体">思源黑体</option>
                  <option value="阿里巴巴普惠体">阿里巴巴普惠体</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  内容风格
                </label>
                <select
                  value={formData.content_style}
                  onChange={(e) => setFormData(prev => ({ ...prev, content_style: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="轻松幽默">轻松幽默</option>
                  <option value="专业严肃">专业严肃</option>
                  <option value="温馨感人">温馨感人</option>
                  <option value="激励向上">激励向上</option>
                  <option value="神秘悬疑">神秘悬疑</option>
                </select>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              disabled={loading || uploadingAvatar || !formData.name.trim()}
            >
              {loading ? '保存中...' : uploadingAvatar ? '上传头像中...' : '保存'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default function AccountsPage() {
  const {
    accounts,
    selectedAccounts,
    stats,
    loading,
    loadAccounts,
    loadAccountStats,
    createAccount,
    updateAccount,
    deleteAccount,
    bulkDeleteAccounts,
    bulkUpdateStatus,
    uploadAvatar,
    useAccount,
    setSelectedAccounts
  } = useAccountStore();

  const { addNotification } = useNotificationStore();

  // 本地状态
  const [showModal, setShowModal] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | undefined>();
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table');
  const [searchQuery, setSearchQuery] = useState('');
  const [platformFilter, setPlatformFilter] = useState<PlatformType | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<AccountStatus | 'all'>('all');
  const [uploadingAvatarIds, setUploadingAvatarIds] = useState<Set<number>>(new Set());

  // 加载数据
  useEffect(() => {
    loadAccounts();
    loadAccountStats();
  }, [loadAccounts, loadAccountStats]);

  // 当账号列表更新时，同步更新正在编辑的账号数据
  useEffect(() => {
    if (editingAccount) {
      const updatedAccount = accounts.find(acc => acc.id === editingAccount.id);
      if (updatedAccount) {
        setEditingAccount(updatedAccount);
      }
    }
  }, [accounts, editingAccount]);

  // 处理快速头像上传
  const handleQuickAvatarUpload = async (accountId: number, accountName: string, file: File) => {
    // 添加到上传状态
    setUploadingAvatarIds(prev => new Set(prev).add(accountId));
    
    try {
      await uploadAvatar(accountId, file);
      // 刷新账号列表以显示新头像
      await loadAccounts();
      addNotification({
        type: 'success',
        title: '头像更新成功',
        message: `账号 "${accountName}" 的头像已更新`
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: '头像更新失败',
        message: error.message || '上传头像时发生错误'
      });
    } finally {
      // 从上传状态中移除
      setUploadingAvatarIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(accountId);
        return newSet;
      });
    }
  };

  // 筛选后的账号列表
  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = !searchQuery || 
      account.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (account.description && account.description.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesPlatform = platformFilter === 'all' || account.platform === platformFilter;
    const matchesStatus = statusFilter === 'all' || account.status === statusFilter;
    
    return matchesSearch && matchesPlatform && matchesStatus;
  });

  // 处理创建账号
  const handleCreateAccount = async (data: AccountCreate | AccountUpdate): Promise<Account> => {
    try {
      const account = await createAccount(data as AccountCreate);
      // 刷新账号列表和统计信息
      await loadAccounts();
      await loadAccountStats();
      addNotification({
        type: 'success',
        title: '创建成功',
        message: `账号 "${data.name}" 已创建`
      });
      return account;
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: '创建失败',
        message: error.message || '创建账号时发生错误'
      });
      throw error;
    }
  };

  // 处理更新账号
  const handleUpdateAccount = async (data: AccountCreate | AccountUpdate): Promise<Account> => {
    if (!editingAccount) throw new Error('No account to update');
    
    try {
      const account = await updateAccount(editingAccount.id, data as AccountUpdate);
      // 刷新账号列表和统计信息
      await loadAccounts();
      await loadAccountStats();
      addNotification({
        type: 'success',
        title: '更新成功',
        message: `账号 "${data.name}" 已更新`
      });
      return account;
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: '更新失败',
        message: error.message || '更新账号时发生错误'
      });
      throw error;
    }
  };

  // 处理删除账号
  const handleDeleteAccount = async (id: number, name: string) => {
    if (!confirm(`确定要删除账号 "${name}" 吗？此操作不可恢复。`)) return;
    
    try {
      await deleteAccount(id);
      // 刷新账号列表和统计信息
      await loadAccounts();
      await loadAccountStats();
      addNotification({
        type: 'success',
        title: '删除成功',
        message: `账号 "${name}" 已删除`
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: '删除失败',
        message: error.message || '删除账号时发生错误'
      });
    }
  };

  // 处理批量删除
  const handleBulkDelete = async () => {
    if (selectedAccounts.length === 0) return;
    if (!confirm(`确定要删除选中的 ${selectedAccounts.length} 个账号吗？此操作不可恢复。`)) return;
    
    try {
      await bulkDeleteAccounts(selectedAccounts);
      setSelectedAccounts([]);
      // 刷新账号列表和统计信息
      await loadAccounts();
      await loadAccountStats();
      addNotification({
        type: 'success',
        title: '批量删除成功',
        message: `已删除 ${selectedAccounts.length} 个账号`
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: '批量删除失败',
        message: error.message || '批量删除时发生错误'
      });
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-6 py-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-end mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">账号管理</h1>
          <p className="text-gray-600">管理各平台账号信息，包括Reddit、YouTube、TikTok等社交媒体账号</p>
        </div>
        <div className="flex gap-3">
          <button className="bg-white hover:bg-gray-50 text-gray-700 border border-gray-200 px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2">
            <ArrowDownTrayIcon className="w-4 h-4" />
            导出账号
          </button>
          <button
            onClick={() => {
              setEditingAccount(undefined);
              setShowModal(true);
            }}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2"
          >
            <PlusIcon className="w-4 h-4" />
            新建账号
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <div className="text-2xl font-bold text-blue-500 mb-1">{stats.total_accounts}</div>
            <div className="text-sm text-gray-600">账号总数</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <div className="text-2xl font-bold text-green-500 mb-1">{stats.by_status[AccountStatus.UNUSED] || 0}</div>
            <div className="text-sm text-gray-600">可用账号</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <div className="text-2xl font-bold text-yellow-500 mb-1">{stats.by_status[AccountStatus.USED] || 0}</div>
            <div className="text-sm text-gray-600">已使用</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <div className="text-2xl font-bold text-red-500 mb-1">{stats.by_status[AccountStatus.DISABLED] || 0}</div>
            <div className="text-sm text-gray-600">已禁用</div>
          </div>
        </div>
      )}

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6 flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索账号名称或描述..."
              className="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
          </div>
          
          <select
            value={platformFilter}
            onChange={(e) => setPlatformFilter(e.target.value as PlatformType | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
          >
            <option value="all">全部平台</option>
            {Object.entries(platformNames).map(([value, label]) => (
              <option key={value} value={value}>{label}</option>
            ))}
          </select>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as AccountStatus | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
          >
            <option value="all">全部状态</option>
            {Object.entries(statusNames).map(([value, label]) => (
              <option key={value} value={value}>{label}</option>
            ))}
          </select>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="bg-gray-100 rounded-md p-1 flex">
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 rounded text-sm ${
                viewMode === 'table'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <ListBulletIcon className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('card')}
              className={`p-2 rounded text-sm ${
                viewMode === 'card'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Squares2X2Icon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* 批量操作栏 */}
      {selectedAccounts.length > 0 && (
        <div className="bg-blue-500 text-white p-4 rounded-lg mb-6 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <span>已选择 {selectedAccounts.length} 个账号</span>
          </div>
          <div className="flex gap-2">
            <button
              onClick={handleBulkDelete}
              className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              批量删除
            </button>
            <button
              onClick={() => setSelectedAccounts([])}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              取消选择
            </button>
          </div>
        </div>
      )}

      {/* 账号列表 */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      ) : filteredAccounts.length === 0 ? (
        <div className="text-center py-16 text-gray-500">
          <div className="w-16 h-16 mx-auto mb-4 text-gray-300">
            <ExclamationTriangleIcon className="w-full h-full" />
          </div>
          <div className="text-lg font-medium mb-2">没有找到匹配的账号</div>
          <div className="text-gray-400">
            尝试调整搜索条件或创建新的账号
          </div>
        </div>
      ) : viewMode === 'table' ? (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedAccounts.length === filteredAccounts.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedAccounts(filteredAccounts.map(acc => acc.id));
                      } else {
                        setSelectedAccounts([]);
                      }
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">账号名称</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">平台</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">状态</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">使用次数</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">创建时间</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">操作</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredAccounts.map((account) => (
                <tr key={account.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <input
                      type="checkbox"
                      checked={selectedAccounts.includes(account.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedAccounts([...selectedAccounts, account.id]);
                        } else {
                          setSelectedAccounts(selectedAccounts.filter(id => id !== account.id));
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                        {account.avatar_url ? (
                          <img
                            src={`${getFullAvatarUrl(account.avatar_url)}?v=${new Date(account.updated_at || account.created_at).getTime()}`}
                            alt={account.name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <UserIcon className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{account.name}</div>
                        {account.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">{account.description}</div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${platformColors[account.platform]}`}>
                      {platformNames[account.platform]}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColors[account.status]}`}>
                      {statusNames[account.status]}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">{account.usage_count}</td>
                  <td className="px-4 py-3 text-sm text-gray-500">
                    {new Date(account.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex gap-1">
                      <button
                        onClick={() => {
                          setEditingAccount(account);
                          setShowModal(true);
                        }}
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="编辑"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <label className={`p-1 cursor-pointer transition-colors ${
                        uploadingAvatarIds.has(account.id) 
                          ? 'text-blue-600 animate-pulse' 
                          : 'text-gray-400 hover:text-green-600'
                      }`} title={uploadingAvatarIds.has(account.id) ? '上传中...' : '上传头像'}>
                        <PhotoIcon className="w-4 h-4" />
                        <input
                          type="file"
                          accept="image/*"
                          disabled={uploadingAvatarIds.has(account.id)}
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              await handleQuickAvatarUpload(account.id, account.name, file);
                              // 清空input以允许重复选择同一文件
                              e.target.value = '';
                            }
                          }}
                          className="hidden"
                        />
                      </label>
                      <button
                        onClick={() => handleDeleteAccount(account.id, account.name)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="删除"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAccounts.map((account) => (
            <div key={account.id} className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                      {account.avatar_url ? (
                        <img
                          src={`${getFullAvatarUrl(account.avatar_url)}?v=${new Date(account.updated_at || account.created_at).getTime()}`}
                          alt={account.name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <UserIcon className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{account.name}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded ${platformColors[account.platform]}`}>
                          {platformNames[account.platform]}
                        </span>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded ${statusColors[account.status]}`}>
                          {statusNames[account.status]}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={selectedAccounts.includes(account.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedAccounts([...selectedAccounts, account.id]);
                    } else {
                      setSelectedAccounts(selectedAccounts.filter(id => id !== account.id));
                    }
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
              
              {account.description && (
                <p className="text-sm text-gray-600 mb-3">{account.description}</p>
              )}
              
              <div className="text-xs text-gray-500 mb-4">
                <div>使用次数: {account.usage_count}</div>
                <div>创建时间: {new Date(account.created_at).toLocaleDateString()}</div>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    setEditingAccount(account);
                    setShowModal(true);
                  }}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
                >
                  编辑
                </button>
                <label className={`px-3 py-2 rounded text-sm font-medium transition-colors cursor-pointer flex items-center justify-center ${
                  uploadingAvatarIds.has(account.id)
                    ? 'bg-green-400 animate-pulse cursor-not-allowed'
                    : 'bg-green-500 hover:bg-green-600'
                } text-white`} title={uploadingAvatarIds.has(account.id) ? '上传中...' : '上传头像'}>
                  <PhotoIcon className="w-4 h-4" />
                  <input
                    type="file"
                    accept="image/*"
                    disabled={uploadingAvatarIds.has(account.id)}
                    onChange={async (e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        await handleQuickAvatarUpload(account.id, account.name, file);
                        // 清空input以允许重复选择同一文件
                        e.target.value = '';
                      }
                    }}
                    className="hidden"
                  />
                </label>
                <button
                  onClick={() => handleDeleteAccount(account.id, account.name)}
                  className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 新建/编辑模态框 */}
      <AccountModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        account={editingAccount}
        onSave={editingAccount ? handleUpdateAccount : handleCreateAccount}
        onAvatarUploaded={() => {
          // 头像上传后刷新账号列表
          loadAccounts();
          loadAccountStats();
        }}
      />
    </div>
  );
}
