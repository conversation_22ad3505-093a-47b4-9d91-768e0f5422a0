/**
 * 状态管理测试页面
 */

'use client'

import React from 'react'
import { useSettingsStore } from '@/store/settingsStore'
import { useResourceStore } from '@/store/resourceStore'
import { useGenerationStore } from '@/store/generationStore'
import { Button } from '@/components/ui/Button'

export default function StateTestPage() {
  // 获取状态
  const settings = useSettingsStore()
  const resources = useResourceStore()
  const generation = useGenerationStore()

  const testStates = () => {
    console.log('Settings State:', settings)
    console.log('Resources State:', resources)
    console.log('Generation State:', generation)
  }

  const testSettingsUpdate = () => {
    settings.updateTTSConfig({
      provider: 'openai',
      apiKey: 'test-key-' + Date.now(),
      voice: 'alloy'
    })
    
    settings.updateLLMConfig({
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.8
    })
  }

  const testResourcesUpdate = () => {
    const musicId = resources.addBackgroundMusic({
      name: '测试音乐 ' + Date.now(),
      filePath: '/test/music.mp3',
      duration: 120,
      category: '测试',
      tags: ['测试', '音乐'],
      isBuiltIn: false
    })

    resources.selectBackgroundMusic(musicId)

    const promptId = resources.addPrompt({
      name: '测试提示词',
      content: '这是一个测试提示词 {variable}',
      category: '测试',
      variables: ['variable'],
      isBuiltIn: false
    })

    resources.selectPrompt(promptId)
  }

  const testGenerationUpdate = () => {
    generation.updateConfig({
      videoDuration: 90,
      videoResolution: '1080p',
      backgroundMusicVolume: 0.5
    })
  }

  const clearAllData = () => {
    settings.resetToDefault()
    resources.resetResources()
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">状态管理测试</h1>
        
        {/* 操作按钮 */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
          <Button onClick={testStates} variant="outline">
            打印状态到控制台
          </Button>
          <Button onClick={testSettingsUpdate} variant="primary">
            测试设置更新
          </Button>
          <Button onClick={testResourcesUpdate} variant="primary">
            测试资源更新
          </Button>
          <Button onClick={testGenerationUpdate} variant="primary">
            测试生成配置更新
          </Button>
          <Button onClick={clearAllData} variant="danger">
            清空所有数据
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 设置状态 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 text-blue-600">设置状态</h2>
            
            <div className="space-y-3">
              <div>
                <strong>TTS Provider:</strong> {settings.tts.provider}
              </div>
              <div>
                <strong>TTS Voice:</strong> {settings.tts.voice}
              </div>
              <div>
                <strong>TTS Speed:</strong> {settings.tts.speed}
              </div>
              <div>
                <strong>LLM Provider:</strong> {settings.llm.provider}
              </div>
              <div>
                <strong>LLM Model:</strong> {settings.llm.model}
              </div>
              <div>
                <strong>Temperature:</strong> {settings.llm.temperature}
              </div>
              <div>
                <strong>Theme:</strong> {settings.general.theme}
              </div>
              <div>
                <strong>Language:</strong> {settings.general.language}
              </div>
              <div>
                <strong>Auto Save:</strong> {settings.general.autoSave ? '是' : '否'}
              </div>
            </div>

            <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
              <strong>API Keys:</strong><br/>
              TTS: {settings.tts.apiKey ? '已设置' : '未设置'}<br/>
              LLM: {settings.llm.apiKey ? '已设置' : '未设置'}
            </div>
          </div>

          {/* 资源状态 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 text-green-600">资源状态</h2>
            
            <div className="space-y-3">
              <div>
                <strong>背景音乐:</strong> {resources.backgroundMusic.length} 个
                {resources.selectedBackgroundMusic && (
                  <div className="text-sm text-gray-600">
                    已选择: {resources.backgroundMusic.find(m => m.id === resources.selectedBackgroundMusic)?.name}
                  </div>
                )}
              </div>
              
              <div>
                <strong>视频素材:</strong> {resources.videoMaterials.length} 个
                {resources.selectedVideoMaterials.length > 0 && (
                  <div className="text-sm text-gray-600">
                    已选择: {resources.selectedVideoMaterials.length} 个
                  </div>
                )}
              </div>
              
              <div>
                <strong>提示词:</strong> {resources.prompts.length} 个
                {resources.selectedPrompt && (
                  <div className="text-sm text-gray-600">
                    已选择: {resources.prompts.find(p => p.id === resources.selectedPrompt)?.name}
                  </div>
                )}
              </div>
              
              <div>
                <strong>账户:</strong> {resources.accounts.length} 个
                {resources.selectedAccount && (
                  <div className="text-sm text-gray-600">
                    已选择: {resources.accounts.find(a => a.id === resources.selectedAccount)?.name}
                  </div>
                )}
              </div>
              
              <div>
                <strong>封面模板:</strong> {resources.coverTemplates.length} 个
                {resources.selectedCoverTemplate && (
                  <div className="text-sm text-gray-600">
                    已选择: {resources.coverTemplates.find(t => t.id === resources.selectedCoverTemplate)?.name}
                  </div>
                )}
              </div>
            </div>

            <div className="mt-4">
              <h3 className="font-medium mb-2">背景音乐列表:</h3>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {resources.backgroundMusic.map(music => (
                  <div key={music.id} className="text-sm p-2 bg-gray-50 rounded">
                    {music.name} 
                    <span className="text-gray-500">({music.duration}s)</span>
                    {music.isBuiltIn && <span className="text-blue-500 ml-2">[内置]</span>}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 生成状态 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 text-purple-600">生成状态</h2>
            
            <div className="space-y-3">
              <div>
                <strong>视频时长:</strong> {generation.config.videoDuration}s
              </div>
              <div>
                <strong>分辨率:</strong> {generation.config.videoResolution}
              </div>
              <div>
                <strong>帧率:</strong> {generation.config.frameRate}fps
              </div>
              <div>
                <strong>背景音乐音量:</strong> {(generation.config.backgroundMusicVolume * 100).toFixed(0)}%
              </div>
              <div>
                <strong>语音音量:</strong> {(generation.config.voiceVolume * 100).toFixed(0)}%
              </div>
              <div>
                <strong>显示字幕:</strong> {generation.config.showSubtitles ? '是' : '否'}
              </div>
              <div>
                <strong>任务数:</strong> {generation.tasks.length}
              </div>
              {generation.currentTask && (
                <div>
                  <strong>当前任务:</strong> {generation.currentTask}
                </div>
              )}
            </div>

            <div className="mt-4">
              <h3 className="font-medium mb-2">选中资源:</h3>
              <div className="space-y-1 text-sm">
                <div>提示词: {generation.config.selectedPrompt || '未选择'}</div>
                <div>账户: {generation.config.selectedAccount || '未选择'}</div>
                <div>背景音乐: {generation.config.selectedBackgroundMusic || '未选择'}</div>
                <div>视频素材: {generation.config.selectedVideoMaterials.length || 0} 个</div>
                <div>封面模板: {generation.config.selectedCoverTemplate || '未选择'}</div>
              </div>
            </div>

            <div className="mt-4">
              <h3 className="font-medium mb-2">任务列表:</h3>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {generation.tasks.length === 0 ? (
                  <div className="text-sm text-gray-500">暂无任务</div>
                ) : (
                  generation.tasks.map(task => (
                    <div key={task.id} className="text-sm p-2 bg-gray-50 rounded">
                      {task.id}
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${
                        task.status === 'completed' ? 'bg-green-100 text-green-800' :
                        task.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                        task.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {task.status}
                      </span>
                      {task.status === 'processing' && (
                        <span className="ml-2 text-gray-600">({task.progress}%)</span>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 持久化测试 */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">持久化测试</h2>
          <p className="text-gray-600 mb-4">
            修改上述状态后，刷新页面检查状态是否被正确保存和恢复。
          </p>
          <Button 
            onClick={() => window.location.reload()}
            variant="outline"
          >
            刷新页面
          </Button>
        </div>
      </div>
    </div>
  )
}
