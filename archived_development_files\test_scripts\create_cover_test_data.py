"""
创建封面模板测试数据
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def create_test_data():
    try:
        # 初始化数据库
        from src.core.database import init_db, get_db
        from src.models.resources import CoverTemplate
        from sqlalchemy.orm import Session
        import uuid
        
        print("初始化数据库...")
        init_db()
        
        # 获取数据库会话
        db = next(get_db())
        
        # 检查是否已有数据
        existing_count = db.query(CoverTemplate).count()
        print(f"现有模板数量: {existing_count}")
        
        if existing_count == 0:
            print("创建测试模板数据...")
            
            # 创建测试模板
            test_templates = [
                {
                    "id": str(uuid.uuid4()),
                    "name": "经典商务模板",
                    "preview_path": "/templates/business_preview.png",
                    "template_path": "/templates/business.json",
                    "variables": ["title", "nickname", "timestamp"],
                    "description": "适用于商务内容的经典模板",
                    "category": "经典",
                    "tags": ["商务", "正式"],
                    "width": 1920,
                    "height": 1080,
                    "format": "png",
                    "is_built_in": True,
                    "usage_count": 15
                },
                {
                    "id": str(uuid.uuid4()),
                    "name": "现代简约模板",
                    "preview_path": "/templates/modern_preview.png", 
                    "template_path": "/templates/modern.json",
                    "variables": ["title", "nickname", "avatar"],
                    "description": "现代化简约风格模板",
                    "category": "现代",
                    "tags": ["简约", "现代"],
                    "width": 1920,
                    "height": 1080,
                    "format": "png",
                    "is_built_in": True,
                    "usage_count": 8
                },
                {
                    "id": str(uuid.uuid4()),
                    "name": "科技风格模板",
                    "preview_path": "/templates/tech_preview.png",
                    "template_path": "/templates/tech.json", 
                    "variables": ["title", "subtitle", "timestamp"],
                    "description": "科技感十足的模板设计",
                    "category": "科技",
                    "tags": ["科技", "未来"],
                    "width": 1920,
                    "height": 1080,
                    "format": "png",
                    "is_built_in": True,
                    "usage_count": 22
                }
            ]
            
            for template_data in test_templates:
                db_template = CoverTemplate(**template_data)
                db.add(db_template)
            
            db.commit()
            print(f"成功创建 {len(test_templates)} 个测试模板")
        else:
            print("已存在模板数据，跳过创建")
        
        # 验证数据
        final_count = db.query(CoverTemplate).count()
        print(f"最终模板数量: {final_count}")
        
        # 显示所有模板
        templates = db.query(CoverTemplate).all()
        print("\n现有模板:")
        for template in templates:
            print(f"  - {template.name} ({template.category}) - 使用次数: {template.usage_count}")
        
        db.close()
        print("\n测试数据创建完成!")
        
    except Exception as e:
        print(f"创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_test_data()
