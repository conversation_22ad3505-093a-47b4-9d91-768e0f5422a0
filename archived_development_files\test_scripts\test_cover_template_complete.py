"""
完整的封面模板API测试
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

import asyncio
import httpx
from fastapi.testclient import TestClient

def test_cover_template_complete():
    """完整测试封面模板API"""
    try:
        from main import app
        
        client = TestClient(app)
        
        print("=== 开始完整封面模板API测试 ===")
        
        # 1. 测试健康检查
        print("\n1. 测试健康检查...")
        response = client.get("/health")
        print(f"Health check: {response.status_code} - {response.json()}")
        
        response = client.get("/api/health")
        print(f"API health check: {response.status_code} - {response.json()}")
        
        # 2. 测试获取统计信息
        print("\n2. 测试获取统计信息...")
        response = client.get("/api/cover-templates/stats")
        print(f"Stats: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"  统计数据: {stats}")
        else:
            print(f"  Error: {response.text}")
        
        # 3. 测试获取可用变量
        print("\n3. 测试获取可用变量...")
        response = client.get("/api/cover-templates/variables")
        print(f"Variables: {response.status_code}")
        if response.status_code == 200:
            variables = response.json()
            print(f"  可用变量数量: {len(variables.get('data', {}).get('variables', []))}")
            for var in variables.get('data', {}).get('variables', [])[:3]:  # 只显示前3个
                print(f"    - {var.get('name')}: {var.get('label')} ({var.get('type')})")
        else:
            print(f"  Error: {response.text}")
        
        # 4. 测试获取模板列表
        print("\n4. 测试获取模板列表...")
        response = client.get("/api/cover-templates")
        print(f"Template list: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            templates = data if isinstance(data, list) else []
            print(f"  模板数量: {len(templates)}")
        else:
            print(f"  Error: {response.text}")
        
        # 5. 测试获取分类列表
        print("\n5. 测试获取分类列表...")
        response = client.get("/api/cover-templates/categories/list")
        print(f"Categories: {response.status_code}")
        if response.status_code == 200:
            categories = response.json()
            print(f"  分类数据: {categories}")
        else:
            print(f"  Error: {response.text}")
            
        # 6. 测试创建模板
        print("\n6. 测试创建模板...")
        test_template = {
            "name": "测试模板",
            "preview_path": "/test/preview.png",
            "template_path": "/test/template.json",
            "variables": ["avatar", "nickname", "title"],
            "description": "这是一个测试模板",
            "category": "test",
            "tags": ["测试", "样例"],
            "width": 1920,
            "height": 1080,
            "format": "png"
        }
        
        response = client.post("/api/cover-templates", json=test_template)
        print(f"Create template: {response.status_code}")
        if response.status_code == 200:
            created_template = response.json()
            template_id = created_template.get("id")
            print(f"  创建成功，模板ID: {template_id}")
            
            # 7. 测试获取单个模板
            if template_id:
                print(f"\n7. 测试获取单个模板 (ID: {template_id})...")
                response = client.get(f"/api/cover-templates/{template_id}")
                print(f"Get template: {response.status_code}")
                if response.status_code == 200:
                    template = response.json()
                    print(f"  模板名称: {template.get('name')}")
                else:
                    print(f"  Error: {response.text}")
                
                # 8. 测试更新模板
                print(f"\n8. 测试更新模板...")
                update_data = {
                    "name": "更新后的测试模板",
                    "description": "这是更新后的描述"
                }
                response = client.put(f"/api/cover-templates/{template_id}", json=update_data)
                print(f"Update template: {response.status_code}")
                if response.status_code == 200:
                    updated_template = response.json()
                    print(f"  更新后名称: {updated_template.get('name')}")
                else:
                    print(f"  Error: {response.text}")
                
                # 9. 测试预览模板
                print(f"\n9. 测试预览模板...")
                preview_data = {
                    "variables": {
                        "avatar": "test_avatar.jpg",
                        "nickname": "测试用户",
                        "title": "测试标题"
                    }
                }
                response = client.post(f"/api/cover-templates/{template_id}/preview", json=preview_data)
                print(f"Preview template: {response.status_code}")
                if response.status_code == 200:
                    preview = response.json()
                    print(f"  预览响应: {preview}")
                else:
                    print(f"  Error: {response.text}")
                
                # 10. 测试使用模板（增加使用次数）
                print(f"\n10. 测试使用模板...")
                response = client.post(f"/api/cover-templates/{template_id}/use")
                print(f"Use template: {response.status_code}")
                if response.status_code == 200:
                    use_result = response.json()
                    print(f"  使用结果: {use_result}")
                else:
                    print(f"  Error: {response.text}")
                
                # 11. 测试删除模板
                print(f"\n11. 测试删除模板...")
                response = client.delete(f"/api/cover-templates/{template_id}")
                print(f"Delete template: {response.status_code}")
                if response.status_code == 200:
                    delete_result = response.json()
                    print(f"  删除结果: {delete_result}")
                else:
                    print(f"  Error: {response.text}")
                    
        else:
            print(f"  Error: {response.text}")
        
        print("\n=== 封面模板API测试完成 ===")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cover_template_complete()
