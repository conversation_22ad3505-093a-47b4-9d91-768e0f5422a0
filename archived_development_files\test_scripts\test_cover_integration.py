"""
集成测试：视频生成中的封面截图功能
测试在视频生成过程中使用封面模板生成网页截图作为封面
"""

import asyncio
import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

# 设置路径
backend_path = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_path))

# 加载环境变量
load_dotenv(dotenv_path=backend_path / '.env')

from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, VideoMaterial, BackgroundMusic, Prompt, CoverTemplate, VideoGenerationTask, VideoGenerationJob, TaskStatus
from backend.src.services.video_generation_service import VideoGenerationService
from backend.src.services.cover_screenshot_service import cover_screenshot_service
from loguru import logger

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_cover_generation_in_video_workflow():
    """测试视频生成工作流中的封面生成功能"""
    db = SessionLocal()
    try:
        print("=== 视频生成中的封面截图功能测试 ===")
        
        # 1. 检查必要的资源
        account = db.query(Account).first()
        if not account:
            print("❌ 数据库中没有找到账号")
            return
        
        video_materials = db.query(VideoMaterial).limit(2).all()
        if len(video_materials) < 2:
            print("❌ 数据库中视频素材不足")
            return
        
        prompt = db.query(Prompt).first()
        if not prompt:
            print("❌ 数据库中没有找到提示词")
            return
        
        music = db.query(BackgroundMusic).first()
        if not music:
            print("❌ 数据库中没有找到背景音乐")
            return
        
        template = db.query(CoverTemplate).first()
        if not template:
            print("❌ 数据库中没有找到封面模板")
            return
        
        print(f"✅ 资源检查通过:")
        print(f"   账号: {account.name}")
        print(f"   视频素材: {len(video_materials)} 个")
        print(f"   提示词: {prompt.name}")
        print(f"   背景音乐: {music.name}")
        print(f"   封面模板: {template.name}")
        
        # 2. 创建测试任务
        job = VideoGenerationJob(name="封面截图测试作业", total_tasks=1, status=TaskStatus.PENDING)
        db.add(job)
        db.commit()
        db.refresh(job)
        
        task = VideoGenerationTask(
            job_id=job.id,
            task_name="封面截图测试任务",
            account_id=account.id,
            status=TaskStatus.PENDING,
            generated_story="这是一个测试故事，用来验证封面截图功能是否能够正常工作在视频生成流程中。",
            first_sentence="这是一个测试故事，用来验证封面截图功能。"
        )
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 创建测试任务: {task.id}")
        
        # 3. 准备作业配置
        job_config = {
            "video_material_group": "shorts",
            "material_selection": "random",
            "prompt_id": prompt.id,
            "voice_settings": {"voice": "zh_male_beijingxiaoye_emo_v2_mars_bigtts", "speed": 1.2},
            "background_music_group": music.category or "default",
            "music_selection": "random",
            "cover_template_id": template.id,
            "subtitle_settings": {"font": "Arial", "size": 24, "color": "#ffffff"},
            "video_settings": {"resolution": "1080x1920", "fps": 30, "format": "mp4"}
        }
        
        # 4. 创建视频生成服务
        video_service = VideoGenerationService(SessionLocal)
        helpers = video_service._create_helpers()
        
        # 5. 测试封面生成
        print("🔄 测试封面生成...")
        
        try:
            cover_path = await helpers._generate_cover(task, job_config)
            
            if cover_path:
                print(f"✅ 封面生成成功: {cover_path}")
                
                # 检查文件是否存在
                full_cover_path = backend_path / cover_path
                if full_cover_path.exists():
                    print(f"✅ 封面文件确实存在")
                    print(f"   文件大小: {full_cover_path.stat().st_size} bytes")
                    
                    # 更新任务状态
                    task.cover_image_path = cover_path
                    task.current_step = "封面生成完成"
                    task.progress = 70
                    db.commit()
                    
                    print("✅ 任务状态已更新")
                else:
                    print(f"❌ 封面文件不存在: {full_cover_path}")
            else:
                print("❌ 封面生成返回空路径")
                
        except Exception as e:
            print(f"❌ 封面生成失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. 清理测试数据
        print("🔄 清理测试数据...")
        db.delete(task)
        db.delete(job)
        db.commit()
        print("✅ 测试数据已清理")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await cover_screenshot_service.cleanup()
        db.close()

async def test_manual_cover_generation():
    """手动测试封面生成"""
    db = SessionLocal()
    try:
        print("\n=== 手动封面生成测试 ===")
        
        # 获取资源
        account = db.query(Account).first()
        template = db.query(CoverTemplate).first()
        
        if not account or not template:
            print("❌ 缺少必要资源")
            return
        
        # 准备输出路径
        output_dir = Path("test_outputs")
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / f"manual_cover_{template.id}.png"
        
        # 生成封面
        title = "手动测试封面生成功能，验证模板变量替换和网页截图是否正常工作"
        
        success = await cover_screenshot_service.generate_cover_for_video_task(
            task=None,  # 手动测试不需要任务对象
            template_id=template.id,
            account=account,
            title=title,
            output_path=str(output_path),
            db=db
        )
        
        if success:
            print(f"✅ 手动封面生成成功: {output_path}")
        else:
            print("❌ 手动封面生成失败")
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

async def main():
    """主测试函数"""
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    
    try:
        # 测试手动封面生成
        await test_manual_cover_generation()
        
        # 测试视频工作流中的封面生成
        await test_cover_generation_in_video_workflow()
        
        print("\n=== 集成测试完成 ===")
        print("📝 注意事项:")
        print("   1. 确保数据库中有足够的测试数据")
        print("   2. 确保封面模板包含 reddit-cover 元素")
        print("   3. 确保 Playwright 浏览器已正确安装")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
