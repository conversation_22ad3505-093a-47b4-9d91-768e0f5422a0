#!/usr/bin/env python3
"""
调试视频查询问题
"""

import sys
sys.path.append('src')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.models.resources import VideoMaterial
from src.core.config import get_settings

def debug_video_query():
    # 连接数据库
    settings = get_settings()
    database_url = settings.database_url
    print(f"数据库URL: {database_url}")
    
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 查询所有视频素材
        print("执行查询: db.query(VideoMaterial).all()")
        video_materials = db.query(VideoMaterial).all()
        print(f"查询结果数量: {len(video_materials)}")
        
        for i, material in enumerate(video_materials):
            print(f"\n--- 视频素材 {i+1} ---")
            print(f"ID: {material.id}")
            print(f"Name: {material.name}")
            print(f"Category: {material.category}")
            print(f"Format: {material.format}")
            print(f"Duration: {material.duration}")
            print(f"File Path: {material.file_path}")
            
            # 测试转换为前端格式
            try:
                frontend_data = material.to_frontend_format()
                print("前端格式转换成功:")
                print(f"  ID: {frontend_data['id']}")
                print(f"  Name: {frontend_data['name']}")
                print(f"  Type: {frontend_data['type']}")
                print(f"  Category: {frontend_data['category']}")
            except Exception as e:
                print(f"前端格式转换失败: {e}")
                
    except Exception as e:
        print(f"查询错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    debug_video_query()
