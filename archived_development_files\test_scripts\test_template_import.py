#!/usr/bin/env python3
"""
测试模板导入服务的图片资源处理功能
"""

import os
import sys
from pathlib import Path

# 添加backend到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from src.services.template_import_service import TemplateImportService
from src.core.database import get_db_session
from src.models.resources import CoverTemplate

def test_template_import():
    """测试模板导入功能"""
    print("=== 测试模板导入服务 ===")
    
    # 创建服务实例
    service = TemplateImportService()
    
    # 获取数据库会话
    db = get_db_session()
    
    try:
        # 测试社交媒体模板路径
        template_path = Path("reddit-template/social_post_template.html")
        
        if not template_path.exists():
            print(f"❌ 模板文件不存在: {template_path}")
            return False
        
        print(f"✅ 找到模板文件: {template_path}")
        
        # 读取HTML内容进行分析
        with open(template_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 测试变量提取
        variables = service.extract_variables_from_html(html_content)
        print(f"📋 提取的变量: {variables}")
        
        # 测试图片路径提取
        image_paths = service.extract_image_paths_from_html(html_content)
        print(f"🖼️  提取的图片路径: {image_paths}")
        
        # 检查图片文件是否存在
        template_dir = template_path.parent
        missing_images = []
        for img_path in image_paths:
            img_file = template_dir / img_path
            if not img_file.exists():
                missing_images.append(img_path)
        
        if missing_images:
            print(f"⚠️  缺失的图片文件: {missing_images}")
        else:
            print("✅ 所有图片文件都存在")
        
        # 清理旧模板（如果存在）
        existing = db.query(CoverTemplate).filter(CoverTemplate.name == "社交媒体帖子模板").first()
        if existing:
            db.delete(existing)
            db.commit()
            print("🗑️  清理了已存在的模板")
        
        # 初始化默认模板
        print("📥 开始导入模板...")
        service.init_default_templates(db)
        
        # 验证导入结果
        imported_template = db.query(CoverTemplate).filter(CoverTemplate.name == "社交媒体帖子模板").first()
        
        if imported_template:
            print("✅ 模板导入成功")
            print(f"   - 模板ID: {imported_template.id}")
            print(f"   - 模板名称: {imported_template.name}")
            print(f"   - 变量列表: {imported_template.variables}")
            print(f"   - 模板路径: {imported_template.template_path}")
            
            # 检查图片目录
            templates_dir = service.templates_dir
            images_dir = templates_dir / f"{imported_template.id}_images"
            
            if images_dir.exists():
                print(f"📁 图片目录已创建: {images_dir}")
                
                # 列出复制的图片文件
                for root, dirs, files in os.walk(images_dir):
                    for file in files:
                        rel_path = Path(root).relative_to(images_dir) / file
                        print(f"   📷 {rel_path}")
            else:
                print("⚠️  图片目录未创建")
            
            # 读取处理后的HTML内容
            template_file = templates_dir / f"{imported_template.id}.html"
            if template_file.exists():
                with open(template_file, 'r', encoding='utf-8') as f:
                    processed_html = f.read()
                
                # 检查图片路径是否已更新
                updated_image_paths = service.extract_image_paths_from_html(processed_html)
                print(f"🔄 更新后的图片路径: {updated_image_paths}")
            
            return True
            
        else:
            print("❌ 模板导入失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

def main():
    """主函数"""
    success = test_template_import()
    
    if success:
        print("\n🎉 模板导入测试成功！")
    else:
        print("\n💥 模板导入测试失败！")
        
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
