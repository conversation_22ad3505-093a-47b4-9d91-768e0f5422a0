#!/usr/bin/env python3
"""
添加custom_title字段到video_generation_tasks表的脚本
"""

import sqlite3
import sys
import os
from pathlib import Path

def add_custom_title_column():
    """添加custom_title字段到video_generation_tasks表"""
    
    # 获取数据库文件路径
    db_path = "reddit_story_generator.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='video_generation_tasks'
        """)
        
        if not cursor.fetchone():
            print("❌ video_generation_tasks表不存在")
            return False
        
        # 检查custom_title字段是否已存在
        cursor.execute("PRAGMA table_info(video_generation_tasks)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'custom_title' in columns:
            print("✅ custom_title字段已存在，无需添加")
            return True
        
        # 添加custom_title字段
        print("🔄 正在添加custom_title字段...")
        cursor.execute("""
            ALTER TABLE video_generation_tasks 
            ADD COLUMN custom_title TEXT
        """)
        
        # 提交更改
        conn.commit()
        print("✅ custom_title字段添加成功")
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(video_generation_tasks)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'custom_title' in columns:
            print("✅ 字段验证成功")
            return True
        else:
            print("❌ 字段验证失败")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🚀 开始添加custom_title字段...")
    
    if add_custom_title_column():
        print("🎉 字段添加完成！")
        sys.exit(0)
    else:
        print("💥 字段添加失败！")
        sys.exit(1)
