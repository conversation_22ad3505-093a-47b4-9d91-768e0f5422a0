#!/usr/bin/env python3
"""
测试模板导入修复功能
"""

import requests
import json
from pathlib import Path

def test_template_import():
    """测试模板导入功能"""
    print("🧪 开始测试模板导入修复...")
    
    backend_url = "http://localhost:8000"
    template_file = Path("reddit-template/social_post_template.html")
    
    if not template_file.exists():
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    # 准备导入数据
    with open(template_file, 'r', encoding='utf-8') as f:
        file_content = f.read()
    
    # 检查模板变量是否保持完整
    variables = []
    import re
    pattern = r'\{\{(\w+)\}\}'
    variables = re.findall(pattern, file_content)
    print(f"📝 发现模板变量: {variables}")
    
    # 准备请求数据
    files = {
        'file': ('social_post_template.html', file_content, 'text/html')
    }
    data = {
        'name': 'Reddit优化封面模板-测试导入',
        'description': '测试修复后的导入功能',
        'category': '社交媒体'
    }
    
    try:
        # 测试导入API
        print(f"🚀 发送导入请求到: {backend_url}/api/cover-templates/import-html")
        response = requests.post(
            f"{backend_url}/api/cover-templates/import-html",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 导入成功!")
            print(f"📊 响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 检查响应格式
            if 'success' in result and result['success']:
                print("✅ API响应格式正确 (success: true)")
                
                if 'data' in result:
                    template_data = result['data']
                    template_id = template_data.get('id')
                    
                    if template_id:
                        print(f"🆔 模板ID: {template_id}")
                        
                        # 测试渲染功能
                        print("🎨 测试模板渲染...")
                        render_data = {
                            'avatar': 'https://example.com/avatar.jpg',
                            'account_name': '测试用户',
                            'title': '这是一个测试标题'
                        }
                        
                        render_response = requests.post(
                            f"{backend_url}/api/cover-templates/{template_id}/render",
                            json=render_data,
                            timeout=30
                        )
                        
                        if render_response.status_code == 200:
                            render_result = render_response.json()
                            if render_result.get('success'):
                                rendered_html = render_result['data']['rendered_html']
                                
                                # 检查变量是否被正确替换
                                for var_name, var_value in render_data.items():
                                    if f"{{{{{var_name}}}}}" in rendered_html:
                                        print(f"❌ 变量 {{{{{var_name}}}}} 未被替换")
                                    else:
                                        print(f"✅ 变量 {{{{{var_name}}}}} 已正确替换为: {var_value}")
                                
                                # 检查未使用的变量是否保持原样
                                remaining_vars = re.findall(pattern, rendered_html)
                                if remaining_vars:
                                    print(f"📝 剩余未替换变量: {remaining_vars}")
                                
                                print("✅ 模板渲染测试通过")
                                return True
                            else:
                                print(f"❌ 渲染失败: {render_result.get('message')}")
                        else:
                            print(f"❌ 渲染请求失败: {render_response.status_code}")
                            print(f"响应内容: {render_response.text}")
                    
                    return True
                else:
                    print("❌ 响应中缺少data字段")
            else:
                print("❌ API响应格式错误 (success != true)")
                print(f"错误信息: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 导入失败: {response.status_code}")
            print(f"响应内容: {response.text}")
        
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保后端服务正在运行 (python backend/main.py)")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_frontend_api_paths():
    """测试前端API路径修复"""
    print("\n🔗 测试前端API路径修复...")
    
    frontend_api_file = Path("frontend/src/lib/api/coverTemplates.ts")
    if not frontend_api_file.exists():
        print(f"❌ 前端API文件不存在: {frontend_api_file}")
        return False
    
    with open(frontend_api_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有尾部斜杠
    problematic_paths = [
        '/api/cover-templates/import-html/',
        '/api/cover-templates/stats/',
        '/api/cover-templates/variables/',
        '/api/cover-templates/thumbnail/${templateId}/',
        '/api/cover-templates/preview/${templateId}/'
    ]
    
    issues_found = []
    for path in problematic_paths:
        if path in content:
            issues_found.append(path)
    
    if issues_found:
        print(f"❌ 发现问题路径: {issues_found}")
        return False
    else:
        print("✅ 前端API路径已修复，无尾部斜杠问题")
        return True

if __name__ == "__main__":
    print("🔧 模板导入修复功能测试\n")
    
    # 测试前端路径修复
    frontend_ok = test_frontend_api_paths()
    
    # 测试后端导入功能
    backend_ok = test_template_import()
    
    print(f"\n📋 测试结果总结:")
    print(f"  前端API路径修复: {'✅ 通过' if frontend_ok else '❌ 失败'}")
    print(f"  后端导入功能: {'✅ 通过' if backend_ok else '❌ 失败'}")
    
    if frontend_ok and backend_ok:
        print("\n🎉 所有测试通过！模板导入功能已修复")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题")
