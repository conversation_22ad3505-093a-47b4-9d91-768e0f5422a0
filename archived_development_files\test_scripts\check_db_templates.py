#!/usr/bin/env python3
"""
检查数据库中是否有封面模板数据
"""

import sqlite3
import os
import json

def check_database():
    print("=== 检查数据库中的封面模板数据 ===\n")
    
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), "backend", "reddit_story_generator.db")
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cover_templates';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("cover_templates 表不存在")
            
            # 查看所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"数据库中存在的表: {[table[0] for table in tables]}")
            return
        
        print("cover_templates 表存在")
        
        # 查看表结构
        cursor.execute("PRAGMA table_info(cover_templates);")
        columns = cursor.fetchall()
        print(f"表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        print()
        
        # 查询模板数量
        cursor.execute("SELECT COUNT(*) FROM cover_templates;")
        count = cursor.fetchone()[0]
        print(f"模板总数: {count}")
        
        if count > 0:
            # 查询所有模板
            cursor.execute("SELECT * FROM cover_templates;")
            templates = cursor.fetchall()
            
            print("\n所有模板:")
            for i, template in enumerate(templates, 1):
                print(f"  模板 {i}:")
                for j, col in enumerate(columns):
                    col_name = col[1]
                    value = template[j]
                    if col_name in ['elements', 'background', 'variables', 'tags'] and value:
                        try:
                            # 尝试解析JSON
                            parsed_value = json.loads(value) if isinstance(value, str) else value
                            print(f"    {col_name}: {json.dumps(parsed_value, ensure_ascii=False, indent=6)}")
                        except:
                            print(f"    {col_name}: {value}")
                    else:
                        print(f"    {col_name}: {value}")
                print()
        else:
            print("数据库中没有模板数据")
            print("\n让我创建一些测试数据...")
            
            # 创建测试模板
            test_template = {
                'id': 'test-template-1',
                'name': '测试模板1',
                'preview_path': 'covers/preview_test1.png',
                'template_path': 'covers/template_test1.json',
                'variables': json.dumps([]),
                'is_built_in': False,
                'description': '用于测试的模板',
                'category': '测试',
                'tags': json.dumps(['测试', '调试']),
                'usage_count': 0,
                'width': 1920,
                'height': 1080,
                'format': 'png',
                'elements': json.dumps([
                    {
                        'id': 'text1',
                        'type': 'text',
                        'x': 100,
                        'y': 100,
                        'width': 200,
                        'height': 50,
                        'content': '测试文本',
                        'fontSize': 24,
                        'color': '#000000'
                    }
                ]),
                'background': json.dumps({
                    'type': 'gradient',
                    'value': {
                        'direction': 'to bottom right',
                        'colors': ['#667eea', '#764ba2']
                    }
                })
            }
            
            # 插入测试数据
            placeholders = ', '.join(['?' for _ in test_template.values()])
            column_names = ', '.join(test_template.keys())
            
            cursor.execute(f"INSERT INTO cover_templates ({column_names}) VALUES ({placeholders})", 
                         list(test_template.values()))
            
            conn.commit()
            print("已创建测试模板")
            
            # 再次查询确认
            cursor.execute("SELECT COUNT(*) FROM cover_templates;")
            new_count = cursor.fetchone()[0]
            print(f"现在模板总数: {new_count}")
            
    except Exception as e:
        print(f"检查数据库时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_database()
