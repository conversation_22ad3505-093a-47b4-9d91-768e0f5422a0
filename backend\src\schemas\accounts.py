"""
账号管理数据模型
用于管理多平台账号信息，支持个性化视频生成
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum


class PlatformType(str, Enum):
    """平台类型枚举"""
    REDDIT = "reddit"
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"
    BILIBILI = "bilibili"
    INSTAGRAM = "instagram"
    TWITTER = "twitter"


class AccountStatus(str, Enum):
    """账号状态枚举"""
    UNUSED = "unused"      # 未使用
    USED = "used"         # 已使用
    DISABLED = "disabled"  # 已禁用


class AccountCreate(BaseModel):
    """创建账号请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="账号名称")
    description: Optional[str] = Field(None, max_length=500, description="账号描述")
    platform: PlatformType = Field(..., description="所属平台")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    
    # 个性化设置
    brand_color: Optional[str] = Field("#2563eb", description="品牌主色调")
    font_style: Optional[str] = Field("default", description="字体风格")
    content_style: Optional[str] = Field("formal", description="内容风格")
    
    # 平台特定设置
    platform_settings: Optional[dict] = Field(default_factory=dict, description="平台特定配置")


class AccountUpdate(BaseModel):
    """更新账号请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    platform: Optional[PlatformType] = None
    avatar_url: Optional[str] = None
    status: Optional[AccountStatus] = None
    brand_color: Optional[str] = None
    font_style: Optional[str] = None
    content_style: Optional[str] = None
    platform_settings: Optional[dict] = None


class AccountResponse(BaseModel):
    """账号响应模型"""
    id: int
    name: str
    description: Optional[str]
    platform: PlatformType
    status: AccountStatus
    avatar_url: Optional[str]
    
    # 个性化设置
    brand_color: str
    font_style: str
    content_style: str
    platform_settings: dict
    
    # 统计信息
    usage_count: int = 0
    created_at: datetime
    updated_at: datetime
    last_used_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class AccountList(BaseModel):
    """账号列表响应模型"""
    accounts: List[AccountResponse]
    total: int
    page: int
    page_size: int


class AccountStats(BaseModel):
    """账号统计信息"""
    total_accounts: int
    by_platform: dict[PlatformType, int]
    by_status: dict[AccountStatus, int]
    most_used: Optional[AccountResponse]
    recently_created: List[AccountResponse]
