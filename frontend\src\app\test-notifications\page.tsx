/**
 * 通知功能测试页面
 * 用于测试各种类型的通知弹窗
 */

'use client'

import React from 'react'
import { useNotificationStore } from '../../store/notificationStore'

export default function NotificationTestPage() {
  const { addNotification } = useNotificationStore()

  const testSuccess = () => {
    addNotification({
      type: 'success',
      title: '操作成功',
      message: '这是一条成功通知，3秒后自动消失',
      duration: 3000
    })
  }

  const testError = () => {
    addNotification({
      type: 'error',
      title: '操作失败',
      message: '这是一条错误通知，5秒后自动消失',
      duration: 5000
    })
  }

  const testWarning = () => {
    addNotification({
      type: 'warning',
      title: '警告',
      message: '这是一条警告通知，手动关闭'
    })
  }

  const testInfo = () => {
    addNotification({
      type: 'info',
      title: '信息',
      message: '这是一条信息通知，手动关闭'
    })
  }

  return (
    <div style={{ padding: '2rem', maxWidth: '600px', margin: '0 auto' }}>
      <h1>通知功能测试</h1>
      <p>点击以下按钮测试不同类型的通知弹窗：</p>
      
      <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', marginTop: '2rem' }}>
        <button 
          onClick={testSuccess}
          style={{
            background: '#10b981',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.5rem',
            cursor: 'pointer'
          }}
        >
          测试成功通知
        </button>
        
        <button 
          onClick={testError}
          style={{
            background: '#ef4444',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.5rem',
            cursor: 'pointer'
          }}
        >
          测试错误通知
        </button>
        
        <button 
          onClick={testWarning}
          style={{
            background: '#f59e0b',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.5rem',
            cursor: 'pointer'
          }}
        >
          测试警告通知
        </button>
        
        <button 
          onClick={testInfo}
          style={{
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.5rem',
            cursor: 'pointer'
          }}
        >
          测试信息通知
        </button>
      </div>
      
      <div style={{ marginTop: '2rem' }}>
        <h2>说明：</h2>
        <ul>
          <li>成功和错误通知会自动消失</li>
          <li>警告和信息通知需要手动关闭</li>
          <li>通知出现在页面右上角</li>
          <li>可以同时显示多个通知</li>
        </ul>
      </div>
    </div>
  )
}
