#!/usr/bin/env python3
"""
验证前端API修复效果
"""
import requests
import json

API_BASE = "http://localhost:8000/api"

def test_frontend_fixes():
    print("🔧 测试前端API修复效果")
    print("=" * 50)
    
    # 1. 测试素材分类 - material_count字段
    print("\n1️⃣ 测试素材分类API (material_count字段)")
    try:
        response = requests.get(f"{API_BASE}/video-categories/")
        if response.status_code == 200:
            data = response.json()
            categories = data.get('data', data)
            if categories:
                first_cat = categories[0]
                print(f"   ✅ 分类名称: {first_cat.get('name')}")
                print(f"   ✅ 素材个数: {first_cat.get('material_count')}")
                print(f"   🎯 前端应显示: {first_cat.get('name')} ({first_cat.get('material_count')}个素材)")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 2. 测试提示词分组
    print("\n2️⃣ 测试提示词分组")
    try:
        response = requests.get(f"{API_BASE}/prompts/")
        if response.status_code == 200:
            data = response.json()
            prompts = data.get('data', data)
            if prompts:
                categories = set(p.get('category') for p in prompts)
                print(f"   ✅ 真实分组: {sorted(list(categories))}")
                print(f"   🎯 前端下拉框应显示这些分组")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 3. 测试封面模板
    print("\n3️⃣ 测试封面模板API")
    try:
        response = requests.get(f"{API_BASE}/cover-templates/")
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and 'templates' in data['data']:
                templates = data['data']['templates']
                total = data['data']['total']
                print(f"   ✅ 模板总数: {total}")
                if templates:
                    first_template = templates[0]
                    print(f"   ✅ 首个模板: id={first_template.get('id')}, name={first_template.get('name')}")
                    print(f"   🎯 前端应显示: {len(templates)} 个模板选项")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 4. 测试系统设置 - TTS配置
    print("\n4️⃣ 测试系统设置API (TTS配置)")
    try:
        response = requests.get(f"{API_BASE}/settings/")
        if response.status_code == 200:
            data = response.json()
            settings = data.get('data', data)
            if 'tts' in settings:
                tts_config = settings['tts']
                print(f"   ✅ TTS提供商: {tts_config.get('provider')}")
                print(f"   ✅ 当前音色: {tts_config.get('voice')}")
                print(f"   🎯 前端应根据provider生成对应音色列表")
        else:
            print(f"   ⚠️  API返回状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    print("\n" + "=" * 50)
    print("📝 修复总结:")
    print("1. ✅ API路径已修复（添加斜杠避免重定向）")
    print("2. ✅ 封面模板API已适配{templates: [...], total: N}格式")
    print("3. ✅ 前端API调用已修复使用正确的apiService结构")
    print("4. ⚠️  需要检查settings/background-music等API是否有数据")
    print("5. 🎯 建议测试前端页面，确认所有下拉框都有数据")

if __name__ == "__main__":
    test_frontend_fixes()
