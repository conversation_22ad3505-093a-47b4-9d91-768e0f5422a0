'use client';

import { useState, useEffect } from 'react';
import { 
  DocumentArrowUpIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  TrashIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { importHtmlTemplate, getAllTemplates, renderTemplate, getTemplateVariables, deleteTemplate } from '@/lib/api/coverTemplates';

// 模板接口
interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  templateType: 'html' | 'canvas';
  variables: string[];
  hasVariables: boolean;
  variableCount: number;
  createdAt: string;
  templatePath: string;
}

// 分类常量
const CATEGORIES = ['全部', '社交媒体', '科技', '经典', '现代', '简约', '其他'];

export default function CoverTemplatesPage() {
  // 状态管理
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  
  // 模态框状态
  const [showImportModal, setShowImportModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  
  // 导入状态
  const [isImporting, setIsImporting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importForm, setImportForm] = useState({
    name: '',
    description: '',
    category: '社交媒体'
  });
  
  // 预览状态
  const [previewHtml, setPreviewHtml] = useState('');
  const [templateVariables, setTemplateVariables] = useState<string[]>([]);
  const [variableValues, setVariableValues] = useState<Record<string, string>>({});

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    htmlTemplates: 0,
    withVariables: 0,
    categories: 0
  });

  // 获取模板列表
  const fetchTemplates = async () => {
    setIsLoading(true);
    try {
      const response = await getAllTemplates();
      console.log('API响应:', response);
      
      if (response && response.success && response.data?.templates) {
        const templateList = response.data.templates.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || '',
          category: item.category || '其他',
          templateType: item.templateType || 'canvas',
          variables: item.variables || [],
          hasVariables: item.hasVariables || false,
          variableCount: item.variableCount || 0,
          createdAt: item.createdAt || '',
          templatePath: item.templatePath || ''
        }));
        setTemplates(templateList);
        updateStats(templateList);
      } else {
        console.error('API响应格式错误:', response?.message || '响应格式不正确');
        setTemplates([]);
        updateStats([]);
      }
    } catch (error) {
      console.error('获取模板列表失败:', error);
      setTemplates([]);
      updateStats([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 更新统计数据
  const updateStats = (templateList: Template[]) => {
    const stats = {
      total: templateList.length,
      htmlTemplates: templateList.filter(t => t.templateType === 'html').length,
      withVariables: templateList.filter(t => t.hasVariables).length,
      categories: new Set(templateList.map(t => t.category)).size
    };
    setStats(stats);
  };

  // 筛选模板
  useEffect(() => {
    let filtered = templates;
    
    // 按分类筛选
    if (selectedCategory !== '全部') {
      filtered = filtered.filter(t => t.category === selectedCategory);
    }
    
    // 按搜索词筛选
    if (searchTerm) {
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        t.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    setFilteredTemplates(filtered);
  }, [templates, selectedCategory, searchTerm]);

  // 页面加载时获取数据
  useEffect(() => {
    fetchTemplates();
  }, []);

  // 处理文件选择
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    if (file.type !== 'text/html') {
      alert('❌ 请选择HTML文件！\n\n支持的文件类型：.html');
      return;
    }

    try {
      // 读取文件内容并检查是否包含id="reddit-cover"元素
      const fileContent = await file.text();
      
      // 使用更严格的检查：确保包含id="reddit-cover"且不在注释中
      // 先移除HTML注释，再检查
      const contentWithoutComments = fileContent.replace(/<!--[\s\S]*?-->/g, '');
      const hasRedditCoverElement = /id\s*=\s*["']reddit-cover["']/.test(contentWithoutComments);
      
      if (!hasRedditCoverElement) {
        alert(`❌ HTML模板格式不符合要求！

文件：${file.name}

错误原因：
缺少必需的根元素 id="reddit-cover"

正确格式示例：
<div id="reddit-cover">
  <!-- 您的模板内容 -->
</div>

请修改您的HTML文件，确保包含带有 id="reddit-cover" 的根元素。`);
        // 清空文件选择
        event.target.value = '';
        return;
      }

      setSelectedFile(file);
      if (!importForm.name) {
        setImportForm(prev => ({
          ...prev,
          name: file.name.replace('.html', '')
        }));
      }
    } catch (error) {
      console.error('读取文件失败:', error);
      alert('❌ 读取文件失败！\n\n请确保文件没有损坏，然后重试。');
      event.target.value = '';
    }
  };

  // 导入HTML模板
  const handleImportTemplate = async () => {
    if (!selectedFile || !importForm.name) {
      alert('❌ 请选择文件并填写模板名称');
      return;
    }

    setIsImporting(true);
    try {
      // 再次检查文件内容（双重保险）
      const fileContent = await selectedFile.text();
      
      // 使用严格的正则表达式检查（排除注释）
      const contentWithoutComments = fileContent.replace(/<!--[\s\S]*?-->/g, '');
      const hasRedditCoverElement = /id\s*=\s*["']reddit-cover["']/.test(contentWithoutComments);
      
      if (!hasRedditCoverElement) {
        alert(`❌ HTML模板验证失败！

文件：${selectedFile.name}

错误：缺少必需的 id="reddit-cover" 元素

请修改您的HTML文件，确保包含带有 id="reddit-cover" 的根元素后重新选择文件。`);
        setIsImporting(false);
        return;
      }

      console.log('开始导入模板:', {
        name: importForm.name,
        description: importForm.description,
        category: importForm.category,
        fileSize: selectedFile.size,
        fileName: selectedFile.name
      });

      // 显示进度提示
      console.log('⏳ 正在处理模板文件，请耐心等待...');
      const startTime = Date.now();

      const response = await importHtmlTemplate(
        selectedFile,
        importForm.name,
        importForm.description,
        importForm.category
      );
      
      const endTime = Date.now();
      const processingTime = (endTime - startTime) / 1000;
      console.log(`✅ 模板处理完成，耗时: ${processingTime.toFixed(2)} 秒`);
      
      console.log('导入响应:', response);
      
      if (response && response.success) {
        alert(`✅ HTML模板导入成功！\n处理时间: ${processingTime.toFixed(2)} 秒`);
        setShowImportModal(false);
        setSelectedFile(null);
        setImportForm({ name: '', description: '', category: '社交媒体' });
        fetchTemplates(); // 刷新列表
      } else {
        console.error('导入失败，响应:', response);
        alert(`❌ 导入失败: ${response?.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('导入过程中发生错误:', error);
      
      // 检查是否是超时错误
      if (error instanceof Error && error.message.includes('超时')) {
        alert('⏰ 导入超时\n\n可能原因：\n• 模板文件较大\n• 包含较多远程图片\n• 网络连接较慢\n\n建议：\n• 减少模板中的远程图片\n• 检查网络连接\n• 稍后重试');
      } else if (error instanceof TypeError && error.message.includes('fetch')) {
        alert('❌ 网络连接失败，请检查后端服务是否正常运行');
      } else if (error instanceof Error) {
        alert(`❌ 导入失败: ${error.message}`);
      } else {
        alert('❌ 导入失败，请重试');
      }
    } finally {
      setIsImporting(false);
    }
  };

  // 预览模板
  const handlePreviewTemplate = async (template: Template) => {
    setSelectedTemplate(template);
    setShowPreviewModal(true);
    setPreviewHtml(''); // 清空之前的预览内容
    
    try {
      // 获取模板变量
      const varsResponse = await getTemplateVariables(template.id);
      if (varsResponse.success) {
        const variables = varsResponse.data.variables || [];
        setTemplateVariables(variables);
        
        // 初始化变量值
        const initialValues: Record<string, string> = {};
        variables.forEach((varName: string) => {
          switch (varName) {
            case 'avatar':
              initialValues[varName] = 'https://via.placeholder.com/50x50/667eea/ffffff?text=头像';
              break;
            case 'account_name':
              initialValues[varName] = '示例用户名';
              break;
            case 'title':
              initialValues[varName] = '这是一个示例标题';
              break;
            case 'description':
              initialValues[varName] = '这是一个示例描述内容';
              break;
            default:
              initialValues[varName] = `示例${varName}`;
          }
        });
        setVariableValues(initialValues);
        
        // 渲染模板
        try {
          const renderResponse = await renderTemplate(template.id, initialValues);
          if (renderResponse.success) {
            setPreviewHtml(renderResponse.data.rendered_html);
          } else {
            setPreviewHtml(`<div class="p-4 text-red-600">渲染失败: ${renderResponse.message}</div>`);
          }
        } catch (renderError) {
          console.error('渲染模板失败:', renderError);
          setPreviewHtml(`<div class="p-4 text-red-600">渲染失败: ${renderError instanceof Error ? renderError.message : '未知错误'}</div>`);
        }
      } else {
        setPreviewHtml(`<div class="p-4 text-red-600">获取模板变量失败: ${varsResponse.message}</div>`);
      }
    } catch (error) {
      console.error('预览模板失败:', error);
      setPreviewHtml(`<div class="p-4 text-red-600">预览失败: ${error instanceof Error ? error.message : '未知错误'}</div>`);
    }
  };

  // 更新预览
  const updatePreview = async () => {
    if (!selectedTemplate) return;
    
    try {
      const renderResponse = await renderTemplate(selectedTemplate.id, variableValues);
      if (renderResponse.success) {
        setPreviewHtml(renderResponse.data.rendered_html);
      } else {
        setPreviewHtml(`<div class="p-4 text-red-600">渲染失败: ${renderResponse.message}</div>`);
      }
    } catch (error) {
      console.error('更新预览失败:', error);
      setPreviewHtml(`<div class="p-4 text-red-600">更新预览失败: ${error instanceof Error ? error.message : '未知错误'}</div>`);
    }
  };

  // 删除模板
  const handleDeleteTemplate = async (template: Template) => {
    if (!confirm(`确定要删除模板"${template.name}"吗？`)) return;
    
    try {
      const response = await deleteTemplate(template.id);
      if (response.success) {
        alert('模板删除成功！');
        fetchTemplates();
      } else {
        alert(`删除失败: ${response.message}`);
      }
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败，请重试');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b">
        <div className="px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">封面模板管理</h1>
              <p className="mt-2 text-gray-600">创建和管理HTML封面模板，支持变量绑定和动态内容</p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={fetchTemplates}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50"
              >
                <svg className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                刷新
              </button>
              <button
                onClick={() => setShowImportModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <DocumentArrowUpIcon className="w-4 h-4" />
                导入HTML模板
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-blue-600 mb-1">{stats.total}</div>
            <div className="text-sm text-gray-600">总模板数</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-green-600 mb-1">{stats.htmlTemplates}</div>
            <div className="text-sm text-gray-600">HTML模板</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-orange-600 mb-1">{stats.withVariables}</div>
            <div className="text-sm text-gray-600">支持变量</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-purple-600 mb-1">{stats.categories}</div>
            <div className="text-sm text-gray-600">分类数量</div>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="p-6 border-b">
            <div className="flex flex-col md:flex-row gap-4">
              {/* 分类筛选 */}
              <div className="flex flex-wrap gap-2">
                {CATEGORIES.map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
              
              {/* 搜索框 */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索模板..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 模板列表 */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变量</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                      加载中...
                    </td>
                  </tr>
                ) : filteredTemplates.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                      {searchTerm || selectedCategory !== '全部' ? '未找到匹配的模板' : '暂无模板，请导入HTML模板'}
                    </td>
                  </tr>
                ) : (
                  filteredTemplates.map((template) => (
                    <tr key={template.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{template.name}</div>
                          <div className="text-sm text-gray-500">{template.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {template.category}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          template.templateType === 'html' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {template.templateType === 'html' ? 'HTML' : 'Canvas'}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        {template.hasVariables ? (
                          <div className="flex items-center gap-1">
                            <TagIcon className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-green-600">{template.variableCount} 个变量</span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">无变量</span>
                        )}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {template.createdAt}
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handlePreviewTemplate(template)}
                            className="p-1 text-blue-600 hover:text-blue-800"
                            title="预览"
                          >
                            <EyeIcon className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteTemplate(template)}
                            className="p-1 text-red-600 hover:text-red-800"
                            title="删除"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 导入模板模态框 */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">导入HTML模板</h3>
            
            <div className="space-y-4">
              {/* 文件选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择HTML文件 
                  <span className="text-red-500">*</span>
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  📋 要求：HTML文件必须包含 <code className="bg-gray-100 px-1 rounded">id="reddit-cover"</code> 的根元素
                </p>
                <input
                  type="file"
                  accept=".html"
                  onChange={handleFileSelect}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {selectedFile && (
                  <p className="mt-1 text-sm text-green-600">✅ 已选择: {selectedFile.name}</p>
                )}
              </div>
              
              {/* 模板名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">模板名称 *</label>
                <input
                  type="text"
                  value={importForm.name}
                  onChange={(e) => setImportForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入模板名称"
                />
              </div>
              
              {/* 描述 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">描述</label>
                <textarea
                  value={importForm.description}
                  onChange={(e) => setImportForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="请输入模板描述"
                />
              </div>
              
              {/* 分类 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">分类</label>
                <select
                  value={importForm.category}
                  onChange={(e) => setImportForm(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {CATEGORIES.slice(1).map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => {
                  setShowImportModal(false);
                  setSelectedFile(null);
                  setImportForm({ name: '', description: '', category: '社交媒体' });
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleImportTemplate}
                disabled={!selectedFile || !importForm.name || isImporting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isImporting ? '导入中...' : '导入'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 预览模态框 */}
      {showPreviewModal && selectedTemplate && (
        <div 
          className="modal-overlay-main-content bg-black bg-opacity-60"
          onClick={(e) => {
            // 点击背景关闭弹窗
            if (e.target === e.currentTarget) {
              setShowPreviewModal(false);
            }
          }}
        >
          <div 
            className="bg-white rounded-xl shadow-2xl overflow-hidden modal-content"
            style={{
              width: templateVariables.length > 0 ? '950px' : '640px', // 调整宽度：有变量时950px，无变量时640px
              height: '85vh',
              minHeight: '600px',
              maxHeight: '900px',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              margin: '20px', // 添加边距
              transform: 'translateZ(0)', // 启用硬件加速
              animation: 'modalFadeIn 0.2s ease-out'
            }}
            onClick={(e) => e.stopPropagation()} // 防止点击弹窗内容时关闭
          >
            {/* 头部 */}
            <div 
              className="px-6 py-4 border-b bg-white"
              style={{ 
                flexShrink: 0,
                height: '70px',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <div className="flex items-center justify-between w-full">
                <h3 className="text-lg font-medium text-gray-900">模板预览 - {selectedTemplate.name}</h3>
                <button
                  onClick={() => setShowPreviewModal(false)}
                  className="text-gray-400 hover:text-gray-600 p-1"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            
            {/* 内容区域 */}
            <div 
              style={{
                flex: 1,
                display: 'flex',
                minHeight: 0,
                height: 'calc(100% - 70px)'
              }}
            >
              {/* 左侧变量控制 */}
              {templateVariables.length > 0 && (
                <div 
                  className="border-r bg-gray-50"
                  style={{
                    width: '350px',
                    flexShrink: 0,
                    overflowY: 'auto',
                    height: '100%'
                  }}
                >
                  <div className="p-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-4">模板变量</h4>
                    <div className="space-y-4">
                      {templateVariables.map((varName) => (
                        <div key={varName}>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {varName === 'avatar' ? '头像URL' :
                             varName === 'account_name' ? '账号名称' :
                             varName === 'title' ? '标题' :
                             varName === 'description' ? '描述' : varName}
                          </label>
                          <input
                            type="text"
                            value={variableValues[varName] || ''}
                            onChange={(e) => {
                              setVariableValues(prev => ({
                                ...prev,
                                [varName]: e.target.value
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      ))}
                      <button
                        onClick={updatePreview}
                        className="w-full px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm mt-4"
                      >
                        更新预览
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {/* 右侧预览区域 */}
              <div 
                className="bg-gray-50"
                style={{
                  width: templateVariables.length > 0 ? '600px' : '640px', // 有变量时600px，无变量时使用全部宽度640px
                  flexShrink: 0,
                  padding: '20px',
                  height: '100%',
                  overflow: 'hidden',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {previewHtml ? (
                  <div 
                    className="bg-white rounded-lg border shadow-sm"
                    style={{
                      width: templateVariables.length > 0 ? '560px' : '600px', // 根据是否有变量调整宽度
                      height: 'calc(100% - 40px)', // 减去padding的高度
                      overflow: 'hidden',
                      position: 'relative'
                    }}
                  >
                    <iframe
                      srcDoc={previewHtml}
                      style={{
                        width: '100%',
                        height: '100%',
                        border: 'none',
                        background: 'white',
                        display: 'block'
                      }}
                      sandbox="allow-same-origin"
                      title="模板预览"
                    />
                  </div>
                ) : (
                  <div 
                    className="bg-white rounded-lg border shadow-sm text-gray-500"
                    style={{
                      width: templateVariables.length > 0 ? '560px' : '600px', // 根据是否有变量调整宽度
                      height: 'calc(100% - 40px)', // 减去padding的高度
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '16px'
                    }}
                  >
                    加载预览中...
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
