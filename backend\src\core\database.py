"""
数据库连接管理
"""

import logging
# 全局禁用SQLAlchemy日志 - 在导入之前设置
logging.getLogger('sqlalchemy').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.engine').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.pool').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.dialects').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.orm').setLevel(logging.CRITICAL)

from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker
from src.core.config import get_settings
from src.models import BaseModel
from src.models.resources import BackgroundMusic, VideoMaterial, Prompt, CoverTemplate
from src.models.accounts import Account
from loguru import logger

settings = get_settings()

# 创建数据库引擎 - 强制关闭echo
engine = create_engine(
    settings.database_url,
    echo=False,  # 强制关闭SQL输出
    echo_pool=False,  # 关闭连接池日志
    pool_pre_ping=True,
    pool_recycle=300,
    logging_name=None  # 禁用日志名称
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 元数据
metadata = MetaData()

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_session_maker():
    """获取会话工厂"""
    return SessionLocal

def get_db_session():
    """获取数据库会话 - 直接返回session对象"""
    return SessionLocal()

def init_db():
    """初始化数据库"""
    # 创建所有表
    BaseModel.metadata.create_all(bind=engine)
    logger.info("数据库初始化完成")
    
    # 创建默认设置记录
    from src.models.settings import Settings
    db = SessionLocal()
    try:
        # 检查是否已有设置记录
        existing_settings = db.query(Settings).first()
        if not existing_settings:
            logger.info("未找到现有设置记录，正在创建默认设置...")
            # 创建默认设置
            default_settings = Settings()
            db.add(default_settings)
            db.commit()
            logger.info("默认设置记录创建成功并已提交。")
        else:
            logger.info("已存在设置记录，跳过创建默认设置。")
        
        # 创建一些内置资源示例
        _create_builtin_resources(db)
        
    except Exception as e:
        logger.error(f"创建默认设置失败: {e}")
        db.rollback()
    finally:
        db.close()

def _create_builtin_resources(db):
    """创建内置资源示例"""
    import uuid
    
    # 创建内置提示词
    existing_prompts = db.query(Prompt).filter(Prompt.is_built_in == True).count()
    if existing_prompts == 0:
        builtin_prompts = [
            # Prompt(
            #     id=str(uuid.uuid4()),
            #     name="Reddit故事改写",
            #     content="请将以下Reddit故事改写为更适合视频narration的版本，保持故事的核心情节和情感，但使语言更加生动和适合朗读:\n\n{story_content}",
            #     category="rewrite",
            #     variables=["story_content"],
            #     is_built_in=True,
            #     description="将Reddit原始故事改写为适合视频旁白的版本"
            # ),
            # Prompt(
            #     id=str(uuid.uuid4()),
            #     name="故事标题生成",
            #     content="基于以下故事内容，生成一个吸引人的视频标题:\n\n{story_content}\n\n要求:\n1. 标题要简洁有力\n2. 包含情感冲突或悬念\n3. 适合中文观众\n4. 字数控制在20字以内",
            #     category="title",
            #     variables=["story_content"],
            #     is_built_in=True,
            #     description="为故事生成吸引人的视频标题"
            # ),
            # Prompt(
            #     id=str(uuid.uuid4()),
            #     name="视频描述生成",
            #     content="为以下故事视频生成一个详细的描述:\n\n标题: {title}\n故事内容: {story_content}\n\n请包含:\n1. 故事简介\n2. 关键情节提示\n3. 相关标签建议",
            #     category="description",
            #     variables=["title", "story_content"],
            #     is_built_in=True,
            #     description="生成视频的详细描述和标签"
            # )
        ]
        
        for prompt in builtin_prompts:
            db.add(prompt)
        
        logger.info("创建内置提示词模板")
    
    db.commit()
