/**
 * 背景音乐管理页面 - 使用Tailwind CSS样式
 */

'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { useMusicStore, type MusicFile } from '@/store/musicStore'
import { useMusicApi } from '@/hooks/useApi'
import { useNotificationStore } from '@/store/notificationStore'

export default function MusicPage() {
  const [isDragging, setIsDragging] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentMusic, setCurrentMusic] = useState<MusicFile | null>(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [showCategoryManager, setShowCategoryManager] = useState(false)
  const [availableCategoriesRaw, setAvailableCategoriesRaw] = useState<string[]>(() => {
    // 确保初始状态始终是数组
    return ['general']
  })
  
  // 新增：保存全量音乐数据用于统计计算
  const [allMusicFiles, setAllMusicFiles] = useState<MusicFile[]>([])
  
  // 安全的 setAvailableCategories 包装函数，确保总是设置数组
  const setAvailableCategories = useCallback((value: string[] | ((prev: string[]) => string[])) => {
    if (typeof value === 'function') {
      setAvailableCategoriesRaw(prev => {
        const result = value(Array.isArray(prev) ? prev : ['general'])
        return Array.isArray(result) ? result : ['general']
      })
    } else {
      setAvailableCategoriesRaw(Array.isArray(value) ? value : ['general'])
    }
  }, [])
  
  const availableCategories = availableCategoriesRaw

  const { addNotification } = useNotificationStore()
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  
  const { 
    musicFiles, 
    currentPage, 
    pageSize, 
    total,
    setMusicFiles, 
    addMusicFile, 
    removeMusicFile, 
    setLoading,
    setError,
    setPage,
    setTotal,
    getTotalCount,
    getTotalSize
  } = useMusicStore()
  
  const { 
    uploadMusic, 
    getMusicList, 
    getCategoryList,
    addCategory,
    deleteCategory,
    deleteMusic, 
    getMusicPlayUrl,
    loading,
    error: apiError 
  } = useMusicApi()
  
  // 调试：检查musicStore的状态
  console.log('🏪 MusicStore State:', {
    musicFiles: musicFiles,
    musicFilesLength: musicFiles.length,
    currentPage,
    total,
    loading
  })

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化音频时长
  const formatDuration = (duration: number | string): string => {
    if (typeof duration === 'string') return duration
    const minutes = Math.floor(duration / 60)
    const seconds = Math.floor(duration % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // 加载全量音乐列表 - 用于统计计算
  const loadAllMusicForStats = useCallback(async () => {
    try {
      const response = await getMusicList({
        page: 1,
        pageSize: 1000, // 降低pageSize，避免后端拒绝请求
        // 不传递分类和搜索参数，获取全量数据
      })
      
      if (response.success && response.data) {
        const data = response.data as any
        const actualData = data.data || data
        const items = actualData.items || []
        console.log('🏪 All music for stats loaded:', items.length, items) // 调试日志
        return items
      }
      console.warn('🏪 Failed to load all music for stats:', response)
      return []
    } catch (err) {
      console.error('Failed to load all music for stats:', err)
      return []
    }
  }, [getMusicList])

  // 加载音乐列表（用于显示）
  const loadMusicList = useCallback(async () => {
    setLoading(true)
    try {
      const response = await getMusicList({
        page: currentPage,
        pageSize,
        category: selectedCategory === 'all' ? undefined : selectedCategory,
        search: searchTerm || undefined
      })
      
      console.log('🎵 API Response:', response) // 调试：查看完整响应
      console.log('🎵 Response.success:', response.success) // 调试：查看success字段
      console.log('🎵 Response.data:', response.data) // 调试：查看data字段
      
      if (response.success && response.data) {
        const data = response.data as any
        console.log('🎵 Data type:', typeof data) // 调试：查看data类型
        console.log('🎵 Data.data:', data.data) // 调试：查看嵌套的data字段
        console.log('🎵 Data.items:', data.items) // 调试：查看items字段
        
        // 修复：正确访问嵌套的data结构
        const actualData = data.data || data
        const items = actualData.items || []
        console.log('🎵 Music Items:', items) // 调试：查看音乐数据
        console.log('🎵 Items Count:', items.length) // 调试：查看数据数量
        setMusicFiles(items)
        setTotal(actualData.total || 0)
        setError(null)
      }
    } catch (err) {
      setError('获取音乐列表失败')
      console.error('Failed to load music list:', err)
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, selectedCategory, searchTerm, getMusicList, setMusicFiles, setTotal, setLoading, setError])

  // 加载分类列表
  const loadCategoryList = useCallback(async () => {
    try {
      const response = await getCategoryList()
      console.log('分类列表响应:', response) // 调试日志
      
      if (response?.success && response?.data) {
        // 处理嵌套的data结构：response.data.data 才是真正的分类数组
        const responseData = response.data as any
        console.log('分类响应数据:', responseData) // 调试日志
        
        // 正确解析：从嵌套结构中获取分类数据
        const categories = responseData.data || responseData || []
        console.log('解析后的分类数据:', categories) // 调试日志
        
        // 确保数据是数组格式
        const finalCategories = Array.isArray(categories) ? categories : ['general']
        
        console.log('最终分类列表:', finalCategories) // 调试日志
        setAvailableCategories(finalCategories)
      } else {
        console.warn('分类列表响应格式异常:', response)
        setAvailableCategories(['general'])
      }
    } catch (err) {
      console.error('Failed to load category list:', err)
      // 如果加载失败，使用默认分类
      setAvailableCategories(['general'])
    }
  }, [getCategoryList])

  // 添加新分类
  const handleAddCategory = useCallback(async (categoryName: string) => {
    try {
      const response = await addCategory(categoryName)
      if (response.success) {
        // 重新加载分类列表
        await loadCategoryList()
        addNotification({
          type: 'success',
          title: '分类已添加',
          message: `分类 "${categoryName}" 已成功添加`
        })
        return true
      } else {
        addNotification({
          type: 'error',
          title: '添加失败',
          message: response.error || '添加分类时发生错误'
        })
        return false
      }
    } catch (err: any) {
      addNotification({
        type: 'error',
        title: '添加失败',
        message: err.message || '添加分类时发生错误'
      })
      return false
    }
  }, [addCategory, loadCategoryList, addNotification])

  // 删除分类
  const handleDeleteCategory = useCallback(async (categoryName: string) => {
    try {
      const response = await deleteCategory(categoryName)
      if (response.success) {
        // 重新加载分类列表
        await loadCategoryList()
        // 如果删除的是当前选中的分类，切换到全部
        if (selectedCategory === categoryName) {
          setSelectedCategory('all')
        }
        addNotification({
          type: 'success',
          title: '分类已删除',
          message: `分类 "${categoryName}" 已成功删除`
        })
        return true
      } else {
        addNotification({
          type: 'error',
          title: '删除失败',
          message: response.error || '删除分类时发生错误'
        })
        return false
      }
    } catch (err: any) {
      addNotification({
        type: 'error',
        title: '删除失败',
        message: err.message || '删除分类时发生错误'
      })
      return false
    }
  }, [deleteCategory, loadCategoryList, selectedCategory, addNotification])

  // 页面加载时获取音乐列表和分类列表
  useEffect(() => {
    const loadInitialData = async () => {
      // 直接加载数据和分类，初始状态是全部数据
      await Promise.all([loadMusicList(), loadCategoryList()])
    }
    
    loadInitialData()
  }, [loadMusicList, loadCategoryList])
  
  // 当 musicFiles 更新且为全部分类时，同步更新 allMusicFiles 用于统计
  useEffect(() => {
    if (selectedCategory === 'all' && musicFiles.length > 0) {
      // 只有在显示"全部"分类且有数据时，musicFiles 才是全量数据
      setAllMusicFiles(musicFiles)
      console.log('🏪 Updated all music files for stats:', musicFiles.length, musicFiles)
    }
  }, [musicFiles, selectedCategory])

  // 当分类或搜索条件变化时重新加载
  useEffect(() => {
    // 重置到第一页，然后加载
    if (currentPage !== 1) {
      setPage(1)
    } else {
      loadMusicList()
    }
  }, [selectedCategory, searchTerm, currentPage, setPage, loadMusicList])

  // 处理文件上传
  const handleFileUpload = useCallback(async (files: FileList) => {
    const validExtensions = ['.mp3', '.wav', '.m4a', '.flac']
    setLoading(true)

    try {
      let successCount = 0
      let failCount = 0
      
      // 确定上传分类
      const uploadCategory = selectedCategory === 'all' ? 'general' : selectedCategory

      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const extension = '.' + file.name.split('.').pop()?.toLowerCase()
        
        if (validExtensions.includes(extension)) {
          try {
            const response = await uploadMusic(file, uploadCategory)
            
            if (response.success) {
              successCount++
            } else {
              failCount++
              console.error(`上传 ${file.name} 失败:`, response.error)
            }
          } catch (err) {
            failCount++
            console.error(`上传 ${file.name} 失败:`, err)
          }
        } else {
          failCount++
          console.warn(`跳过不支持的文件格式: ${file.name}`)
        }
      }

      // 上传完成后重新加载列表和分类  
      await Promise.all([loadMusicList(), loadCategoryList()])
      
      // 显示结果提示
      if (successCount > 0) {
        setError(null)
        addNotification({
          type: 'success',
          title: '上传成功',
          message: `成功上传 ${successCount} 个文件到分类 "${uploadCategory}"${failCount > 0 ? `，失败 ${failCount} 个` : ''}`
        })
      } else if (failCount > 0) {
        addNotification({
          type: 'error', 
          title: '上传失败',
          message: `上传失败，共 ${failCount} 个文件`
        })
      }
      
    } catch (err) {
      setError('音乐上传失败')
      console.error('Failed to upload music:', err)
    } finally {
      setLoading(false)
    }
  }, [selectedCategory, uploadMusic, loadMusicList, loadCategoryList, setLoading, setError, addNotification])

  // 拖拽事件处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFileUpload(files)
    }
  }, [handleFileUpload])

  // 点击上传
  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  // 文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      handleFileUpload(files)
    }
  }

  // 播放音乐
  const handlePlayMusic = (music: MusicFile) => {
    if (currentMusic?.id === music.id && isPlaying) {
      audioRef.current?.pause()
      setIsPlaying(false)
    } else {
      if (audioRef.current) {
        const musicUrl = getMusicPlayUrl(music.id.toString())
        audioRef.current.src = musicUrl
        audioRef.current.play()
        setIsPlaying(true)
        setCurrentMusic(music)
      }
    }
  }

  // 删除音乐
  const handleDeleteMusic = async (music: MusicFile) => {
    if (confirm(`确定要删除音乐 "${music.name}" 吗？`)) {
      try {
        setLoading(true)
        const response = await deleteMusic(music.id.toString())
        if (response.success) {
          await loadMusicList()
          
          // 如果正在播放的是被删除的音乐，停止播放
          if (currentMusic?.id === music.id) {
            audioRef.current?.pause()
            setIsPlaying(false)
            setCurrentMusic(null)
          }
          
          addNotification({
            type: 'success',
            title: '删除成功',
            message: `音乐 "${music.name}" 已删除`
          })
          setError(null)
        } else {
          addNotification({
            type: 'error',
            title: '删除失败',
            message: response.error || '删除音乐时发生错误'
          })
        }
      } catch (err: any) {
        addNotification({
          type: 'error',
          title: '删除失败',
          message: err.message || '删除音乐时发生错误'
        })
        console.error('Failed to delete music:', err)
      } finally {
        setLoading(false)
      }
    }
  }

  // 音频播放器事件
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const updateTime = () => setCurrentTime(audio.currentTime)
    const updateDuration = () => setDuration(audio.duration)
    const handleEnded = () => {
      setIsPlaying(false)
      setCurrentTime(0)
    }

    audio.addEventListener('timeupdate', updateTime)
    audio.addEventListener('loadedmetadata', updateDuration)
    audio.addEventListener('ended', handleEnded)

    return () => {
      audio.removeEventListener('timeupdate', updateTime)
      audio.removeEventListener('loadedmetadata', updateDuration)
      audio.removeEventListener('ended', handleEnded)
    }
  }, [])

  // 过滤音乐文件
  const filteredMusic = musicFiles.filter((music: MusicFile) => {
    // 按分类过滤
    if (selectedCategory !== 'all' && music.category !== selectedCategory) {
      return false
    }
    
    // 按搜索词过滤
    if (searchTerm && !music.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false
    }
    
    return true
  })
  
  console.log('🎯 Total music files:', musicFiles.length)
  console.log('🎯 All music files (for stats):', allMusicFiles.length)
  console.log('🎯 Filtered music files:', filteredMusic.length)
  console.log('🎯 Selected category:', selectedCategory)
  console.log('🎯 Available categories:', availableCategories)

  // 获取分类统计 - 基于全量数据，不受当前过滤影响
  const getCategoryStats = useCallback(() => {
    return allMusicFiles.reduce((stats, file) => {
      const category = file.category || 'general'
      stats[category] = (stats[category] || 0) + 1
      return stats
    }, {} as Record<string, number>)
  }, [allMusicFiles])

  const categoryStats = getCategoryStats()
  
  // 获取全部音乐文件的总数 - 基于全量数据，不受过滤影响
  const totalMusicCount = allMusicFiles.length

  return (
    <div className="max-w-7xl mx-auto px-6 py-6">
      {/* 页面头部 */}
      <div className="flex justify-between items-end mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">背景音乐管理</h1>
          <p className="text-gray-600">管理视频生成所需的背景音乐文件，支持多种音频格式</p>
        </div>
        <div className="flex gap-3">
          {/* <button className="bg-white hover:bg-gray-50 text-gray-700 border border-gray-200 px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2">
            <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd"/>
            </svg>
            导出音乐
          </button> */}
          {/* <button
            onClick={handleUploadClick}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2"
          >
            <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd"/>
            </svg>
            批量导入
          </button> */}
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-blue-500 mb-1">{totalMusicCount}</div>
          <div className="text-sm text-gray-600">音乐文件总数</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-green-500 mb-1">{getTotalSize()}</div>
          <div className="text-sm text-gray-600">总存储大小</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-purple-500 mb-1">{Array.isArray(availableCategories) ? availableCategories.length : 0}</div>
          <div className="text-sm text-gray-600">音乐分类</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
          <div className="text-2xl font-bold text-orange-500 mb-1">{filteredMusic.length}</div>
          <div className="text-sm text-gray-600">筛选结果</div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6 flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索音乐文件..."
              className="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <svg className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"/>
            </svg>
          </div>
          
          <div className="flex gap-2">
            <button 
              className={`px-3 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === 'all'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              onClick={() => setSelectedCategory('all')}
            >
              全部 ({totalMusicCount})
            </button>
            {Array.isArray(availableCategories) && availableCategories.map((category) => (
              <button 
                key={category}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                onClick={() => setSelectedCategory(category)}
              >
                {category} ({categoryStats[category] || 0})
              </button>
            ))}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button 
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            onClick={() => setShowCategoryManager(true)}
          >
            分类管理
          </button>
        </div>
      </div>

      {/* 拖拽上传区域 */}
      <div 
        className={`border-2 border-dashed rounded-lg p-8 text-center mb-6 cursor-pointer transition-colors ${
          isDragging 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 bg-gray-50 hover:border-blue-500 hover:bg-blue-50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleUploadClick}
      >
        <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd"/>
        </svg>
        <div className="text-lg font-medium text-gray-900 mb-2">拖拽音乐文件到这里，或点击选择文件</div>
        <div className="text-sm text-gray-500 mb-3">支持 MP3、WAV、M4A、FLAC 格式，可批量上传</div>
        
        {/* 显示当前选择的分类 */}
        <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">
          <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zM12 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1V4zM12 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-3z" clipRule="evenodd"/>
          </svg>
          上传到分类: {selectedCategory === 'all' ? 'general' : selectedCategory}
        </div>
      </div>

      {/* 音乐列表 */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      ) : filteredMusic.length === 0 ? (
        <div className="text-center py-16 text-gray-500">
          <div className="w-16 h-16 mx-auto mb-4 text-gray-300">
            <svg className="w-full h-full" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zM12 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1V4zM12 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-3z" clipRule="evenodd"/>
            </svg>
          </div>
          <div className="text-lg font-medium mb-2">没有找到匹配的音乐文件</div>
          <div className="text-gray-400">
            尝试调整搜索条件或上传新的音乐文件
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMusic.map((music: MusicFile) => (
            <div key={music.id} className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12a7.971 7.971 0 00-1.343-4.243 1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">{music.name}</div>
                  <div className="text-xs text-gray-500">
                    {music.format} • {typeof music.size === 'string' ? music.size : formatFileSize(music.size || 0)} • {formatDuration(typeof music.duration === 'string' ? music.duration : music.duration || 0)}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  {music.category || 'general'}
                </span>
                <div className="flex gap-2">
                  <button
                    onClick={() => handlePlayMusic(music)}
                    className={`p-2 rounded-full transition-colors ${
                      currentMusic?.id === music.id && isPlaying
                        ? 'bg-red-100 text-red-600 hover:bg-red-200'
                        : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                    }`}
                  >
                    {currentMusic?.id === music.id && isPlaying ? (
                      <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd"/>
                    </svg>
                    ) : (
                      <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"/>
                      </svg>
                    )}
                  </button>
                  <button
                    onClick={() => handleDeleteMusic(music)}
                    className="p-2 rounded-full bg-red-100 text-red-600 hover:bg-red-200 transition-colors"
                  >
                    <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd"/>
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l1.293-1.293z" clipRule="evenodd"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 分页 */}
      {total > pageSize && (
        <div className="flex justify-center items-center space-x-4 mt-8">
          <button
            onClick={() => setPage(Math.max(1, currentPage - 1))}
            disabled={currentPage <= 1}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          
          <div className="flex items-center space-x-2">
            {Array.from({ length: Math.ceil(total / pageSize) }, (_, i) => i + 1)
              .filter(page => {
                // 显示当前页前后各2页
                return Math.abs(page - currentPage) <= 2 || page === 1 || page === Math.ceil(total / pageSize)
              })
              .map((page, index, array) => (
                <React.Fragment key={page}>
                  {index > 0 && array[index - 1] !== page - 1 && (
                    <span className="text-gray-400">...</span>
                  )}
                  <button
                    onClick={() => setPage(page)}
                    className={`px-3 py-1 rounded-md text-sm ${
                      currentPage === page
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {page}
                  </button>
                </React.Fragment>
              ))}
          </div>
          
          <button
            onClick={() => setPage(Math.min(Math.ceil(total / pageSize), currentPage + 1))}
            disabled={currentPage >= Math.ceil(total / pageSize)}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      )}

      {/* 音频播放器 */}
      {currentMusic && (
        <div className="fixed bottom-4 right-4 w-80 bg-white rounded-lg border border-gray-200 shadow-lg z-50">
          <div className="flex items-center justify-between p-3 border-b border-gray-200">
            <div className="text-sm font-medium text-gray-900 truncate">{currentMusic.name}</div>
            <button
              onClick={() => {
                audioRef.current?.pause()
                setIsPlaying(false)
                setCurrentMusic(null)
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
              </svg>
            </button>
          </div>
          
          <div className="p-3">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handlePlayMusic(currentMusic)}
                className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
              >
                {isPlaying ? (
                  <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd"/>
                  </svg>
                ) : (
                  <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"/>
                  </svg>
                )}
              </button>
              
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-1">
                  <div 
                    className="bg-blue-500 h-1 rounded-full transition-all duration-100"
                    style={{ width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%' }}
                  ></div>
                </div>
              </div>
              
              <div className="text-xs text-gray-500 min-w-max">
                {formatDuration(currentTime)} / {formatDuration(duration)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        multiple
        accept=".mp3,.wav,.m4a,.flac"
        onChange={handleFileSelect}
      />

      {/* 音频元素 */}
      <audio ref={audioRef} style={{ display: 'none' }} />

      {/* 分类管理模态框 */}
      {showCategoryManager && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">分类管理</h2>
              <button
                onClick={() => setShowCategoryManager(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                </svg>
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-3">当前分类</h3>
                <div className="space-y-2">
                  {Array.isArray(availableCategories) && availableCategories.map((category: string) => (
                    <div key={category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="font-medium text-gray-900">{category}</span>
                        <span className="text-sm text-gray-500">{categoryStats[category] || 0} 个文件</span>
                      </div>
                      {category !== 'general' && (
                        <button 
                          className="text-red-600 hover:text-red-800 text-sm"
                          onClick={async () => {
                            if (confirm(`确定要删除分类 "${category}" 吗？`)) {
                              await handleDeleteCategory(category)
                            }
                          }}
                        >
                          删除
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-3">添加新分类</h3>
                <div className="flex gap-2">
                  <input 
                    type="text" 
                    placeholder="输入分类名称"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyPress={async (e) => {
                      if (e.key === 'Enter') {
                        const input = e.target as HTMLInputElement
                        const newCategory = input.value.trim()
                        if (newCategory && Array.isArray(availableCategories) && !availableCategories.includes(newCategory)) {
                          const success = await handleAddCategory(newCategory)
                          if (success) {
                            input.value = ''
                          }
                        }
                      }
                    }}
                  />
                  <button 
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    onClick={async (e) => {
                      const input = (e.target as HTMLElement).parentElement?.querySelector('input') as HTMLInputElement
                      const newCategory = input?.value.trim()
                      if (newCategory && Array.isArray(availableCategories) && !availableCategories.includes(newCategory)) {
                        const success = await handleAddCategory(newCategory)
                        if (success) {
                          input.value = ''
                        }
                      }
                    }}
                  >
                    添加
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
