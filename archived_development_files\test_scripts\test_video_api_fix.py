#!/usr/bin/env python
"""
测试视频生成API的修复情况
"""

import requests
import json

def test_video_generation_api():
    """测试视频生成API"""
    base_url = "http://localhost:8001/api/video-generator"
    
    try:
        # 测试获取作业列表
        print("测试获取作业列表...")
        response = requests.get(f"{base_url}/jobs?page=1&limit=10")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取作业列表成功")
            print(f"返回数据结构: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 获取作业列表失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保后端已启动")
    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")

if __name__ == "__main__":
    test_video_generation_api()
