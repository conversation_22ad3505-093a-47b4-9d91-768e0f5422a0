#!/usr/bin/env python3
"""
测试5段视频转场 - 验证修复后的转场时间计算（解决10秒后定格问题）
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_five_test_segments():
    """创建5个不同时长的测试视频片段"""
    
    test_videos = []
    
    # 创建5个不同时长的视频片段（模拟真实场景）
    video_configs = [
        ('red', '#FF0000', 3.0),      # 3秒红色
        ('green', '#00FF00', 4.0),    # 4秒绿色
        ('blue', '#0000FF', 2.5),     # 2.5秒蓝色
        ('yellow', '#FFFF00', 3.5),   # 3.5秒黄色
        ('purple', '#800080', 2.0),   # 2秒紫色
    ]
    
    durations = []
    
    for i, (name, color, duration) in enumerate(video_configs):
        output_path = f"test_five_{name}_{duration}s.mp4"
        
        if not Path(output_path).exists():
            logger.info(f"创建{duration}秒测试视频: {output_path}")
            
            # 创建指定时长的纯色视频
            (
                ffmpeg
                .input(f'color={color}:size=1080x1920:duration={duration}:rate=30', f='lavfi')
                .output(output_path, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
        
        test_videos.append(output_path)
        durations.append(duration)
    
    return test_videos, durations

def test_five_segment_transitions():
    """测试5段视频的转场效果"""
    
    logger.info("🎬 开始测试5段视频转场效果...")
    
    # 创建测试视频
    test_videos, real_durations = create_five_test_segments()
    
    logger.info(f"测试视频片段: {test_videos}")
    logger.info(f"实际时长: {real_durations}")
    logger.info(f"总原始时长: {sum(real_durations)}秒")
    
    # 测试配置
    test_configs = [
        {
            'name': '5段淡入淡出转场',
            'transition_type': 'fade',
            'duration': 0.5,
            'output': 'five_segment_fade.mp4'
        },
        {
            'name': '5段溶解转场',
            'transition_type': 'dissolve',
            'duration': 0.8,
            'output': 'five_segment_dissolve.mp4'
        },
        {
            'name': '5段无转场对比',
            'transition_type': 'none',
            'duration': 0.0,
            'output': 'five_segment_none.mp4'
        }
    ]
    
    success_count = 0
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
        logger.info(f"转场类型: {config['transition_type']}")
        logger.info(f"转场时长: {config['duration']}s")
        logger.info(f"视频片段时长: {real_durations}")
        logger.info(f"输出文件: {config['output']}")
        
        # 计算预期时长
        if config['transition_type'] == 'none':
            expected_duration = sum(real_durations)
        else:
            # 有转场：总时长 = 原始时长 - (转场数量 × 转场时长)
            transition_count = len(real_durations) - 1  # 5段视频有4个转场
            expected_duration = sum(real_durations) - (transition_count * config['duration'])
        
        logger.info(f"预期总时长: {expected_duration}s (原始{sum(real_durations)}s - {transition_count if config['transition_type'] != 'none' else 0}个转场)")
        
        # 详细的时间计算说明
        if config['transition_type'] != 'none':
            logger.info("详细时间计算:")
            cumulative = 0
            for j, dur in enumerate(real_durations):
                if j == 0:
                    logger.info(f"  片段{j+1}({dur}s): 0s - {dur}s")
                    cumulative = dur
                else:
                    start_time = cumulative - config['duration']
                    end_time = cumulative + dur - config['duration']
                    logger.info(f"  片段{j+1}({dur}s): {start_time}s - {end_time}s (转场{config['duration']}s)")
                    cumulative = end_time
        
        try:
            start_time = time.time()
            
            # 创建视频流
            streams = []
            for video_path in test_videos:
                stream = ffmpeg.input(video_path)
                streams.append(stream)
            
            logger.info(f"创建了 {len(streams)} 个视频流")
            
            # 应用转场效果
            final_stream = VideoCompositionService._create_video_with_transitions(
                streams, config['transition_type'], config['duration'], real_durations
            )
            
            # 输出视频
            out = ffmpeg.output(
                final_stream,
                config['output'],
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p'
            ).overwrite_output()
            
            # 执行FFmpeg命令
            import subprocess
            
            cmd = ffmpeg.compile(out)
            logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            try:
                # 等待最多120秒
                stdout, stderr = process.communicate(timeout=120)
                
                if process.returncode == 0:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功!")
                        logger.info(f"   文件大小: {file_size} bytes")
                        logger.info(f"   处理时间: {processing_time:.2f}秒")
                        logger.info(f"   预期总时长: {expected_duration}s")
                        
                        # 详细的播放顺序说明
                        color_names = ['红色', '绿色', '蓝色', '黄色', '紫色']
                        if config['transition_type'] == 'none':
                            sequence = " → ".join([f"{color_names[j]}({real_durations[j]}s)" for j in range(len(real_durations))])
                            logger.info(f"   预期效果: {sequence} = {expected_duration}s")
                        else:
                            logger.info(f"   预期效果: 5段视频用{config['duration']}s转场连接")
                            logger.info(f"   关键验证: 10秒后应该还在播放，不会定格")
                        
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                else:
                    logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                    if stderr:
                        stderr_text = stderr.decode('utf-8', errors='ignore')
                        logger.error(f"stderr: {stderr_text[:500]}...")
                        
            except subprocess.TimeoutExpired:
                logger.error(f"❌ {config['name']} 测试超时（120秒）")
                process.kill()
                process.communicate()
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n=== 5段转场测试完成 ===")
    logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
    
    if success_count > 0:
        logger.info("\n📋 5段转场测试文件:")
        logger.info("- five_segment_fade.mp4 (5段淡入淡出转场)")
        logger.info("- five_segment_dissolve.mp4 (5段溶解转场)")
        logger.info("- five_segment_none.mp4 (无转场对比)")
        
        logger.info("\n🔍 关键验证要点:")
        logger.info("1. 视频应该完整播放所有5个片段")
        logger.info("2. 10秒后不应该定格在某个画面")
        logger.info("3. 每个颜色段应该按预期时长播放")
        logger.info("4. 转场应该在正确的时间点发生")
        logger.info("5. 总时长应该符合计算预期")
        
        logger.info("\n🎬 预期播放顺序:")
        logger.info("红色(3s) → 绿色(4s) → 蓝色(2.5s) → 黄色(3.5s) → 紫色(2s)")
        logger.info("转场版本会在片段切换时有平滑过渡")
        
        logger.info("\n⚠️ 特别关注:")
        logger.info("如果10秒后视频还在正常播放（黄色或紫色片段），说明定格问题修复成功！")
        
        return True
    else:
        logger.error("❌ 所有5段转场测试都失败了")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_five_red_3.0s.mp4',
        'test_five_green_4.0s.mp4',
        'test_five_blue_2.5s.mp4',
        'test_five_yellow_3.5s.mp4',
        'test_five_purple_2.0s.mp4'
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            Path(file_path).unlink()
            logger.info(f"清理测试文件: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始5段视频转场测试")
    logger.info("验证修复后的转场时间计算（解决10秒后定格问题）")
    logger.info("测试场景: 5个不同时长的视频片段")
    
    try:
        success = test_five_segment_transitions()
        
        if success:
            logger.info("\n🎉 5段转场测试完成!")
            logger.info("转场时间计算修复成功！")
            logger.info("现在应该不会出现10秒后定格的问题了。")
        else:
            logger.error("\n❌ 5段转场测试失败")
            sys.exit(1)
            
    finally:
        # 清理测试文件
        cleanup_test_files()
