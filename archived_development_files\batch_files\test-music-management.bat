@echo off
echo 🎵 背景音乐管理功能测试
echo ================================

echo.
echo 1. 启动后端服务...
cd backend
start "Backend Server" cmd /k "python main.py"

echo.
echo 2. 等待后端启动...
timeout /t 5 /nobreak

echo.
echo 3. 测试音乐API...
cd ..
python test_music_api.py

echo.
echo 4. 启动前端服务...
cd frontend
start "Frontend Server" cmd /k "npm run dev"

echo.
echo 5. 打开浏览器测试页面...
timeout /t 3 /nobreak
start http://localhost:3000/music

echo.
echo ✅ 测试环境启动完成！
echo.
echo 测试步骤：
echo 1. 尝试拖拽上传音乐文件
echo 2. 测试分类管理功能
echo 3. 刷新页面验证分类持久化
echo 4. 验证音乐列表显示
echo.
pause
