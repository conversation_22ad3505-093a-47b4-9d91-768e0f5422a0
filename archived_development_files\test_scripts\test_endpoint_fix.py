#!/usr/bin/env python
"""
测试修复后的设置系统：确保端点地址正确保存
"""

import asyncio
import aiohttp
import json
import sqlite3

API_BASE = "http://localhost:8000"
DATABASE_PATH = "D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"

async def test_endpoint_preservation():
    """测试端点地址是否正确保存"""
    print("🔍 测试设置系统端点地址保存...")
    
    async with aiohttp.ClientSession() as session:
        # 1. 准备包含完整端点的设置数据
        test_settings = {
            "tts": {
                "provider": "coze",
                "apiKey": "test-coze-token-123",
                "endpoint": "https://api.coze.cn/v1/workflow/run",
                "voice": "zh_male_wennuanahu_moon_bigtts",
                "model": "7520141766219563047",
                "speed": 1.2
            },
            "llm": {
                "provider": "yunwu",
                "apiKey": "test-yunwu-key-456",
                "endpoint": "https://yunwu.ai/v1/chat/completions",
                "model": "gpt-4o",
                "temperature": 0.8,
                "maxTokens": 3000,
                "systemPrompt": "你是一个测试用的AI助手"
            },
            "general": {
                "theme": "dark",
                "language": "zh-CN",
                "autoSave": True,
                "outputDirectory": "/test/output"
            }
        }
        
        print("\n📝 保存包含端点地址的设置...")
        async with session.put(
            f"{API_BASE}/api/settings",
            json=test_settings,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status == 200:
                update_data = await response.json()
                if update_data.get('success'):
                    print("✅ 设置保存成功")
                else:
                    print(f"❌ 设置保存失败: {update_data.get('message')}")
                    return
            else:
                response_text = await response.text()
                print(f"❌ API调用失败: {response.status}")
                print(f"   响应内容: {response_text}")
                return
        
        # 2. 从API获取设置，验证端点是否保存
        print("\n🔍 从API获取设置，验证端点保存...")
        async with session.get(f"{API_BASE}/api/settings") as response:
            if response.status == 200:
                settings_data = await response.json()
                if settings_data.get('success'):
                    retrieved_settings = settings_data['data']
                    
                    # 检查关键信息
                    tts_endpoint = retrieved_settings['tts'].get('endpoint', '')
                    llm_endpoint = retrieved_settings['llm'].get('endpoint', '')
                    
                    print(f"   TTS端点: {tts_endpoint}")
                    print(f"   LLM端点: {llm_endpoint}")
                    
                    if tts_endpoint == "https://api.coze.cn/v1/workflow/run":
                        print("   ✅ Coze TTS端点正确保存")
                    else:
                        print(f"   ❌ Coze TTS端点错误: 期望 'https://api.coze.cn/v1/workflow/run', 实际 '{tts_endpoint}'")
                    
                    if llm_endpoint == "https://yunwu.ai/v1/chat/completions":
                        print("   ✅ 云雾API端点正确保存")
                    else:
                        print(f"   ❌ 云雾API端点错误: 期望 'https://yunwu.ai/v1/chat/completions', 实际 '{llm_endpoint}'")
                        
                else:
                    print(f"❌ 获取设置失败: {settings_data.get('message')}")
            else:
                print(f"❌ API调用失败: {response.status}")

def check_database_endpoints():
    """直接检查数据库中的端点信息"""
    print("\n🔍 直接检查数据库中的端点信息...")
    
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT tts_provider, tts_endpoint, tts_api_key, tts_model,
                   llm_provider, llm_endpoint, llm_api_key, llm_model
            FROM settings 
            ORDER BY updated_at DESC 
            LIMIT 1
        """)
        
        record = cursor.fetchone()
        if record:
            tts_provider, tts_endpoint, tts_api_key, tts_model, llm_provider, llm_endpoint, llm_api_key, llm_model = record
            
            print("   数据库中的设置:")
            print(f"   🎤 TTS:")
            print(f"      提供商: {tts_provider}")
            print(f"      端点: {tts_endpoint}")
            print(f"      API密钥: {'已设置' if tts_api_key else '未设置'}")
            print(f"      模型/工作流ID: {tts_model}")
            
            print(f"   🤖 LLM:")
            print(f"      提供商: {llm_provider}")
            print(f"      端点: {llm_endpoint}")
            print(f"      API密钥: {'已设置' if llm_api_key else '未设置'}")
            print(f"      模型: {llm_model}")
            
            # 验证关键信息
            missing_info = []
            if not tts_endpoint:
                missing_info.append("TTS端点")
            if not llm_endpoint:
                missing_info.append("LLM端点")
            if not tts_api_key:
                missing_info.append("TTS API密钥")
            if not llm_api_key:
                missing_info.append("LLM API密钥")
                
            if missing_info:
                print(f"   ⚠️ 缺少信息: {', '.join(missing_info)}")
            else:
                print("   ✅ 所有关键信息都已保存")
                
        else:
            print("   ❌ 数据库中没有设置记录")
            
    except Exception as e:
        print(f"   ❌ 数据库查询错误: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 测试修复后的设置系统（端点地址保存）")
    print("=" * 60)
    
    try:
        await test_endpoint_preservation()
        check_database_endpoints()
        
        print("\n" + "=" * 60)
        print("✅ 测试完成")
        print("=" * 60)
        print("\n💡 修复说明:")
        print("1. 移除了localStorage依赖，设置完全保存在数据库中")
        print("2. 添加了Coze TTS和云雾API的默认端点地址")
        print("3. 前端加载时会从后端获取完整设置信息")
        print("4. 确保所有关键配置信息不会丢失")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
