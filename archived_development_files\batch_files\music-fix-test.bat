@echo off
echo 🎵 背景音乐管理功能修复
echo ================================

echo.
echo 1. 运行数据库迁移...
python migrate_music_categories.py

echo.
echo 2. 启动后端服务...
cd backend
start "Backend Server" cmd /k "python main.py"

echo.
echo 3. 等待后端启动...
timeout /t 5 /nobreak

echo.
echo 4. 测试修复后的API...
cd ..
python comprehensive_music_test.py

echo.
echo 5. 启动前端服务...
cd frontend  
start "Frontend Server" cmd /k "npm run dev"

echo.
echo 6. 打开浏览器测试页面...
timeout /t 3 /nobreak
start http://localhost:3000/music

echo.
echo ✅ 修复完成！
echo.
echo 测试要点：
echo 1. 分类管理 - 添加分类后刷新页面，分类应该持久存在
echo 2. 音乐上传 - 上传后应该立即在列表中显示
echo 3. 分类显示 - 拖拽区域应该显示当前选择的分类
echo.
pause
