#!/usr/bin/env python3
"""
深度分析任务队列阻塞原因
"""
import sys
from pathlib import Path
import asyncio
import threading

# 添加backend路径
backend_dir = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_dir))

from src.core.database import get_session_maker
from src.models.video_generation import VideoGenerationJob, VideoGenerationTask, TaskStatus

def analyze_queue_blocking():
    """分析任务队列阻塞的原因"""
    session_maker = get_session_maker()
    db = session_maker()
    
    print("🔍 任务队列阻塞分析报告")
    print("=" * 60)
    
    try:
        # 1. 检查运行中的作业
        running_jobs = db.query(VideoGenerationJob).filter(
            VideoGenerationJob.status == TaskStatus.RUNNING
        ).all()
        
        print(f"\n📊 当前运行中的作业数量: {len(running_jobs)}")
        
        for job in running_jobs:
            print(f"\n🎯 作业详情:")
            print(f"  ID: {job.id}")
            print(f"  名称: {job.name}")
            print(f"  创建时间: {job.created_at}")
            print(f"  开始时间: {job.started_at}")
            print(f"  总任务数: {job.total_tasks}")
            print(f"  完成任务数: {job.completed_tasks}")
            
            # 检查该作业下的任务状态
            all_tasks = db.query(VideoGenerationTask).filter(
                VideoGenerationTask.job_id == job.id
            ).order_by(VideoGenerationTask.created_at).all()
            
            pending_tasks = [t for t in all_tasks if t.status == TaskStatus.PENDING]
            running_tasks = [t for t in all_tasks if t.status == TaskStatus.RUNNING]
            completed_tasks = [t for t in all_tasks if t.status == TaskStatus.COMPLETED]
            failed_tasks = [t for t in all_tasks if t.status == TaskStatus.FAILED]
            
            print(f"\n  📝 任务状态分布:")
            print(f"    待执行任务: {len(pending_tasks)}")
            print(f"    运行中任务: {len(running_tasks)}")
            print(f"    已完成任务: {len(completed_tasks)}")
            print(f"    失败任务: {len(failed_tasks)}")
            
            # 显示失败任务的详情
            if failed_tasks:
                print(f"\n  ❌ 失败任务详情:")
                for task in failed_tasks:
                    print(f"    任务ID: {task.id}")
                    print(f"    任务名称: {task.task_name}")
                    print(f"    错误信息: {task.error_message}")
                    print(f"    进度: {task.progress}%")
                    print(f"    当前步骤: {task.current_step}")
                    print(f"    失败时间: {task.updated_at}")
                    print(f"    重试次数: {task.retry_count}")
                    print("    ---")
            
            # 显示待执行任务的详情
            if pending_tasks:
                print(f"\n  ⏳ 待执行任务详情:")
                for task in pending_tasks:
                    print(f"    任务ID: {task.id}")
                    print(f"    任务名称: {task.task_name}")
                    print(f"    创建时间: {task.created_at}")
                    print(f"    队列位置: {all_tasks.index(task) + 1}")
                    print("    ---")
            
            # 显示运行中任务的详情
            if running_tasks:
                print(f"\n  🏃 运行中任务详情:")
                for task in running_tasks:
                    print(f"    任务ID: {task.id}")
                    print(f"    任务名称: {task.task_name}")
                    print(f"    开始时间: {task.started_at}")
                    print(f"    进度: {task.progress}%")
                    print(f"    当前步骤: {task.current_step}")
                    print("    ---")
        
        # 2. 检查是否有孤立的任务（所属作业不是运行状态）
        orphaned_tasks = db.query(VideoGenerationTask).join(VideoGenerationJob).filter(
            VideoGenerationJob.status != TaskStatus.RUNNING,
            VideoGenerationTask.status.in_([TaskStatus.PENDING, TaskStatus.RUNNING])
        ).all()
        
        if orphaned_tasks:
            print(f"\n⚠️  发现 {len(orphaned_tasks)} 个孤立任务（作业非运行状态但任务仍在队列中）:")
            for task in orphaned_tasks:
                print(f"  任务ID: {task.id} | 作业状态: {task.job.status} | 任务状态: {task.status}")
        
        # 3. 分析根本原因
        print(f"\n🎯 根本原因分析:")
        
        if not running_jobs:
            print("  ✅ 没有运行中的作业，这是正常状态")
        else:
            for job in running_jobs:
                tasks = db.query(VideoGenerationTask).filter(
                    VideoGenerationTask.job_id == job.id
                ).all()
                
                failed_count = len([t for t in tasks if t.status == TaskStatus.FAILED])
                pending_count = len([t for t in tasks if t.status == TaskStatus.PENDING])
                running_count = len([t for t in tasks if t.status == TaskStatus.RUNNING])
                
                if failed_count > 0 and pending_count > 0:
                    print(f"  ❌ 作业 {job.name} 可能因为任务失败而阻塞后续任务")
                    print(f"     - 失败任务数: {failed_count}")
                    print(f"     - 待执行任务数: {pending_count}")
                    print(f"     - 建议: 检查失败任务的错误信息，或手动重试失败任务")
                
                if running_count > 1:
                    print(f"  ⚠️  作业 {job.name} 有多个任务同时运行，可能导致资源冲突")
                    print(f"     - 运行中任务数: {running_count}")
                    print(f"     - 建议: 检查任务队列处理逻辑是否正确")
                
                if running_count == 0 and pending_count > 0:
                    print(f"  🔍 作业 {job.name} 没有运行中的任务但有待执行任务")
                    print(f"     - 可能原因: 任务队列处理器异常、任务执行条件不满足")
        
        # 4. 提供解决方案
        print(f"\n💡 建议的解决方案:")
        print("  1. 检查任务队列处理器是否正常运行")
        print("  2. 重试所有失败的任务")
        print("  3. 检查日志文件查看详细错误信息")
        print("  4. 如果问题持续，考虑重启服务")
        
    finally:
        db.close()

def check_task_queue_worker_status():
    """检查任务队列工作线程状态"""
    print(f"\n🔧 任务队列工作线程状态检查:")
    
    # 检查是否有视频生成服务实例在运行
    current_threads = threading.enumerate()
    print(f"  当前活跃线程数: {len(current_threads)}")
    
    for thread in current_threads:
        print(f"    - {thread.name}: {thread.is_alive()}")
    
    # 检查异步任务
    try:
        loop = asyncio.get_running_loop()
        tasks = asyncio.all_tasks(loop)
        print(f"  当前异步任务数: {len(tasks)}")
        for task in tasks:
            print(f"    - {task.get_name()}: {task.done()}")
    except RuntimeError:
        print("  没有运行中的事件循环")

if __name__ == "__main__":
    analyze_queue_blocking()
    check_task_queue_worker_status()
