#!/usr/bin/env python3
"""
直接测试F5-TTS服务
"""

import requests
from gradio_client import Client, handle_file
import os

def test_f5_service_direct():
    """直接测试F5-TTS服务"""
    
    endpoint = "http://application-e4ee63ebd100431995375a4a07ab41b320250817000003.ssh-hdc.xingluan.cn:1800/"
    
    print(f"🔍 直接测试F5-TTS服务: {endpoint}")
    
    # 1. 测试HTTP连接
    print("\n1. 测试HTTP连接...")
    try:
        response = requests.get(endpoint, timeout=10)
        print(f"✅ HTTP连接成功，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ HTTP连接失败: {e}")
        return False
    
    # 2. 测试Gradio客户端
    print("\n2. 测试Gradio客户端...")
    try:
        client = Client(endpoint)
        print("✅ Gradio客户端创建成功")
        
        # 3. 检查API端点
        print("\n3. 检查可用的API端点...")
        try:
            print(f"可用端点: {client.endpoints}")
        except Exception as e:
            print(f"⚠️  无法获取端点信息: {e}")
        
        # 4. 测试实际调用
        print("\n4. 测试实际API调用...")
        
        # 检查参考音频文件
        ref_audio_path = "data/f5_tts_voices/e8e22b5f-4b2c-40cd-ac4c-e61b92286506.MP3"
        if not os.path.exists(ref_audio_path):
            print(f"❌ 参考音频文件不存在: {ref_audio_path}")
            return False
        
        print(f"✅ 参考音频文件存在: {ref_audio_path}")
        
        # 调用F5-TTS API
        try:
            result = client.predict(
                ref_audio_input=handle_file(ref_audio_path),
                ref_text_input="what hidden truth made you regret hating your parent for years",
                gen_text_input="这是一个测试。",
                remove_silence=False,
                randomize_seed=True,
                seed_input=0,
                cross_fade_duration_slider=0.15,
                nfe_slider=32,
                speed_slider=1.0,
                api_name="/basic_tts"
            )
            
            print(f"✅ F5-TTS API调用成功!")
            print(f"结果类型: {type(result)}")
            print(f"结果内容: {result}")
            
            # 检查结果
            if result and len(result) > 0:
                audio_output = result[0] if isinstance(result, (list, tuple)) else result
                if audio_output and os.path.exists(audio_output):
                    file_size = os.path.getsize(audio_output)
                    print(f"✅ 生成音频文件: {audio_output}, 大小: {file_size} 字节")
                    return True
                else:
                    print(f"❌ 音频文件不存在或为空: {audio_output}")
                    return False
            else:
                print(f"❌ API返回结果为空")
                return False
                
        except Exception as e:
            print(f"❌ F5-TTS API调用失败: {e}")
            import traceback
            print(traceback.format_exc())
            return False
        
    except Exception as e:
        print(f"❌ Gradio客户端创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🧪 F5-TTS服务直接测试\n")
    
    success = test_f5_service_direct()
    
    if success:
        print("\n🎉 F5-TTS服务工作正常！")
    else:
        print("\n❌ F5-TTS服务存在问题。")
