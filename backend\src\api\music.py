"""
背景音乐管理API
"""

from fastapi import APIRouter, Depends, HTTPException, Query, File, UploadFile, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional
import uuid
import os
import shutil
from pathlib import Path
import mutagen
from mutagen.mp3 import MP3
from mutagen.wave import WAVE
from mutagen.flac import FLAC
from mutagen.mp4 import MP4

from src.core.database import get_db
from src.core.responses import ApiResponse
from src.models.resources import BackgroundMusic, MusicCategory
from src.schemas.resources import (
    BackgroundMusicCreate,
    BackgroundMusicUpdate,
    BackgroundMusicResponse,
    ResourceQueryParams,
    BatchOperationRequest,
    BatchOperationResponse
)

router = APIRouter(prefix="/background-music", tags=["background-music"])

# 上传目录配置
UPLOAD_DIR = Path("uploads/music")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 支持的音频格式
SUPPORTED_FORMATS = {
    '.mp3': 'MP3',
    '.wav': 'WAV', 
    '.m4a': 'M4A',
    '.flac': 'FLAC'
}

def get_audio_metadata(file_path: str):
    """获取音频文件元数据"""
    try:
        audio_file = mutagen.File(file_path, easy=True)
        if audio_file is None:
            return None
            
        duration = getattr(audio_file.info, 'length', 0)
        bitrate = getattr(audio_file.info, 'bitrate', 0)
        sample_rate = getattr(audio_file.info, 'sample_rate', 0)
        
        return {
            'duration': duration,
            'bitrate': bitrate,
            'sample_rate': sample_rate
        }
    except Exception:
        return None

@router.post("/upload")
async def upload_music(
    file: UploadFile = File(...),
    category: str = Form("general"),
    tags: str = Form(""),
    db: Session = Depends(get_db)
):
    """上传背景音乐文件"""
    try:
        # 验证文件名
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
            
        # 验证文件格式
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in SUPPORTED_FORMATS:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式。支持的格式: {', '.join(SUPPORTED_FORMATS.keys())}"
            )
        
        # 生成文件ID和路径
        file_id = str(uuid.uuid4())
        safe_filename = f"{file_id}{file_extension}"
        file_path = UPLOAD_DIR / safe_filename
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 获取文件信息
        file_size = file_path.stat().st_size
        metadata = get_audio_metadata(str(file_path))
        
        # 解析标签
        tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()] if tags else []
        
        # 确保分类存在，如果不存在则创建
        existing_category = db.query(MusicCategory).filter(MusicCategory.name == category).first()
        if not existing_category:
            new_category = MusicCategory(name=category, description=f"{category}分类")
            db.add(new_category)
            db.commit()
        
        # 创建数据库记录
        music = BackgroundMusic(
            id=file_id,
            name=file.filename,
            file_path=str(file_path),
            duration=metadata.get('duration', 0) if metadata else 0,
            category=category,
            tags=tag_list,
            file_size=file_size,
            format=SUPPORTED_FORMATS[file_extension],
            bitrate=metadata.get('bitrate', 0) if metadata else 0,
            sample_rate=metadata.get('sample_rate', 0) if metadata else 0,
            is_built_in=False
        )
        
        db.add(music)
        db.commit()
        db.refresh(music)
        
        response = ApiResponse.create_success(
            data=music.to_frontend_format(),
            message="音乐上传成功"
        )
        
        return response.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        # 清理已上传的文件
        file_path = None
        try:
            if file_path and file_path.exists():
                file_path.unlink()
        except:
            pass
        raise HTTPException(status_code=500, detail=f"音乐上传失败: {str(e)}")

@router.get("")
async def get_music_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="分类过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_built_in: Optional[bool] = Query(None, description="是否内置"),
    db: Session = Depends(get_db)
):
    """获取背景音乐列表"""
    try:
        # 构建查询
        query = db.query(BackgroundMusic).filter(BackgroundMusic.is_deleted == False)
        
        # 应用过滤条件
        if category:
            query = query.filter(BackgroundMusic.category == category)
        
        if is_built_in is not None:
            query = query.filter(BackgroundMusic.is_built_in == is_built_in)
        
        if search:
            query = query.filter(
                or_(
                    BackgroundMusic.name.contains(search),
                    BackgroundMusic.category.contains(search)
                )
            )
        
        # 计算总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        items = query.offset(offset).limit(page_size).all()
        
        # 转换为前端格式
        music_list = [item.to_frontend_format() for item in items]
        
        response = ApiResponse.create_success(
            data={
                "total": total,
                "page": page,
                "pageSize": page_size,
                "items": music_list
            },
            message="获取背景音乐列表成功"
        )
        
        return response.model_dump()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取背景音乐列表失败: {str(e)}")

@router.get("/{music_id}/play")
async def play_music(
    music_id: str,
    db: Session = Depends(get_db)
):
    """播放/下载背景音乐"""
    try:
        music = db.query(BackgroundMusic).filter(BackgroundMusic.id == music_id).first()
        
        if not music:
            raise HTTPException(status_code=404, detail="背景音乐不存在")
        
        file_path = Path(str(music.file_path))
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="音乐文件不存在")
        
        return FileResponse(
            path=str(file_path),
            media_type="audio/mpeg",
            filename=str(music.name)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"播放音乐失败: {str(e)}")

@router.get("/{music_id}")
async def get_background_music(
    music_id: str,
    db: Session = Depends(get_db)
):
    """获取单个背景音乐详情"""
    try:
        music = db.query(BackgroundMusic).filter(BackgroundMusic.id == music_id).first()
        
        if not music:
            raise HTTPException(status_code=404, detail="背景音乐不存在")
        
        response = ApiResponse.create_success(
            data=music.to_frontend_format(),
            message="获取背景音乐详情成功"
        )
        
        return response.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取背景音乐详情失败: {str(e)}")

@router.post("")
async def create_background_music(
    music_data: BackgroundMusicCreate,
    db: Session = Depends(get_db)
):
    """创建背景音乐记录"""
    try:
        # 创建新记录
        music = BackgroundMusic(
            id=str(uuid.uuid4()),
            name=music_data.name,
            file_path=music_data.file_path,
            duration=music_data.duration,
            category=music_data.category,
            tags=music_data.tags,
            is_built_in=music_data.is_built_in,
            file_size=music_data.file_size,
            format=music_data.format,
            bitrate=music_data.bitrate,
            sample_rate=music_data.sample_rate
        )
        
        db.add(music)
        db.commit()
        db.refresh(music)
        
        response = ApiResponse.create_success(
            data=music.to_frontend_format(),
            message="创建背景音乐成功"
        )
        
        return response.model_dump()
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建背景音乐失败: {str(e)}")

@router.put("/{music_id}")
async def update_background_music(
    music_id: str,
    music_data: BackgroundMusicUpdate,
    db: Session = Depends(get_db)
):
    """更新背景音乐信息"""
    try:
        music = db.query(BackgroundMusic).filter(BackgroundMusic.id == music_id).first()
        
        if not music:
            raise HTTPException(status_code=404, detail="背景音乐不存在")
        
        # 更新字段
        update_data = music_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(music, field, value)
        
        db.commit()
        db.refresh(music)
        
        response = ApiResponse.create_success(
            data=music.to_frontend_format(),
            message="更新背景音乐成功"
        )
        
        return response.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新背景音乐失败: {str(e)}")

@router.delete("/{music_id}")
async def delete_background_music(
    music_id: str,
    db: Session = Depends(get_db)
):
    """删除背景音乐"""
    try:
        music = db.query(BackgroundMusic).filter(BackgroundMusic.id == music_id).first()
        
        if not music:
            raise HTTPException(status_code=404, detail="背景音乐不存在")
          # 检查是否为内置音乐
        if getattr(music, 'is_built_in', False):
            raise HTTPException(status_code=400, detail="不能删除内置背景音乐")
        
        db.delete(music)
        db.commit()
        
        response = ApiResponse.create_success(
            message="删除背景音乐成功"
        )
        
        return response.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除背景音乐失败: {str(e)}")

@router.post("/batch")
async def batch_operation_background_music(
    batch_request: BatchOperationRequest,
    db: Session = Depends(get_db)
):
    """批量操作背景音乐"""
    try:
        success_count = 0
        error_count = 0
        errors = []
        
        for music_id in batch_request.ids:
            try:
                music = db.query(BackgroundMusic).filter(BackgroundMusic.id == music_id).first()
                
                if not music:
                    errors.append({"id": music_id, "error": "音乐不存在"})
                    error_count += 1
                    continue
                
                if batch_request.operation == "delete":
                    if getattr(music, 'is_built_in', False):
                        errors.append({"id": music_id, "error": "不能删除内置音乐"})
                        error_count += 1
                        continue
                    
                    db.delete(music)
                    success_count += 1
                    
                elif batch_request.operation == "update_category":
                    if batch_request.params and "category" in batch_request.params:
                        music.category = batch_request.params["category"]
                        success_count += 1
                    else:
                        errors.append({"id": music_id, "error": "缺少分类参数"})
                        error_count += 1
                        
                else:
                    errors.append({"id": music_id, "error": f"不支持的操作: {batch_request.operation}"})
                    error_count += 1
                    
            except Exception as e:
                errors.append({"id": music_id, "error": str(e)})
                error_count += 1
        
        if success_count > 0:
            db.commit()
        
        response = ApiResponse.create_success(
            data={
                "successCount": success_count,
                "errorCount": error_count,
                "errors": errors
            },
            message=f"批量操作完成，成功: {success_count}，失败: {error_count}"
        )
        
        return response.model_dump()
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")

@router.get("/categories/list")
async def get_music_categories(db: Session = Depends(get_db)):
    """获取所有背景音乐分类"""
    try:
        # 从独立的分类表获取分类
        categories = db.query(MusicCategory).all()
        category_names: List[str] = []
        for cat in categories:
            category_names.append(getattr(cat, 'name'))
        
        # 确保至少有一个默认分类
        if 'general' not in category_names:
            # 如果没有general分类，创建一个
            general_category = MusicCategory(name='general', description='默认分类')
            db.add(general_category)
            db.commit()
            category_names.append('general')
        
        response = ApiResponse.create_success(
            data=category_names,
            message="获取分类列表成功"
        )
        
        return response.model_dump()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")

@router.post("/categories")
async def add_music_category(
    category: str = Form(...),
    db: Session = Depends(get_db)
):
    """添加新的音乐分类"""
    try:
        # 验证分类名称
        if not category or not category.strip():
            raise HTTPException(status_code=400, detail="分类名称不能为空")
        
        category = category.strip()
        
        # 检查分类是否已存在于分类表中
        existing = db.query(MusicCategory).filter(MusicCategory.name == category).first()
        if existing:
            raise HTTPException(status_code=400, detail="分类已存在")
        
        # 实际创建分类记录到数据库
        new_category = MusicCategory(name=category, description=f"{category}分类")
        db.add(new_category)
        db.commit()
        db.refresh(new_category)
        
        response = ApiResponse.create_success(
            data={"category": category},
            message="分类添加成功"
        )
        
        return response.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"添加分类失败: {str(e)}")

@router.delete("/categories/{category}")
async def delete_music_category(
    category: str,
    db: Session = Depends(get_db)
):
    """删除音乐分类"""
    try:
        # 不允许删除general分类
        if category == 'general':
            raise HTTPException(status_code=400, detail="不能删除默认分类")
        
        # 检查分类是否存在于分类表中
        category_record = db.query(MusicCategory).filter(MusicCategory.name == category).first()
        if not category_record:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 检查是否有音乐文件使用此分类
        music_count = db.query(BackgroundMusic).filter(BackgroundMusic.category == category).count()
        if music_count > 0:
            raise HTTPException(status_code=400, detail=f"无法删除分类，还有 {music_count} 个音乐文件使用此分类")
        
        # 删除分类记录
        db.delete(category_record)
        db.commit()
        
        response = ApiResponse.create_success(
            data={"category": category},
            message="分类删除成功"
        )
        
        return response.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除分类失败: {str(e)}")
