#!/usr/bin/env python
"""
简化的序列化测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple():
    print("🔍 简化序列化测试...")
    
    try:
        # 1. 测试导入
        from src.core.responses import ApiResponse
        print("✅ ApiResponse导入成功")
        
        # 2. 测试简单数据
        simple_response = ApiResponse.success(
            data={"test": "value"},
            message="测试成功"
        )
        
        print(f"✅ 简单响应创建成功: {type(simple_response)}")
        
        # 3. 测试序列化
        simple_dict = simple_response.model_dump()
        print(f"✅ 简单响应序列化: {simple_dict}")
        
        # 4. 测试schemas导入
        from src.schemas.settings import SettingsResponse, TTSConfig, LLMConfig, GeneralSettings
        print("✅ schemas导入成功")
        
        # 5. 创建示例数据
        tts_config = TTSConfig(
            provider="openai",
            voice="nova",
            speed=1.0,
            apiKey="test",
            endpoint="https://api.openai.com/v1",
            model="tts-1"
        )
        print("✅ TTSConfig创建成功")
        
        llm_config = LLMConfig(
            provider="openai",
            model="gpt-3.5-turbo",
            apiKey="test",
            endpoint="https://api.openai.com/v1"
        )
        print("✅ LLMConfig创建成功")
        
        general_settings = GeneralSettings(
            theme="light",
            language="zh-CN"
        )
        print("✅ GeneralSettings创建成功")
        
        # 6. 创建SettingsResponse
        settings_response = SettingsResponse(
            tts=tts_config,
            llm=llm_config,
            general=general_settings
        )
        print("✅ SettingsResponse创建成功")
        
        # 7. 测试SettingsResponse序列化
        settings_dict = settings_response.model_dump()
        print(f"✅ SettingsResponse序列化成功")
        
        # 8. 创建完整ApiResponse
        full_response = ApiResponse.success(
            data=settings_response,
            message="获取设置成功"
        )
        print("✅ 完整ApiResponse创建成功")
        
        # 9. 测试完整序列化
        full_dict = full_response.model_dump()
        print(f"✅ 完整响应序列化成功")
        print(f"   success: {full_dict.get('success')}")
        print(f"   message: {full_dict.get('message')}")
        print(f"   data keys: {list(full_dict.get('data', {}).keys())}")
        
        print("\n🎉 所有测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple()
