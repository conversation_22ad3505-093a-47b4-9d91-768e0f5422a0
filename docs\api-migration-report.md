# Next.js API 代理迁移报告

## 📋 项目概述
将前端代码中所有使用 Next.js API 代理的调用改造为直接调用后端 FastAPI 服务，统一使用 `DirectHttpClient` 和 `NEXT_PUBLIC_API_BASE_URL` 环境变量。

## 🎯 迁移目标
1. **统一API调用方式**: 使用 `DirectHttpClient` 替代原生 `fetch` 调用
2. **统一地址配置**: 使用 `NEXT_PUBLIC_API_BASE_URL` 替代硬编码的 `http://localhost:8000`
3. **移除Next.js代理**: 清理所有 `/api/*` 代理路由调用
4. **提升性能**: 直接调用后端，减少代理层开销
5. **简化部署**: 消除对Next.js API路由的依赖

## 📊 现状分析

### 发现的问题
1. **混合调用方式**: 部分模块使用 `fetch`，部分使用 `DirectHttpClient`
2. **硬编码地址**: 多处直接使用 `http://localhost:8000`
3. **环境变量不统一**: 同时存在 `NEXT_PUBLIC_API_URL` 和 `NEXT_PUBLIC_API_BASE_URL`
4. **代理路径依赖**: 大量 `/api/*` 路径调用

### 影响范围统计
- **涉及文件数**: 11个核心文件
- **API端点数**: 50+ 个不同端点
- **功能模块数**: 11个主要模块

## 🔍 详细模块分析

### 🔴 高优先级模块 (核心业务功能)

#### 1. 视频生成模块 (Generation)
**文件**: `src/store/generationStore.ts`
**问题**: 
- 使用原生 `fetch` 调用 `/api/generation/*` 端点
- 没有统一的错误处理和重试机制
**影响**: 核心视频生成功能受影响
**代理端点**:
```
POST /api/generation/story
POST /api/generation/audio  
POST /api/generation/prepare-materials
POST /api/generation/compose
POST /api/generation/cover
POST /api/generation/cancel/${taskId}
GET /api/generation/task/${taskId}
```

#### 2. 设置管理模块 (Settings)
**文件**: 
- `src/store/settingsStore.ts`
- `src/app/settings/page.tsx`
**问题**: 
- 混用 `fetch` 和可能的 `DirectHttpClient`
- 测试功能依赖代理路由
**影响**: 系统配置和连通性测试
**代理端点**:
```
GET /api/settings
PUT /api/settings
POST /api/settings/test-tts
POST /api/settings/test-llm
```

#### 3. 账号管理模块 (Accounts) 
**文件**: `src/store/accountStore.ts`
**问题**: 
- 使用相对路径 `/api/accounts/` 基础配置
- 缺少统一的API客户端
**影响**: 用户账号管理功能
**代理端点**:
```
GET /api/accounts/
POST /api/accounts/
PUT /api/accounts/${id}
DELETE /api/accounts/${id}
GET /api/accounts/stats
DELETE /api/accounts/bulk/delete
POST /api/accounts/bulk/status
POST /api/accounts/${id}/avatar
POST /api/accounts/${id}/use
```

### 🟡 中优先级模块 (功能支持)

#### 4. 提示词管理模块 (Prompts)
**文件**: `src/lib/api/prompts.ts`
**问题**: 
- 已有部分直接调用框架，但使用硬编码API_BASE
- 需要统一使用 `DirectHttpClient`
**代理端点**:
```
GET /api/prompts/
POST /api/prompts/
PUT /api/prompts/${id}
DELETE /api/prompts/${id}
POST /api/prompts/${id}/use
```

#### 5. 封面模板管理模块 (Cover Templates)
**文件**: 
- `src/lib/api/coverTemplates.ts`
- `src/components/SimpleCanvasEditor.tsx`
**问题**: 
- 使用硬编码 API_BASE
- 组件中直接使用 `fetch`
**代理端点**: 13个不同端点

#### 6. 资源验证模块 (Resources)
**文件**: `src/store/resourceStore.ts`  
**问题**: 
- 单独的 `fetch` 调用验证API
**代理端点**:
```
POST /api/resources/validate
```

### 🟢 低优先级模块 (工具和测试)

#### 7-11. 其他模块
- LLM API 模块
- 视频素材管理模块  
- 视频分类管理模块
- API Hooks 模块
- 测试页面

## 🛠️ 技术方案

### 核心改造策略
1. **统一API客户端**: 所有模块使用 `DirectHttpClient`
2. **环境变量标准化**: 统一使用 `NEXT_PUBLIC_API_BASE_URL`
3. **路径标准化**: 将 `/api/*` 改为后端实际路径
4. **错误处理统一**: 利用 `DirectHttpClient` 的统一错误处理

### 改造模式
```typescript
// ❌ 旧方式
const response = await fetch('/api/settings', {
  method: 'GET',
  headers: { 'Content-Type': 'application/json' }
})

// ✅ 新方式  
const client = new DirectHttpClient('/settings')
const response = await client.get<SettingsResponse>('/')
```

### 环境变量统一
```bash
# 统一使用
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# 废弃 
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## 📈 预期收益

### 性能提升
- **减少网络延迟**: 消除Next.js代理层
- **降低资源消耗**: 减少服务器端处理开销
- **提升并发能力**: 直接连接后端服务

### 维护性改善  
- **代码统一**: 单一API调用方式
- **配置集中**: 环境变量统一管理
- **错误处理**: 统一的异常处理机制

### 部署简化
- **架构简化**: 移除Next.js API路由依赖
- **配置简单**: 只需配置后端地址
- **故障排查**: 直接的调用链路

## ⚠️ 风险评估

### 技术风险
- **兼容性**: 确保所有API调用正确映射
- **错误处理**: 统一异常处理机制
- **超时配置**: 合理设置请求超时

### 业务风险  
- **功能中断**: 迁移过程中可能影响功能
- **数据一致性**: 确保API调用语义不变
- **用户体验**: 避免性能回退

### 缓解措施
- **分模块迁移**: 按优先级逐步改造
- **充分测试**: 每个模块完成后进行功能测试
- **回滚准备**: 保留原代码作为备份

## 📋 下一步行动
1. 创建详细的修复进度表
2. 按优先级开始模块改造
3. 每个模块完成后进行测试验证
4. 最终清理和优化

---
**报告生成时间**: 2025-07-08  
**预计完成时间**: 根据进度表安排
