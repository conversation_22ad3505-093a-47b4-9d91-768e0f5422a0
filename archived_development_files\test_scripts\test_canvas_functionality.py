#!/usr/bin/env python3
"""
测试封面模板的画布功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

async def test_canvas_functionality():
    """测试画布功能"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        print("正在访问封面模板页面...")
        await page.goto('http://localhost:3000/covers')
        
        # 等待页面加载完成
        await page.wait_for_selector('.bg-white.rounded-lg.shadow-sm', timeout=10000)
        print("页面加载完成")
        
        # 等待一下确保数据加载
        await page.wait_for_timeout(2000)
        
        # 检查是否有模板列表
        template_rows = await page.query_selector_all('tbody tr')
        if template_rows:
            print(f"找到 {len(template_rows)} 个模板")
            
            # 点击第一个模板的编辑按钮
            edit_button = await template_rows[0].query_selector('button:has-text("编辑")')
            if edit_button:
                print("点击编辑按钮...")
                await edit_button.click()
                
                # 等待编辑器加载
                await page.wait_for_timeout(1000)
                
                # 检查工具栏是否存在
                toolbar = await page.query_selector('.flex.gap-2 button:has-text("文本")')
                if toolbar:
                    print("工具栏加载成功")
                    
                    # 点击文本工具
                    text_tool = await page.query_selector('button:has-text("文本")')
                    await text_tool.click()
                    print("选择文本工具")
                    
                    # 等待工具选择生效
                    await page.wait_for_timeout(500)
                    
                    # 在画布上点击添加文本元素
                    canvas = await page.query_selector('[ref="canvasRef"]') or await page.query_selector('.relative.w-80.h-45')
                    if canvas:
                        canvas_box = await canvas.bounding_box()
                        if canvas_box:
                            # 在画布中心点击
                            click_x = canvas_box['x'] + canvas_box['width'] / 2
                            click_y = canvas_box['y'] + canvas_box['height'] / 2
                            await page.click(f'css=.relative.w-80', position={'x': 100, 'y': 80})
                            print("在画布上添加文本元素")
                            
                            # 等待元素创建
                            await page.wait_for_timeout(1000)
                            
                            # 检查是否有元素被添加
                            canvas_elements = await page.query_selector_all('.relative.w-80 > div')
                            print(f"画布上现在有 {len(canvas_elements)} 个元素")
                            
                            # 测试保存功能
                            save_button = await page.query_selector('button:has-text("保存")')
                            if save_button:
                                print("点击保存按钮...")
                                await save_button.click()
                                await page.wait_for_timeout(1000)
                                print("保存完成")
                            
                            # 测试导出功能
                            export_button = await page.query_selector('button:has-text("导出PNG")')
                            if export_button:
                                print("点击导出PNG按钮...")
                                await export_button.click()
                                await page.wait_for_timeout(1000)
                                print("导出完成")
                            
                            print("✅ 画布功能测试通过")
                        else:
                            print("❌ 无法获取画布位置")
                    else:
                        print("❌ 找不到画布元素")
                else:
                    print("❌ 找不到工具栏")
            else:
                print("❌ 找不到编辑按钮")
        else:
            print("❌ 没有找到模板，可能需要先创建模板")
            
            # 尝试创建新模板
            create_button = await page.query_selector('button:has-text("新建模板")')
            if create_button:
                print("点击新建模板...")
                await create_button.click()
                await page.wait_for_timeout(1000)
                
                # 填写表单
                name_input = await page.query_selector('input[placeholder*="请输入模板名称"]')
                if name_input:
                    await name_input.fill("测试模板")
                    
                    # 提交表单
                    submit_button = await page.query_selector('button:has-text("创建模板")')
                    if submit_button:
                        await submit_button.click()
                        print("创建模板成功")
                        await page.wait_for_timeout(2000)
        
        # 保持浏览器打开一段时间以便观察
        await page.wait_for_timeout(5000)
        await browser.close()

if __name__ == '__main__':
    asyncio.run(test_canvas_functionality())
