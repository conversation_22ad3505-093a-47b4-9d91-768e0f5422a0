"""
最小化API测试 - 逐步调试
"""

import requests

def test_step_by_step():
    base_url = "http://localhost:8000"
    
    print("Step 1: 测试基本连接")
    try:
        response = requests.get(f"{base_url}/health", timeout=3)
        print(f"Health check: {response.status_code}")
        if response.status_code != 200:
            print("❌ 基本健康检查失败")
            return
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return
    
    print("\nStep 2: 测试API路由")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=3)
        print(f"API health: {response.status_code}")
        if response.status_code != 200:
            print("❌ API路由不可用")
            return
    except Exception as e:
        print(f"❌ API路由失败: {e}")
        return
    
    print("\nStep 3: 测试GET封面模板列表")
    try:
        response = requests.get(f"{base_url}/api/cover-templates", timeout=5)
        print(f"GET templates: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"当前模板数: {len(data.get('data', []))}")
        else:
            print(f"错误响应: {response.text}")
            return
    except Exception as e:
        print(f"❌ GET请求失败: {e}")
        return
    
    print("\nStep 4: 测试最简单的POST请求")
    # 最简化的数据
    simple_data = {
        "name": "simple_test",
        "variables": []
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/cover-templates",
            json=simple_data,
            timeout=5
        )
        print(f"Simple POST: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ 最简单的POST成功")
        else:
            print("❌ 最简单的POST失败")
            
    except Exception as e:
        print(f"❌ POST请求失败: {e}")
        return

if __name__ == "__main__":
    test_step_by_step()
