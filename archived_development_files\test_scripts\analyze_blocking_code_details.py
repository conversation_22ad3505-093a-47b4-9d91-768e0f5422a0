#!/usr/bin/env python3
"""
深度分析视频生成过程中的具体阻塞代码实现
"""

import sys
import re
import ast
from pathlib import Path

def analyze_blocking_implementations():
    """分析具体的阻塞代码实现"""
    
    print("🔍 视频生成阻塞代码实现深度分析")
    print("=" * 80)
    
    backend_dir = Path(__file__).parent / 'backend'
    
    blocking_points = [
        {
            "name": "Whisper音频分析",
            "file": "src/services/video_generation_service.py",
            "method": "analyze_audio",
            "search_pattern": r"model\.transcribe\(",
            "risk": "🔴 极高",
            "type": "CPU密集型AI推理"
        },
        {
            "name": "FFmpeg视频合成",
            "file": "src/services/video_generation_helpers.py", 
            "method": "compose_video",
            "search_pattern": r"\.run\(",
            "risk": "🔴 极高",
            "type": "CPU密集型进程调用"
        },
        {
            "name": "Playwright封面截图",
            "file": "src/services/cover_screenshot_service.py",
            "method": "generate_cover_screenshot", 
            "search_pattern": r"subprocess\.run\(",
            "risk": "🔴 高",
            "type": "子进程阻塞"
        },
        {
            "name": "TTS语音生成",
            "file": "src/services/tts_service.py",
            "method": "_call_coze_tts_api",
            "search_pattern": r"async with.*\.post\(",
            "risk": "🟡 中等",
            "type": "网络I/O（已异步）"
        },
        {
            "name": "LLM文案生成", 
            "file": "src/services/llm_service.py",
            "method": "_call_openai_compatible_api",
            "search_pattern": r"async with.*\.post\(",
            "risk": "🟡 中等", 
            "type": "网络I/O（已异步）"
        }
    ]
    
    for i, point in enumerate(blocking_points, 1):
        print(f"\n{i}. 🔍 分析 {point['name']}")
        print("-" * 60)
        
        file_path = backend_dir / point['file']
        if not file_path.exists():
            print(f"   ❌ 文件不存在: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找相关代码段
            matches = re.finditer(point['search_pattern'], content, re.MULTILINE)
            match_count = 0
            
            for match in matches:
                match_count += 1
                start_pos = match.start()
                
                # 获取匹配行号
                lines_before = content[:start_pos].count('\n')
                line_number = lines_before + 1
                
                # 获取上下文（前后5行）
                lines = content.split('\n')
                start_line = max(0, line_number - 6)
                end_line = min(len(lines), line_number + 5)
                context_lines = lines[start_line:end_line]
                
                print(f"   📍 发现阻塞代码 #{match_count}")
                print(f"   📂 文件: {point['file']}")
                print(f"   📍 行号: {line_number}")
                print(f"   ⚠️  风险级别: {point['risk']}")
                print(f"   🔧 阻塞类型: {point['type']}")
                print(f"   📝 代码上下文:")
                
                for i, line in enumerate(context_lines):
                    line_num = start_line + i + 1
                    marker = ">>> " if line_num == line_number else "    "
                    print(f"      {line_num:3d} {marker}{line}")
                print()
            
            if match_count == 0:
                print(f"   ℹ️  未找到匹配的阻塞模式: {point['search_pattern']}")
                
        except Exception as e:
            print(f"   ❌ 分析文件失败: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 阻塞代码总结分析")
    print("=" * 80)
    
    # 具体分析每个关键阻塞点
    analyze_whisper_blocking(backend_dir)
    analyze_ffmpeg_blocking(backend_dir)
    analyze_playwright_blocking(backend_dir)
    
    print("\n🛠️ 修复建议优先级")
    print("-" * 50)
    print("1. 🥇 FFmpeg视频合成 - 立即修复（影响最大）")
    print("   替换: ffmpeg.run() → asyncio.create_subprocess_exec()")
    print("   位置: video_generation_helpers.py")
    print()
    print("2. 🥈 Whisper音频分析 - 高优先级修复")
    print("   替换: model.transcribe() → run_in_executor()")
    print("   位置: video_generation_service.py")
    print()
    print("3. 🥉 Playwright截图 - 中优先级修复")
    print("   替换: subprocess.run() → asyncio.create_subprocess_exec()")
    print("   位置: cover_screenshot_service.py")

def analyze_whisper_blocking(backend_dir):
    """详细分析Whisper阻塞实现"""
    print("\n🔍 Whisper阻塞详细分析")
    print("-" * 40)
    
    file_path = backend_dir / "src/services/video_generation_service.py"
    if file_path.exists():
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找analyze_audio方法
        if "def analyze_audio" in content:
            print("✅ 找到Whisper阻塞方法: analyze_audio()")
            print("🔴 阻塞详情:")
            print("   • model.transcribe() 是完全同步调用")
            print("   • 在CPU上执行AI模型推理")
            print("   • 处理word_timestamps=True增加计算量")
            print("   • 阻塞时间: 15-60秒")
            print("   • 影响: 完全阻塞FastAPI事件循环")
        else:
            print("❌ 未找到analyze_audio方法")

def analyze_ffmpeg_blocking(backend_dir):
    """详细分析FFmpeg阻塞实现"""
    print("\n🔍 FFmpeg阻塞详细分析")
    print("-" * 40)
    
    file_path = backend_dir / "src/services/video_generation_helpers.py"
    if file_path.exists():
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计.run(调用次数
        run_calls = content.count('.run(')
        print(f"✅ 找到FFmpeg阻塞调用: {run_calls}次")
        print("🔴 阻塞详情:")
        print("   • 第1次调用: _create_intermediate_video()")
        print("     - 创建标准化中间视频文件")
        print("     - 阻塞时间: 1-5分钟")
        print("   • 第2次调用: compose_video()最终合成")
        print("     - 合成音频+视频+字幕+封面")
        print("     - 阻塞时间: 1-5分钟")
        print("   • 总阻塞时间: 2-10分钟")
        print("   • 影响: 完全阻塞HTTP服务器")

def analyze_playwright_blocking(backend_dir):
    """详细分析Playwright阻塞实现"""
    print("\n🔍 Playwright阻塞详细分析")
    print("-" * 40)
    
    file_path = backend_dir / "src/services/cover_screenshot_service.py"
    if file_path.exists():
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "subprocess.run" in content:
            print("✅ 找到Playwright阻塞调用: subprocess.run()")
            print("🔴 阻塞详情:")
            print("   • 启动独立Python进程执行截图脚本")
            print("   • 在进程中启动Chromium浏览器")
            print("   • 等待页面渲染和资源加载")
            print("   • 执行截图并保存文件")
            print("   • 阻塞时间: 10-30秒")
            print("   • 影响: 阻塞事件循环")
        else:
            print("❌ 未找到subprocess.run调用")

if __name__ == "__main__":
    analyze_blocking_implementations()
