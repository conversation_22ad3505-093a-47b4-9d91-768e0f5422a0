#!/usr/bin/env python3
"""
测试F5-TTS音色测试功能
验证音色测试API是否真正调用F5-TTS服务
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_voice_test_api():
    """测试音色测试API"""
    
    print("🚀 测试F5-TTS音色测试功能...")
    
    # 1. 首先获取现有音色列表
    print("\n1. 获取音色列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/f5-tts-voices")
        if response.status_code == 200:
            data = response.json()
            voices = data.get('data', [])
            if not voices:
                print("❌ 没有可用的音色，请先创建音色")
                return False
            
            print(f"✅ 找到 {len(voices)} 个音色:")
            for voice in voices:
                print(f"   - {voice['name']} (ID: {voice['id']})")
            
            # 选择第一个音色进行测试
            test_voice = voices[0]
            voice_id = test_voice['id']
            voice_name = test_voice['name']
            
        else:
            print(f"❌ 获取音色列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取音色列表失败: {e}")
        return False
    
    # 2. 测试音色（这应该会调用真正的F5-TTS服务）
    print(f"\n2. 测试音色: {voice_name}")
    print("⏳ 正在调用F5-TTS服务生成测试音频...")
    
    start_time = time.time()
    
    try:
        # 准备测试数据
        form_data = {
            'test_text': '这是一个F5-TTS音色测试，如果您听到这段话，说明音色配置正确。'
        }
        
        # 发送测试请求
        response = requests.post(
            f"{BASE_URL}/api/f5-tts-voices/{voice_id}/test",
            data=form_data
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  请求耗时: {duration:.2f} 秒")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 音色测试成功!")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查响应数据
            if data.get('success') and data.get('data'):
                test_data = data['data']
                audio_size = test_data.get('audio_size', 0)
                
                print(f"\n📊 测试结果:")
                print(f"   音色ID: {test_data.get('voice_id')}")
                print(f"   音色名称: {test_data.get('voice_name')}")
                print(f"   测试文本: {test_data.get('test_text')}")
                print(f"   生成音频大小: {audio_size} 字节")
                print(f"   测试结果: {test_data.get('test_result')}")
                
                # 验证是否真正调用了F5-TTS服务
                if duration > 2.0 and audio_size > 0:
                    print(f"\n🎉 验证通过！")
                    print(f"   ✅ 请求耗时 {duration:.2f} 秒（符合TTS服务调用预期）")
                    print(f"   ✅ 生成了 {audio_size} 字节的音频文件")
                    print(f"   ✅ 这表明确实调用了真正的F5-TTS服务")
                    return True
                else:
                    print(f"\n⚠️  可能的问题:")
                    if duration <= 2.0:
                        print(f"   - 请求耗时过短 ({duration:.2f}s)，可能没有真正调用TTS服务")
                    if audio_size <= 0:
                        print(f"   - 没有生成音频文件或文件大小为0")
                    return False
            else:
                print(f"❌ 响应格式错误: {data}")
                return False
                
        elif response.status_code == 400:
            try:
                error_data = response.json()
                error_detail = error_data.get('detail', '未知错误')
                if 'F5-TTS服务端点未配置' in error_detail:
                    print(f"❌ 配置错误: {error_detail}")
                    print(f"💡 请在设置页面配置F5-TTS服务端点")
                else:
                    print(f"❌ 请求错误: {error_detail}")
            except:
                print(f"❌ 请求错误: {response.text}")
            return False
            
        else:
            print(f"❌ 音色测试失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def check_f5_tts_config():
    """检查F5-TTS配置"""
    print("\n🔧 检查F5-TTS配置...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/settings")
        if response.status_code == 200:
            data = response.json()
            tts_config = data.get('data', {}).get('tts', {})
            
            provider = tts_config.get('provider')
            f5_endpoint = tts_config.get('f5TtsEndpoint')
            
            print(f"   TTS提供商: {provider}")
            print(f"   F5-TTS端点: {f5_endpoint}")
            
            if provider == 'f5-tts' and f5_endpoint:
                print("✅ F5-TTS配置正常")
                return True
            else:
                print("❌ F5-TTS配置不完整")
                return False
        else:
            print(f"❌ 获取设置失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 F5-TTS音色测试功能验证\n")
    
    # 检查配置
    config_ok = check_f5_tts_config()
    
    if config_ok:
        # 测试音色测试功能
        test_ok = test_voice_test_api()
        
        print(f"\n📊 测试总结:")
        print(f"   配置检查: {'✅' if config_ok else '❌'}")
        print(f"   音色测试: {'✅' if test_ok else '❌'}")
        
        if test_ok:
            print("\n🎉 F5-TTS音色测试功能工作正常！")
            print("现在点击前端的'测试'按钮应该会真正调用F5-TTS服务。")
        else:
            print("\n❌ F5-TTS音色测试功能存在问题，请检查配置和服务。")
    else:
        print("\n❌ 请先在设置页面配置F5-TTS服务端点。")
