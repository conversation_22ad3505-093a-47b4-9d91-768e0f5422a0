#!/usr/bin/env python3
"""
快速检查前端数据联动的核心问题
"""
import requests
import json

API_BASE = "http://localhost:8000/api"

def quick_check():
    print("🔍 快速检查前端数据联动核心问题\n")
    
    try:
        # 1. 检查素材分类API - 重点看material_count字段
        print("1️⃣ 检查素材分类API")
        response = requests.get(f"{API_BASE}/video-categories")
        if response.status_code == 200:
            categories = response.json()
            if categories:
                print(f"   ✅ 返回 {len(categories)} 个分类")
                first = categories[0]
                if 'material_count' in first:
                    print(f"   ✅ 包含 material_count: {first['material_count']}")
                else:
                    print(f"   ❌ 缺失 material_count 字段！字段: {list(first.keys())}")
            else:
                print("   ⚠️  分类列表为空")
        else:
            print(f"   ❌ API错误: {response.status_code}")
        
        # 2. 检查提示词API - 重点看category字段
        print("\n2️⃣ 检查提示词API")
        response = requests.get(f"{API_BASE}/prompts")
        if response.status_code == 200:
            prompts = response.json()
            if prompts:
                print(f"   ✅ 返回 {len(prompts)} 个提示词")
                categories = set(p.get('category', 'None') for p in prompts)
                print(f"   📂 分组: {list(categories)}")
            else:
                print("   ⚠️  提示词列表为空")
        else:
            print(f"   ❌ API错误: {response.status_code}")
        
        # 3. 检查TTS设置
        print("\n3️⃣ 检查TTS设置")
        response = requests.get(f"{API_BASE}/settings")
        if response.status_code == 200:
            settings = response.json()
            if 'tts' in settings:
                tts = settings['tts']
                print(f"   ✅ TTS配置: provider={tts.get('provider')}, voice={tts.get('voice')}")
            else:
                print("   ❌ 缺失TTS配置")
        else:
            print(f"   ❌ API错误: {response.status_code}")
        
        # 4. 检查背景音乐API - 重点看category字段
        print("\n4️⃣ 检查背景音乐API")
        response = requests.get(f"{API_BASE}/background-music")
        if response.status_code == 200:
            music = response.json()
            if music:
                print(f"   ✅ 返回 {len(music)} 首音乐")
                categories = set(m.get('category', 'None') for m in music)
                print(f"   📂 分类: {list(categories)}")
            else:
                print("   ⚠️  音乐列表为空")
        else:
            print(f"   ❌ API错误: {response.status_code}")
        
        # 5. 检查封面模板API
        print("\n5️⃣ 检查封面模板API")
        response = requests.get(f"{API_BASE}/cover-templates")
        if response.status_code == 200:
            templates = response.json()
            if templates:
                print(f"   ✅ 返回 {len(templates)} 个模板")
                first = templates[0]
                if 'id' in first and 'name' in first:
                    print(f"   ✅ 包含必需字段: id={first['id']}, name={first['name']}")
                else:
                    print(f"   ❌ 缺失必需字段！字段: {list(first.keys())}")
            else:
                print("   ⚠️  模板列表为空")
        else:
            print(f"   ❌ API错误: {response.status_code}")
        
        # 6. 检查账号API
        print("\n6️⃣ 检查账号API")
        response = requests.get(f"{API_BASE}/accounts")
        if response.status_code == 200:
            accounts = response.json()
            if accounts:
                print(f"   ✅ 返回 {len(accounts)} 个账号")
                first = accounts[0]
                if 'id' in first:
                    print(f"   ✅ 包含id字段: {first['id']}")
                else:
                    print(f"   ❌ 缺失id字段！字段: {list(first.keys())}")
            else:
                print("   ⚠️  账号列表为空")
        else:
            print(f"   ❌ API错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        print("请确保后端服务正在运行 (http://localhost:8000)")

if __name__ == "__main__":
    quick_check()
