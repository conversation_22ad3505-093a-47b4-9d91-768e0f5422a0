import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { getAllPrompts, createPrompt, updatePrompt, deletePrompt, usePrompt, type FrontendPrompt } from '@/lib/api/prompts';

export type PromptType = 'system' | 'story' | 'narration' | 'description';

export interface Prompt extends FrontendPrompt {}

export interface PromptStats {
  total: number;
  systemCount: number;
  storyCount: number;
  narrationCount: number;
  descriptionCount: number;
  tagCount: number;
  totalTestCount: number;
}

interface PromptStore {
  prompts: Prompt[];
  currentCategory: 'all' | PromptType;
  searchQuery: string;
  selectedTags: string[];
  sortBy: 'updated' | 'created' | 'name' | 'tests';
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentCategory: (category: 'all' | PromptType) => void;
  setSearchQuery: (query: string) => void;
  setSelectedTags: (tags: string[]) => void;
  setSortBy: (sortBy: 'updated' | 'created' | 'name' | 'tests') => void;
  
  // API Actions
  fetchPrompts: () => Promise<void>;
  addPrompt: (prompt: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'testCount'>) => Promise<void>;
  updatePrompt: (id: string, updates: Partial<Prompt>) => Promise<void>;
  deletePrompt: (id: string) => Promise<void>;
  incrementTestCount: (id: string) => Promise<void>;
  
  // Computed values
  getFilteredPrompts: () => Prompt[];
  getStats: () => PromptStats;
  getAllTags: () => string[];
}

export const usePromptStore = create<PromptStore>()(
  persist(
    (set, get) => ({
      prompts: [],
      currentCategory: 'all',
      searchQuery: '',
      selectedTags: [],
      sortBy: 'updated',
      isLoading: false,
      error: null,

      setCurrentCategory: (category) => set({ currentCategory: category }),
      setSearchQuery: (query) => set({ searchQuery: query }),
      setSelectedTags: (tags) => set({ selectedTags: tags }),
      setSortBy: (sortBy) => set({ sortBy }),

      fetchPrompts: async () => {
        set({ isLoading: true, error: null });
        try {
          const prompts = await getAllPrompts();
          set({ prompts, isLoading: false });
        } catch (error) {
          console.error('Failed to fetch prompts:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch prompts',
            isLoading: false 
          });
        }
      },

      addPrompt: async (promptData) => {
        set({ isLoading: true, error: null });
        try {
          const newPrompt = await createPrompt(promptData);
          if (!newPrompt) {
            throw new Error('Failed to create prompt - received null response');
          }
          set(state => ({
            prompts: [...state.prompts, newPrompt],
            isLoading: false
          }));
        } catch (error) {
          console.error('Failed to create prompt:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to create prompt',
            isLoading: false 
          });
          throw error; // Re-throw to allow UI handling
        }
      },

      updatePrompt: async (id, updates) => {
        set({ isLoading: true, error: null });
        try {
          const updatedPrompt = await updatePrompt(id, updates);
          if (!updatedPrompt) {
            throw new Error('Failed to update prompt - received null response');
          }
          set(state => ({
            prompts: state.prompts.map(prompt =>
              prompt.id === id ? updatedPrompt : prompt
            ),
            isLoading: false
          }));
        } catch (error) {
          console.error('Failed to update prompt:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update prompt',
            isLoading: false 
          });
          throw error;
        }
      },

      deletePrompt: async (id) => {
        set({ isLoading: true, error: null });
        try {
          await deletePrompt(id);
          set(state => ({
            prompts: state.prompts.filter(prompt => prompt.id !== id),
            isLoading: false
          }));
        } catch (error) {
          console.error('Failed to delete prompt:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to delete prompt',
            isLoading: false 
          });
          throw error;
        }
      },

      incrementTestCount: async (id) => {
        try {
          await usePrompt(id);
          // Update local state optimistically
          set(state => ({
            prompts: state.prompts.map(prompt =>
              prompt.id === id
                ? { ...prompt, testCount: prompt.testCount + 1 }
                : prompt
            )
          }));
        } catch (error) {
          console.error('Failed to increment test count:', error);
          // Could implement error handling here if needed
        }
      },

      getFilteredPrompts: () => {
        const { prompts, currentCategory, searchQuery, selectedTags, sortBy } = get();
        
        let filtered = prompts;

        // 按分类过滤
        if (currentCategory !== 'all') {
          filtered = filtered.filter(prompt => prompt.type === currentCategory);
        }

        // 按搜索关键词过滤
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          filtered = filtered.filter(prompt =>
            prompt.title.toLowerCase().includes(query) ||
            prompt.content.toLowerCase().includes(query) ||
            prompt.description?.toLowerCase().includes(query)
          );
        }

        // 按标签过滤
        if (selectedTags.length > 0) {
          filtered = filtered.filter(prompt =>
            selectedTags.some(tag => prompt.tags.includes(tag))
          );
        }

        // 排序
        filtered.sort((a, b) => {
          switch (sortBy) {
            case 'created':
              return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
            case 'updated':
              return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
            case 'name':
              return a.title.localeCompare(b.title);
            case 'tests':
              return b.testCount - a.testCount;
            default:
              return 0;
          }
        });

        return filtered;
      },

      getStats: () => {
        const { prompts } = get();
        
        return {
          total: prompts.length,
          systemCount: prompts.filter(p => p.type === 'system').length,
          storyCount: prompts.filter(p => p.type === 'story').length,
          narrationCount: prompts.filter(p => p.type === 'narration').length,
          descriptionCount: prompts.filter(p => p.type === 'description').length,
          tagCount: new Set(prompts.flatMap(p => p.tags)).size,
          totalTestCount: prompts.reduce((sum, p) => sum + p.testCount, 0)
        };
      },

      getAllTags: () => {
        const { prompts } = get();
        return Array.from(new Set(prompts.flatMap(p => p.tags))).sort();
      }
    }),
    {
      name: 'prompt-store',
      version: 2, // Increment version to clear old data
      skipHydration: true, // 跳过服务端水合，防止 SSR 错误
      partialize: (state) => ({
        // 只持久化这些字段，不持久化运行时状态
        currentCategory: state.currentCategory,
        searchQuery: state.searchQuery,
        selectedTags: state.selectedTags,
        sortBy: state.sortBy,
      }),
    }
  )
);
