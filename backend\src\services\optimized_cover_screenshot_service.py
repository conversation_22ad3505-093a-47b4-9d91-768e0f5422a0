"""
优化版封面截图服务
专注于性能和稳定性改进
"""

import os
import sys
import asyncio
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional
from uuid import uuid4
import logging
import json
import time

from sqlalchemy.orm import Session

from ..models.resources import CoverTemplate
from ..models.accounts import Account
from .template_import_service import template_import_service

logger = logging.getLogger(__name__)

class OptimizedCoverScreenshotService:
    """优化版封面截图服务"""
    
    def __init__(self):
        self.timeout_seconds = 45  # 减少超时时间
        self.max_retries = 2
        
    def _create_optimized_screenshot_script(self, html_content: str, output_path: str, fast_mode: bool = True) -> str:
        """创建优化的截图脚本"""
        script_content = f'''
import sys
import os
from playwright.sync_api import sync_playwright
from pathlib import Path
import time

# 确保输出使用UTF-8编码
if os.name == 'nt':  # Windows系统
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def main():
    html_content = """{html_content}"""
    output_path = r"{output_path}"
    fast_mode = {fast_mode}
    
    try:
        with sync_playwright() as p:
            # 优化的浏览器启动参数
            browser_args = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
            
            if not fast_mode:
                browser_args.append('--force-device-scale-factor=2')
            
            browser = p.chromium.launch(headless=True, args=browser_args)
            context = browser.new_context(
                viewport={{"width": 1080, "height": 1920}},
                device_scale_factor=2 if not fast_mode else 1
            )
            page = context.new_page()
            
            # 快速模式：减少网络监听
            if not fast_mode:
                def handle_request_failed(request):
                    print(f"网络请求失败: {{request.url}}", file=sys.stderr)
                page.on("requestfailed", handle_request_failed)
            
            # 设置页面内容
            page.set_content(html_content, wait_until="domcontentloaded", timeout=15000)
            
            if fast_mode:
                # 快速模式：只等待基本加载
                page.wait_for_timeout(2000)
            else:
                # 标准模式：等待图片加载
                try:
                    # 预加载图片
                    page.evaluate("""
                        () => {{
                            const images = Array.from(document.querySelectorAll('img'));
                            images.forEach(img => {{
                                if (img.src && !img.complete) {{
                                    const newImg = new Image();
                                    newImg.src = img.src;
                                }}
                            }});
                        }}
                    """)
                    
                    # 等待图片加载
                    page.wait_for_function("""
                        () => {{
                            const images = Array.from(document.querySelectorAll('img'));
                            if (images.length === 0) return true;
                            
                            let loaded = 0;
                            images.forEach(img => {{
                                if (img.complete && (img.naturalWidth > 0 || img.src.startsWith('data:'))) {{
                                    loaded++;
                                }}
                            }});
                            
                            return loaded === images.length;
                        }}
                    """, timeout=15000)
                    
                    page.wait_for_timeout(1500)
                except:
                    # 如果图片加载超时，继续执行
                    page.wait_for_timeout(3000)
            
            # 查找目标元素并截图
            cover_element = page.query_selector("#reddit-cover")
            
            # 创建输出目录
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            if not cover_element:
                # 如果找不到reddit-cover元素，截图整个页面
                page.screenshot(
                    path=output_path,
                    type='png',
                    full_page=True
                )
            else:
                # 截图目标元素
                cover_element.screenshot(
                    path=output_path,
                    type='png',
                    omit_background=False,
                    animations='disabled'
                )
            
            browser.close()
            
            # 验证文件生成
            if Path(output_path).exists() and Path(output_path).stat().st_size > 1000:
                print(f"SUCCESS: {{output_path}}")
                sys.exit(0)
            else:
                print("ERROR: 生成的文件无效", file=sys.stderr)
                sys.exit(1)
            
    except Exception as e:
        print(f"ERROR: {{e}}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        
        # 创建临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(script_content)
            return f.name
    
    async def generate_cover_screenshot(
        self,
        template_id: str,
        account: Account,
        title: str,
        output_path: str,
        db: Session,
        additional_variables: Optional[Dict[str, str]] = None,
        fast_mode: bool = False
    ) -> bool:
        """
        生成封面截图 - 优化版本
        
        Args:
            template_id: 模板ID
            account: 账号信息
            title: 标题文本
            output_path: 输出路径
            db: 数据库会话
            additional_variables: 额外的变量
            fast_mode: 快速模式（牺牲一些质量换取速度）
            
        Returns:
            bool: 是否成功生成
        """
        script_path = None
        retry_count = 0
        
        while retry_count <= self.max_retries:
            try:
                if retry_count > 0:
                    logger.info(f"重试生成截图，第 {retry_count} 次")
                
                logger.info(f"开始生成封面截图: template_id={template_id}, fast_mode={fast_mode}")
                
                # 准备头像路径
                avatar_path = self._get_avatar_path(account)
                
                # 准备变量数据
                variables = {
                    'avatar': avatar_path,
                    'account_name': account.name,
                    'title': title,
                    'description': title[:100] + '...' if len(title) > 100 else title,
                }
                
                # 添加额外变量
                if additional_variables:
                    variables.update(additional_variables)
                
                # 渲染模板
                rendered_html = template_import_service.render_template(
                    template_id=template_id,
                    variables=variables,
                    db=db,
                    base_url="http://localhost:8000"
                )
                
                # 转义HTML内容
                escaped_html = rendered_html.replace('"""', '\\"\\"\\"').replace('\\', '\\\\')
                
                # 创建截图脚本
                script_path = self._create_optimized_screenshot_script(
                    escaped_html, 
                    output_path, 
                    fast_mode or retry_count > 0  # 重试时使用快速模式
                )
                
                # 运行截图脚本
                timeout = self.timeout_seconds - (retry_count * 10)  # 重试时减少超时时间
                
                result = subprocess.run([
                    sys.executable, script_path
                ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=timeout)
                
                if result.returncode == 0:
                    # 验证输出文件
                    if Path(output_path).exists() and Path(output_path).stat().st_size > 1000:
                        logger.info(f"成功生成封面截图: {output_path}")
                        return True
                    else:
                        logger.warning("截图文件生成但无效")
                        retry_count += 1
                        continue
                else:
                    logger.error(f"截图脚本执行失败 (退出码: {result.returncode})")
                    logger.error(f"标准错误输出: {result.stderr}")
                    if result.stdout:
                        logger.error(f"标准输出: {result.stdout}")
                    
                    retry_count += 1
                    continue
                
            except subprocess.TimeoutExpired:
                logger.error(f"截图脚本执行超时 (第 {retry_count + 1} 次尝试)")
                retry_count += 1
                continue
                
            except Exception as e:
                logger.error(f"生成封面截图失败: {e}")
                retry_count += 1
                if retry_count > self.max_retries:
                    break
                continue
                
            finally:
                # 清理临时脚本文件
                if script_path and os.path.exists(script_path):
                    try:
                        os.remove(script_path)
                        script_path = None
                    except:
                        pass
        
        # 所有重试都失败了
        logger.error(f"经过 {self.max_retries + 1} 次尝试，封面截图生成失败")
        return False
    
    async def batch_generate_screenshots(
        self,
        requests: list,
        db: Session,
        max_concurrent: int = 2
    ) -> Dict[str, bool]:
        """
        批量生成截图，限制并发数避免资源争用
        
        Args:
            requests: 截图请求列表，每个请求包含所需参数
            db: 数据库会话
            max_concurrent: 最大并发数
            
        Returns:
            Dict[str, bool]: 每个请求的结果
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        results = {}
        
        async def generate_single(request):
            async with semaphore:
                try:
                    request_id = request.get('id', str(uuid4()))
                    success = await self.generate_cover_screenshot(
                        template_id=request['template_id'],
                        account=request['account'],
                        title=request['title'],
                        output_path=request['output_path'],
                        db=db,
                        additional_variables=request.get('additional_variables'),
                        fast_mode=request.get('fast_mode', False)
                    )
                    results[request_id] = success
                    return success
                except Exception as e:
                    logger.error(f"批量截图请求失败: {e}")
                    results[request.get('id', 'unknown')] = False
                    return False
        
        # 执行所有请求
        tasks = [generate_single(req) for req in requests]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    def _get_avatar_path(self, account) -> str:
        """获取头像路径 - 优化版本"""
        try:
            if hasattr(account, 'avatar_file_path') and account.avatar_file_path:
                from pathlib import Path
                import base64
                import mimetypes
                
                backend_dir = Path(__file__).parent.parent.parent
                avatar_file_path = backend_dir / account.avatar_file_path
                
                if avatar_file_path.exists() and avatar_file_path.is_file():
                    try:
                        # 检查文件大小限制
                        file_size = avatar_file_path.stat().st_size
                        if file_size > 2 * 1024 * 1024:  # 2MB限制，比原来更严格
                            logger.warning(f"头像文件过大: {file_size} bytes")
                            return self._get_default_avatar()
                        
                        # 读取并编码
                        with open(avatar_file_path, 'rb') as f:
                            file_data = f.read()
                        
                        mime_type, _ = mimetypes.guess_type(str(avatar_file_path))
                        if not mime_type or not mime_type.startswith('image/'):
                            mime_type = 'image/png'
                        
                        base64_data = base64.b64encode(file_data).decode('utf-8')
                        return f"data:{mime_type};base64,{base64_data}"
                        
                    except Exception as e:
                        logger.error(f"读取头像文件失败: {e}")
                        return self._get_default_avatar()
                else:
                    return self._get_default_avatar()
            
            return self._get_default_avatar()
            
        except Exception as e:
            logger.error(f"处理头像路径时出错: {e}")
            return self._get_default_avatar()
    
    def _get_default_avatar(self) -> str:
        """获取默认头像"""
        return 'https://via.placeholder.com/50/666666/FFFFFF?text=U'
    
    async def cleanup(self):
        """清理资源"""
        pass

# 创建优化版实例
optimized_cover_screenshot_service = OptimizedCoverScreenshotService()
