#!/usr/bin/env python3
"""
封面叠加测试脚本
用于快速测试视频和封面的合成功能
"""

import sys
import os
import asyncio
from pathlib import Path
import ffmpeg
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.services.video_generation_helpers import VideoCompositionService

async def test_cover_overlay(video_path: str, cover_path: str, output_path: str):
    """
    测试封面叠加功能
    
    Args:
        video_path: 输入视频文件路径
        cover_path: 封面图片文件路径
        output_path: 输出视频文件路径
    """
    try:
        # 检查输入文件是否存在
        if not Path(video_path).exists():
            logger.error(f"视频文件不存在: {video_path}")
            return False
            
        if not Path(cover_path).exists():
            logger.error(f"封面文件不存在: {cover_path}")
            return False
        
        logger.info(f"开始测试封面叠加")
        logger.info(f"视频文件: {video_path}")
        logger.info(f"封面文件: {cover_path}")
        logger.info(f"输出文件: {output_path}")
        
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        duration = float(video_info['duration'])
        
        logger.info(f"视频信息: {width}x{height}, 时长: {duration}s")
        
        # 创建视频流
        video_stream = ffmpeg.input(video_path)
        
        # 创建封面叠加层
        cover_width = int(width * 0.8)  # 80% of video width
        logger.info(f"封面宽度: {cover_width}px")
        
        # 创建基础缩放后的封面
        cover_scaled = (
            ffmpeg
            .input(filename=cover_path)
            .filter('scale', cover_width, -1)  # -1 表示高度自动计算以保持纵横比
        )
        
        # 测试不同的封面配置
        test_configs = [
            {
                'name': '居中无动画',
                'position': 'center',
                'animation': 'none',
                'duration': min(5.0, duration)
            },
            {
                'name': '居中淡入淡出',
                'position': 'center', 
                'animation': 'fade_in_out',
                'duration': min(5.0, duration)
            },
            {
                'name': '上方淡入',
                'position': 'top',
                'animation': 'fade_in',
                'duration': min(3.0, duration)
            }
        ]
        
        for i, config in enumerate(test_configs):
            test_output = output_path.replace('.mp4', f'_test_{i+1}_{config["name"]}.mp4')
            logger.info(f"\n=== 测试 {i+1}: {config['name']} ===")
            
            try:
                # 应用封面叠加
                cover_settings = {
                    'position': config['position'],
                    'animation': config['animation'],
                    'animation_duration': 0.5
                }
                
                video_with_cover = VideoCompositionService._apply_cover_overlay(
                    video_stream, cover_scaled, config['duration'], cover_settings
                )
                
                # 输出视频
                ffmpeg_stream = (
                    ffmpeg
                    .output(video_with_cover, test_output, vcodec='libx264', preset='fast', pix_fmt='yuv420p')
                    .overwrite_output()
                )
                
                logger.info(f"开始生成测试视频: {test_output}")
                ffmpeg_stream.run(quiet=True)
                
                if Path(test_output).exists():
                    file_size = Path(test_output).stat().st_size
                    logger.info(f"✅ 测试 {i+1} 成功! 文件大小: {file_size} bytes")
                else:
                    logger.error(f"❌ 测试 {i+1} 失败: 输出文件不存在")
                    
            except Exception as e:
                logger.error(f"❌ 测试 {i+1} 失败: {e}")
                
        logger.info("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 4:
        print("用法: python test_cover_overlay.py <视频文件> <封面文件> <输出文件>")
        print("示例: python test_cover_overlay.py input.mp4 cover.jpg output.mp4")
        sys.exit(1)
    
    video_path = sys.argv[1]
    cover_path = sys.argv[2] 
    output_path = sys.argv[3]
    
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")
    
    # 运行测试
    asyncio.run(test_cover_overlay(video_path, cover_path, output_path))

if __name__ == "__main__":
    main()
