#!/usr/bin/env python3
"""
检查数据库中的视频素材表
"""

import sqlite3
import os

def check_video_materials_table():
    # 数据库路径
    db_path = 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db'
    if not os.path.exists(db_path):
        print('数据库文件不存在:', db_path)
        return

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='video_materials'")
        table_exists = cursor.fetchone()

        if table_exists:
            print('✓ video_materials 表存在')
            
            # 查看表结构
            cursor.execute('PRAGMA table_info(video_materials)')
            columns = cursor.fetchall()
            print('表结构:')
            for col in columns:
                print(f'  {col[1]} ({col[2]})')
            
            # 查看数据行数
            cursor.execute('SELECT COUNT(*) FROM video_materials')
            count = cursor.fetchone()[0]
            print(f'数据行数: {count}')
            
            if count > 0:
                # 显示前几行数据
                cursor.execute('SELECT * FROM video_materials LIMIT 3')
                rows = cursor.fetchall()
                print('前3行数据:')
                for row in rows:
                    print(f'  {row}')
        else:
            print('✗ video_materials 表不存在')
            
            # 列出所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print('数据库中的表:')
            for table in tables:
                print(f'  {table[0]}')

    except Exception as e:
        print(f'检查数据库时出错: {e}')
    finally:
        conn.close()

if __name__ == "__main__":
    check_video_materials_table()
