#!/usr/bin/env python3
"""
测试修复后的头像处理逻辑
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

from src.core.database import get_db_session
from src.models.accounts import Account
from src.services.cover_screenshot_service import CoverScreenshotService

async def test_avatar_handling():
    """测试头像处理"""
    print("=== 测试头像处理逻辑 ===")
    
    db = get_db_session()
    try:
        # 获取第一个账号
        account = db.query(Account).first()
        if not account:
            print("未找到账号")
            return
        
        print(f"账号信息:")
        print(f"  ID: {account.id}")
        print(f"  名称: {account.name}")
        print(f"  avatar_url: {account.avatar_url}")
        print(f"  avatar_file_path: {account.avatar_file_path}")
        
        # 测试头像路径处理
        service = CoverScreenshotService()
        avatar_path = service._get_avatar_path(account)
        
        print(f"\n处理后的头像路径: {avatar_path}")
        
        # 如果是file://格式，检查文件是否真的存在
        if avatar_path.startswith('file://'):
            from urllib.parse import urlparse
            parsed = urlparse(avatar_path)
            local_path = parsed.path
            if os.name == 'nt':  # Windows
                local_path = local_path[1:]  # 去掉开头的斜杠
            exists = os.path.exists(local_path)
            print(f"本地文件存在: {exists}")
            if exists:
                print(f"文件大小: {os.path.getsize(local_path)} bytes")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_avatar_handling())
