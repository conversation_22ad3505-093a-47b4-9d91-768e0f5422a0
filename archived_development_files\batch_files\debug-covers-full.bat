@echo off
echo ===================================
echo 封面模板API调试脚本
echo ===================================

echo.
echo 1. 检查数据库中的模板数据...
python check_db_templates.py

echo.
echo 2. 启动后端服务...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend"
start "Backend Server" python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

echo 等待后端服务启动...
timeout /t 8

echo.
echo 3. 测试API端点...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator"
python debug_cover_templates_api.py

echo.
echo 4. 启动前端开发服务器...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\frontend"
start "Frontend Server" npm run dev

echo.
echo 前后端服务已启动，请在浏览器中访问：
echo 前端: http://localhost:3000/covers
echo 后端API: http://localhost:8000/api/cover-templates
echo.

pause
