#!/usr/bin/env python3
"""
测试模板复制功能
"""

import requests
import json
import time

# 配置
BACKEND_URL = "http://localhost:8000"
API_BASE = f"{BACKEND_URL}/api"

def test_template_copy():
    """测试模板复制功能"""
    print("🧪 测试模板复制功能...")
    
    # 第一步：创建一个带有复杂数据的模板
    original_template = {
        "name": "原始测试模板",
        "category": "现代",
        "description": "这是一个用于测试复制功能的模板",
        "variables": [
            {
                "name": "title",
                "type": "text",
                "label": "标题",
                "defaultValue": "示例标题"
            },
            {
                "name": "subtitle",
                "type": "text", 
                "label": "副标题",
                "defaultValue": "示例副标题"
            }
        ],
        "elements": [
            {
                "id": "text1",
                "type": "text",
                "x": 100,
                "y": 100,
                "width": 300,
                "height": 50,
                "content": "主标题",
                "style": {
                    "fontSize": 24,
                    "fontWeight": "bold",
                    "color": "#333333"
                }
            },
            {
                "id": "text2", 
                "type": "text",
                "x": 100,
                "y": 200,
                "width": 250,
                "height": 40,
                "content": "副标题",
                "style": {
                    "fontSize": 16,
                    "color": "#666666"
                }
            }
        ],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#FF6B6B", "#4ECDC4"]
            }
        },
        "is_built_in": False,
        "width": 1920,
        "height": 1080,
        "format": "png"
    }
    
    # 创建原始模板
    try:
        response = requests.post(
            f"{API_BASE}/cover-templates",
            json=original_template,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ 创建原始模板失败: {response.status_code} - {response.text}")
            return False
            
        result = response.json()
        template_data = result.get('data', result)
        template_id = template_data.get('id')
        print(f"✅ 原始模板创建成功，ID: {template_id}")
        
    except Exception as e:
        print(f"❌ 创建原始模板时发生错误: {e}")
        return False
    
    # 第二步：获取模板详情来验证数据完整性
    try:
        response = requests.get(f"{API_BASE}/cover-templates/{template_id}")
        
        if response.status_code != 200:
            print(f"❌ 获取模板详情失败: {response.status_code}")
            return False
            
        result = response.json()
        template_details = result.get('data', result)
        
        print(f"📋 原始模板详情:")
        print(f"   名称: {template_details.get('name')}")
        print(f"   元素数量: {len(template_details.get('elements', []))}")
        print(f"   变量数量: {len(template_details.get('variables', []))}")
        print(f"   背景类型: {template_details.get('background', {}).get('type')}")
        
        # 验证elements和background字段
        elements = template_details.get('elements', [])
        background = template_details.get('background', {})
        
        if not elements:
            print("⚠️ 警告：原始模板没有elements数据")
        if not background:
            print("⚠️ 警告：原始模板没有background数据")
            
        print("✅ 模板详情获取成功")
        
    except Exception as e:
        print(f"❌ 获取模板详情时发生错误: {e}")
        return False
    
    # 第三步：创建复制模板
    copy_template = {
        **template_details,
        "name": f"{template_details.get('name')} - 副本",
        "description": f"复制自 {template_details.get('name')}",
        "is_built_in": False
    }
    
    # 删除不需要的字段
    for field in ['id', 'createdAt', 'updatedAt', 'usageCount', 'thumbnailPath', 'thumbnailUrl', 'elementCount', 'variableCount', 'hasVariables', 'canvasSize', 'isPublic']:
        copy_template.pop(field, None)
    
    try:
        response = requests.post(
            f"{API_BASE}/cover-templates",
            json=copy_template,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ 创建复制模板失败: {response.status_code} - {response.text}")
            return False
            
        result = response.json()
        copy_data = result.get('data', result)
        copy_id = copy_data.get('id')
        print(f"✅ 复制模板创建成功，ID: {copy_id}")
        
    except Exception as e:
        print(f"❌ 创建复制模板时发生错误: {e}")
        return False
    
    # 第四步：验证复制的模板数据
    try:
        response = requests.get(f"{API_BASE}/cover-templates/{copy_id}")
        
        if response.status_code != 200:
            print(f"❌ 获取复制模板详情失败: {response.status_code}")
            return False
            
        result = response.json()
        copy_details = result.get('data', result)
        
        print(f"📋 复制模板详情:")
        print(f"   名称: {copy_details.get('name')}")
        print(f"   元素数量: {len(copy_details.get('elements', []))}")
        print(f"   变量数量: {len(copy_details.get('variables', []))}")
        print(f"   背景类型: {copy_details.get('background', {}).get('type')}")
        
        # 比较复制前后的数据
        original_elements = template_details.get('elements', [])
        copy_elements = copy_details.get('elements', [])
        
        original_variables = template_details.get('variables', [])
        copy_variables = copy_details.get('variables', [])
        
        original_background = template_details.get('background', {})
        copy_background = copy_details.get('background', {})
        
        success = True
        
        if len(original_elements) != len(copy_elements):
            print(f"❌ 元素数量不匹配: 原始 {len(original_elements)} vs 复制 {len(copy_elements)}")
            success = False
        else:
            print(f"✅ 元素数量匹配: {len(copy_elements)} 个")
            
        if len(original_variables) != len(copy_variables):
            print(f"❌ 变量数量不匹配: 原始 {len(original_variables)} vs 复制 {len(copy_variables)}")
            success = False
        else:
            print(f"✅ 变量数量匹配: {len(copy_variables)} 个")
            
        if original_background.get('type') != copy_background.get('type'):
            print(f"❌ 背景类型不匹配: 原始 {original_background.get('type')} vs 复制 {copy_background.get('type')}")
            success = False
        else:
            print(f"✅ 背景类型匹配: {copy_background.get('type')}")
        
        if success:
            print("🎉 模板复制功能测试成功！所有数据都正确复制了。")
        else:
            print("❌ 模板复制功能测试失败！部分数据未正确复制。")
            
        return success
        
    except Exception as e:
        print(f"❌ 验证复制模板时发生错误: {e}")
        return False

def check_backend():
    """检查后端是否运行"""
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    print("🚀 开始测试模板复制功能...")
    
    # 检查后端是否运行
    if not check_backend():
        print("❌ 后端服务未运行，请先启动后端服务")
        print("提示：在 backend 目录下运行: python -m uvicorn src.main:app --reload")
        return 1
    
    # 测试复制功能
    success = test_template_copy()
    
    if success:
        print("✅ 所有测试通过！")
        return 0
    else:
        print("❌ 测试失败！")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
