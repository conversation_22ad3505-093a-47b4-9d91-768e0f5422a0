#!/usr/bin/env python3
"""
调试动画效果问题 - 测试slide_in_left是否正确传递
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_animation_debug():
    """调试动画效果传递问题"""
    
    # 测试文件路径
    video_path = "test.mp4"
    cover_path = "test.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔍 开始调试动画效果传递问题...")
    
    # 测试不同的cover_settings格式
    test_configs = [
        {
            'name': '字符串格式测试',
            'cover_settings': {
                'position': 'center',
                'animation': 'slide_in_left',  # 字符串格式
                'animation_duration': 1.5
            },
            'output': 'debug_string_format.mp4'
        },
        {
            'name': '枚举格式测试',
            'cover_settings': {
                'position': 'center',
                'animation': 'fade_in',  # 对比用fade_in
                'animation_duration': 1.5
            },
            'output': 'debug_enum_format.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.6)
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"cover_settings: {config['cover_settings']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"debug_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    logger.info("🔍 调用_apply_cover_overlay方法...")
                    start_time = time.time()
                    
                    # 应用封面叠加效果 - 这里会触发调试日志
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, 4.0, config['cover_settings']
                    )
                    
                    # 输出视频
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=5.0
                    ).overwrite_output()
                    
                    # 执行，设置超时
                    import subprocess
                    
                    cmd = ffmpeg.compile(out)
                    
                    # 使用subprocess执行，设置超时
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    try:
                        # 等待最多30秒
                        stdout, stderr = process.communicate(timeout=30)
                        
                        if process.returncode == 0:
                            end_time = time.time()
                            processing_time = end_time - start_time
                            
                            # 检查结果
                            if Path(config['output']).exists():
                                file_size = Path(config['output']).stat().st_size
                                logger.info(f"✅ {config['name']} 测试成功!")
                                logger.info(f"   文件大小: {file_size} bytes")
                                logger.info(f"   处理时间: {processing_time:.2f}秒")
                                success_count += 1
                            else:
                                logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        else:
                            logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                            
                    except subprocess.TimeoutExpired:
                        logger.error(f"❌ {config['name']} 测试超时（30秒）")
                        process.kill()
                        process.communicate()
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"\n=== 动画调试测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count > 0:
            logger.info("\n📋 调试测试文件:")
            logger.info("- debug_string_format.mp4 (字符串格式)")
            logger.info("- debug_enum_format.mp4 (枚举格式)")
            
            logger.info("\n🔍 请检查上面的调试日志:")
            logger.info("1. 查看'🔍 调试'开头的日志行")
            logger.info("2. 确认animation值是否正确传递")
            logger.info("3. 查看是否有默认值回退的情况")
            
            return True
        else:
            logger.error("❌ 所有调试测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 调试测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("🚀 开始动画效果调试测试")
    logger.info("目标：找出为什么slide_in_left变成了fade_in_out")
    
    success = test_animation_debug()
    
    if success:
        logger.info("\n🎉 动画调试测试完成!")
        logger.info("请查看上面的调试日志，找出问题原因。")
    else:
        logger.error("\n❌ 动画调试测试失败")
        sys.exit(1)
