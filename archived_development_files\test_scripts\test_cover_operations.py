#!/usr/bin/env python3
"""
测试封面模板操作功能
"""

import requests
import json
import time
import subprocess
import sys
from pathlib import Path

# 配置
BACKEND_URL = "http://localhost:8000"
API_BASE = f"{BACKEND_URL}/api"

def test_create_template():
    """测试创建模板"""
    print("🧪 测试创建模板...")
    
    template_data = {
        "name": "测试模板",
        "category": "现代",
        "description": "这是一个测试模板",
        "variables": [],
        "elements": [],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        },
        "is_built_in": False,
        "width": 1920,
        "height": 1080,
        "format": "png"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/cover-templates",
            json=template_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 创建模板成功: {result}")
            return result.get('data', {}).get('id')
        else:
            print(f"❌ 创建模板失败: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_list_templates():
    """测试获取模板列表"""
    print("🧪 测试获取模板列表...")
    
    try:
        response = requests.get(f"{API_BASE}/cover-templates")
        
        if response.status_code == 200:
            result = response.json()
            templates = result.get('data', {}).get('templates', [])
            print(f"✅ 获取模板列表成功，共 {len(templates)} 个模板")
            
            for template in templates:
                print(f"  - {template.get('name')} ({template.get('id')})")
            
            return templates
        else:
            print(f"❌ 获取模板列表失败: {response.status_code} - {response.text}")
            return []
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return []

def test_delete_template(template_id):
    """测试删除模板"""
    print(f"🧪 测试删除模板 {template_id}...")
    
    try:
        response = requests.delete(f"{API_BASE}/cover-templates/{template_id}")
        
        if response.status_code == 200:
            print(f"✅ 删除模板成功")
            return True
        else:
            print(f"❌ 删除模板失败: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def check_backend():
    """检查后端是否运行"""
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    print("🚀 开始测试封面模板操作...")
    
    # 检查后端是否运行
    if not check_backend():
        print("❌ 后端服务未运行，请先启动后端服务")
        print("提示：在 backend 目录下运行: python -m uvicorn src.main:app --reload")
        return 1
    
    # 测试获取模板列表
    initial_templates = test_list_templates()
    
    # 测试创建模板
    template_id = test_create_template()
    
    if template_id:
        # 再次获取模板列表，验证新模板是否创建成功
        updated_templates = test_list_templates()
        
        if len(updated_templates) > len(initial_templates):
            print("✅ 模板创建验证成功")
        else:
            print("⚠️ 模板创建可能未生效")
        
        # 测试删除模板
        test_delete_template(template_id)
        
        # 再次获取模板列表，验证模板是否删除成功
        final_templates = test_list_templates()
        
        if len(final_templates) == len(initial_templates):
            print("✅ 模板删除验证成功")
        else:
            print("⚠️ 模板删除可能未生效")
    
    print("🎉 测试完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
