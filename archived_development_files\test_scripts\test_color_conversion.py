"""
测试HTML颜色到ASS颜色的转换
"""

def html_color_to_ass(html_color):
    """将HTML颜色转换为ASS格式"""
    if html_color.startswith('#'):
        html_color = html_color[1:]
    r = int(html_color[0:2], 16)
    g = int(html_color[2:4], 16) 
    b = int(html_color[4:6], 16)
    return f"&H00{b:02X}{g:02X}{r:02X}"

# 测试颜色转换
colors = [
    ('#FFFFFF', '白色'),
    ('#FF0000', '红色'),
    ('#00FF00', '绿色'),
    ('#0000FF', '蓝色'),
    ('#FFFF00', '黄色'),
    ('#FF00FF', '紫色'),
    ('#00FFFF', '青色'),
]

print("🎨 HTML颜色到ASS颜色转换测试:")
print("HTML颜色 -> ASS颜色 (描述)")
print("-" * 40)

for html_color, description in colors:
    ass_color = html_color_to_ass(html_color)
    print(f"{html_color} -> {ass_color} ({description})")

print("\n💡 ASS颜色格式说明:")
print("ASS使用BGR顺序，格式为 &H00BBGGRR")
print("其中BB=蓝色, GG=绿色, RR=红色")
