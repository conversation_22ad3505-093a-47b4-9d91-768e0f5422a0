@echo off
chcp 65001 >nul
echo ================================
echo Reddit Story Video Generator
echo Backend Fix & Test 
echo ================================
echo.

set "PROJECT_ROOT=%~dp0"
set "BACKEND_DIR=%PROJECT_ROOT%backend"

echo [1/7] 检查后端目录...
if not exist "%BACKEND_DIR%" (
    echo ❌ Backend 目录不存在: %BACKEND_DIR%
    pause
    exit /b 1
)

echo [2/7] 检查 Python...
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python 未安装。请先安装 Python 3.11+
    pause
    exit /b 1
)

echo [3/7] 进入后端目录...
cd /d "%BACKEND_DIR%"

echo [4/7] 创建/激活虚拟环境...
if not exist "venv" (
    echo   → 创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
)

echo   → 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

echo [5/7] 安装依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo [6/7] 依赖检查...
echo   → 检查 FastAPI...
python -c "import fastapi; print(f'FastAPI {fastapi.__version__} ✅')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ FastAPI 未正确安装
    pip install fastapi uvicorn
)

echo   → 检查 uvicorn...
python -c "import uvicorn; print(f'Uvicorn ✅')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Uvicorn 未正确安装
    pip install uvicorn
)

echo [7/7] 启动后端服务器...
echo.
echo ================================
echo 🚀 启动后端服务器
echo ================================
echo.
echo 🔧 修复说明:
echo   • 使用简化版本 simple_main.py
echo   • 修复了模块导入问题
echo   • 基本 API 端点可用
echo.
echo 📍 测试 URLs:
echo   • Health: http://localhost:8000/api/health
echo   • Test:   http://localhost:8000/api/test
echo   • Docs:   http://localhost:8000/docs
echo.
echo 按 Ctrl+C 停止服务器
echo.

python simple_main.py
if %errorlevel% neq 0 (
    echo.
    echo ⚠️ 简化版本启动失败，检查错误...
    pause
)
