"""
测试修复后的封面截图功能
"""

import asyncio
import os
import sys
from pathlib import Path

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate
from backend.src.services.cover_screenshot_service import cover_screenshot_service

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_fixed_screenshot():
    """测试修复后的截图功能"""
    db = SessionLocal()
    try:
        print("=== 测试修复后的封面截图功能 ===")
        
        # 获取测试资源
        account = db.query(Account).first()
        template = db.query(CoverTemplate).first()
        
        if not account or not template:
            print("❌ 缺少必要的测试资源")
            return
        
        print(f"✅ 使用账号: {account.name}")
        print(f"✅ 使用模板: {template.name}")
        
        output_path = "test_outputs/fixed_screenshot_test.png"
        
        print(f"🔄 开始生成封面截图...")
        
        # 调用封面生成函数
        success = await cover_screenshot_service.generate_cover_screenshot(
            template_id=template.id,
            account=account,
            title="这是修复后的测试标题",
            output_path=output_path,
            db=db
        )
        
        if success and Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"✅ 封面生成成功!")
            print(f"   输出路径: {output_path}")
            print(f"   文件大小: {file_size} 字节")
        else:
            print("❌ 封面生成失败 - 任务应该终止")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_fixed_screenshot())
