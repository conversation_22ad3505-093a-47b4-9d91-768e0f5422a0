#!/usr/bin/env python3
"""
测试后端导入问题
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

print("Testing imports...")

try:
    print("1. Testing basic imports...")
    from fastapi import FastAPI
    print("✓ FastAPI imported successfully")
    
    print("2. Testing src.core imports...")
    from src.core.config import get_settings
    print("✓ src.core.config imported successfully")
    
    print("3. Testing src.core.database imports...")
    from src.core.database import init_db
    print("✓ src.core.database imported successfully")
    
    print("4. Testing src.api.routes imports...")
    from src.api.routes import api_router
    print("✓ src.api.routes imported successfully")
    
    print("5. Testing src.api.video imports...")
    from src.api.video import router as video_router
    print("✓ src.api.video imported successfully")
    
    print("6. Testing src.models.resources imports...")
    from src.models.resources import VideoMaterial
    print("✓ src.models.resources imported successfully")
    
    print("7. Testing src.schemas.resources imports...")
    from src.schemas.resources import VideoMaterialResponse
    print("✓ src.schemas.resources imported successfully")
    
    print("\n✓ All imports successful!")
    
except Exception as e:
    print(f"✗ Import failed: {e}")
    import traceback
    traceback.print_exc()
