/**
 * 全局加载状态管理
 * 用于控制全局loading效果，阻止用户操作
 */

import { create } from 'zustand'

export interface LoadingTask {
  id: string
  type: 'upload' | 'download' | 'processing' | 'general'
  title: string
  description?: string
  progress?: number
  startTime: number
  estimatedDuration?: number // 预估总时长（秒）
  simulateProgress?: boolean // 是否模拟进度
}

interface GlobalLoadingState {
  // 状态
  isGlobalLoading: boolean
  currentTasks: LoadingTask[]
  
  // 操作方法
  startGlobalLoading: (task: Omit<LoadingTask, 'id' | 'startTime'>) => string
  updateTaskProgress: (taskId: string, progress: number, description?: string) => void
  finishTask: (taskId: string) => void
  finishAllTasks: () => void
  
  // 查询方法
  getActiveTask: () => LoadingTask | null
  getTotalProgress: () => number
  isTaskActive: (taskId: string) => boolean
  
  // 内部方法
  _simulateProgress: (taskId: string) => void
}

export const useGlobalLoadingStore = create<GlobalLoadingState>((set, get) => ({
  // 初始状态
  isGlobalLoading: false,
  currentTasks: [],

  // 开始全局loading任务
  startGlobalLoading: (task) => {
    const taskId = `${task.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newTask: LoadingTask = {
      ...task,
      id: taskId,
      startTime: Date.now(),
      progress: task.progress || 0,
      estimatedDuration: task.estimatedDuration || 10, // 默认10秒
      simulateProgress: task.simulateProgress !== false // 默认开启模拟进度
    }

    set((state) => ({
      isGlobalLoading: true,
      currentTasks: [...state.currentTasks, newTask]
    }))

    console.log('🟢 开始全局loading任务:', newTask)
    
    // 如果启用模拟进度，开始模拟
    if (newTask.simulateProgress) {
      get()._simulateProgress(taskId)
    }
    
    return taskId
  },

  // 更新任务进度
  updateTaskProgress: (taskId, progress, description) => {
    set((state) => ({
      currentTasks: state.currentTasks.map(task =>
        task.id === taskId
          ? { ...task, progress, description: description || task.description }
          : task
      )
    }))

    console.log(`🟢 更新任务进度 ${taskId}: ${progress}%`, description)
  },

  // 完成任务
  finishTask: (taskId) => {
    set((state) => {
      const remainingTasks = state.currentTasks.filter(task => task.id !== taskId)
      return {
        currentTasks: remainingTasks,
        isGlobalLoading: remainingTasks.length > 0
      }
    })

    console.log('🟢 完成任务:', taskId)
  },

  // 完成所有任务
  finishAllTasks: () => {
    set({
      isGlobalLoading: false,
      currentTasks: []
    })

    console.log('🟢 完成所有任务')
  },

  // 获取当前活跃任务
  getActiveTask: () => {
    const { currentTasks } = get()
    return currentTasks.length > 0 ? currentTasks[0] : null
  },

  // 获取总体进度
  getTotalProgress: () => {
    const { currentTasks } = get()
    if (currentTasks.length === 0) return 0

    const totalProgress = currentTasks.reduce((sum, task) => sum + (task.progress || 0), 0)
    return Math.round(totalProgress / currentTasks.length)
  },

  // 检查任务是否活跃
  isTaskActive: (taskId) => {
    const { currentTasks } = get()
    return currentTasks.some(task => task.id === taskId)
  },

  // 模拟进度更新
  _simulateProgress: (taskId) => {
    const task = get().currentTasks.find(t => t.id === taskId)
    if (!task || !task.simulateProgress) return

    let currentProgress = task.progress || 0
    const estimatedDuration = task.estimatedDuration || 10
    const updateInterval = 200 // 200ms更新一次
    const totalUpdates = (estimatedDuration * 1000) / updateInterval
    const progressIncrement = (95 - currentProgress) / totalUpdates // 只增长到95%，留5%给真正完成

    const progressTimer = setInterval(() => {
      const state = get()
      const currentTask = state.currentTasks.find(t => t.id === taskId)
      
      if (!currentTask || !state.isTaskActive(taskId)) {
        clearInterval(progressTimer)
        return
      }

      currentProgress = Math.min(currentProgress + progressIncrement, 95)
      
      const elapsedTime = Math.floor((Date.now() - task.startTime) / 1000)
      let description = currentTask.description || ''
      
      // 根据进度阶段更新描述
      if (currentProgress < 20) {
        description = description.replace(/正在.+/, '正在初始化...')
      } else if (currentProgress < 50) {
        description = description.replace(/正在.+/, '正在处理中...')
      } else if (currentProgress < 80) {
        description = description.replace(/正在.+/, '即将完成...')
      } else {
        description = description.replace(/正在.+/, '正在收尾工作...')
      }

      state.updateTaskProgress(taskId, currentProgress, description)

      // 如果达到95%就停止自动增长，等待手动完成
      if (currentProgress >= 95) {
        clearInterval(progressTimer)
      }
    }, updateInterval)
  }
}))

// 导出类型
export type { GlobalLoadingState }
