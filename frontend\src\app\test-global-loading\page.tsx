/**
 * 全局Loading测试页面
 * 用于测试视频素材上传时的全局loading效果
 */

'use client'

import React, { useState } from 'react'
import { useVideoMaterialStore } from '@/store/videoMaterialStore'
import { useGlobalLoadingStore } from '@/store/globalLoadingStore'

const GlobalLoadingTestPage: React.FC = () => {
  const { uploadFile, bulkUploadFiles } = useVideoMaterialStore()
  const { startGlobalLoading, updateTaskProgress, finishTask } = useGlobalLoadingStore()
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setSelectedFiles(files)
  }

  // 测试单个文件上传
  const handleSingleUpload = async () => {
    if (selectedFiles.length === 0) {
      alert('请先选择文件')
      return
    }

    try {
      const file = selectedFiles[0]
      console.log('开始单个文件上传:', file.name)
      await uploadFile(file, 'test', (progress) => {
        console.log(`上传进度: ${progress}%`)
      })
      console.log('单个文件上传完成')
      alert('上传成功！')
    } catch (error) {
      console.error('上传失败:', error)
      alert('上传失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 测试批量文件上传
  const handleBulkUpload = async () => {
    if (selectedFiles.length === 0) {
      alert('请先选择文件')
      return
    }

    try {
      console.log('开始批量文件上传:', selectedFiles.map(f => f.name))
      const result = await bulkUploadFiles(selectedFiles, 'test')
      console.log('批量上传完成:', result)
      alert(`批量上传完成！成功: ${result.success.length}, 失败: ${result.failed.length}`)
    } catch (error) {
      console.error('批量上传失败:', error)
      alert('批量上传失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 测试自定义Loading
  const handleCustomLoading = () => {
    const taskId = startGlobalLoading({
      type: 'processing',
      title: '测试模拟进度',
      description: '这是一个模拟进度的loading测试',
      progress: 0,
      estimatedDuration: 15,
      simulateProgress: true
    })

    // 15秒后自动完成
    setTimeout(() => {
      updateTaskProgress(taskId, 100, '测试完成!')
      setTimeout(() => {
        finishTask(taskId)
      }, 1000)
    }, 15000)
  }

  // 测试快速Loading
  const handleFastLoading = () => {
    const taskId = startGlobalLoading({
      type: 'general',
      title: '快速测试',
      description: '这是一个快速的loading测试',
      progress: 0,
      estimatedDuration: 5,
      simulateProgress: true
    })

    // 5秒后自动完成
    setTimeout(() => {
      updateTaskProgress(taskId, 100, '快速测试完成!')
      setTimeout(() => {
        finishTask(taskId)
      }, 800)
    }, 5000)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            全局Loading测试页面
          </h1>

          <div className="space-y-8">
            {/* 文件选择区域 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                选择测试文件
              </h2>
              <input
                type="file"
                multiple
                accept="video/*,image/*"
                onChange={handleFileSelect}
                className="block w-full text-sm text-gray-500 dark:text-gray-400
                          file:mr-4 file:py-2 file:px-4
                          file:rounded-lg file:border-0
                          file:text-sm file:font-medium
                          file:bg-blue-50 file:text-blue-700
                          hover:file:bg-blue-100
                          dark:file:bg-blue-900 dark:file:text-blue-300"
              />
              {selectedFiles.length > 0 && (
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  已选择 {selectedFiles.length} 个文件:
                  <ul className="mt-1 list-disc list-inside">
                    {selectedFiles.map((file, index) => (
                      <li key={index}>{file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* 测试按钮区域 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <button
                onClick={handleSingleUpload}
                disabled={selectedFiles.length === 0}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 
                          text-white font-medium py-3 px-6 rounded-lg
                          transition-colors duration-200"
              >
                测试单个文件上传
              </button>

              <button
                onClick={handleBulkUpload}
                disabled={selectedFiles.length === 0}
                className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 
                          text-white font-medium py-3 px-6 rounded-lg
                          transition-colors duration-200"
              >
                测试批量文件上传
              </button>

              <button
                onClick={handleCustomLoading}
                className="bg-purple-500 hover:bg-purple-600 
                          text-white font-medium py-3 px-6 rounded-lg
                          transition-colors duration-200"
              >
                测试模拟进度 (15s)
              </button>

              <button
                onClick={handleFastLoading}
                className="bg-orange-500 hover:bg-orange-600 
                          text-white font-medium py-3 px-6 rounded-lg
                          transition-colors duration-200"
              >
                快速测试 (5s)
              </button>
            </div>

            {/* 说明文档 */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                测试说明
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>单个文件上传</strong>: 测试单个视频文件的上传功能，会显示全局loading遮罩</li>
                <li>• <strong>批量文件上传</strong>: 测试多个文件的批量上传功能，会显示全局loading遮罩</li>
                <li>• <strong>模拟进度测试</strong>: 测试15秒的模拟进度效果，观察进度条平滑增长</li>
                <li>• <strong>快速测试</strong>: 测试5秒的快速loading效果</li>
                <li>• 在loading期间，整个界面会被遮罩覆盖，防止用户进行其他操作</li>
                <li>• loading遮罩会显示当前任务的进度、描述信息和已用时间</li>
                <li>• 进度条包含流动光效和跳动光点动画</li>
              </ul>
            </div>

            {/* 注意事项 */}
            <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
              <div className="flex">
                <svg className="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    注意事项
                  </h4>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    请确保后端服务正在运行，否则上传测试会失败。测试文件建议选择小一些的视频文件以便快速验证功能。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GlobalLoadingTestPage
