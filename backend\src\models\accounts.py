"""
账号管理数据库模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Enum as SQLEnum
from sqlalchemy.sql import func

from . import Base
from ..schemas.accounts import PlatformType, AccountStatus


class Account(Base):
    """账号数据库模型"""
    __tablename__ = "accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True, comment="账号名称")
    description = Column(Text, nullable=True, comment="账号描述")
    platform = Column(SQLEnum(PlatformType), nullable=False, index=True, comment="所属平台")
    status = Column(SQLEnum(AccountStatus), 
                   nullable=False, 
                   default=AccountStatus.UNUSED, 
                   index=True, 
                   comment="账号状态")
    
    # 头像相关
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    avatar_file_path = Column(String(500), nullable=True, comment="头像文件路径")
    
    # 个性化设置
    brand_color = Column(String(10), nullable=False, default="#2563eb", comment="品牌主色调")
    font_style = Column(String(50), nullable=False, default="default", comment="字体风格")
    content_style = Column(String(50), nullable=False, default="formal", comment="内容风格")
    
    # 平台特定设置
    platform_settings = Column(JSON, nullable=False, default=dict, comment="平台特定配置")
    
    # 统计信息
    usage_count = Column(Integer, nullable=False, default=0, comment="使用次数")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), 
                       server_default=func.now(), 
                       nullable=False, 
                       comment="创建时间")
    updated_at = Column(DateTime(timezone=True), 
                       server_default=func.now(), 
                       onupdate=func.now(), 
                       nullable=False, 
                       comment="更新时间")
    last_used_at = Column(DateTime(timezone=True), 
                         nullable=True, 
                         comment="最后使用时间")
    
    def __repr__(self):
        return f"<Account(id={self.id}, name='{self.name}', platform='{self.platform}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'platform': self.platform,
            'status': self.status,
            'avatar_url': self.avatar_url,
            'brand_color': self.brand_color,
            'font_style': self.font_style,
            'content_style': self.content_style,
            'platform_settings': self.platform_settings,
            'usage_count': self.usage_count,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'last_used_at': self.last_used_at
        }
