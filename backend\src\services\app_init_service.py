"""
应用初始化服务
在应用启动时自动执行初始化任务
"""

import os
import logging
from typing import Optional
from sqlalchemy.orm import Session

from ..core.database import get_db_session
from ..services.template_import_service import template_import_service
from ..models.resources import CoverTemplate

logger = logging.getLogger(__name__)

class AppInitService:
    """应用初始化服务"""
    
    def __init__(self):
        self.initialized = False
    
    def init_database_templates(self, db: Optional[Session] = None):
        """初始化数据库中的模板数据"""
        should_close_db = False
        if db is None:
            db = get_db_session()
            should_close_db = True
        
        try:
            # 检查是否已有模板数据
            template_count = db.query(CoverTemplate).count()
            logger.info(f"当前数据库中有 {template_count} 个模板")
            
            # 如果没有模板，则初始化默认模板
            if template_count == 0:
                logger.info("开始初始化默认模板...")
                template_import_service.init_default_templates(db)
                logger.info("默认模板初始化完成")
            else:
                # 检查是否缺少特定的默认模板
                social_template = db.query(CoverTemplate).first()
                
                if not social_template:
                    logger.info("缺少封面模板，开始导入默认封面模板...")
                    social_post_template_path = "social_post_template.html"
                    if os.path.exists(social_post_template_path):
                        template_import_service.import_html_template(
                            html_file_path=social_post_template_path,
                            name="默认封面模板",
                            description="适用于各种社交媒体平台的帖子封面模板，支持头像、用户名、标题和描述变量",
                            category="社交媒体",
                            db=db
                        )
                        logger.info("默认封面模板导入完成")
                    else:
                        logger.warning("默认封面模板文件不存在")
            
        except Exception as e:
            logger.error(f"初始化数据库模板失败: {e}")
            raise
        finally:
            if should_close_db:
                db.close()
    
    def init_directories(self):
        """初始化必要的目录结构"""
        try:
            directories = [
                "templates",
                "uploads/covers",
                "uploads/previews",
                "temp"
            ]
            
            for directory in directories:
                os.makedirs(directory, exist_ok=True)
                logger.debug(f"确保目录存在: {directory}")
            
        except Exception as e:
            logger.error(f"初始化目录结构失败: {e}")
            raise
    
    def startup(self):
        """应用启动时的初始化"""
        if self.initialized:
            logger.info("应用已初始化，跳过")
            return
        
        try:
            logger.info("开始应用初始化...")
            
            # 1. 初始化目录结构
            self.init_directories()
            
            # 2. 初始化数据库模板
            self.init_database_templates()
            
            self.initialized = True
            logger.info("应用初始化完成")
            
        except Exception as e:
            logger.error(f"应用初始化失败: {e}")
            raise

# 创建全局实例
app_init_service = AppInitService()
