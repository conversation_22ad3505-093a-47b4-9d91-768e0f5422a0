#!/usr/bin/env python3
"""
测试新的文件命名和导出系统
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))

from src.database import get_session_maker
from src.models import Account, VideoGenerationJob, VideoGenerationTask, TaskStatus
from src.services.video_generation_helpers import VideoGenerationServiceHelpers
from sqlalchemy.orm import sessionmaker

async def test_new_naming_system():
    """测试新的文件命名系统"""
    print("🔧 测试新的文件命名和导出系统")
    
    session_maker = get_session_maker()
    db = session_maker()
    
    try:
        # 1. 查找现有账号
        account = db.query(Account).first()
        if not account:
            print("❌ 数据库中没有找到账号")
            return
        
        print(f"✅ 找到账号: {account.name}")
        
        # 2. 创建测试作业
        job = VideoGenerationJob(
            name="新命名系统测试作业",
            total_tasks=3,
            status=TaskStatus.PENDING
        )
        db.add(job)
        db.commit()
        db.refresh(job)
        
        print(f"✅ 创建测试作业: {job.name} (ID: {job.id})")
        
        # 3. 为同一账号创建多个测试任务
        tasks = []
        for i in range(3):
            task = VideoGenerationTask(
                job_id=job.id,
                task_name=f"测试任务_{i+1}",
                account_id=account.id,
                status=TaskStatus.PENDING,
                generated_story=f"这是第{i+1}个测试故事的内容..."
            )
            db.add(task)
            tasks.append(task)
        
        db.commit()
        for task in tasks:
            db.refresh(task)
        
        print(f"✅ 创建了 {len(tasks)} 个测试任务")
        
        # 4. 测试文件命名功能
        helper = VideoGenerationServiceHelpers(session_maker)
        
        print("\n📋 测试文件命名规则:")
        for i, task in enumerate(tasks):
            # 测试不同扩展名的文件命名
            for ext in ['mp3', 'mp4', 'txt', 'srt', 'png']:
                filename = helper._generate_standard_filename(task, ext)
                print(f"   任务 {i+1} ({ext}): {filename}")
        
        # 5. 测试序号获取功能
        print("\n🔢 测试序号获取:")
        for i, task in enumerate(tasks):
            sequence = helper._get_account_sequence_in_batch(str(job.id), str(account.id), str(task.id))
            print(f"   任务 {task.task_name}: 序号 {sequence}")
        
        # 6. 测试文件导出功能（模拟）
        print("\n📁 测试文件导出功能:")
        test_task = tasks[0]
        
        # 创建模拟文件
        from backend.src.services.video_generation_helpers import UPLOADS_DIR
        
        # 创建临时音频和视频文件用于测试
        temp_audio = UPLOADS_DIR / "temp_test_audio.mp3"
        temp_video = UPLOADS_DIR / "temp_test_video.mp4"
        
        temp_audio.parent.mkdir(parents=True, exist_ok=True)
        temp_video.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建空的测试文件
        temp_audio.write_text("fake audio content")
        temp_video.write_text("fake video content")
        
        try:
            # 测试导出功能
            await helper._export_generated_files(
                test_task, 
                "这是测试文案内容", 
                str(temp_audio), 
                str(temp_video)
            )
            print("✅ 文件导出测试完成")
            
            # 检查导出的文件
            export_dir = UPLOADS_DIR / "exports"
            if export_dir.exists():
                exported_files = list(export_dir.glob("*"))
                print(f"📂 导出目录中的文件:")
                for file in exported_files:
                    print(f"   - {file.name}")
            
        finally:
            # 清理临时文件
            if temp_audio.exists():
                temp_audio.unlink()
            if temp_video.exists():
                temp_video.unlink()
        
        print("\n✅ 新命名系统测试完成!")
        print("📋 新命名规则说明:")
        print("   格式: 账号名_任务名称_视频N.扩展名")
        print("   - N是该账号在当前批次中的序号")
        print("   - 同时导出文案(.txt)、语音(.mp3)、视频(.mp4)三个文件")
        print("   - 所有文件使用相同的基础名称，放在统一的exports目录中")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_new_naming_system())
