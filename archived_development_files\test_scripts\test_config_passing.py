#!/usr/bin/env python3
"""
测试配置传递
"""

from src.core.database import get_session_maker
from src.models.settings import Settings
from src.schemas.settings import TTSConfig

def test_config_retrieval():
    """测试配置获取"""
    
    print("🔍 测试配置获取...")
    
    # 1. 从数据库获取设置
    session_maker = get_session_maker()
    session = session_maker()
    try:
        settings = session.query(Settings).first()
        if not settings:
            print("❌ 没有找到设置记录")
            return False
        
        print(f"✅ 找到设置记录")
        print(f"   TTS提供商: {settings.tts_provider}")
        print(f"   F5-TTS端点 (数据库): {settings.f5_tts_endpoint}")
        
        # 2. 转换为前端格式
        frontend_format = settings.to_frontend_format()
        tts_config = frontend_format['tts']
        print(f"   F5-TTS端点 (前端格式): {tts_config.get('f5TtsEndpoint')}")
        
        # 3. 创建TTSConfig对象
        tts_config_obj = TTSConfig(
            provider="f5-tts",
            f5TtsEndpoint=settings.f5_tts_endpoint
        )
        print(f"   TTSConfig对象: {tts_config_obj}")
        
        # 4. 转换为字典
        config_dict = tts_config_obj.model_dump()
        print(f"   配置字典: {config_dict}")
        print(f"   f5TtsEndpoint: {config_dict.get('f5TtsEndpoint')}")
        print(f"   f5_tts_endpoint: {config_dict.get('f5_tts_endpoint')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置获取失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False
    finally:
        session.close()

if __name__ == "__main__":
    print("🧪 配置传递测试\n")
    
    success = test_config_retrieval()
    
    if success:
        print("\n✅ 配置获取测试通过")
    else:
        print("\n❌ 配置获取测试失败")
