<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit故事视频生成器 - 系统设置</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: all 0.3s ease;
        }

        /* 主题变量定义 */
        :root {
            /* 蓝色主题（默认） */
            --theme-primary: #2563eb;
            --theme-primary-hover: #1d4ed8;
            --theme-primary-light: #eff6ff;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f9fafb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #374151;
            --border-primary: #e5e7eb;
            --border-secondary: #d1d5db;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
        }

        /* 绿色主题 */
        [data-theme="green"] {
            --theme-primary: #059669;
            --theme-primary-hover: #047857;
            --theme-primary-light: #ecfdf5;
        }

        /* 紫色主题 */
        [data-theme="purple"] {
            --theme-primary: #7c3aed;
            --theme-primary-hover: #6d28d9;
            --theme-primary-light: #f3f4f6;
        }

        /* 橙色主题 */
        [data-theme="orange"] {
            --theme-primary: #ea580c;
            --theme-primary-hover: #dc2626;
            --theme-primary-light: #fff7ed;
        }

        /* 红色主题 */
        [data-theme="red"] {
            --theme-primary: #dc2626;
            --theme-primary-hover: #b91c1c;
            --theme-primary-light: #fef2f2;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 页面标题 */
        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .page-description {
            font-size: 16px;
            color: var(--text-secondary);
        }

        /* 设置卡片 */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .settings-card {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-primary);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-primary);
        }

        .card-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: var(--theme-primary);
            fill: currentColor;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .card-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 表单组件 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-secondary);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            resize: vertical;
            min-height: 80px;
            transition: all 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--theme-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-help {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        /* 网格布局 */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        /* 按钮组 */
        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid var(--border-primary);
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }

        .btn-primary {
            background: var(--theme-primary);
            color: white;
            border-color: var(--theme-primary);
        }

        .btn-primary:hover {
            background: var(--theme-primary-hover);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-tertiary);
            border-color: var(--border-secondary);
        }

        .btn-secondary:hover {
            border-color: var(--theme-primary);
            color: var(--theme-primary);
        }

        .btn-test {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .btn-test:hover {
            background: #047857;
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        .status-success {
            background: #ecfdf5;
            color: var(--success-color);
            border: 1px solid #bbf7d0;
        }

        .status-error {
            background: #fef2f2;
            color: var(--error-color);
            border: 1px solid #fecaca;
        }

        .status-warning {
            background: #fffbeb;
            color: var(--warning-color);
            border: 1px solid #fed7aa;
        }

        /* 配置项 */
        .config-item {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid var(--border-primary);
        }

        .config-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }

        .config-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .config-toggle {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--border-secondary);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .config-toggle.active {
            background: var(--theme-primary);
        }

        .config-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .config-toggle.active::after {
            transform: translateX(20px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">系统设置</h1>
            <p class="page-description">配置文字转语音服务和大模型服务，确保系统正常运行</p>
        </div>

        <!-- 设置内容 -->
        <div class="settings-grid">
            <!-- TTS服务配置 -->
            <div class="settings-card">
                <div class="card-header">
                    <svg class="card-icon" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <div>
                        <h2 class="card-title">文字转语音服务 (TTS)</h2>
                        <p class="card-subtitle">配置语音合成服务提供商和相关认证信息</p>
                    </div>
                </div>

                <!-- TTS服务选择 -->
                <div class="form-group">
                    <label class="form-label">服务提供商</label>
                    <select class="form-select" id="ttsProvider">
                        <option value="">请选择TTS服务商</option>
                        <option value="openai">OpenAI TTS</option>
                        <option value="azure">Azure Cognitive Services</option>
                        <option value="google">Google Cloud Text-to-Speech</option>
                        <option value="aws">Amazon Polly</option>
                        <option value="f5-tts">F5-TTS (本地部署)</option>
                    </select>
                    <div class="form-help">选择您要使用的文字转语音服务提供商</div>
                </div>

                <!-- OpenAI TTS 配置 -->
                <div class="config-item" id="openaiConfig" style="display: none;">
                    <div class="config-header">
                        <h3 class="config-title">OpenAI TTS 配置</h3>
                        <span class="status-indicator status-warning">未测试</span>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">API Key</label>
                            <input type="password" class="form-input" placeholder="sk-...">
                            <div class="form-help">您的OpenAI API密钥</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">语音模型</label>
                            <select class="form-select">
                                <option value="tts-1">TTS-1 (标准)</option>
                                <option value="tts-1-hd">TTS-1-HD (高清)</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">声音</label>
                            <select class="form-select">
                                <option value="alloy">Alloy</option>
                                <option value="echo">Echo</option>
                                <option value="fable">Fable</option>
                                <option value="onyx">Onyx</option>
                                <option value="nova">Nova</option>
                                <option value="shimmer">Shimmer</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">语速</label>
                            <input type="range" class="form-input" min="0.25" max="4.0" step="0.25" value="1.0">
                            <div class="form-help">语音播放速度 (0.25x - 4.0x)</div>
                        </div>
                    </div>
                </div>

                <!-- F5-TTS 本地配置 -->
                <div class="config-item" id="f5ttsConfig" style="display: none;">
                    <div class="config-header">
                        <h3 class="config-title">F5-TTS 本地部署配置</h3>
                        <span class="status-indicator status-error">连接失败</span>
                    </div>
                    <div class="form-group">
                        <label class="form-label">API 地址</label>
                        <input type="url" class="form-input" placeholder="http://localhost:8000" value="http://localhost:8000">
                        <div class="form-help">F5-TTS本地部署的API服务地址</div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">参考音频</label>
                            <input type="file" class="form-input" accept=".wav,.mp3">
                            <div class="form-help">上传参考音频文件用于声音克隆</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">参考文本</label>
                            <textarea class="form-textarea" placeholder="输入参考音频对应的文字内容"></textarea>
                        </div>
                    </div>
                </div>

                <div class="button-group">
                    <button class="btn btn-test">测试连接</button>
                    <button class="btn btn-primary">保存配置</button>
                </div>
            </div>

            <!-- 大模型配置 -->
            <div class="settings-card">
                <div class="card-header">
                    <svg class="card-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <h2 class="card-title">大模型服务 (LLM)</h2>
                        <p class="card-subtitle">配置用于生成故事内容的大语言模型服务</p>
                    </div>
                </div>

                <!-- 大模型服务选择 -->
                <div class="form-group">
                    <label class="form-label">服务提供商</label>
                    <select class="form-select" id="llmProvider">
                        <option value="">请选择大模型服务商</option>
                        <option value="openai">OpenAI</option>
                        <option value="anthropic">Anthropic Claude</option>
                        <option value="gemini">Google Gemini</option>
                        <option value="custom">自定义 (OpenAI格式兼容)</option>
                    </select>
                    <div class="form-help">选择您要使用的大语言模型服务提供商</div>
                </div>

                <!-- OpenAI 大模型配置 -->
                <div class="config-item" id="openaiLlmConfig" style="display: none;">
                    <div class="config-header">
                        <h3 class="config-title">OpenAI 模型配置</h3>
                        <span class="status-indicator status-success">连接正常</span>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">API Key</label>
                            <input type="password" class="form-input" placeholder="sk-...">
                            <div class="form-help">您的OpenAI API密钥</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">模型</label>
                            <select class="form-select">
                                <option value="gpt-4">GPT-4</option>
                                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">温度 (Temperature)</label>
                            <input type="number" class="form-input" min="0" max="2" step="0.1" value="0.7">
                            <div class="form-help">控制输出的随机性 (0-2)</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">最大令牌数</label>
                            <input type="number" class="form-input" min="100" max="4000" value="2000">
                            <div class="form-help">单次生成的最大字符数</div>
                        </div>
                    </div>
                </div>

                <!-- 自定义模型配置 -->
                <div class="config-item" id="customLlmConfig" style="display: none;">
                    <div class="config-header">
                        <h3 class="config-title">自定义模型配置</h3>
                        <span class="status-indicator status-warning">未测试</span>
                    </div>
                    <div class="form-group">
                        <label class="form-label">API 基础地址</label>
                        <input type="url" class="form-input" placeholder="https://api.example.com/v1">
                        <div class="form-help">兼容OpenAI格式的API地址</div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">API Key</label>
                            <input type="password" class="form-input" placeholder="your-api-key">
                            <div class="form-help">您的API密钥</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">模型名称</label>
                            <input type="text" class="form-input" placeholder="gpt-3.5-turbo">
                            <div class="form-help">要使用的模型名称</div>
                        </div>
                    </div>
                </div>

                <div class="button-group">
                    <button class="btn btn-test">测试连接</button>
                    <button class="btn btn-primary">保存配置</button>
                    <button class="btn btn-secondary">重置为默认</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // TTS服务商切换
        document.getElementById('ttsProvider').addEventListener('change', function() {
            const provider = this.value;
            
            // 隐藏所有配置
            document.getElementById('openaiConfig').style.display = 'none';
            document.getElementById('f5ttsConfig').style.display = 'none';
            
            // 显示对应配置
            switch(provider) {
                case 'openai':
                    document.getElementById('openaiConfig').style.display = 'block';
                    break;
                case 'f5-tts':
                    document.getElementById('f5ttsConfig').style.display = 'block';
                    break;
                // 可以添加其他服务商的配置
            }
        });

        // LLM服务商切换
        document.getElementById('llmProvider').addEventListener('change', function() {
            const provider = this.value;
            
            // 隐藏所有配置
            document.getElementById('openaiLlmConfig').style.display = 'none';
            document.getElementById('customLlmConfig').style.display = 'none';
            
            // 显示对应配置
            switch(provider) {
                case 'openai':
                    document.getElementById('openaiLlmConfig').style.display = 'block';
                    break;
                case 'custom':
                    document.getElementById('customLlmConfig').style.display = 'block';
                    break;
                // 可以添加其他服务商的配置
            }
        });

        // 测试连接按钮
        document.querySelectorAll('.btn-test').forEach(btn => {
            btn.addEventListener('click', function() {
                // 这里将来会调用实际的测试API
                this.textContent = '测试中...';
                this.disabled = true;
                
                // 模拟测试过程
                setTimeout(() => {
                    this.textContent = '测试连接';
                    this.disabled = false;
                    
                    // 模拟测试结果 - 更新状态指示器
                    const statusIndicator = this.closest('.settings-card').querySelector('.status-indicator');
                    if (statusIndicator) {
                        statusIndicator.className = 'status-indicator status-success';
                        statusIndicator.textContent = '连接正常';
                    }
                }, 2000);
            });
        });

        // 保存配置按钮
        document.querySelectorAll('.btn-primary').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent === '保存配置') {
                    this.textContent = '保存中...';
                    this.disabled = true;
                    
                    // 模拟保存过程
                    setTimeout(() => {
                        this.textContent = '保存配置';
                        this.disabled = false;
                        
                        // 显示保存成功提示（这里可以添加toast提示）
                        alert('配置保存成功！');
                    }, 1000);
                }
            });
        });

        // 重置按钮
        document.querySelectorAll('.btn-secondary').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent === '重置为默认') {
                    if (confirm('确定要重置为默认配置吗？这将清除所有当前设置。')) {
                        // 这里将来会重置表单
                        alert('已重置为默认配置');
                    }
                }
            });
        });

        // 页面加载时恢复保存的主题
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'blue';
            if (savedTheme !== 'blue') {
                document.documentElement.setAttribute('data-theme', savedTheme);
            }
        });
    </script>
</body>
</html>
