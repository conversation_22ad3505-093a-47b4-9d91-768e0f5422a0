/**
 * API调用hooks
 * 统一管理与后端的API交互
 */

import { useState, useEffect, useCallback } from 'react'
import { ApiResponse, ApiError as ApiErrorType } from '../types/store'

// API基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

// 创建API客户端
class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    
    // 只有在body不是FormData时才设置JSON headers
    const isFormData = options.body instanceof FormData
    const defaultHeaders: HeadersInit = isFormData ? {} : {
      'Content-Type': 'application/json',
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new ApiError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData
        )
      }

      const data = await response.json()
      
      return {
        success: true,
        data,
        message: data.message
      }
    } catch (error) {
      if (error instanceof ApiError) {
        return {
          success: false,
          error: error.message
        }
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // GET请求
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    const isFormData = data instanceof FormData
    return this.request<T>(endpoint, {
      method: 'POST',
      body: isFormData ? data : (data ? JSON.stringify(data) : undefined),
    })
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // 文件上传
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value))
      })
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {}, // 让浏览器自动设置Content-Type
    })
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient()

// API错误类
class ApiError extends Error {
  status: number
  details?: any

  constructor(message: string, status: number, details?: any) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.details = details
  }
}

// 通用API hook
export function useApi<T>() {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const execute = useCallback(async (
    apiCall: () => Promise<ApiResponse<T>>
  ): Promise<ApiResponse<T>> => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await apiCall()
      
      if (response.success) {
        setData(response.data || null)
      } else {
        setError(response.error || 'Unknown error')
      }
      
      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      setLoading(false)
    }
  }, [])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])

  return {
    data,
    loading,
    error,
    execute,
    reset
  }
}

// 设置相关API hooks
export const useSettingsApi = () => {
  const { execute, loading, error } = useApi()

  const testTTSConnection = useCallback(async (config: any) => {
    return execute(() => apiClient.post('/api/settings/test-tts', config))
  }, [execute])

  const testLLMConnection = useCallback(async (config: any) => {
    return execute(() => apiClient.post('/api/settings/test-llm', config))
  }, [execute])

  const saveSettings = useCallback(async (settings: any) => {
    return execute(() => apiClient.post('/api/settings/save', settings))
  }, [execute])

  const loadSettings = useCallback(async () => {
    return execute(() => apiClient.get('/api/settings'))
  }, [execute])

  return {
    testTTSConnection,
    testLLMConnection,
    saveSettings,
    loadSettings,
    loading,
    error
  }
}

// 资源管理API hooks
export const useResourceApi = () => {
  const { execute, loading, error } = useApi()

  // 背景音乐
  const uploadBackgroundMusic = useCallback(async (file: File, metadata: any) => {
    return execute(() => apiClient.upload('/api/resources/background-music', file, metadata))
  }, [execute])

  const deleteBackgroundMusic = useCallback(async (id: string) => {
    return execute(() => apiClient.delete(`/api/resources/background-music/${id}`))
  }, [execute])

  // 视频素材
  const uploadVideoMaterial = useCallback(async (file: File, metadata: any) => {
    return execute(() => apiClient.upload('/api/resources/video-materials', file, metadata))
  }, [execute])

  const deleteVideoMaterial = useCallback(async (id: string) => {
    return execute(() => apiClient.delete(`/api/resources/video-materials/${id}`))
  }, [execute])

  // 提示词模板
  const savePrompt = useCallback(async (prompt: any) => {
    return execute(() => apiClient.post('/api/resources/prompts', prompt))
  }, [execute])

  const updatePrompt = useCallback(async (id: string, prompt: any) => {
    return execute(() => apiClient.put(`/api/resources/prompts/${id}`, prompt))
  }, [execute])

  const deletePrompt = useCallback(async (id: string) => {
    return execute(() => apiClient.delete(`/api/resources/prompts/${id}`))
  }, [execute])

  // 账户管理
  const saveAccount = useCallback(async (account: any) => {
    return execute(() => apiClient.post('/api/resources/accounts', account))
  }, [execute])

  const updateAccount = useCallback(async (id: string, account: any) => {
    return execute(() => apiClient.put(`/api/resources/accounts/${id}`, account))
  }, [execute])

  const deleteAccount = useCallback(async (id: string) => {
    return execute(() => apiClient.delete(`/api/resources/accounts/${id}`))
  }, [execute])

  // 封面模板
  const uploadCoverTemplate = useCallback(async (file: File, metadata: any) => {
    return execute(() => apiClient.upload('/api/resources/cover-templates', file, metadata))
  }, [execute])

  const deleteCoverTemplate = useCallback(async (id: string) => {
    return execute(() => apiClient.delete(`/api/resources/cover-templates/${id}`))
  }, [execute])

  // 验证资源文件
  const validateResource = useCallback(async (filePath: string, type: string) => {
    return execute(() => apiClient.post('/api/resources/validate', { filePath, type }))
  }, [execute])

  return {
    uploadBackgroundMusic,
    deleteBackgroundMusic,
    uploadVideoMaterial,
    deleteVideoMaterial,
    savePrompt,
    updatePrompt,
    deletePrompt,
    saveAccount,
    updateAccount,
    deleteAccount,
    uploadCoverTemplate,
    deleteCoverTemplate,
    validateResource,
    loading,
    error
  }
}

// 视频生成API hooks
export const useGenerationApi = () => {
  const { execute, loading, error } = useApi()

  const generateStory = useCallback(async (config: any) => {
    return execute(() => apiClient.post('/api/generation/story', config))
  }, [execute])

  const generateAudio = useCallback(async (config: any) => {
    return execute(() => apiClient.post('/api/generation/audio', config))
  }, [execute])

  const prepareMaterials = useCallback(async (config: any) => {
    return execute(() => apiClient.post('/api/generation/prepare-materials', config))
  }, [execute])

  const composeVideo = useCallback(async (config: any) => {
    return execute(() => apiClient.post('/api/generation/compose', config))
  }, [execute])

  const generateCover = useCallback(async (config: any) => {
    return execute(() => apiClient.post('/api/generation/cover', config))
  }, [execute])

  const cancelGeneration = useCallback(async (taskId: string) => {
    return execute(() => apiClient.post(`/api/generation/cancel/${taskId}`))
  }, [execute])

  const getTaskDetails = useCallback(async (taskId: string) => {
    return execute(() => apiClient.get(`/api/generation/task/${taskId}`))
  }, [execute])

  const getTaskList = useCallback(async () => {
    return execute(() => apiClient.get('/api/generation/tasks'))
  }, [execute])

  return {
    generateStory,
    generateAudio,
    prepareMaterials,
    composeVideo,
    generateCover,
    cancelGeneration,
    getTaskDetails,
    getTaskList,
    loading,
    error
  }
}

// 系统状态API hooks
export const useSystemApi = () => {
  const { execute, loading, error } = useApi()

  const getSystemStatus = useCallback(async () => {
    return execute(() => apiClient.get('/api/system/status'))
  }, [execute])

  const getSystemInfo = useCallback(async () => {
    return execute(() => apiClient.get('/api/system/info'))
  }, [execute])

  const checkHealth = useCallback(async () => {
    return execute(() => apiClient.get('/api/health'))
  }, [execute])

  return {
    getSystemStatus,
    getSystemInfo,
    checkHealth,
    loading,
    error
  }
}

// WebSocket连接hook
export const useWebSocket = (url: string = 'ws://localhost:8000/ws') => {
  const [socket, setSocket] = useState<WebSocket | null>(null)
  const [connected, setConnected] = useState(false)
  const [messages, setMessages] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const ws = new WebSocket(url)

    ws.onopen = () => {
      setConnected(true)
      setError(null)
      console.log('WebSocket connected')
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        setMessages(prev => [...prev, data])
      } catch (err) {
        console.error('Failed to parse WebSocket message:', err)
      }
    }

    ws.onerror = (event) => {
      setError('WebSocket error')
      console.error('WebSocket error:', event)
    }

    ws.onclose = () => {
      setConnected(false)
      console.log('WebSocket disconnected')
    }

    setSocket(ws)

    return () => {
      ws.close()
    }
  }, [url])

  const sendMessage = useCallback((message: any) => {
    if (socket && connected) {
      socket.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket not connected')
    }
  }, [socket, connected])

  const clearMessages = useCallback(() => {
    setMessages([])
  }, [])

  return {
    connected,
    messages,
    error,
    sendMessage,
    clearMessages
  }
}

// 实时任务状态更新hook
export const useTaskStatusUpdates = (taskId?: string) => {
  const { messages, sendMessage, connected } = useWebSocket()
  const [taskStatus, setTaskStatus] = useState<any>(null)

  useEffect(() => {
    if (taskId && connected) {
      // 订阅任务状态更新
      sendMessage({
        type: 'subscribe_task',
        taskId: taskId
      })
    }

    return () => {
      if (taskId && connected) {
        sendMessage({
          type: 'unsubscribe_task',
          taskId: taskId
        })
      }
    }
  }, [taskId, connected, sendMessage])

  useEffect(() => {
    // 处理接收到的任务状态消息
    const taskMessages = messages.filter(msg => 
      msg.type === 'task_update' && msg.taskId === taskId
    )

    if (taskMessages.length > 0) {
      const latestStatus = taskMessages[taskMessages.length - 1]
      setTaskStatus(latestStatus.data)
    }
  }, [messages, taskId])

  return {
    taskStatus,
    connected
  }
}

// 音乐管理API hooks
export const useMusicApi = () => {
  const { execute, loading, error } = useApi()

  // 上传音乐文件
  const uploadMusic = useCallback(async (file: File, category: string = 'general', tags: string = '') => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', category)
    formData.append('tags', tags)
    
    return execute(() => apiClient.post('/api/background-music/upload', formData))
  }, [execute])

  // 获取音乐列表
  const getMusicList = useCallback(async (params?: {
    page?: number
    pageSize?: number
    category?: string
    search?: string
    isBuiltIn?: boolean
  }) => {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.pageSize) queryParams.append('page_size', params.pageSize.toString())
    if (params?.category) queryParams.append('category', params.category)
    if (params?.search) queryParams.append('search', params.search)
    if (params?.isBuiltIn !== undefined) queryParams.append('is_built_in', params.isBuiltIn.toString())
    
    const url = `/api/background-music${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    return execute(() => apiClient.get(url))
  }, [execute])

  // 获取分类列表
  const getCategoryList = useCallback(async () => {
    return execute(() => apiClient.get('/api/background-music/categories/list'))
  }, [execute])

  // 添加分类
  const addCategory = useCallback(async (category: string) => {
    const formData = new FormData()
    formData.append('category', category)
    return execute(() => apiClient.post('/api/background-music/categories', formData))
  }, [execute])

  // 删除分类
  const deleteCategory = useCallback(async (category: string) => {
    return execute(() => apiClient.delete(`/api/background-music/categories/${encodeURIComponent(category)}`))
  }, [execute])

  // 删除音乐
  const deleteMusic = useCallback(async (id: string) => {
    return execute(() => apiClient.delete(`/api/background-music/${id}`))
  }, [execute])

  // 获取音乐播放URL
  const getMusicPlayUrl = useCallback((id: string) => {
    return `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}/api/background-music/${id}/play`
  }, [])

  return {
    uploadMusic,
    getMusicList,
    getCategoryList,
    addCategory,
    deleteCategory,
    deleteMusic,
    getMusicPlayUrl,
    loading,
    error
  }
}

export { ApiError }
