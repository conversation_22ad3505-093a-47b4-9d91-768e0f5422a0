"""
视频生成API
"""

from loguru import logger
import traceback
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, sessionmaker
from typing import List, Optional

from ..core.database import get_session_maker, get_db
from ..core.responses import success_response, error_response
from ..core.services import get_video_generation_service
from ..models.video_generation import VideoGenerationJob, VideoGenerationTask, TaskStatus
from ..schemas.video_generation import (
    CreateVideoGenerationJobRequest,
    CreateBatchVideoGenerationJobRequest,
    VideoGenerationJobResponse,
    VideoGenerationTaskResponse,
    JobControlRequest,
    TaskControlRequest,
    JobProgressResponse,
    AvailableVoicesResponse,
    VideoPreviewRequest,
    VideoPreviewResponse,
    VideoGenerationJobListResponse,
    ExcelUploadResponse
)
from ..services.video_generation_service import VideoGenerationService
from ..services.tts_service import TTSService
from fastapi import UploadFile, File
import pandas as pd
import io

router = APIRouter(prefix="/video-generator", tags=["video-generator"])


@router.post("/jobs", response_model=VideoGenerationJobResponse)
async def create_video_generation_job(
    request: CreateVideoGenerationJobRequest,
    session_maker: sessionmaker = Depends(get_session_maker)
):
    """创建视频生成作业"""
    try:
        # 使用全局服务实例
        service = get_video_generation_service(session_maker)
        job = await service.create_job(request)
        
        # 自动启动新创建的作业
        await service.start_job(job.id)
        
        return VideoGenerationJobResponse(
            id=job.id,
            name=job.name,
            description=job.description,
            config=job.config or {},
            account_configs=job.account_configs or [],
            status=TaskStatus(job.status),
            total_tasks=job.total_tasks,
            completed_tasks=job.completed_tasks,
            failed_tasks=job.failed_tasks,
            started_at=job.started_at,
            completed_at=job.completed_at,
            estimated_duration=job.estimated_duration,
            error_message=job.error_message,
            created_at=job.created_at,
            updated_at=job.updated_at
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建作业失败: {str(e)}"
        )


@router.post("/upload-excel", response_model=ExcelUploadResponse)
async def upload_excel_stories(
    file: UploadFile = File(..., description="Excel文件")
):
    """上传Excel文件并解析文案列表"""
    try:
        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            return ExcelUploadResponse(
                success=False,
                error="请上传Excel文件（.xlsx或.xls格式）"
            )

        # 读取文件内容
        contents = await file.read()

        # 使用pandas解析Excel
        try:
            df = pd.read_excel(io.BytesIO(contents))
        except Exception as e:
            return ExcelUploadResponse(
                success=False,
                error=f"Excel文件解析失败: {str(e)}"
            )

        # 提取文案列表（取第一列的所有非空值）
        if df.empty:
            return ExcelUploadResponse(
                success=False,
                error="Excel文件为空"
            )

        # 获取第一列数据，过滤空值
        first_column = df.iloc[:, 0].dropna()
        stories = [str(story).strip() for story in first_column if str(story).strip()]

        if not stories:
            return ExcelUploadResponse(
                success=False,
                error="Excel文件中没有找到有效的文案内容"
            )

        # 获取第二列数据（标题），如果存在的话
        titles = []
        if df.shape[1] >= 2:  # 检查是否有第二列
            second_column = df.iloc[:, 1]
            # 为每个文案对应一个标题，如果标题为空则使用空字符串
            for i in range(len(stories)):
                if i < len(second_column) and pd.notna(second_column.iloc[i]):
                    title = str(second_column.iloc[i]).strip()
                    titles.append(title)
                else:
                    titles.append("")  # 空标题
        else:
            # 如果没有第二列，所有标题都为空
            titles = [""] * len(stories)

        return ExcelUploadResponse(
            success=True,
            stories=stories,
            titles=titles,
            total_count=len(stories),
            message=f"成功解析 {len(stories)} 条文案" + (f"，其中 {len([t for t in titles if t])} 条有标题" if any(titles) else "")
        )

    except Exception as e:
        logger.error(f"Excel文件上传处理失败: {str(e)}")
        return ExcelUploadResponse(
            success=False,
            error=f"文件处理失败: {str(e)}"
        )


@router.post("/batch-jobs", response_model=VideoGenerationJobResponse)
async def create_batch_video_generation_job(
    request: CreateBatchVideoGenerationJobRequest,
    session_maker: sessionmaker = Depends(get_session_maker)
):
    """创建批量视频生成作业（基于文案列表）"""
    try:
        # 使用全局服务实例
        service = get_video_generation_service(session_maker)
        job = await service.create_batch_job(request)

        # 自动启动新创建的作业
        await service.start_job(job.id)

        return VideoGenerationJobResponse(
            id=job.id,
            name=job.name,
            description=job.description,
            config=job.config or {},
            account_configs=job.account_configs or [],
            status=TaskStatus(job.status),
            total_tasks=job.total_tasks,
            completed_tasks=job.completed_tasks,
            failed_tasks=job.failed_tasks,
            started_at=job.started_at,
            completed_at=job.completed_at,
            estimated_duration=job.estimated_duration,
            error_message=job.error_message,
            created_at=job.created_at,
            updated_at=job.updated_at
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建批量视频生成作业失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建批量作业失败: {str(e)}"
        )


@router.get("/jobs", response_model=VideoGenerationJobListResponse)
async def list_video_generation_jobs(
    status_filter: Optional[str] = Query(None, description="按状态过滤"),
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """获取视频生成作业列表"""
    try:
        query = db.query(VideoGenerationJob)
        
        if status_filter:
            query = query.filter(VideoGenerationJob.status == status_filter)
        
        total_jobs = query.count() # 获取总数
        jobs = query.order_by(VideoGenerationJob.created_at.desc()).offset(offset).limit(limit).all()
        
        return VideoGenerationJobListResponse(
            jobs=[
                VideoGenerationJobResponse(
                    id=job.id,
                    name=job.name,
                    description=job.description,
                    config=job.config or {},
                    account_configs=job.account_configs or [],
                    status=TaskStatus(job.status),
                    total_tasks=job.total_tasks,
                    completed_tasks=job.completed_tasks,
                    failed_tasks=job.failed_tasks,
                    started_at=job.started_at,
                    completed_at=job.completed_at,
                    estimated_duration=job.estimated_duration,
                    error_message=job.error_message,
                    created_at=job.created_at,
                    updated_at=job.updated_at
                )
                for job in jobs
            ],
            total=total_jobs,
            page=offset // limit + 1 if limit > 0 else 1,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取作业列表失败: {str(e)}")
        logger.error(traceback.print_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取作业列表失败: {str(e)}"
        )


@router.get("/jobs/{job_id}", response_model=VideoGenerationJobResponse)
async def get_video_generation_job(
    job_id: str,
    include_tasks: bool = Query(False, description="是否包含任务列表"),
    db: Session = Depends(get_db)
):
    """获取视频生成作业详情"""
    try:
        job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="作业未找到"
            )
        
        response = VideoGenerationJobResponse(
            id=job.id,
            name=job.name,
            description=job.description,
            config=job.config or {},
            account_configs=job.account_configs or [],
            status=TaskStatus(job.status),
            total_tasks=job.total_tasks,
            completed_tasks=job.completed_tasks,
            failed_tasks=job.failed_tasks,
            started_at=job.started_at,
            completed_at=job.completed_at,
            estimated_duration=job.estimated_duration,
            error_message=job.error_message,
            created_at=job.created_at,
            updated_at=job.updated_at
        )
        
        if include_tasks:
            tasks = db.query(VideoGenerationTask).filter(
                VideoGenerationTask.job_id == job_id
            ).all()
            
            response.tasks = [
                VideoGenerationTaskResponse(
                    id=task.id,
                    job_id=task.job_id,
                    task_name=task.task_name,
                    account_id=task.account_id,
                    status=TaskStatus(task.status),
                    progress=task.progress,
                    current_step=task.current_step,
                    generated_story=task.generated_story,
                    first_sentence=task.first_sentence,
                    audio_file_path=task.audio_file_path,
                    subtitle_file_path=task.subtitle_file_path,
                    cover_image_path=task.cover_image_path,
                    final_video_path=task.final_video_path,
                    used_materials=task.used_materials,
                    used_music_id=task.used_music_id,
                    started_at=task.started_at,
                    completed_at=task.completed_at,
                    duration=task.duration,
                    audio_analysis=task.audio_analysis,
                    error_message=task.error_message,
                    retry_count=task.retry_count,
                    created_at=task.created_at,
                    updated_at=task.updated_at
                )
                for task in tasks
            ]
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取作业详情失败: {str(e)}"
        )


@router.post("/jobs/{job_id}/control")
async def control_video_generation_job(
    job_id: str,
    request: JobControlRequest,
    session_maker: sessionmaker = Depends(get_session_maker)
):
    """控制视频生成作业（启动、暂停、恢复、取消）"""
    try:
        service = get_video_generation_service(session_maker)
        
        if request.action == "start":
            success = await service.start_job(job_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法启动作业"
                )
        elif request.action == "pause":
            success = service.pause_job(job_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法暂停作业"
                )
        elif request.action == "resume":
            success = service.resume_job(job_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法恢复作业"
                )
        elif request.action == "cancel":
            success = service.cancel_job(job_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法取消作业"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的操作"
            )
        
        return success_response({"success": True}, f"操作执行成功: {request.action}")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"操作执行失败: {str(e)}"
        )


@router.get("/jobs/{job_id}/progress", response_model=JobProgressResponse)
async def get_job_progress(
    job_id: str,
    db: Session = Depends(get_db)
):
    """获取作业进度"""
    try:
        job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="作业未找到"
            )
        
        tasks = db.query(VideoGenerationTask).filter(
            VideoGenerationTask.job_id == job_id
        ).all()
        
        running_tasks = [t for t in tasks if t.status == TaskStatus.RUNNING]
        pending_tasks = [t for t in tasks if t.status == TaskStatus.PENDING]
        
        # 计算进度百分比
        if job.total_tasks > 0:
            progress_percentage = (job.completed_tasks / job.total_tasks) * 100
        else:
            progress_percentage = 0
        
        return JobProgressResponse(
            job_id=job.id,
            status=TaskStatus(job.status),
            total_tasks=job.total_tasks,
            completed_tasks=job.completed_tasks,
            failed_tasks=job.failed_tasks,
            running_tasks=len(running_tasks),
            pending_tasks=len(pending_tasks),
            progress_percentage=progress_percentage,
            estimated_remaining_time=None,  # TODO: 计算预估剩余时间
            current_tasks=[
                VideoGenerationTaskResponse(
                    id=task.id,
                    job_id=task.job_id,
                    task_name=task.task_name,
                    account_id=task.account_id,
                    status=TaskStatus(task.status),
                    progress=task.progress,
                    current_step=task.current_step,
                    generated_story=task.generated_story,
                    first_sentence=task.first_sentence,
                    audio_file_path=task.audio_file_path,
                    subtitle_file_path=task.subtitle_file_path,
                    cover_image_path=task.cover_image_path,
                    final_video_path=task.final_video_path,
                    used_materials=task.used_materials,
                    used_music_id=task.used_music_id,
                    started_at=task.started_at,
                    completed_at=task.completed_at,
                    duration=task.duration,
                    audio_analysis=task.audio_analysis,
                    error_message=task.error_message,
                    retry_count=task.retry_count,
                    created_at=task.created_at,
                    updated_at=task.updated_at
                )
                for task in running_tasks
            ]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取作业进度失败: {str(e)}"
        )


@router.get("/voices", response_model=AvailableVoicesResponse)
async def get_available_voices(session_maker: sessionmaker = Depends(get_session_maker)):
    """获取可用的音色列表"""
    try:
        tts_service = TTSService(session_maker)
        voices = tts_service.get_available_voices()
        
        return AvailableVoicesResponse(voices=voices)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取音色列表失败: {str(e)}"
        )


@router.post("/preview", response_model=VideoPreviewResponse)
async def preview_video_config(
    request: VideoPreviewRequest,
    db: Session = Depends(get_db)
):
    """预览视频配置（生成封面预览）"""
    try:
        # TODO: 实现预览功能
        # 这里应该调用封面生成服务来生成预览图
        
        from ..models.accounts import Account
        account = db.query(Account).filter(Account.id == request.account_id).first()
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号未找到"
            )
        
        # 暂时返回占位符
        return VideoPreviewResponse(
            cover_preview_url=f"/api/cover-templates/preview/{request.cover_template_id}",
            account_name=account.name,
            sample_title=request.sample_title
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预览生成失败: {str(e)}"
        )


@router.post("/tasks/{task_id}/control")
async def control_video_generation_task(
    task_id: str,
    request: TaskControlRequest,
    session_maker: sessionmaker = Depends(get_session_maker)
):
    """控制单个视频生成任务（重试、取消）"""
    try:
        service = get_video_generation_service(session_maker)
        
        if request.action == "retry":
            success = await service.retry_task(task_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法重试任务"
                )
        elif request.action == "cancel":
            success = service.cancel_task(task_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法取消任务"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的操作"
            )
        
        return success_response({"success": True}, f"任务操作执行成功: {request.action}")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"任务操作执行失败: {str(e)}"
        )


@router.get("/tasks", response_model=List[VideoGenerationTaskResponse])
async def list_video_generation_tasks(
    job_id: Optional[str] = Query(None, description="按作业ID过滤任务"),
    status_filter: Optional[str] = Query(None, description="按状态过滤任务"),
    db: Session = Depends(get_db)
):
    """获取视频生成任务列表"""
    try:
        query = db.query(VideoGenerationTask)
        
        if job_id:
            query = query.filter(VideoGenerationTask.job_id == job_id)
        if status_filter:
            query = query.filter(VideoGenerationTask.status == status_filter)
            
        tasks = query.order_by(VideoGenerationTask.created_at.desc()).all()
        
        return [
            VideoGenerationTaskResponse(
                id=task.id,
                job_id=task.job_id,
                task_name=task.task_name,
                account_id=task.account_id,
                status=TaskStatus(task.status),
                progress=task.progress,
                current_step=task.current_step,
                generated_story=task.generated_story,
                first_sentence=task.first_sentence,
                audio_file_path=task.audio_file_path,
                subtitle_file_path=task.subtitle_file_path,
                cover_image_path=task.cover_image_path,
                final_video_path=task.final_video_path,
                used_materials=task.used_materials,
                used_music_id=task.used_music_id,
                started_at=task.started_at,
                completed_at=task.completed_at,
                duration=task.duration,
                audio_analysis=task.audio_analysis,
                error_message=task.error_message,
                retry_count=task.retry_count,
                created_at=task.created_at,
                updated_at=task.updated_at
            )
            for task in tasks
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务列表失败: {str(e)}"
        )
