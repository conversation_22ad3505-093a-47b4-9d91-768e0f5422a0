#!/usr/bin/env python3
"""
创建一个非常明显的fade效果测试
"""

import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_obvious_fade():
    """创建非常明显的fade效果"""
    
    # 首先创建一个纯色的测试图像
    logger.info("创建测试用的纯色图像...")
    
    # 创建红色图像
    cmd_red = [
        'ffmpeg', '-y',
        '-f', 'lavfi', '-i', 'color=red:size=400x300:duration=5',
        '-c:v', 'libx264',
        'red_image.mp4' help
    ]
    
    result = subprocess.run(cmd_red, capture_output=True, text=True)
    if result.returncode != 0:
        logger.error(f"创建红色图像失败: {result.stderr}")
        return
    
    logger.info("✅ 红色图像创建成功")
    
    # 测试1: 非常明显的fade in (2秒)
    logger.info("测试1: 明显的fade in效果")
    cmd1 = [
        'ffmpeg', '-y',
        '-i', 'red_image.mp4',
        '-vf', 'fade=in:0:60',  # 60帧 = 2秒 @ 30fps
        '-c:v', 'libx264',
        'obvious_fade_in.mp4'
    ]
    
    result1 = subprocess.run(cmd1, capture_output=True, text=True)
    if result1.returncode == 0:
        logger.info("✅ obvious_fade_in.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result1.stderr}")
    
    # 测试2: 非常明显的fade out (最后2秒)
    logger.info("测试2: 明显的fade out效果")
    cmd2 = [
        'ffmpeg', '-y',
        '-i', 'red_image.mp4',
        '-vf', 'fade=out:90:60',  # 从第90帧开始fade out 60帧
        '-c:v', 'libx264',
        'obvious_fade_out.mp4'
    ]
    
    result2 = subprocess.run(cmd2, capture_output=True, text=True)
    if result2.returncode == 0:
        logger.info("✅ obvious_fade_out.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result2.stderr}")
    
    # 测试3: fade in + fade out
    logger.info("测试3: fade in + fade out组合")
    cmd3 = [
        'ffmpeg', '-y',
        '-i', 'red_image.mp4',
        '-vf', 'fade=in:0:60,fade=out:90:60',
        '-c:v', 'libx264',
        'obvious_fade_both.mp4'
    ]
    
    result3 = subprocess.run(cmd3, capture_output=True, text=True)
    if result3.returncode == 0:
        logger.info("✅ obvious_fade_both.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result3.stderr}")
    
    # 测试4: 使用时间参数的fade
    logger.info("测试4: 使用时间参数的fade")
    cmd4 = [
        'ffmpeg', '-y',
        '-i', 'red_image.mp4',
        '-vf', 'fade=t=in:st=0:d=2,fade=t=out:st=3:d=2',
        '-c:v', 'libx264',
        'obvious_fade_time.mp4'
    ]
    
    result4 = subprocess.run(cmd4, capture_output=True, text=True)
    if result4.returncode == 0:
        logger.info("✅ obvious_fade_time.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result4.stderr}")
    
    # 现在测试overlay + fade
    logger.info("测试5: overlay + fade组合")
    
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    if Path(video_path).exists():
        cmd5 = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-i', 'red_image.mp4',
            '-filter_complex', 
            '[1:v]scale=400:300,fade=in:0:60,fade=out:90:60[faded];[0:v][faded]overlay=50:50',
            '-t', '5',
            '-c:v', 'libx264',
            'overlay_with_fade.mp4'
        ]
        
        result5 = subprocess.run(cmd5, capture_output=True, text=True)
        if result5.returncode == 0:
            logger.info("✅ overlay_with_fade.mp4 生成成功")
        else:
            logger.error(f"❌ 失败: {result5.stderr}")
    
    logger.info("\n🎬 测试完成！请播放这些文件:")
    logger.info("- obvious_fade_in.mp4: 红色方块淡入")
    logger.info("- obvious_fade_out.mp4: 红色方块淡出")
    logger.info("- obvious_fade_both.mp4: 红色方块淡入+淡出")
    logger.info("- obvious_fade_time.mp4: 使用时间参数的fade")
    logger.info("- overlay_with_fade.mp4: 叠加到视频上的fade效果")
    
    logger.info("\n如果这些文件都没有fade效果，那么问题可能在于:")
    logger.info("1. FFmpeg版本不支持fade滤镜")
    logger.info("2. 视频播放器问题")
    logger.info("3. 其他系统问题")

if __name__ == "__main__":
    create_obvious_fade()
