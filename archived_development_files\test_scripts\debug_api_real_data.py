#!/usr/bin/env python3
"""
实际测试后端API，获取真实的数据格式
"""

import requests
import json
from typing import Dict, Any

API_BASE = "http://localhost:8000/api"

def test_api_endpoint(endpoint: str, name: str) -> Dict[str, Any]:
    """测试API端点并返回真实数据"""
    try:
        url = f"{API_BASE}{endpoint}"
        print(f"\n🔗 测试 {name}: {endpoint}")
        
        response = requests.get(url, timeout=5)
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功")
            print(f"   📦 响应格式: {type(data)}")
            
            if isinstance(data, dict):
                print(f"   🔑 顶级键: {list(data.keys())}")
                if 'data' in data:
                    data_content = data['data']
                    print(f"   📊 data类型: {type(data_content)}")
                    if isinstance(data_content, list) and len(data_content) > 0:
                        print(f"   📝 data长度: {len(data_content)}")
                        print(f"   🎯 首项键: {list(data_content[0].keys()) if data_content else 'N/A'}")
                        if len(data_content) > 0:
                            print(f"   🔍 首项内容: {json.dumps(data_content[0], ensure_ascii=False, indent=2)}")
                    elif isinstance(data_content, dict):
                        print(f"   🔑 data键: {list(data_content.keys())}")
                elif isinstance(data, list) and len(data) > 0:
                    print(f"   📝 列表长度: {len(data)}")
                    print(f"   🎯 首项键: {list(data[0].keys()) if data else 'N/A'}")
            
            return {"success": True, "data": data}
        else:
            error_text = response.text[:200]
            print(f"   ❌ 失败: {error_text}")
            return {"success": False, "error": f"HTTP {response.status_code}: {error_text}"}
            
    except Exception as e:
        print(f"   💥 异常: {e}")
        return {"success": False, "error": str(e)}

def main():
    """测试所有前端需要的API端点"""
    print("🚀 实际API数据格式测试")
    print("=" * 60)
    
    # 按前端使用的API端点进行测试
    endpoints = [
        ("/accounts", "账号列表"),
        ("/video-categories", "视频分类"),
        ("/video-materials", "视频素材"),
        ("/prompts", "提示词"),
        ("/cover-templates", "封面模板"),
        ("/settings", "系统设置"),
        ("/background-music", "背景音乐"),
    ]
    
    results = {}
    
    for endpoint, name in endpoints:
        results[endpoint] = test_api_endpoint(endpoint, name)
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results.values() if r["success"])
    total_count = len(results)
    
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    # 分析关键数据结构
    print("\n📋 关键发现:")
    
    # 检查video-categories结构
    if "/video-categories" in results and results["/video-categories"]["success"]:
        data = results["/video-categories"]["data"]
        print(f"\n🏷️  视频分类API:")
        print(f"   响应格式: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
    
    # 检查prompts结构
    if "/prompts" in results and results["/prompts"]["success"]:
        data = results["/prompts"]["data"]
        print(f"\n💬 提示词API:")
        print(f"   响应格式: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
    
    # 检查settings结构
    if "/settings" in results and results["/settings"]["success"]:
        data = results["/settings"]["data"]
        print(f"\n⚙️  设置API:")
        print(f"   响应格式: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")

if __name__ == "__main__":
    main()
