#!/usr/bin/env python3
"""
测试基础转场效果 - 第一阶段实现
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_video_segments():
    """创建测试用的视频片段"""
    
    test_videos = []
    
    # 创建3个不同颜色的测试视频片段
    colors = [
        ('red', '#FF0000'),
        ('green', '#00FF00'), 
        ('blue', '#0000FF')
    ]
    
    for i, (name, color) in enumerate(colors):
        output_path = f"test_segment_{name}.mp4"
        
        if not Path(output_path).exists():
            logger.info(f"创建测试视频片段: {output_path}")
            
            # 创建5秒的纯色视频
            (
                ffmpeg
                .input('color={}:size=1080x1920:duration=5:rate=30'.format(color), f='lavfi')
                .output(output_path, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
        
        test_videos.append(output_path)
    
    return test_videos

def test_basic_transitions():
    """测试基础转场效果"""
    
    logger.info("🎬 开始测试基础转场效果...")
    
    # 创建测试视频片段
    test_videos = create_test_video_segments()
    
    # 测试配置
    test_configs = [
        {
            'name': '无转场测试',
            'transition_type': 'none',
            'duration': 0.5,
            'output': 'transition_none.mp4'
        },
        {
            'name': '淡入淡出转场',
            'transition_type': 'fade',
            'duration': 1.0,
            'output': 'transition_fade.mp4'
        },
        {
            'name': '溶解转场',
            'transition_type': 'dissolve',
            'duration': 1.0,
            'output': 'transition_dissolve.mp4'
        },
        {
            'name': '不支持的转场类型',
            'transition_type': 'slide_left',  # 第一阶段不支持
            'duration': 1.0,
            'output': 'transition_unsupported.mp4'
        }
    ]
    
    success_count = 0
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
        logger.info(f"转场类型: {config['transition_type']}")
        logger.info(f"转场时长: {config['duration']}s")
        logger.info(f"输出文件: {config['output']}")
        
        try:
            start_time = time.time()
            
            # 创建视频流
            streams = []
            for video_path in test_videos:
                stream = ffmpeg.input(video_path)
                streams.append(stream)
            
            logger.info(f"创建了 {len(streams)} 个视频流")
            
            # 应用转场效果
            if config['transition_type'] == 'none':
                # 测试无转场
                final_stream = ffmpeg.concat(*streams, v=1, a=0)
                logger.info("使用简单连接（无转场）")
            else:
                # 测试转场效果
                final_stream = VideoCompositionService._create_video_with_transitions(
                    streams, config['transition_type'], config['duration']
                )
            
            # 输出视频
            out = ffmpeg.output(
                final_stream,
                config['output'],
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p'
            ).overwrite_output()
            
            # 执行FFmpeg命令
            import subprocess
            
            cmd = ffmpeg.compile(out)
            logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            try:
                # 等待最多60秒
                stdout, stderr = process.communicate(timeout=60)
                
                if process.returncode == 0:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功!")
                        logger.info(f"   文件大小: {file_size} bytes")
                        logger.info(f"   处理时间: {processing_time:.2f}秒")
                        
                        # 说明预期效果
                        if config['transition_type'] == 'none':
                            logger.info("   预期效果: 红-绿-蓝三段视频直接连接，无过渡")
                        elif config['transition_type'] == 'fade':
                            logger.info("   预期效果: 红-绿-蓝三段视频用淡入淡出过渡")
                        elif config['transition_type'] == 'dissolve':
                            logger.info("   预期效果: 红-绿-蓝三段视频用溶解过渡")
                        elif config['transition_type'] == 'slide_left':
                            logger.info("   预期效果: 不支持的转场类型，应该回退到简单连接")
                        
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                else:
                    logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                    if stderr:
                        stderr_text = stderr.decode('utf-8', errors='ignore')
                        logger.error(f"stderr: {stderr_text[:500]}...")
                        
            except subprocess.TimeoutExpired:
                logger.error(f"❌ {config['name']} 测试超时（60秒）")
                process.kill()
                process.communicate()
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n=== 基础转场测试完成 ===")
    logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
    
    if success_count > 0:
        logger.info("\n📋 基础转场测试文件:")
        logger.info("- transition_none.mp4 (无转场)")
        logger.info("- transition_fade.mp4 (淡入淡出转场)")
        logger.info("- transition_dissolve.mp4 (溶解转场)")
        logger.info("- transition_unsupported.mp4 (不支持的转场类型)")
        
        logger.info("\n🔍 第一阶段转场效果:")
        logger.info("✅ none: 无转场，直接连接")
        logger.info("✅ fade: 淡入淡出转场")
        logger.info("✅ dissolve: 溶解转场")
        logger.info("⚠️ 其他类型: 暂不支持，回退到简单连接")
        
        logger.info("\n🎬 验证方法:")
        logger.info("播放transition_*.mp4文件，应该能看到:")
        logger.info("- none: 红绿蓝三段直接切换")
        logger.info("- fade: 红绿蓝三段淡入淡出过渡")
        logger.info("- dissolve: 红绿蓝三段溶解过渡")
        
        logger.info("\n如果能看到明显的转场效果，说明基础转场实现成功！")
        
        return True
    else:
        logger.error("❌ 所有基础转场测试都失败了")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_segment_red.mp4',
        'test_segment_green.mp4', 
        'test_segment_blue.mp4'
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            Path(file_path).unlink()
            logger.info(f"清理测试文件: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始基础转场效果测试")
    logger.info("第一阶段：实现 none、fade、dissolve 转场")
    
    try:
        success = test_basic_transitions()
        
        if success:
            logger.info("\n🎉 基础转场测试完成!")
            logger.info("第一阶段转场效果实现成功！")
            logger.info("现在可以在视频生成时启用转场效果了。")
        else:
            logger.error("\n❌ 基础转场测试失败")
            sys.exit(1)
            
    finally:
        # 清理测试文件
        cleanup_test_files()
