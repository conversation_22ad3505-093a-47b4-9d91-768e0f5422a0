#!/usr/bin/env python3
"""
前后端数据结构一致性验证脚本
验证API返回的数据结构与前端TypeScript定义是否一致
"""

import asyncio
import aiohttp
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Set, Optional

class DataStructureValidator:
    def __init__(self):
        self.api_base = "http://localhost:8000/api/v1"
        self.frontend_types_file = Path(__file__).parent.parent / "frontend" / "src" / "types" / "store.ts"
        
    def extract_typescript_types(self) -> Dict[str, Dict[str, str]]:
        """从TypeScript文件中提取类型定义"""
        if not self.frontend_types_file.exists():
            print(f"❌ Frontend types file not found: {self.frontend_types_file}")
            return {}
        
        content = self.frontend_types_file.read_text(encoding='utf-8')
        types = {}
        
        # 提取接口定义
        interface_pattern = r'interface\s+(\w+)\s*\{([^}]+)\}'
        matches = re.findall(interface_pattern, content, re.DOTALL)
        
        for interface_name, interface_body in matches:
            fields = {}
            # 提取字段定义
            field_pattern = r'(\w+)(\??):\s*([^;]+);'
            field_matches = re.findall(field_pattern, interface_body)
            
            for field_name, optional, field_type in field_matches:
                # 清理类型定义
                field_type = field_type.strip()
                if optional:
                    field_type += " | undefined"
                fields[field_name] = field_type
            
            types[interface_name] = fields
        
        return types
    
    def analyze_api_response_structure(self, data: Any, path: str = "") -> Dict[str, str]:
        """分析API响应的数据结构"""
        structure = {}
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                structure[key] = self._get_type_name(value)
                
                # 如果是嵌套对象，递归分析
                if isinstance(value, dict):
                    nested = self.analyze_api_response_structure(value, current_path)
                    for nested_key, nested_type in nested.items():
                        structure[f"{key}.{nested_key}"] = nested_type
                elif isinstance(value, list) and value and isinstance(value[0], dict):
                    # 数组中的对象类型
                    nested = self.analyze_api_response_structure(value[0], f"{current_path}[0]")
                    for nested_key, nested_type in nested.items():
                        structure[f"{key}[].{nested_key}"] = nested_type
        
        return structure
    
    def _get_type_name(self, value: Any) -> str:
        """获取值的类型名称"""
        if value is None:
            return "null"
        elif isinstance(value, bool):
            return "boolean"
        elif isinstance(value, int):
            return "number"
        elif isinstance(value, float):
            return "number"
        elif isinstance(value, str):
            return "string"
        elif isinstance(value, list):
            if not value:
                return "array (empty)"
            elif all(isinstance(item, (int, float)) for item in value):
                return "number[]"
            elif all(isinstance(item, str) for item in value):
                return "string[]"
            elif all(isinstance(item, dict) for item in value):
                return "object[]"
            else:
                return "array (mixed)"
        elif isinstance(value, dict):
            return "object"
        else:
            return "unknown"
    
    async def fetch_api_data(self) -> Dict[str, Any]:
        """获取所有API端点的数据"""        endpoints = {
            "settings": "/settings",
            "background_music": "/background-music",
            "music_categories": "/background-music/categories/list",
            "video_materials": "/video-materials", 
            "video_categories": "/video-materials/categories/list",
            "prompts": "/prompts",
            "prompt_categories": "/prompts/categories/list",
            "accounts": "/accounts",
            "account_platforms": "/accounts/platforms/list",
            "cover_templates": "/cover-templates",
            "template_categories": "/cover-templates/categories/list"
        }
        
        api_data = {}
        
        async with aiohttp.ClientSession() as session:
            for name, endpoint in endpoints.items():
                try:
                    url = f"{self.api_base}{endpoint}"
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status == 200:
                            data = await response.json()
                            api_data[name] = data
                            print(f"✅ Fetched {name}")
                        else:
                            print(f"❌ Failed to fetch {name}: HTTP {response.status}")
                            api_data[name] = None
                except Exception as e:
                    print(f"❌ Error fetching {name}: {e}")
                    api_data[name] = None
        
        return api_data
    
    def compare_structures(self, typescript_types: Dict[str, Dict[str, str]], 
                          api_data: Dict[str, Any]) -> Dict[str, Any]:
        """比较TypeScript类型和API数据结构"""
        comparison_results = {
            "total_checks": 0,
            "matches": 0,
            "mismatches": 0,
            "missing_in_api": 0,
            "missing_in_types": 0,
            "details": []
        }
        
        # 映射API端点到TypeScript类型
        endpoint_type_mapping = {
            "background_music": "BackgroundMusic",
            "video_materials": "VideoMaterial", 
            "prompts": "Prompt",
            "accounts": "Account",
            "cover_templates": "CoverTemplate",
            "settings": "Settings"
        }
        
        for endpoint, type_name in endpoint_type_mapping.items():
            if endpoint not in api_data or api_data[endpoint] is None:
                continue
                
            if type_name not in typescript_types:
                comparison_results["details"].append({
                    "endpoint": endpoint,
                    "type": type_name,
                    "status": "❌ TypeScript type not found"
                })
                comparison_results["missing_in_types"] += 1
                continue
            
            # 获取API数据结构
            api_response = api_data[endpoint]
            
            # 如果API返回列表，取第一个元素分析
            if isinstance(api_response, dict) and "data" in api_response:
                data_items = api_response["data"]
                if isinstance(data_items, list) and data_items:
                    sample_item = data_items[0]
                elif isinstance(data_items, dict):
                    sample_item = data_items
                else:
                    continue
            else:
                sample_item = api_response
            
            api_structure = self.analyze_api_response_structure(sample_item)
            ts_structure = typescript_types[type_name]
            
            # 比较字段
            endpoint_result = {
                "endpoint": endpoint,
                "type": type_name,
                "field_comparisons": [],
                "status": "✅ MATCH"
            }
            
            all_fields = set(api_structure.keys()) | set(ts_structure.keys())
            
            for field in all_fields:
                comparison_results["total_checks"] += 1
                
                if field in api_structure and field in ts_structure:
                    api_type = api_structure[field]
                    ts_type = ts_structure[field]
                    
                    # 简化类型比较
                    if self._types_compatible(api_type, ts_type):
                        comparison_results["matches"] += 1
                        endpoint_result["field_comparisons"].append({
                            "field": field,
                            "api_type": api_type,
                            "ts_type": ts_type,
                            "status": "✅ MATCH"
                        })
                    else:
                        comparison_results["mismatches"] += 1
                        endpoint_result["field_comparisons"].append({
                            "field": field,
                            "api_type": api_type,
                            "ts_type": ts_type,
                            "status": "❌ TYPE MISMATCH"
                        })
                        endpoint_result["status"] = "❌ MISMATCH"
                        
                elif field in api_structure:
                    comparison_results["missing_in_types"] += 1
                    endpoint_result["field_comparisons"].append({
                        "field": field,
                        "api_type": api_structure[field],
                        "ts_type": "MISSING",
                        "status": "⚠️ MISSING IN TS"
                    })
                    endpoint_result["status"] = "⚠️ PARTIAL"
                    
                else:  # field in ts_structure
                    comparison_results["missing_in_api"] += 1
                    endpoint_result["field_comparisons"].append({
                        "field": field,
                        "api_type": "MISSING",
                        "ts_type": ts_structure[field],
                        "status": "⚠️ MISSING IN API"
                    })
                    endpoint_result["status"] = "⚠️ PARTIAL"
            
            comparison_results["details"].append(endpoint_result)
        
        return comparison_results
    
    def _types_compatible(self, api_type: str, ts_type: str) -> bool:
        """检查API类型和TypeScript类型是否兼容"""
        # 简化的类型兼容性检查
        
        # 处理可选类型
        if " | undefined" in ts_type:
            ts_type = ts_type.replace(" | undefined", "")
        
        # 直接匹配
        if api_type == ts_type:
            return True
        
        # 数字类型兼容
        if api_type == "number" and ts_type in ["number", "int", "float"]:
            return True
        
        # 字符串类型兼容
        if api_type == "string" and ts_type in ["string", "str"]:
            return True
        
        # 布尔类型兼容
        if api_type == "boolean" and ts_type in ["boolean", "bool"]:
            return True
        
        # 数组类型兼容
        if api_type.endswith("[]") and ts_type.endswith("[]"):
            return True
        
        # 对象类型兼容
        if api_type == "object" and not ts_type.endswith("[]") and ts_type not in ["string", "number", "boolean"]:
            return True
        
        return False
    
    def print_comparison_report(self, results: Dict[str, Any]):
        """打印比较报告"""
        print("\n📊 Data Structure Comparison Report")
        print("=" * 50)
        
        print(f"Total Checks: {results['total_checks']}")
        print(f"✅ Matches: {results['matches']}")
        print(f"❌ Mismatches: {results['mismatches']}")
        print(f"⚠️ Missing in API: {results['missing_in_api']}")
        print(f"⚠️ Missing in Types: {results['missing_in_types']}")
        
        success_rate = results['matches'] / results['total_checks'] * 100 if results['total_checks'] > 0 else 0
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        print("\n📋 Detailed Results:")
        print("-" * 30)
        
        for detail in results['details']:
            if isinstance(detail, dict) and 'endpoint' in detail:
                print(f"\n🔗 {detail['endpoint']} ({detail['type']}) - {detail['status']}")
                
                if 'field_comparisons' in detail:
                    for field_comp in detail['field_comparisons']:
                        status_icon = "✅" if field_comp['status'] == "✅ MATCH" else "❌" if "MISMATCH" in field_comp['status'] else "⚠️"
                        print(f"  {status_icon} {field_comp['field']}: API({field_comp['api_type']}) <-> TS({field_comp['ts_type']})")
        
        return success_rate >= 80  # 80%以上匹配率算成功
    
    async def validate_data_structures(self) -> bool:
        """执行完整的数据结构验证"""
        print("🔍 Starting Data Structure Validation")
        print("=" * 50)
        
        # 1. 提取TypeScript类型
        print("📝 Extracting TypeScript types...")
        typescript_types = self.extract_typescript_types()
        
        if not typescript_types:
            print("❌ No TypeScript types found")
            return False
        
        print(f"✅ Found {len(typescript_types)} TypeScript interfaces")
        
        # 2. 获取API数据
        print("\n📡 Fetching API data...")
        api_data = await self.fetch_api_data()
        
        successful_fetches = sum(1 for data in api_data.values() if data is not None)
        print(f"✅ Successfully fetched {successful_fetches}/{len(api_data)} endpoints")
        
        if successful_fetches == 0:
            print("❌ No API data could be fetched")
            return False
        
        # 3. 比较结构
        print("\n🔄 Comparing structures...")
        comparison_results = self.compare_structures(typescript_types, api_data)
        
        # 4. 生成报告
        success = self.print_comparison_report(comparison_results)
        
        if success:
            print("\n🎉 Data structure validation passed!")
        else:
            print("\n❌ Data structure validation failed!")
            print("Please review the mismatches above and fix them.")
        
        return success

async def main():
    """主函数"""
    validator = DataStructureValidator()
    
    try:
        success = await validator.validate_data_structures()
        return 0 if success else 1
    except Exception as e:
        print(f"\n💥 Validation error: {e}")
        return 1

if __name__ == "__main__":
    print("🔍 Reddit Story Video Generator - Data Structure Validator")
    print("=" * 60)
    exit_code = asyncio.run(main())
    exit(exit_code)
