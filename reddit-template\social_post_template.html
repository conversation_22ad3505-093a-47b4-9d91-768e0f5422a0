<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交媒体帖子模板</title>
    <style>
        .template-container * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .template-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            padding: 20px;
        }

        .template-container .post-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .template-container .post-header {
            display: flex;
            align-items: center;
            padding: 16px 20px;
        }

        .template-container .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            position: relative;
            overflow: hidden;
            background: #f0f0f0; /* 备用背景色 */
        }

        .template-container .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .template-container .user-info {
            flex: 1;
        }

        .template-container .username {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 2px;
        }

        .template-container .verified-badge {
            width: 30px;
            height: 30px;
            margin-left: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .template-container .verified-badge img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .template-container .icon-badges {
            display: flex;
            gap: 3px;
            margin-top: 4px;
        }

        .template-container .icon {
            width: 20px;
            height: 20px;
        }

        .template-container .icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .template-container .post-content {
            padding: 20px;
        }

        .template-container .post-title {
            font-size: 28px;
            font-weight: 700;
            color: #1a1a1a;
            line-height: 1.3;
            margin-bottom: 20px;
        }

        .template-container .post-stats {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 16px 20px;
            color: #657786;
            font-size: 14px;
        }

        .template-container .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .template-container .stat-icon {
            width: 20px;
            height: 20px;
        }

        .template-container .stat-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .template-container .stat-span {
            font-size: 17px;
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 640px) {
            .template-container .post-container {
                margin: 0 10px;
            }
            
            .template-container .post-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="template-container">
        <div class="post-container" id="reddit-cover">
        <!-- 帖子头部 -->
        <div class="post-header">
            <div class="avatar"><img src="{{avatar}}" /></div>
            <div class="user-info">
                <div class="username">
                    {{account_name}}
                    <div class="verified-badge">
                        <img src="http://************/images/auth.png" alt="认证" />
                    </div>
                </div>
                <div class="icon-badges">
                    <div class="icon"><img src="http://************/images/icons/1.png" alt="图标1" /></div>
                    <div class="icon"><img src="http://************/images/icons/2.png" alt="图标2" /></div>
                    <div class="icon"><img src="http://************/images/icons/3.png" alt="图标3" /></div>
                    <div class="icon"><img src="http://************/images/icons/4.png" alt="图标4" /></div>
                    <div class="icon"><img src="http://************/images/icons/5.png" alt="图标5" /></div>
                    <div class="icon"><img src="http://************/images/icons/6.png" alt="图标6" /></div>
                    <div class="icon"><img src="http://************/images/icons/7.png" alt="图标7" /></div>
                    <div class="icon"><img src="http://************/images/icons/8.png" alt="图标8" /></div>
                    <div class="icon"><img src="http://************/images/icons/9.png" alt="图标9" /></div>
                    <div class="icon"><img src="http://************/images/icons/10.png" alt="图标10" /></div>
                </div>
            </div>
        </div>

        <!-- 帖子内容 -->
        <div class="post-content">
            <h1 class="post-title">
                {{title}}
            </h1>
        </div>

        <!-- 帖子统计 -->
        <div class="post-stats">
            <div class="stat-item">
                <div class="stat-icon">
                    <img src="http://************/images/like.png" alt="点赞" />
                </div>
                <span class="stat-span">99+</span>
            </div>
            <div class="stat-item">
                <div class="stat-icon">
                    <img src="http://************/images/comment.png" alt="评论" />
                </div>
                <span class="stat-span">99+</span>
            </div>
        </div>
    </div>

    <script>
        // 可以添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const stats = document.querySelectorAll('.stat-item');
            stats.forEach(stat => {
                stat.addEventListener('click', function() {
                    const count = this.querySelector('span:last-child');
                    let currentCount = parseInt(count.textContent.replace('+', ''));
                    count.textContent = (currentCount + 1) + '+';
                });
            });
        });
    </script>
</body>
</html>
