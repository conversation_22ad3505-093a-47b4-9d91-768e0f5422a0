@echo off
echo === 启动后端服务器并测试视频上传功能 ===

cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend"

echo 1. 启动后端服务器...
start "Backend Server" python main.py

echo 2. 等待服务器启动...
timeout /t 3 /nobreak > nul

echo 3. 测试后端API连接...
python -c "import requests; r = requests.get('http://localhost:8000/api/video-materials/'); print(f'API连接测试: {r.status_code}')"

echo.
echo 后端服务器已启动，请在浏览器中访问前端页面测试上传功能
echo 前端开发服务器命令: cd frontend && npm run dev
echo 后端API文档: http://localhost:8000/docs
echo.
echo 按任意键关闭后端服务器...
pause > nul
taskkill /f /im python.exe 2>nul
echo 后端服务器已关闭
