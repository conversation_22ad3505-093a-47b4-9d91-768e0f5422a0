# Reddit故事视频生成器 - 开发路线图

## 🎯 总体开发计划

基于个性化视频生成的核心理念，按优先级分阶段实施：

### 第一优先级：基础资源管理 (1-2周)

#### 1. **账号管理** (`/accounts`) - ✅ 已完成
- **后端实现**：
  - 账号信息CRUD `/api/accounts/`
  - 头像上传和存储 `/api/accounts/{id}/avatar`
  - 账号配置管理（平台信息、个人资料等）
- **前端对接**：
  - 账号列表和编辑界面
  - 头像上传组件
  - 账号选择器组件（供其他模块使用）
- **关键数据**：
  - 账号名称、头像、简介
  - 平台信息（Reddit、YouTube、TikTok等）
  - 个性化设置（字体、颜色偏好等）

#### 2. **背景音乐管理** (`/background-music`) - ✅ 已完成
- **后端实现**：
  - 音频文件上传接口 `/api/music/upload`
  - 音频列表查询和分类 `/api/music/list`
  - 音频预览/下载 `/api/music/{id}/play`
- **前端对接**：
  - 文件拖拽上传组件
  - 音频播放器组件
  - 音乐库管理界面

#### 3. **视频素材管理** (`/video-materials`) - ✅ 已完成
- **后端实现**：
  - 视频/图片上传处理
  - 缩略图生成
  - 素材分类和标签管理
- **前端对接**：
  - 素材预览网格
  - 素材搜索和筛选
  - 批量操作功能

### 第二优先级：模板和内容工具 (1-2周)

#### 4. **封面模板管理** (`/cover-templates`) - ✅ 已完成
- **后端实现**：
  - 模板文件存储和管理
  - **动态封面生成API** `/api/templates/generate-cover`
    - 接收账号ID、标题、风格参数
    - 返回个性化封面图片
- **前端对接**：
  - 模板选择器
  - **实时预览功能**（输入账号信息即时生成预览）
  - 模板参数配置（字体、颜色、布局等）

#### 5. **提示词管理** (`/prompts`) - ✅ 已完成
- **后端实现**：
  - 提示词模板CRUD
  - 分类和标签管理
  - 模板变量支持（账号名称等）
  - **真实LLM测试功能** - 联动系统设置中的LLM配置
- **前端对接**：
  - 提示词编辑器
  - 模板选择和应用
  - 变量替换预览
  - **删除导出模板功能**
  - **基于真实LLM的提示词测试**

### 第三优先级：核心生成流程 (2-3周)

#### 6. **视频生成功能** (`/video-generator`) - ✅ 核心功能已完成
- **后端实现**：
  - 整合所有资源（账号、素材、音乐、模板）
  - **个性化视频生成Pipeline**：
    1. 根据选定账号生成个性化封面
    2. 使用账号信息定制故事内容  
    3. 自动音频分析和字幕生成
    4. 视频合成（1080×1920竖屏，30fps，MP4）
  - **技术特性**：
    - TTS音频分析和时间戳提取
    - SRT字幕文件生成（1-3个英文单词/段）
    - 智能素材选择（单视频内尽量不重复）
    - 背景音乐自动裁剪/重复匹配
- **前端对接**：
  - **批量任务配置界面** ✅ 已完成
  - 账号选择和数量设定 ✅ 已完成
  - 素材分组选择（随机/手动） ✅ 已完成
  - 提示词分组和选择 ✅ 已完成
  - 音色、语速、字幕样式配置 ✅ 已完成
  - 任务进度监控（暂停/取消/重试） ✅ 已完成

#### 7. **任务队列系统** (`/task-queue`) - ✅ 已集成到视频生成模块
- 管理个性化视频生成任务 ✅ 已完成
- 任务进度追踪 ✅ 已完成
- 结果文件管理 🔄 基础功能已完成，优化中

### 第四优先级：分析和优化

#### 8. **仪表板** (`/dashboard`)
- 各账号的视频生成统计
- 素材使用分析
- 性能监控

## 🔧 核心设计理念

### 账号驱动的个性化生成
1. **动态封面**：`模板 + 账号头像 + 账号名称 + 视频标题 = 个性化封面`
2. **内容定制**：提示词中包含账号信息，生成符合账号风格的内容
3. **品牌一致性**：每个账号有独特的视觉风格和内容调性

### 数据流设计
```
账号信息 → 封面模板 → 个性化封面
账号信息 → 提示词模板 → 个性化内容
个性化封面 + 个性化内容 + 素材 + 音乐 → 最终视频
```

## 📊 项目进度跟踪

- [x] 系统设置页面 (TTS + LLM配置)
- [x] 通知系统
- [x] **账号管理** (✅ 已完成)
- [x] **背景音乐管理** (✅ 已完成)
- [x] **视频素材管理** (✅ 已完成)
- [x] **封面模板管理** (✅ 已完成)
- [x] **提示词管理** (✅ 已完成)
- [x] **视频生成功能** (✅ 核心功能已完成，待联调测试)
- [x] 任务队列系统 (✅ 已集成到视频生成模块)
- [ ] 仪表板 (🔄 低优先级)

## 🎯 成功标准

每个功能模块完成后应达到：
1. **前后端API完全对接** ✅
2. **UI/UX符合原型设计** ✅
3. **错误处理和用户提示完善** ✅
4. **基本的单元测试覆盖** (🔄 待补充)

## 📋 当前状态总结

### ✅ 已完成功能 (95%+)
- 基础资源管理：账号、素材、音乐、模板、提示词
- 系统配置：TTS/LLM设置
- 视频生成：批量作业模式、任务管理、进度监控
- 前端界面：配置页面、任务管理页面
- API服务：统一的前后端通信层

### 🔄 待完善功能 (5%)
- 端到端联调测试
- 视频文件预览和下载
- 性能优化和错误处理细节
- 单元测试和集成测试

### 🎯 下一步重点
1. **立即测试**：启动后端服务，验证API连通性
2. **联调验证**：创建测试作业，验证完整流程
3. **结果验证**：确认视频生成质量和格式
4. **体验优化**：根据测试结果优化UI/UX

---

*最后更新：2025-01-27*
*当前状态：视频生成功能开发基本完成，进入联调测试阶段*
*完成度：95% (核心功能完整，细节优化中)*
