#!/usr/bin/env python3
"""测试ASS字幕文件创建"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

from services.video_generation_helpers import VideoCompositionService

# 创建测试SRT文件
test_srt_content = """1
00:00:01,000 --> 00:00:05,000
这是第一行字幕

2
00:00:06,000 --> 00:00:10,000
这是第二行字幕
颜色应该是红色

3
00:00:11,000 --> 00:00:15,000
这是第三行字幕
位置应该在顶部
"""

# 写入测试SRT文件
with open('test_subtitle.srt', 'w', encoding='utf-8') as f:
    f.write(test_srt_content)

# 测试不同配置
configs = [
    ('Arial', 24, '#FF0000', 'top'),     # 红色，顶部
    ('Arial', 30, '#00FF00', 'center'),  # 绿色，居中
    ('Arial', 32, '#0000FF', 'bottom'),  # 蓝色，底部
]

for i, (font, size, color, pos) in enumerate(configs):
    ass_path = f'test_subtitle_{pos}.ass'
    print(f'\n测试配置 {i+1}: 字体={font}, 大小={size}, 颜色={color}, 位置={pos}')
    
    success = VideoCompositionService._create_ass_subtitle_file(
        'test_subtitle.srt', ass_path, font, size, color, pos
    )
    
    if success:
        print(f'✅ 成功创建: {ass_path}')
        # 读取并显示ASS文件头部
        with open(ass_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line.startswith('Style:'):
                    print(f'样式行: {line}')
                    break
    else:
        print(f'❌ 创建失败: {ass_path}')

# 清理测试文件
files_to_clean = ['test_subtitle.srt'] + [f'test_subtitle_{pos}.ass' for _, _, _, pos in configs]
for file in files_to_clean:
    if os.path.exists(file):
        os.remove(file)
        print(f'清理文件: {file}')
