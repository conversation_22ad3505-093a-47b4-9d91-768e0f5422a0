2025-08-22 14:51:14.058 | ERROR    | src.services.video_generation_helpers:run_ffmpeg_async:57 - 创建中间视频失败 (返回码: 4294967274): ffmpeg version 7.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers    
  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme 
--enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband     
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.100 / 61. 19.100
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a489200] Unknown cover type: 0x1.
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/b7602f12ef984c0594b6ff93c90e1208_素材82.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T03:48:07.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 27075 kb/s
  Stream #0:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 27214 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T03:48:07.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T03:48:07.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a489880] Unknown cover type: 0x1.
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/f163429f191d42a389b2c76ae43013f6_素材131.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T04:11:05.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 22192 kb/s
  Stream #1:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 22302 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T04:11:05.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #1:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T04:11:05.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a920d40] Unknown cover type: 0x1.
Input #2, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/bf063ba538354f09b5ffceaf39fa3866_素材144.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T03:32:34.000000Z
    Hw              : 1
    bitrate         : 26000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 27369 kb/s
  Stream #2:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 27505 kb/s, 60 fps, 60 tbr, 
60 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T03:32:34.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #2:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T03:32:34.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09b8e8a80] Unknown cover type: 0x1.
Input #3, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/18d3b7fe88aa44cebc283aa8ca64d652_素材24.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T03:37:42.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 17858 kb/s
  Stream #3:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 17942 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T03:37:42.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #3:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T03:37:42.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09aa99c80] Unknown cover type: 0x1.
Input #4, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/29b7bbd87946449f94bb8071556eed88_素材393.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T08:53:50.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 18781 kb/s
  Stream #4:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 18949 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T08:53:50.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #4:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T08:53:50.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a54d540] Unknown cover type: 0x1.
Input #5, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/2d66a2d3c918426085387d80ef4ace2a_素材278.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T07:43:14.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 20376 kb/s
  Stream #5:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 20559 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T07:43:14.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #5:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T07:43:14.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a533280] Unknown cover type: 0x1.
Input #6, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/c68d00c73cb94bc6824c8bcba2d2ee53_素材320.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T07:44:18.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 17199 kb/s
  Stream #6:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 17351 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T07:44:18.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #6:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T07:44:18.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a54c100] Unknown cover type: 0x1.
Input #7, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/5634099963a24b44acf74865e44a46ae_素材406.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T08:55:39.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 16128 kb/s
  Stream #7:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 16269 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:39.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #7:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:39.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09aa5da00] Unknown cover type: 0x1.
Input #8, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/493654c488a940638ae0e448878a1a47_素材391.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T08:55:17.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 17234 kb/s
  Stream #8:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 17386 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:17.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #8:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:17.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 (h264) -> scale:default
  Stream #1:0 (h264) -> scale:default
  Stream #2:0 (h264) -> scale:default
  Stream #3:0 (h264) -> scale:default
  Stream #4:0 (h264) -> scale:default
  Stream #5:0 (h264) -> scale:default
  Stream #6:0 (h264) -> scale:default
  Stream #7:0 (h264) -> scale:default
  Stream #8:0 (h264) -> scale:default
  trim:default -> Stream #0:0 (libx264)
Press [q] to stop, [?] for help
[Parsed_xfade_10 @ 000001c09af11880] First input link main timebase (1/30) do 
not match the corresponding second input link xfade timebase (1/60)
[Parsed_xfade_10 @ 000001c09af11880] Failed to configure output pad on Parsed_xfade_10
[fc#0 @ 000001c09a483540] Error reinitializing filters!
[fc#0 @ 000001c09a483540] Task finished with error code: -22 (Invalid argument)
[fc#0 @ 000001c09a483540] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/libx264 @ 000001c09aa53b40] Could not open encoder before EOF       
[vost#0:0/libx264 @ 000001c09aa53b40] Task finished with error code: -22 (Invalid argument)
[vost#0:0/libx264 @ 000001c09aa53b40] Terminating thread with return code -22 
(Invalid argument)
[out#0/mp4 @ 000001c09ab16ec0] Nothing was written into output file, because at least one of its streams received no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    
Conversion failed!

2025-08-22 14:51:14.071 | ERROR    | src.services.video_generation_helpers:run_ffmpeg_async:64 - 创建中间视频异步执行失败: ffmpeg error (see stderr output for detail)
2025-08-22 14:51:14.072 | ERROR    | src.services.video_generation_helpers:run_ffmpeg_async:65 - Traceback (most recent call last):
  File "D:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend\src\services\video_generation_helpers.py", line 58, in run_ffmpeg_async
    raise ffmpeg.Error('ffmpeg', result.stdout, result.stderr)
ffmpeg._run.Error: ffmpeg error (see stderr output for detail)

2025-08-22 14:51:14.072 | ERROR    | src.services.video_generation_helpers:_create_intermediate_video:158 - 创建中间视频失败: ffmpeg version 7.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme 
--enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband     
  libavutil      59. 39.100 / 59. 39.100
  libavcodec     61. 19.100 / 61. 19.100
  libavformat    61.  7.100 / 61.  7.100
  libavdevice    61.  3.100 / 61.  3.100
  libavfilter    10.  4.100 / 10.  4.100
  libswscale      8.  3.100 /  8.  3.100
  libswresample   5.  3.100 /  5.  3.100
  libpostproc    58.  3.100 / 58.  3.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a489200] Unknown cover type: 0x1.
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/b7602f12ef984c0594b6ff93c90e1208_素材82.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T03:48:07.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 27075 kb/s
  Stream #0:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 27214 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T03:48:07.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T03:48:07.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a489880] Unknown cover type: 0x1.
Input #1, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/f163429f191d42a389b2c76ae43013f6_素材131.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T04:11:05.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 22192 kb/s
  Stream #1:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 22302 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T04:11:05.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #1:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T04:11:05.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a920d40] Unknown cover type: 0x1.
Input #2, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/bf063ba538354f09b5ffceaf39fa3866_素材144.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T03:32:34.000000Z
    Hw              : 1
    bitrate         : 26000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 27369 kb/s
  Stream #2:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 27505 kb/s, 60 fps, 60 tbr, 
60 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T03:32:34.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #2:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T03:32:34.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09b8e8a80] Unknown cover type: 0x1.
Input #3, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/18d3b7fe88aa44cebc283aa8ca64d652_素材24.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T03:37:42.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:01.51, start: 0.000000, bitrate: 17858 kb/s
  Stream #3:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 17942 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T03:37:42.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #3:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T03:37:42.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09aa99c80] Unknown cover type: 0x1.
Input #4, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/29b7bbd87946449f94bb8071556eed88_素材393.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T08:53:50.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 18781 kb/s
  Stream #4:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 18949 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T08:53:50.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #4:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T08:53:50.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a54d540] Unknown cover type: 0x1.
Input #5, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/2d66a2d3c918426085387d80ef4ace2a_素材278.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T07:43:14.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 20376 kb/s
  Stream #5:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 20559 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T07:43:14.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #5:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T07:43:14.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a533280] Unknown cover type: 0x1.
Input #6, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/c68d00c73cb94bc6824c8bcba2d2ee53_素材320.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T07:44:18.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 17199 kb/s
  Stream #6:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 17351 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T07:44:18.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #6:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T07:44:18.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09a54c100] Unknown cover type: 0x1.
Input #7, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/5634099963a24b44acf74865e44a46ae_素材406.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T08:55:39.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 16128 kb/s
  Stream #7:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 16269 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:39.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #7:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:39.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
[mov,mp4,m4a,3gp,3g2,mj2 @ 000001c09aa5da00] Unknown cover type: 0x1.
Input #8, mov,mp4,m4a,3gp,3g2,mj2, from 'D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/uploads/video_materials/493654c488a940638ae0e448878a1a47_素材391.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    creation_time   : 2025-06-16T08:55:17.000000Z
    Hw              : 1
    bitrate         : 18000000
    maxrate         : 0
    te_is_reencode  : 1
    encoder         : Lavf61.1.100
  Duration: 00:00:02.02, start: 0.000000, bitrate: 17234 kb/s
  Stream #8:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1440x2560 [SAR 1:1 DAR 9:16], 17386 kb/s, 30 fps, 30 tbr, 
30 tbn (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:17.000000Z
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #8:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 2 kb/s (default)
      Metadata:
        creation_time   : 2025-06-16T08:55:17.000000Z
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 (h264) -> scale:default
  Stream #1:0 (h264) -> scale:default
  Stream #2:0 (h264) -> scale:default
  Stream #3:0 (h264) -> scale:default
  Stream #4:0 (h264) -> scale:default
  Stream #5:0 (h264) -> scale:default
  Stream #6:0 (h264) -> scale:default
  Stream #7:0 (h264) -> scale:default
  Stream #8:0 (h264) -> scale:default
  trim:default -> Stream #0:0 (libx264)
Press [q] to stop, [?] for help
[Parsed_xfade_10 @ 000001c09af11880] First input link main timebase (1/30) do 
[Parsed_xfade_10 @ 000001c09af11880] First input link main timebase (1/30) do not match the corresponding second input link xfade timebase (1/60)
[Parsed_xfade_10 @ 000001c09af11880] Failed to configure output pad on Parsed_xfade_10
[fc#0 @ 000001c09a483540] Error reinitializing filters!
[fc#0 @ 000001c09a483540] Task finished with error code: -22 (Invalid argument)
[fc#0 @ 000001c09a483540] Terminating thread with return code -22 (Invalid argument)
[vost#0:0/libx264 @ 000001c09aa53b40] Could not open encoder before EOF
[vost#0:0/libx264 @ 000001c09aa53b40] Task finished with error code: -22 (Invalid argument)
[vost#0:0/libx264 @ 000001c09aa53b40] Terminating thread with return code -22 (Invalid argument)
[out#0/mp4 @ 000001c09ab16ec0] Nothing was written into output file, because at least one of its streams received 
no packets.
frame=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A
Conversion failed!

2025-08-22 14:51:14.088 | ERROR    | src.services.video_generation_helpers:compose_video:518 - 视频合成失败 (非ffmpeg错误): 创建中间视频失败
2025-08-22 14:51:14.090 | ERROR    | src.services.video_generation_service:_execute_video_composition:673 - 视频合
成失败: 998c46e1-2f11-4aa7-b94c-bf24ce4cbb03, 视频合成失败
2025-08-22 14:51:14.092 | ERROR    | src.services.video_generation_service:_execute_video_composition:674 - Traceback (most recent call last):
  File "D:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend\src\services\video_generation_service.py", line 659, in _execute_video_composition
    video_path = await helper._compose_video(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend\src\services\video_generation_helpers.py", line 1479, in _compose_video
    raise ValueError("视频合成失败")
ValueError: 视频合成失败