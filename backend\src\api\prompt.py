"""
提示词管理API
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import uuid4

from ..core.database import get_db
from ..core.responses import success_response, error_response
from ..models.resources import Prompt
from ..schemas.resources import (
    PromptCreate,
    PromptUpdate,
    PromptResponse,
    PromptQuery,
    BulkPromptResponse
)

router = APIRouter(prefix="/prompts", tags=["prompts"])

@router.get("/", response_model=List[PromptResponse])
async def get_prompts(
    query: PromptQuery = Depends(),
    db: Session = Depends(get_db)
):
    """获取提示词列表"""
    try:
        db_query = db.query(Prompt)
        
        # 分类过滤
        if query.category:
            db_query = db_query.filter(Prompt.category == query.category)
        
        # 搜索过滤
        if query.search:
            db_query = db_query.filter(
                Prompt.name.contains(query.search) |
                Prompt.content.contains(query.search)
            )
        
        # 分页
        if query.skip is not None:
            db_query = db_query.offset(query.skip)
        if query.limit is not None:
            db_query = db_query.limit(query.limit)
        
        prompts = db_query.all()
        
        # 转换为前端格式
        result = []
        for prompt in prompts:
            frontend_data = prompt.to_frontend_format()
            result.append(PromptResponse(**frontend_data))
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取提示词列表失败: {str(e)}"
        )

@router.post("/", response_model=PromptResponse)
async def create_prompt(
    prompt_data: PromptCreate,
    db: Session = Depends(get_db)
):
    """创建提示词"""
    try:
        # 创建新提示词
        db_prompt = Prompt(
            id=str(uuid4()),
            name=prompt_data.name,
            content=prompt_data.content,
            category=prompt_data.category,
            variables=prompt_data.variables,
            is_built_in=prompt_data.is_built_in,
            description=prompt_data.description,
            example_output=prompt_data.example_output
        )
        
        db.add(db_prompt)
        db.commit()
        db.refresh(db_prompt)
        
        # 转换为前端格式
        frontend_data = db_prompt.to_frontend_format()
        return PromptResponse(**frontend_data)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建提示词失败: {str(e)}"
        )

@router.get("/{prompt_id}", response_model=PromptResponse)
async def get_prompt(
    prompt_id: str,
    db: Session = Depends(get_db)
):
    """获取单个提示词"""
    try:
        db_prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
        if not db_prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="提示词未找到"
            )
        
        # 转换为前端格式
        frontend_data = db_prompt.to_frontend_format()
        return PromptResponse(**frontend_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取提示词失败: {str(e)}"
        )

@router.put("/{prompt_id}", response_model=PromptResponse)
async def update_prompt(
    prompt_id: str,
    prompt_data: PromptUpdate,
    db: Session = Depends(get_db)
):
    """更新提示词"""
    try:
        db_prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
        if not db_prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="提示词未找到"
            )
        
        # 更新字段
        update_data = prompt_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_prompt, field, value)
        
        db.commit()
        db.refresh(db_prompt)
        
        # 转换为前端格式
        frontend_data = db_prompt.to_frontend_format()
        return PromptResponse(**frontend_data)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新提示词失败: {str(e)}"
        )

@router.delete("/{prompt_id}")
async def delete_prompt(
    prompt_id: str,
    db: Session = Depends(get_db)
):
    """删除提示词"""
    try:
        db_prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
        if not db_prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="提示词未找到"
            )
        
        db.delete(db_prompt)
        db.commit()
        
        return success_response(None, "提示词删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除提示词失败: {str(e)}"
        )

@router.post("/bulk", response_model=BulkPromptResponse)
async def bulk_create_prompts(
    prompts_data: List[PromptCreate],
    db: Session = Depends(get_db)
):
    """批量创建提示词"""
    try:
        created_prompts = []
        failed_prompts = []
        
        for prompt_data in prompts_data:
            try:
                db_prompt = Prompt(
                    id=str(uuid4()),
                    name=prompt_data.name,
                    content=prompt_data.content,
                    category=prompt_data.category,
                    variables=prompt_data.variables,
                    is_built_in=prompt_data.is_built_in,
                    description=prompt_data.description,
                    example_output=prompt_data.example_output
                )
                
                db.add(db_prompt)
                db.flush()  # 获取生成的ID但不提交
                
                frontend_data = db_prompt.to_frontend_format()
                created_prompts.append(PromptResponse(**frontend_data))
                
            except Exception as e:
                failed_prompts.append({
                    "name": prompt_data.name,
                    "error": str(e)
                })
        
        db.commit()
        
        return BulkPromptResponse(
            success=created_prompts,
            failed=failed_prompts,
            total=len(prompts_data),
            success_count=len(created_prompts),
            failed_count=len(failed_prompts)
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建提示词失败: {str(e)}"
        )

@router.delete("/bulk")
async def bulk_delete_prompts(
    prompt_ids: List[str] = Query(..., description="要删除的提示词ID列表"),
    db: Session = Depends(get_db)
):
    """批量删除提示词"""
    try:
        deleted_count = db.query(Prompt).filter(
            Prompt.id.in_(prompt_ids)
        ).delete(synchronize_session=False)
        
        db.commit()
        
        return success_response(None, f"成功删除{deleted_count}个提示词")
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除提示词失败: {str(e)}"
        )

@router.get("/categories/list")
async def get_prompt_categories(db: Session = Depends(get_db)):
    """获取提示词分类列表"""
    try:
        categories = db.query(Prompt.category).distinct().all()
        category_list = [cat[0] for cat in categories if cat[0]]
        
        return success_response({"categories": category_list}, "获取分类列表成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分类列表失败: {str(e)}"
        )

@router.post("/{prompt_id}/use")
async def use_prompt(
    prompt_id: str,
    db: Session = Depends(get_db)
):
    """使用提示词（增加使用次数）"""
    try:
        db_prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
        if not db_prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="提示词未找到"
            )
          # 增加使用次数
        db_prompt.usage_count += 1  # type: ignore
        db.commit()
        
        return success_response(None, "提示词使用次数已更新")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新使用次数失败: {str(e)}"
        )
