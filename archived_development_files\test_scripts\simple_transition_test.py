#!/usr/bin/env python3
"""
简单的转场测试 - 验证多段转场是否修复
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_transition():
    """简单的转场测试"""
    
    logger.info("🎬 开始简单转场测试...")
    
    try:
        # 创建3个2秒的测试视频
        colors = ['red', 'green', 'blue']
        test_files = []
        
        for color in colors:
            filename = f"test_{color}.mp4"
            logger.info(f"创建 {filename}")
            
            # 创建2秒的纯色视频
            (
                ffmpeg
                .input(f'color={color}:size=320x240:duration=2:rate=30', f='lavfi')
                .output(filename, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
            test_files.append(filename)
        
        # 手动构建转场命令
        logger.info("构建转场命令...")
        
        # 输入文件
        inputs = []
        for file in test_files:
            inputs.extend(['-i', file])
        
        # 滤镜复合
        filter_complex = [
            '[0:v]fps=30[v0]',
            '[1:v]fps=30[v1]', 
            '[2:v]fps=30[v2]',
            '[v0][v1]xfade=transition=fade:duration=0.5:offset=1.5[t1]',
            '[t1][v2]xfade=transition=fade:duration=0.5:offset=3.0[out]'
        ]
        
        cmd = [
            'ffmpeg'
        ] + inputs + [
            '-filter_complex', ';'.join(filter_complex),
            '-map', '[out]',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-y',
            'simple_transition_result.mp4'
        ]
        
        logger.info("执行命令:")
        logger.info(' '.join(cmd))
        
        # 执行
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            if Path('simple_transition_result.mp4').exists():
                file_size = Path('simple_transition_result.mp4').stat().st_size
                logger.info("✅ 简单转场测试成功!")
                logger.info(f"文件大小: {file_size} bytes")
                logger.info("预期时长: 5.0s (2+2+2-2*0.5)")
                logger.info("预期效果: 红色→绿色→蓝色，每段2秒，0.5秒转场")
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ 命令执行失败: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理
        for color in colors:
            filename = f"test_{color}.mp4"
            if Path(filename).exists():
                Path(filename).unlink()

if __name__ == "__main__":
    success = test_simple_transition()
    if success:
        logger.info("🎉 简单转场测试成功!")
    else:
        logger.error("❌ 简单转场测试失败")
        sys.exit(1)
