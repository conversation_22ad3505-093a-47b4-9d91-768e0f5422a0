/**
 * 视频素材管理页面 - 按照原型设计实现
 */

'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { useVideoMaterialStore, type VideoMaterial } from '@/store/videoMaterialStore'
import { useNotificationStore } from '@/store/notificationStore'

// API基础URL配置
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

// 安全的日期格式化函数，避免hydration错误
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    // 使用固定格式避免locale差异
    return date.toISOString().slice(0, 19).replace('T', ' ')
  } catch (error) {
    return '未知时间'
  }
}

export default function VideoMaterialsPage() {
  // 客户端挂载状态检查，避免hydration错误
  const [isMounted, setIsMounted] = useState(false)
  
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedType, setSelectedType] = useState('')
  const [showPreviewModal, setShowPreviewModal] = useState(false)
  const [previewMaterial, setPreviewMaterial] = useState<VideoMaterial | null>(null)
  const [showCategoryModal, setShowCategoryModal] = useState(false)
  const [newCategory, setNewCategory] = useState('')
  const [uploadCategory, setUploadCategory] = useState('general') // 上传时选择的分类
  const [showUploadModal, setShowUploadModal] = useState(false) // 控制上传模态框
  const [isUploadingBatch, setIsUploadingBatch] = useState(false) // 控制是否为批量上传模式
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false) // 控制删除确认对话框
  const [materialToDelete, setMaterialToDelete] = useState<{id: string, material?: VideoMaterial} | null>(null) // 待删除的素材
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const { addNotification } = useNotificationStore()
  
  const {
    materials,
    currentView,
    isLoading,
    error,
    uploadProgress,
    categories,
    isCategoriesLoading,
    addMaterials,
    removeMaterial,
    deleteMaterialFromServer,
    loadMaterials,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    uploadFile,
    bulkUploadFiles,
    getTotalCount,
    getTotalSize,
    getTypeStats,
    getCategoryStats,
    setView,
    setLoading,
    setError
  } = useVideoMaterialStore()

  // 过滤素材 - 只保留视频类型
  const filteredMaterials = materials.filter((material: VideoMaterial) => {
    const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || material.category === selectedCategory
    const matchesType = !selectedType || material.format.toLowerCase() === selectedType
    // 只显示视频类型
    return material.type === 'video' && matchesSearch && matchesCategory && matchesType
  })

  // 获取类型统计 - 移到useEffect之前
  const typeStats = getTypeStats()
  
  // 获取分类统计
  const categoryStats = getCategoryStats()

  // 客户端挂载检查，避免hydration错误
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 页面加载时从后端获取数据
  useEffect(() => {
    console.log('开始加载视频素材数据...')
    
    const loadData = async () => {
      try {
        console.log('调用 loadMaterials()...')
        await loadMaterials()
        console.log('loadMaterials() 完成')
        
        // 同时加载分类列表
        console.log('加载分类列表...')
        await loadCategories()
        console.log('分类列表加载完成')
        
      } catch (error) {
        console.error('加载数据失败:', error)
        console.log('错误详情:', {
          message: error instanceof Error ? error.message : '未知错误',
          stack: error instanceof Error ? error.stack : undefined
        })
      }
    }
    
    // 只在组件首次挂载时加载数据
    loadData()
  }, []) // 移除所有依赖项，只在组件挂载时执行一次

  // 添加调试信息 - 优化依赖项
  useEffect(() => {
    console.log('当前素材数据:', materials)
    console.log('过滤后素材数据:', filteredMaterials.length)
    console.log('加载状态:', isLoading)
    console.log('错误信息:', error)
  }, [materials.length, isLoading, error]) // 只监听关键变化

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化视频时长
  const formatDuration = (duration: number): string => {
    const minutes = Math.floor(duration / 60)
    const seconds = Math.floor(duration % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // 计算宽高比
  const getAspectRatio = (width: number, height: number): string => {
    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
    const divisor = gcd(width, height)
    return `${width/divisor}:${height/divisor}`
  }

  // 安全的日期格式化函数，避免hydration错误
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      // 使用固定格式避免locale差异
      return date.toISOString().slice(0, 19).replace('T', ' ')
    } catch (error) {
      return '未知时间'
    }
  }

  // 处理文件上传 - 只支持视频格式
  const handleFileUpload = useCallback(async (files: FileList, category?: string) => {
    // 如果没有传入category参数，使用当前的uploadCategory状态
    const actualCategory = category || uploadCategory
    
    console.log('🔵 开始上传文件:', {
      fileCount: files.length,
      传入的category: category,
      实际使用的category: actualCategory,
      当前uploadCategory状态: uploadCategory
    })
    
    const fileArray = Array.from(files)
    const validExtensions = ['.mp4', '.mov', '.avi', '.webm', '.mkv']
    
    // 过滤支持的视频文件类型
    const validFiles = fileArray.filter(file => {
      const extension = '.' + file.name.split('.').pop()?.toLowerCase()
      return validExtensions.includes(extension)
    })

    if (validFiles.length === 0) {
      addNotification({
        type: 'error',
        title: '文件格式不支持',
        message: '只支持视频文件格式：MP4、MOV、AVI、WEBM、MKV'
      })
      return
    }

    if (fileArray.length !== validFiles.length) {
      const invalidCount = fileArray.length - validFiles.length
      addNotification({
        type: 'warning',
        title: '部分文件已过滤',
        message: `已过滤 ${invalidCount} 个不支持的文件，只上传视频文件`
      })
    }

    try {
      setLoading(true)
      console.log('🔵 准备上传，分类为:', actualCategory)
      
      if (validFiles.length === 1) {
        // 单文件上传
        console.log('🔵 执行单文件上传，文件:', validFiles[0].name, '分类:', actualCategory)
        const material = await uploadFile(validFiles[0], actualCategory)
        console.log('🔵 单文件上传结果:', material)
        if (material) {
          // 上传成功后，重新加载列表
          await loadMaterials()
          addNotification({
            type: 'success',
            title: '上传成功',
            message: `文件 "${validFiles[0].name}" 已成功上传到分类 "${actualCategory}"`
          })
        }
      } else {
        // 批量上传 - 这是真正的批量上传功能
        console.log('🔵 执行批量上传，文件数:', validFiles.length, '分类:', actualCategory)
        const result = await bulkUploadFiles(validFiles, actualCategory)
        console.log('🔵 批量上传结果:', result)
        
        if (result.success.length > 0) {
          // 批量上传成功后，重新加载列表
          await loadMaterials()
          
          // 显示成功信息
          if (result.failed.length === 0) {
            addNotification({
              type: 'success',
              title: '上传成功',
              message: `成功上传 ${result.success.length} 个视频文件到分类 "${actualCategory}"`
            })
          }
        }
        
        if (result.failed.length > 0) {
          const failedNames = result.failed.map(f => f.name).join(', ')
          const successCount = result.success.length
          const failedCount = result.failed.length
          
          if (successCount > 0) {
            addNotification({
              type: 'warning',
              title: '部分上传成功',
              message: `成功上传 ${successCount} 个文件到分类 "${actualCategory}"，失败 ${failedCount} 个：${failedNames}`
            })
          } else {
            addNotification({
              type: 'error',
              title: '上传失败',
              message: `文件上传失败：${failedNames}`
            })
          }
        }
      }
    } catch (error) {
      console.error('文件上传失败:', error)
      addNotification({
        type: 'error',
        title: '上传失败',
        message: error instanceof Error ? error.message : '文件上传失败'
      })
    } finally {
      setLoading(false)
    }
  }, [uploadFile, bulkUploadFiles, loadMaterials, setLoading, uploadCategory, addNotification])

  // 批量导入处理 - 统一的上传入口
  const handleBatchImport = () => {
    setIsUploadingBatch(true) // 设置为批量上传模式
    setShowUploadModal(true)
  }

  // 文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      console.log('🔵 文件选择器触发，当前分类状态:', uploadCategory)
      handleFileUpload(files, uploadCategory)
    }
    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 确认上传 - 从模态框选择分类后触发文件选择器
  const confirmUpload = () => {
    console.log('🔵 确认上传，当前选择的分类:', uploadCategory)
    
    // 关闭模态框并打开文件选择器
    setShowUploadModal(false)
    
    // 设置文件输入的属性
    if (fileInputRef.current) {
      fileInputRef.current.multiple = isUploadingBatch
      // 短暂延迟确保模态框已关闭，然后触发文件选择器
      setTimeout(() => {
        console.log('🔵 准备打开文件选择器，分类状态:', uploadCategory)
        fileInputRef.current?.click()
      }, 100)
    }
  }

  // 删除素材
  // 显示删除确认对话框
  const handleDeleteMaterial = useCallback((id: string, material?: VideoMaterial) => {
    setMaterialToDelete({ id, material })
    setShowDeleteConfirm(true)
  }, [])

  // 确认删除素材
  const confirmDeleteMaterial = useCallback(async () => {
    if (!materialToDelete) return
    
    try {
      await deleteMaterialFromServer(materialToDelete.id)
      addNotification({
        type: 'success',
        title: '删除成功',
        message: materialToDelete.material ? `素材 "${materialToDelete.material.name}" 已删除` : '素材已删除'
      })
    } catch (error) {
      console.error('删除素材失败:', error)
      addNotification({
        type: 'error',
        title: '删除失败',
        message: error instanceof Error ? error.message : '删除素材失败'
      })
    } finally {
      setShowDeleteConfirm(false)
      setMaterialToDelete(null)
    }
  }, [materialToDelete, deleteMaterialFromServer, addNotification])

  // 取消删除
  const cancelDeleteMaterial = useCallback(() => {
    setShowDeleteConfirm(false)
    setMaterialToDelete(null)
  }, [])

  // 预览素材
  const handlePreviewMaterial = (material: VideoMaterial) => {
    setPreviewMaterial(material)
    setShowPreviewModal(true)
  }

  // 关闭预览
  const handleClosePreview = () => {
    setShowPreviewModal(false)
    setPreviewMaterial(null)
  }

  // 分类管理相关函数
  const handleCategoryManagement = () => {
    setShowCategoryModal(true)
  }

  const handleCloseCategoryModal = () => {
    setShowCategoryModal(false)
    setNewCategory('')
  }

  const handleAddCategory = async () => {
    if (newCategory.trim() && !categories.includes(newCategory.trim())) {
      try {
        await createCategory(newCategory.trim())
        setNewCategory('')
        addNotification({
          type: 'success',
          title: '分类已创建',
          message: `分类 "${newCategory.trim()}" 已成功创建`
        })
      } catch (error) {
        addNotification({
          type: 'error',
          title: '创建失败',
          message: error instanceof Error ? error.message : '创建分类失败'
        })
      }
    }
  }

  const handleDeleteCategory = async (categoryName: string) => {
    try {
      // 我们需要先找到分类的ID，但由于当前我们只存储了名称，这里需要改进
      // 暂时显示错误提示，建议通过分类管理界面进行删除
      addNotification({
        type: 'warning',
        title: '功能限制',
        message: '请通过分类管理界面删除分类'
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: '删除失败',
        message: error instanceof Error ? error.message : '删除分类失败'
      })
    }
  }

  return (
    <>
      {/* 页面样式 */}
      <style jsx>{`
        .container {
          max-width: 1400px;
          margin: 0 auto;
          padding: 24px;
        }

        .page-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          margin-bottom: 32px;
        }

        .page-title-section {
          flex: 1;
        }

        .page-title {
          font-size: 28px;
          font-weight: 700;
          color: var(--text-primary);
          margin-bottom: 8px;
        }

        .page-description {
          font-size: 16px;
          color: var(--text-secondary);
        }

        .page-actions {
          display: flex;
          gap: 12px;
        }

        /* 统计卡片 */
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-bottom: 32px;
        }

        .stat-card {
          background: var(--bg-secondary);
          border-radius: 8px;
          padding: 20px;
          border: 1px solid var(--border-primary);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: var(--theme-primary);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--text-secondary);
        }

        /* 分类标签 */
        .category-tabs {
          display: flex;
          gap: 2px;
          background: var(--bg-secondary);
          border-radius: 8px;
          padding: 4px;
          margin-bottom: 24px;
          border: 1px solid var(--border-primary);
        }

        .category-tab {
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: none;
          background: transparent;
          color: var(--text-secondary);
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .category-tab.active {
          background: var(--theme-primary);
          color: white;
        }

        .category-tab:not(.active):hover {
          background: var(--bg-tertiary);
          color: var(--text-primary);
        }

        .category-count {
          font-size: 12px;
          font-weight: normal;
          opacity: 0.8;
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 6px;
          border-radius: 10px;
          line-height: 1;
        }

        .category-tab:not(.active) .category-count {
          background: var(--bg-tertiary);
          color: var(--text-secondary);
        }

        /* 工具栏 */
        .toolbar {
          background: var(--bg-secondary);
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 24px;
          border: 1px solid var(--border-primary);
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 16px;
        }

        .toolbar-left {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .search-box {
          position: relative;
        }

        .search-input {
          width: 300px;
          padding: 8px 36px 8px 12px;
          border: 1px solid var(--border-secondary);
          border-radius: 6px;
          font-size: 14px;
          background: var(--bg-secondary);
          color: var(--text-primary);
        }

        .search-input:focus {
          outline: none;
          border-color: var(--theme-primary);
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
          color: var(--text-secondary);
        }

        .filter-select {
          padding: 8px 12px;
          border: 1px solid var(--border-secondary);
          border-radius: 6px;
          font-size: 14px;
          background: var(--bg-secondary);
          color: var(--text-primary);
          cursor: pointer;
        }

        .toolbar-right {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .view-toggle {
          display: flex;
          border: 1px solid var(--border-secondary);
          border-radius: 6px;
          overflow: hidden;
        }

        .view-btn {
          padding: 8px 12px;
          background: var(--bg-secondary);
          border: none;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .view-btn.active {
          background: var(--theme-primary);
          color: white;
        }

        .view-btn:not(.active):hover {
          background: var(--bg-tertiary);
        }

        .btn {
          padding: 10px 20px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: 1px solid transparent;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .btn-primary {
          background: var(--theme-primary);
          color: white;
          border-color: var(--theme-primary);
        }

        .btn-primary:hover {
          background: var(--theme-primary-hover);
        }

        .btn-secondary {
          background: var(--bg-secondary);
          color: var(--text-tertiary);
          border-color: var(--border-secondary);
        }

        .btn-secondary:hover {
          border-color: var(--theme-primary);
          color: var(--theme-primary);
        }

        .btn-icon {
          width: 16px;
          height: 16px;
          fill: currentColor;
        }

        /* 素材网格 */
        .materials-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 20px;
        }

        /* 列表视图 */
        .materials-list {
          display: block;
        }

        .materials-list .material-card {
          display: flex;
          margin-bottom: 12px;
          height: 100px;
        }

        .materials-list .material-preview {
          width: 120px;
          height: 100px;
          flex-shrink: 0;
        }

        .materials-list .material-info {
          flex: 1;
          padding: 12px 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .materials-list .material-details {
          flex: 1;
        }

        .materials-list .material-actions {
          flex-shrink: 0;
          margin-left: 16px;
        }

        /* 素材卡片 */
        .material-card {
          background: var(--bg-secondary);
          border-radius: 8px;
          border: 1px solid var(--border-primary);
          overflow: hidden;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .material-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .material-preview {
          width: 100%;
          height: 180px;
          position: relative;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background: var(--bg-tertiary);
        }

        .material-thumbnail {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }

        .material-type-icon {
          width: 48px;
          height: 48px;
          color: var(--text-secondary);
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .material-duration {
          position: absolute;
          bottom: 8px;
          right: 8px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
        }

        .material-type-badge {
          position: absolute;
          top: 8px;
          left: 8px;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          color: white;
        }

        .video-badge {
          background: var(--theme-primary);
        }

        .material-info {
          padding: 16px;
        }

        .material-title {
          font-size: 16px;
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .material-meta {
          font-size: 14px;
          color: var(--text-secondary);
          margin-bottom: 8px;
        }

        .material-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
          margin-bottom: 12px;
        }

        .material-tag {
          background: var(--bg-tertiary);
          color: var(--text-secondary);
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 12px;
        }

        .material-actions {
          display: flex;
          gap: 8px;
        }

        .action-btn {
          flex: 1;
          padding: 6px 12px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: 1px solid transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
        }

        .action-btn-preview {
          background: var(--theme-primary);
          color: white;
          border-color: var(--theme-primary);
        }

        .action-btn-preview:hover {
          background: var(--theme-primary-hover);
        }

        .action-btn-delete {
          background: transparent;
          color: #ef4444;
          border-color: #ef4444;
          position: relative;
          z-index: 1;
        }

        .action-btn-delete:hover {
          background: #ef4444;
          color: white;
          border-color: #ef4444;
          z-index: 2;
        }

        .action-icon {
          width: 12px;
          height: 12px;
          fill: currentColor;
        }

        /* 预览模态框 */
        .preview-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: ${showPreviewModal ? 'flex' : 'none'};
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .preview-content {
          background: var(--bg-secondary);
          border-radius: 8px;
          padding: 24px;
          max-width: 90vw;
          max-height: 90vh;
          position: relative;
        }

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }

        .preview-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
        }

        .preview-close {
          width: 24px;
          height: 24px;
          color: var(--text-secondary);
          cursor: pointer;
          border: none;
          background: none;
        }

        .preview-video-container {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 300px;
          margin-bottom: 16px;
        }

        .preview-media {
          max-width: 100%;
          max-height: 60vh;
          border-radius: 6px;
          display: block;
          margin: 0 auto;
          object-fit: contain;
        }

        .preview-info {
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid var(--border-primary);
        }

        .preview-meta {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 12px;
          font-size: 14px;
        }

        .meta-item {
          display: flex;
          flex-direction: column;
        }

        .meta-label {
          color: var(--text-secondary);
          margin-bottom: 2px;
        }

        .meta-value {
          color: var(--text-primary);
          font-weight: 500;
        }

        /* 空状态 */
        .empty-state {
          text-align: center;
          padding: 60px 20px;
          color: var(--text-secondary);
        }

        .empty-icon {
          width: 64px;
          height: 64px;
          margin: 0 auto 16px;
          color: var(--text-secondary);
        }

        .empty-title {
          font-size: 18px;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .empty-description {
          font-size: 14px;
          line-height: 1.5;
        }

        /* 隐藏文件输入 */
        .hidden-input {
          display: none;
        }

        /* 错误提示 */
        .error-banner {
          background: #fee2e2;
          border: 1px solid #fecaca;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 24px;
        }

        .error-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .error-icon {
          width: 20px;
          height: 20px;
          color: #dc2626;
          flex-shrink: 0;
        }

        .error-content span {
          flex: 1;
          color: #dc2626;
          font-size: 14px;
        }

        .error-close {
          width: 20px;
          height: 20px;
          color: #dc2626;
          background: none;
          border: none;
          cursor: pointer;
          flex-shrink: 0;
        }

        .error-close:hover {
          color: #991b1b;
        }

        /* 上传进度条 */
        .upload-progress {
          background: var(--bg-secondary);
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 24px;
          border: 1px solid var(--border-primary);
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: var(--bg-tertiary);
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 8px;
        }

        .progress-fill {
          height: 100%;
          background: var(--theme-primary);
          transition: width 0.3s ease;
        }

        .progress-text {
          font-size: 14px;
          color: var(--text-secondary);
          text-align: center;
        }

        /* 加载状态 */
        .loading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
        }

        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid var(--border-secondary);
          border-top: 3px solid var(--theme-primary);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* 分类管理模态框 */
        .category-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: ${showCategoryModal ? 'flex' : 'none'};
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        /* 上传模态框 */
        .upload-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .category-modal-content {
          background: var(--bg-secondary);
          border-radius: 8px;
          padding: 24px;
          width: 90%;
          max-width: 500px;
          max-height: 70vh;
          overflow-y: auto;
        }

        .category-modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 16px;
          border-bottom: 1px solid var(--border-primary);
        }

        .category-modal-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
        }

        .category-add-section {
          display: flex;
          gap: 8px;
          margin-bottom: 20px;
        }

        .category-input {
          flex: 1;
          padding: 8px 12px;
          border: 1px solid var(--border-secondary);
          border-radius: 6px;
          font-size: 14px;
          background: var(--bg-secondary);
          color: var(--text-primary);
        }

        .category-list {
          max-height: 300px;
          overflow-y: auto;
        }

        .category-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: var(--bg-tertiary);
          border-radius: 6px;
          margin-bottom: 8px;
          border: 1px solid var(--border-secondary);
        }

        .category-name {
          font-size: 14px;
          color: var(--text-primary);
        }

        .category-default {
          color: var(--text-secondary);
          font-style: italic;
        }

        .category-delete-btn {
          background: var(--error-color);
          color: white;
          border: none;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.2s;
        }

        .category-delete-btn:hover {
          background: #dc2626;
        }

        .category-delete-btn:disabled {
          background: var(--text-disabled);
          cursor: not-allowed;
        }

        /* 删除确认对话框 */
        .delete-confirm-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .delete-confirm-content {
          background: var(--bg-secondary);
          border-radius: 8px;
          padding: 24px;
          width: 90%;
          max-width: 480px;
        }

        .delete-confirm-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 16px;
          border-bottom: 1px solid var(--border-primary);
        }

        .delete-confirm-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
        }

        .delete-confirm-body {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          margin-bottom: 24px;
        }

        .delete-warning-icon {
          color: #f59e0b;
          flex-shrink: 0;
        }

        .delete-confirm-text {
          flex: 1;
        }

        .delete-confirm-message {
          font-size: 16px;
          color: var(--text-primary);
          margin-bottom: 8px;
          line-height: 1.5;
        }

        .delete-confirm-warning {
          font-size: 14px;
          color: var(--text-secondary);
          margin: 0;
          line-height: 1.4;
        }

        .delete-confirm-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
        }

        .btn-danger {
          background: #ef4444;
          color: white;
          border-color: #ef4444;
        }

        .btn-danger:hover {
          background: #dc2626;
          border-color: #dc2626;
        }
      `}</style>

      {/* 防止hydration错误，只在客户端挂载后渲染 */}
      {!isMounted ? (
        <div className="container">
          <div className="loading-overlay">
            <div className="loading-spinner"></div>
          </div>
        </div>
      ) : (
        <div className="container">
        {/* 页面标题 */}
        <div className="page-header">
          <div className="page-title-section">
            <h1 className="page-title">视频素材管理</h1>
            <p className="page-description">管理视频生成所需的视频素材文件</p>
          </div>
          <div className="page-actions">
            <button className="btn btn-secondary" onClick={handleCategoryManagement}>
              <svg className="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
              </svg>
              分类管理
            </button>
            <button className="btn btn-primary" onClick={handleBatchImport}>
              <svg className="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd"/>
              </svg>
              上传视频
            </button>
          </div>
        </div>

        {/* 上传进度条 */}
        {isLoading && uploadProgress > 0 && (
          <div className="upload-progress">
            <div className="progress-bar">
              <div className="progress-fill" style={{ width: `${uploadProgress}%` }}></div>
            </div>
            <div className="progress-text">{uploadProgress.toFixed(1)}% 上传中...</div>
          </div>
        )}

        {/* 统计信息 */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{getTotalCount()}</div>
            <div className="stat-label">素材文件总数</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{getTotalSize()}</div>
            <div className="stat-label">占用存储空间</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{typeStats.video || 0}</div>
            <div className="stat-label">视频文件数量</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{filteredMaterials.length}</div>
            <div className="stat-label">筛选结果</div>
          </div>
        </div>

        {/* 分类标签 */}
        <div className="category-tabs">
          <button 
            className={`category-tab ${selectedCategory === 'all' ? 'active' : ''}`}
            onClick={() => setSelectedCategory('all')}
          >
            全部视频
            <span className="category-count">({materials.filter(m => m.type === 'video').length})</span>
          </button>
          {categories.map((category, index) => {
            // 为空字符串或null的分类显示为"默认分类"
            const displayName = category || '默认分类'
            // 获取该分类的统计数据，对于空字符串分类，需要查找'general'或空值
            const categoryKey = category || 'general'
            const count = categoryStats[categoryKey] || categoryStats[category] || 0
            
            return (
              <button 
                key={index}
                className={`category-tab ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {displayName}
                <span className="category-count">({count})</span>
              </button>
            )
          })}
        </div>

        {/* 工具栏 */}
        <div className="toolbar">
          <div className="toolbar-left">
            <div className="search-box">
              <input
                type="text"
                className="search-input"
                placeholder="搜索素材文件..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <svg className="search-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"/>
              </svg><svg className="search-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"/>
              </svg>
            </div>
            <select 
              className="filter-select"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
            >
              <option value="">所有格式</option>
              <option value="mp4">MP4</option>
              <option value="mov">MOV</option>
              <option value="avi">AVI</option>
              <option value="webm">WEBM</option>
              <option value="mkv">MKV</option>
            </select>
          </div>
          <div className="toolbar-right">
            <div className="view-toggle">
              <button 
                className={`view-btn ${currentView === 'grid' ? 'active' : ''}`}
                onClick={() => setView('grid')}
              >
                <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                </svg>
              </button>
              <button 
                className={`view-btn ${currentView === 'list' ? 'active' : ''}`}
                onClick={() => setView('list')}
              >
                <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* 素材网格/列表 */}
        <div className={currentView === 'grid' ? 'materials-grid' : 'materials-list'} style={{ position: 'relative' }}>
          {isLoading && (
            <div className="loading-overlay">
              <div className="loading-spinner"></div>
            </div>
          )}
          {filteredMaterials.length === 0 && !isLoading ? (
            <div className="empty-state" style={{ gridColumn: currentView === 'grid' ? '1 / -1' : 'auto' }}>
              <svg className="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
              </svg>
              <div className="empty-title">还没有素材文件</div>
              <div className="empty-description">
                点击"上传视频"按钮开始添加视频素材
              </div>
            </div>
          ) : (
            filteredMaterials.map((material: VideoMaterial) => (
              <div key={material.id} className="material-card">
                <div className="material-preview">
                  {material.thumbnailUrl ? (
                    <img 
                      src={`${API_BASE}${material.thumbnailUrl}`}
                      alt={material.name} 
                      className="material-thumbnail"
                      onError={(e) => {
                        console.error('缩略图加载失败:', material.thumbnailUrl)
                        // 隐藏img元素，显示默认图标
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        const parent = target.parentElement
                        if (parent) {
                          const icon = parent.querySelector('.material-type-icon')
                          if (icon) {
                            (icon as HTMLElement).style.display = 'block'
                          }
                        }
                      }}
                    />
                  ) : null}
                  
                  <svg 
                    className="material-type-icon" 
                    viewBox="0 0 20 20" 
                    fill="currentColor"
                    style={{ display: material.thumbnailUrl ? 'none' : 'block' }}
                  >
                    {material.type === 'video' ? (
                      <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                    ) : (
                      <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                    )}
                  </svg>
                  
                  {material.duration && (
                    <div className="material-duration">{material.duration}</div>
                  )}
                  
                  <div className={`material-type-badge video-badge`}>
                    视频
                  </div>
                </div>

                <div className="material-info">
                  {currentView === 'list' ? (
                    <div className="material-details">
                      <div className="material-title">{material.name}</div>
                      <div className="material-meta">
                        {material.format} • {material.size} • {material.dimensions.width}x{material.dimensions.height} • {material.aspectRatio}
                        {material.duration && ` • ${material.duration}`}
                      </div>
                      <div className="material-tags">
                        {material.tags.map((tag, index) => (
                          <span key={index} className="material-tag">{tag}</span>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="material-title">{material.name}</div>
                      <div className="material-meta">
                        {material.format} • {material.size} • {material.dimensions.width}x{material.dimensions.height}
                      </div>
                      <div className="material-tags">
                        {material.tags.map((tag, index) => (
                          <span key={index} className="material-tag">{tag}</span>
                        ))}
                      </div>
                    </>
                  )}

                  <div className="material-actions">
                    <button 
                      className="action-btn action-btn-preview"
                      onClick={() => handlePreviewMaterial(material)}
                    >
                      <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                      </svg>
                      预览
                    </button>
                    <button 
                      className="action-btn action-btn-delete"
                      onClick={() => handleDeleteMaterial(material.id, material)}
                    >
                      <svg className="action-icon" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd"/>
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"/>
                      </svg>
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* 预览模态框 */}
        {showPreviewModal && previewMaterial && (
          <div className="preview-modal" onClick={handleClosePreview}>
            <div className="preview-content" onClick={(e) => e.stopPropagation()}>
              <div className="preview-header">
                <div className="preview-title">素材预览 - {previewMaterial.name}</div>
                <button className="preview-close" onClick={handleClosePreview}>
                  <svg viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                </button>
              </div>
              
              {previewMaterial.type === 'video' ? (
                <div className="preview-video-container">
                  <video 
                    src={previewMaterial.url ? `${API_BASE}${previewMaterial.url}` : previewMaterial.path}
                    className="preview-media" 
                    controls
                    onError={(e) => {
                      console.error('视频加载失败:', e)
                      console.log('尝试的URL:', previewMaterial.url ? `${API_BASE}${previewMaterial.url}` : previewMaterial.path)
                    }}
                  />
                </div>
              ) : null}

              <div className="preview-info">
                <div className="preview-meta">
                  <div className="meta-item">
                    <div className="meta-label">文件名</div>
                    <div className="meta-value">{previewMaterial.name}</div>
                  </div>
                  <div className="meta-item">
                    <div className="meta-label">类型</div>
                    <div className="meta-value">视频</div>
                  </div>
                  <div className="meta-item">
                    <div className="meta-label">格式</div>
                    <div className="meta-value">{previewMaterial.format}</div>
                  </div>
                  <div className="meta-item">
                    <div className="meta-label">文件大小</div>
                    <div className="meta-value">{previewMaterial.size}</div>
                  </div>
                  <div className="meta-item">
                    <div className="meta-label">分辨率</div>
                    <div className="meta-value">{previewMaterial.dimensions.width}x{previewMaterial.dimensions.height}</div>
                  </div>
                  <div className="meta-item">
                    <div className="meta-label">宽高比</div>
                    <div className="meta-value">{previewMaterial.aspectRatio}</div>
                  </div>
                  {previewMaterial.duration && (
                    <div className="meta-item">
                      <div className="meta-label">时长</div>
                      <div className="meta-value">{previewMaterial.duration}</div>
                    </div>
                  )}
                  <div className="meta-item">
                    <div className="meta-label">创建时间</div>
                    <div className="meta-value">
                      {isMounted && previewMaterial.createdAt ? formatDate(previewMaterial.createdAt) : '加载中...'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 分类管理模态框 */}
        {showCategoryModal && (
          <div className="category-modal" onClick={handleCloseCategoryModal}>
            <div className="category-modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="category-modal-header">
                <div className="category-modal-title">分类管理</div>
                <button className="preview-close" onClick={handleCloseCategoryModal}>
                  <svg viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                </button>
              </div>

              {/* 说明文字 */}
              <div style={{ marginBottom: '20px', padding: '12px', backgroundColor: 'var(--bg-tertiary)', borderRadius: '6px', fontSize: '14px', color: 'var(--text-secondary)' }}>
                <strong>说明：</strong>在这里管理视频分类。您可以添加新分类或删除不需要的分类。
              </div>

              {/* 添加分类区域 */}
              <div className="category-add-section">
                <input
                  type="text"
                  className="category-input"
                  placeholder="输入新分类名称"
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleAddCategory()
                    }
                  }}
                />
                <button 
                  className="btn btn-primary" 
                  onClick={handleAddCategory}
                  disabled={!newCategory.trim() || isCategoriesLoading}
                >
                  添加分类
                </button>
              </div>

              {/* 分类列表 */}
              <div className="category-list">
                {isCategoriesLoading ? (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <div className="loading-spinner" style={{ width: '24px', height: '24px', margin: '0 auto' }}></div>
                    <div style={{ marginTop: '8px', fontSize: '14px', color: 'var(--text-secondary)' }}>加载中...</div>
                  </div>
                ) : categories.length === 0 ? (
                  <div style={{ textAlign: 'center', padding: '20px', fontSize: '14px', color: 'var(--text-secondary)' }}>
                    暂无分类，请添加新分类
                  </div>
                ) : (
                  categories.map((category, index) => (
                    <div key={index} className="category-item">
                      <span className="category-name">
                        {category || '默认分类'}
                      </span>
                      <button
                        className="category-delete-btn"
                        onClick={() => handleDeleteCategory(category)}
                        disabled={isCategoriesLoading}
                      >
                        删除
                      </button>
                    </div>
                  ))
                )}
              </div>

              {/* 刷新按钮 */}
              <div style={{ textAlign: 'center', marginTop: '20px' }}>
                <button 
                  className="btn btn-primary" 
                  onClick={() => loadCategories()}
                  disabled={isCategoriesLoading}
                >
                  {isCategoriesLoading ? '加载中...' : '刷新分类列表'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 上传分类选择模态框 */}
        {showUploadModal && (
          <div className="upload-modal" onClick={() => setShowUploadModal(false)}>
            <div className="category-modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="category-modal-header">
                <div className="category-modal-title">
                  上传视频
                </div>
                <button className="preview-close" onClick={() => setShowUploadModal(false)}>
                  <svg viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                </button>
              </div>

              <div style={{ marginBottom: '20px', fontSize: '14px', color: 'var(--text-secondary)' }}>
                选择要上传到的分类，确认后可以选择一个或多个视频文件进行上传
                <br />
                <span style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
                  当前选择的分类: <strong>{uploadCategory}</strong>
                </span>
              </div>

              {/* 分类选择 */}
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', fontWeight: '500', color: 'var(--text-primary)' }}>
                  选择分类：
                </label>
                <select 
                  value={uploadCategory} 
                  onChange={(e) => {
                    console.log('🔵 分类选择改变:', e.target.value)
                    setUploadCategory(e.target.value)
                  }}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: '1px solid var(--border-secondary)',
                    borderRadius: '6px',
                    fontSize: '14px',
                    background: 'var(--bg-secondary)',
                    color: 'var(--text-primary)'
                  }}
                >
                  <option value="general">默认分类</option>
                  {categories.map((category, index) => (
                    <option key={index} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* 操作按钮 */}
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <button 
                  className="btn btn-secondary" 
                  onClick={() => setShowUploadModal(false)}
                >
                  取消
                </button>
                <button 
                  className="btn btn-primary" 
                  onClick={confirmUpload}
                >
                  选择文件
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 删除确认对话框 */}
        {showDeleteConfirm && materialToDelete && (
          <div className="delete-confirm-modal" onClick={cancelDeleteMaterial}>
            <div className="delete-confirm-content" onClick={(e) => e.stopPropagation()}>
              <div className="delete-confirm-header">
                <div className="delete-confirm-title">
                  确认删除素材
                </div>
                <button className="preview-close" onClick={cancelDeleteMaterial}>
                  <svg viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                </button>
              </div>

              <div className="delete-confirm-body">
                <div className="delete-warning-icon">
                  <svg width="48" height="48" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
                  </svg>
                </div>
                
                <div className="delete-confirm-text">
                  <p className="delete-confirm-message">
                    确定要删除素材 "<strong>{materialToDelete.material?.name || '未知文件'}</strong>" 吗？
                  </p>
                  <p className="delete-confirm-warning">
                    此操作无法撤销，文件将被永久删除。
                  </p>
                </div>
              </div>

              <div className="delete-confirm-actions">
                <button 
                  className="btn btn-secondary" 
                  onClick={cancelDeleteMaterial}
                >
                  取消
                </button>
                <button 
                  className="btn btn-danger" 
                  onClick={confirmDeleteMaterial}
                >
                  确认删除
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          className="hidden-input"
          multiple
          accept=".mp4,.mov,.avi,.webm,.mkv"
          onChange={handleFileSelect}
        />
        </div>
      )}
    </>
  )
}
