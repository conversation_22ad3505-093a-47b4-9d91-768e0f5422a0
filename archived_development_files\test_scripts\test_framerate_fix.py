#!/usr/bin/env python3
"""
测试帧率不匹配修复 - 验证混合帧率视频的转场效果
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_mixed_framerate_videos():
    """创建不同帧率的测试视频片段"""
    
    test_videos = []
    
    # 创建不同帧率的视频片段（模拟真实场景中的帧率不匹配问题）
    video_configs = [
        ('red_30fps', '#FF0000', 3.0, 30),    # 30fps红色
        ('green_60fps', '#00FF00', 4.0, 60),  # 60fps绿色（问题源）
        ('blue_30fps', '#0000FF', 3.0, 30),   # 30fps蓝色
    ]
    
    durations = []
    
    for i, (name, color, duration, fps) in enumerate(video_configs):
        output_path = f"test_mixed_{name}.mp4"
        
        if not Path(output_path).exists():
            logger.info(f"创建{fps}fps, {duration}秒测试视频: {output_path}")
            
            # 创建指定帧率和时长的纯色视频
            (
                ffmpeg
                .input(f'color={color}:size=1080x1920:duration={duration}:rate={fps}', f='lavfi')
                .output(output_path, vcodec='libx264', pix_fmt='yuv420p', r=fps)
                .overwrite_output()
                .run(quiet=True)
            )
        
        test_videos.append(output_path)
        durations.append(duration)
    
    return test_videos, durations

def test_mixed_framerate_transitions():
    """测试混合帧率视频的转场效果"""
    
    logger.info("🎬 开始测试混合帧率视频的转场效果...")
    
    # 创建不同帧率的测试视频
    test_videos, real_durations = create_mixed_framerate_videos()
    
    logger.info(f"测试视频片段: {test_videos}")
    logger.info(f"实际时长: {real_durations}")
    logger.info("帧率: [30fps, 60fps, 30fps] - 模拟真实场景中的帧率不匹配")
    
    # 测试配置
    test_configs = [
        {
            'name': '修复后的混合帧率淡入淡出转场',
            'transition_type': 'fade',
            'duration': 1.0,
            'output': 'fixed_mixed_fps_fade.mp4'
        },
        {
            'name': '修复后的混合帧率溶解转场',
            'transition_type': 'dissolve',
            'duration': 0.8,
            'output': 'fixed_mixed_fps_dissolve.mp4'
        }
    ]
    
    success_count = 0
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
        logger.info(f"转场类型: {config['transition_type']}")
        logger.info(f"转场时长: {config['duration']}s")
        logger.info(f"视频片段时长: {real_durations}")
        logger.info(f"输出文件: {config['output']}")
        
        try:
            start_time = time.time()
            
            # 创建视频流
            streams = []
            for video_path in test_videos:
                stream = ffmpeg.input(video_path)
                streams.append(stream)
            
            logger.info(f"创建了 {len(streams)} 个视频流")
            
            # 应用修复后的转场效果（包含帧率统一）
            final_stream = VideoCompositionService._create_video_with_transitions(
                streams, config['transition_type'], config['duration'], real_durations
            )
            
            # 输出视频
            out = ffmpeg.output(
                final_stream,
                config['output'],
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p'
            ).overwrite_output()
            
            # 执行FFmpeg命令
            import subprocess
            
            cmd = ffmpeg.compile(out)
            logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            try:
                # 等待最多90秒
                stdout, stderr = process.communicate(timeout=90)
                
                if process.returncode == 0:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功!")
                        logger.info(f"   文件大小: {file_size} bytes")
                        logger.info(f"   处理时间: {processing_time:.2f}秒")
                        
                        # 计算预期的总时长
                        total_duration = sum(real_durations)
                        transition_count = len(real_durations) - 1
                        expected_duration = total_duration - (transition_count * config['duration'])
                        
                        logger.info(f"   预期总时长: {expected_duration:.1f}秒")
                        logger.info(f"   预期效果: 红(30fps,3s)→绿(60fps→30fps,4s)→蓝(30fps,3s)")
                        logger.info(f"   帧率统一: 所有片段统一为30fps，避免timebase不匹配")
                        
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                else:
                    logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                    if stderr:
                        stderr_text = stderr.decode('utf-8', errors='ignore')
                        logger.error(f"stderr: {stderr_text[:500]}...")
                        
                        # 检查是否还有timebase错误
                        if "timebase" in stderr_text.lower():
                            logger.error("⚠️ 仍然存在timebase不匹配问题，需要进一步修复")
                        
            except subprocess.TimeoutExpired:
                logger.error(f"❌ {config['name']} 测试超时（90秒）")
                process.kill()
                process.communicate()
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n=== 混合帧率转场修复测试完成 ===")
    logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
    
    if success_count > 0:
        logger.info("\n📋 帧率修复验证测试文件:")
        logger.info("- fixed_mixed_fps_fade.mp4 (修复后的混合帧率淡入淡出)")
        logger.info("- fixed_mixed_fps_dissolve.mp4 (修复后的混合帧率溶解转场)")
        
        logger.info("\n🔍 修复验证要点:")
        logger.info("1. 视频应该正常生成，不会出现timebase错误")
        logger.info("2. 转场应该平滑，没有帧率跳跃")
        logger.info("3. 所有片段统一为30fps")
        logger.info("4. 60fps的绿色片段应该正确转换为30fps")
        
        logger.info("\n🎬 播放顺序:")
        logger.info("红色(30fps,3s) → [转场] → 绿色(60fps→30fps,4s) → [转场] → 蓝色(30fps,3s)")
        logger.info("如果播放流畅且无错误，说明帧率不匹配问题修复成功！")
        
        return True
    else:
        logger.error("❌ 所有混合帧率转场测试都失败了")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_mixed_red_30fps.mp4',
        'test_mixed_green_60fps.mp4', 
        'test_mixed_blue_30fps.mp4'
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            Path(file_path).unlink()
            logger.info(f"清理测试文件: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始混合帧率转场修复验证")
    logger.info("测试不同帧率视频片段的转场效果")
    logger.info("模拟真实场景：30fps + 60fps + 30fps")
    
    try:
        success = test_mixed_framerate_transitions()
        
        if success:
            logger.info("\n🎉 混合帧率转场修复验证完成!")
            logger.info("修复成功！现在可以处理不同帧率的视频转场了。")
        else:
            logger.error("\n❌ 混合帧率转场修复验证失败")
            sys.exit(1)
            
    finally:
        # 清理测试文件
        cleanup_test_files()
