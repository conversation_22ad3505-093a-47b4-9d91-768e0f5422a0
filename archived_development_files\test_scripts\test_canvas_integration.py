#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证画布编辑器的核心功能
启动开发服务器并进行简单的集成测试
"""

import subprocess
import time
import os
import webbrowser
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains

def test_canvas_editor_integration():
    """测试画布编辑器的集成功能"""
    
    print("🧪 开始集成测试画布编辑器...")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_window_size(1920, 1080)
        
        # 访问封面模板页面
        url = "http://localhost:3000/covers"
        print(f"📱 访问页面: {url}")
        driver.get(url)
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 检查页面标题
        title = driver.find_element(By.TAG_NAME, "h1").text
        print(f"   页面标题: {title}")
        
        # 点击"新建模板"按钮
        create_btn = driver.find_element(By.XPATH, "//button[contains(text(), '新建模板')]")
        create_btn.click()
        print("   ✅ 点击新建模板按钮")
        
        # 等待模态框出现
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), '新建封面模板')]"))
        )
        
        # 填写模板信息
        name_input = driver.find_element(By.NAME, "name")
        name_input.send_keys("测试模板")
        
        category_select = driver.find_element(By.NAME, "category")
        category_select.send_keys("现代")
        
        description_textarea = driver.find_element(By.NAME, "description")
        description_textarea.send_keys("这是一个测试模板")
        
        # 提交表单
        submit_btn = driver.find_element(By.XPATH, "//button[@type='submit' and contains(text(), '创建模板')]")
        submit_btn.click()
        print("   ✅ 创建新模板")
        
        # 等待进入编辑器页面
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//h1[contains(text(), '模板编辑器')]"))
        )
        print("   ✅ 进入编辑器页面")
        
        # 测试工具栏
        text_tool = driver.find_element(By.XPATH, "//button[contains(text(), '文本')]")
        text_tool.click()
        print("   ✅ 选择文本工具")
        
        # 在画布上点击添加文本
        canvas = driver.find_element(By.CSS_SELECTOR, "[style*='background']")
        ActionChains(driver).click(canvas).perform()
        print("   ✅ 在画布上添加文本元素")
        
        # 等待一下让元素渲染
        time.sleep(1)
        
        # 检查是否有元素被添加
        elements = driver.find_elements(By.CSS_SELECTOR, ".absolute")
        if elements:
            print(f"   ✅ 成功添加了 {len(elements)} 个元素")
        else:
            print("   ❌ 没有找到添加的元素")
        
        # 测试属性面板
        property_panel = driver.find_element(By.XPATH, "//h3[contains(text(), '属性面板')]")
        if property_panel:
            print("   ✅ 属性面板存在")
        
        # 测试背景设置
        background_section = driver.find_elements(By.XPATH, "//h4[contains(text(), '画布背景')]")
        if background_section:
            print("   ✅ 背景设置功能存在")
        
        # 测试保存按钮
        save_btn = driver.find_element(By.XPATH, "//button[contains(text(), '保存')]")
        save_btn.click()
        print("   ✅ 点击保存按钮")
        
        # 检查保存反馈（应该有alert）
        try:
            WebDriverWait(driver, 2).until(EC.alert_is_present())
            alert = driver.switch_to.alert
            alert_text = alert.text
            alert.accept()
            print(f"   ✅ 保存成功，提示信息: {alert_text}")
        except:
            print("   ❌ 没有保存提示信息")
        
        print("\n🎉 集成测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False
        
    finally:
        try:
            driver.quit()
        except:
            pass

def start_dev_server():
    """启动开发服务器"""
    print("🚀 启动开发服务器...")
    
    try:
        # 检查是否已经有服务器在运行
        result = subprocess.run(
            ["curl", "-s", "http://localhost:3000"],
            capture_output=True,
            timeout=5
        )
        if result.returncode == 0:
            print("   ✅ 开发服务器已在运行")
            return True
    except:
        pass
    
    # 启动新的服务器
    try:
        os.chdir("frontend")
        subprocess.Popen(
            ["npm", "run", "dev"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        print("   🔄 正在启动开发服务器...")
        time.sleep(10)  # 等待服务器启动
        return True
    except Exception as e:
        print(f"   ❌ 启动服务器失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 画布编辑器集成测试")
    print("=" * 50)
    
    # 检查环境
    try:
        import selenium
        print("✅ Selenium 已安装")
    except ImportError:
        print("❌ Selenium 未安装，请运行: pip install selenium")
        return
    
    # 启动开发服务器
    if not start_dev_server():
        print("❌ 无法启动开发服务器")
        return
    
    # 运行集成测试
    success = test_canvas_editor_integration()
    
    if success:
        print("\n🎊 所有测试通过！画布编辑器功能正常。")
    else:
        print("\n⚠️  测试中发现问题，需要进一步检查。")

if __name__ == "__main__":
    main()
