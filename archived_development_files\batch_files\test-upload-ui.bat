@echo off
echo ===== 测试视频上传UI功能 =====
echo.

echo 1. 启动后端服务...
cd backend
start /B python main.py
echo 后端服务已启动在端口 8000

echo.
echo 2. 等待后端启动...
timeout /t 3 /nobreak >nul

echo.
echo 3. 启动前端服务...
cd ..\frontend
start /B npm run dev
echo 前端服务已启动在端口 3000

echo.
echo 4. 等待前端启动...
timeout /t 5 /nobreak >nul

echo.
echo ===== 测试指南 =====
echo 请在浏览器中访问: http://localhost:3000/videos
echo.
echo 测试步骤:
echo 1. 点击页面右上角的 "批量导入" 按钮
echo 2. 在弹出的模态框中选择一个分类（如 "general"）
echo 3. 点击 "选择多个文件" 按钮
echo 4. 在文件选择器中选择一个或多个MP4视频文件
echo 5. 验证上传是否成功，页面是否刷新显示新上传的视频
echo.
echo 测试点击单个上传按钮的流程也是类似的
echo.
echo 拖拽测试:
echo 1. 直接将视频文件拖拽到页面的拖拽区域
echo 2. 在弹出的分类选择框中选择分类
echo 3. 点击确认进行上传
echo.
echo 按任意键退出...
pause >nul
