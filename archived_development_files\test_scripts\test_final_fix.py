#!/usr/bin/env python3
"""
测试最终修复 - 移除enable参数后的fade效果
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_final_fix():
    """测试最终修复后的fade效果"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试最终修复后的fade效果...")
    logger.info("关键修复：移除overlay的enable参数，避免与fade效果冲突")
    
    # 测试配置
    test_configs = [
        {
            'name': '最终修复-淡入效果',
            'animation': 'fade_in',
            'duration': 5.0,
            'animation_duration': 2.0,  # 更长的动画时间，更明显
            'output': 'final_fade_in.mp4'
        },
        {
            'name': '最终修复-淡出效果', 
            'animation': 'fade_out',
            'duration': 5.0,
            'animation_duration': 2.0,
            'output': 'final_fade_out.mp4'
        },
        {
            'name': '最终修复-淡入淡出效果',
            'animation': 'fade_in_out',
            'duration': 6.0,
            'animation_duration': 2.0,
            'output': 'final_fade_in_out.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"final_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    # 应用封面叠加效果（使用最终修复后的方法）
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=8  # 生成8秒测试视频
                    ).overwrite_output()
                    
                    logger.info("开始执行FFmpeg命令...")
                    
                    # 显示FFmpeg命令（用于调试）
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令: {' '.join(cmd)}")
                    
                    # 执行
                    ffmpeg.run(out, quiet=True)
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功! 文件大小: {file_size} bytes")
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
        
        logger.info(f"\n=== 最终修复测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count == len(test_configs):
            logger.info("🎉 所有最终修复测试通过!")
            
            logger.info("\n📋 最终修复测试结果文件:")
            logger.info("- final_fade_in.mp4 (最终修复-淡入效果)")
            logger.info("- final_fade_out.mp4 (最终修复-淡出效果)")
            logger.info("- final_fade_in_out.mp4 (最终修复-淡入淡出效果)")
            
            logger.info("\n🔍 关键修复点:")
            logger.info("1. 使用正确的FFmpeg fade参数: t='in/out', st=开始时间, d=持续时间")
            logger.info("2. 移除overlay的enable参数，避免与fade效果冲突")
            logger.info("3. 让fade滤镜自然控制封面的显示时间")
            
            logger.info("\n🎬 验证方法:")
            logger.info("播放final_*.mp4文件，应该能看到:")
            logger.info("- final_fade_in.mp4: 封面从透明逐渐变为不透明")
            logger.info("- final_fade_out.mp4: 封面从不透明逐渐变为透明")
            logger.info("- final_fade_in_out.mp4: 封面先淡入再淡出")
            
            return True
        else:
            logger.warning(f"⚠️ 部分测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始最终修复测试")
    logger.info("本次修复的关键点：移除overlay的enable参数，避免与fade效果冲突")
    
    success = test_final_fix()
    
    if success:
        logger.info("\n🎉 最终修复测试完成!")
        logger.info("如果这次还是没有fade效果，那么问题可能在于:")
        logger.info("1. FFmpeg版本不支持fade滤镜")
        logger.info("2. 视频播放器不支持显示fade效果")
        logger.info("3. 需要使用不同的fade实现方法")
    else:
        logger.error("\n❌ 最终修复测试失败")
        sys.exit(1)
