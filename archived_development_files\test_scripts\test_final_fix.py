#!/usr/bin/env python3
"""
测试最终修复效果的脚本
使用实际的视频生成服务来测试修复后的效果
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "backend" / "src"))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_with_real_service():
    """使用真实的视频生成服务测试"""
    try:
        from services.video_generation_helpers import VideoCompositionService
        from models.video_material import VideoMaterial
        from models.background_music import BackgroundMusic
        
        # 配置路径
        MATERIALS_DIR = Path("backend/uploads/video_materials")
        OUTPUT_DIR = Path("test_outputs")
        OUTPUT_DIR.mkdir(exist_ok=True)
        
        # 1. 选择测试视频素材
        video_files = []
        total_duration = 0
        target_duration = 35
        
        for file_path in MATERIALS_DIR.glob("*.mp4"):
            if "thumb_" not in file_path.name:
                try:
                    import ffmpeg
                    probe = ffmpeg.probe(str(file_path))
                    video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
                    if video_stream:
                        duration = float(video_stream.get('duration', 0))
                        if duration > 0:
                            video_files.append({
                                'path': file_path,
                                'duration': duration
                            })
                            total_duration += duration
                            logger.info(f"选择视频: {file_path.name}, 时长: {duration:.2f}s")
                            
                            if total_duration >= target_duration:
                                break
                except Exception as e:
                    logger.warning(f"跳过文件 {file_path.name}: {e}")
                    continue
        
        if not video_files:
            logger.error("没有找到合适的测试视频")
            return False
        
        logger.info(f"总共选择 {len(video_files)} 个视频，总时长: {total_duration:.2f}s")
        
        # 2. 创建模拟的VideoMaterial对象
        materials = []
        for i, video_file in enumerate(video_files):
            # 创建一个简单的模拟对象
            class MockVideoMaterial:
                def __init__(self, file_path, duration):
                    self.id = i + 1
                    self.file_path = str(file_path)
                    self.duration = duration
                    self.width = 1080
                    self.height = 1920
                    self.fps = 30
            
            materials.append(MockVideoMaterial(video_file['path'], video_file['duration']))
        
        # 3. 创建模拟的BackgroundMusic对象
        class MockBackgroundMusic:
            def __init__(self):
                self.id = 1
                self.file_path = "backend/uploads/background_music/default.mp3"  # 假设存在
        
        background_music = MockBackgroundMusic()
        
        # 4. 创建测试音频（模拟TTS生成的语音）
        import ffmpeg
        test_audio_path = OUTPUT_DIR / "test_speech.wav"
        audio_stream = ffmpeg.input(f'sine=frequency=440:duration={target_duration}', f='lavfi')
        output_stream = ffmpeg.output(audio_stream, str(test_audio_path), acodec='pcm_s16le', ar=44100)
        
        process = await asyncio.create_subprocess_exec(
            *ffmpeg.compile(output_stream.overwrite_output()),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await process.communicate()
        
        if process.returncode != 0:
            logger.error("创建测试音频失败")
            return False
        
        logger.info(f"✅ 创建测试音频成功: {test_audio_path}")
        
        # 5. 创建测试字幕
        srt_content = f"""1
00:00:00,000 --> 00:00:05,000
真实服务测试 - 开始

2
00:00:05,000 --> 00:00:10,000
真实服务测试 - 5秒

3
00:00:10,000 --> 00:00:15,000
真实服务测试 - 10秒

4
00:00:15,000 --> 00:00:20,000
真实服务测试 - 15秒

5
00:00:20,000 --> 00:00:25,000
真实服务测试 - 20秒

6
00:00:25,000 --> 00:00:30,000
真实服务测试 - 25秒

7
00:00:30,000 --> 00:00:35,000
真实服务测试 - 30秒
"""
        
        srt_path = OUTPUT_DIR / "test_real_service.srt"
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        # 6. 创建测试封面
        cover_path = OUTPUT_DIR / "test_cover.png"
        cover_stream = ffmpeg.input('color=c=blue:size=1080x1920:duration=1', f='lavfi')
        cover_output = ffmpeg.output(cover_stream, str(cover_path), vframes=1)
        
        process = await asyncio.create_subprocess_exec(
            *ffmpeg.compile(cover_output.overwrite_output()),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await process.communicate()
        
        # 7. 配置测试参数
        video_settings = {
            'resolution': '1080x1920',
            'fps': 30,
            'format': 'mp4'
        }
        
        audio_settings = {
            'speech_volume': 1.0,
            'background_music_volume': 0.15,
            'enable_background_music': False  # 简化测试，不使用背景音乐
        }
        
        subtitle_settings = {
            'font_family': 'Arial',
            'font_size': 24,
            'font_color': '#FFFFFF',
            'position': 'bottom',
            'enabled': True
        }
        
        cover_settings = {
            'enabled': True,
            'position': 'top_right',
            'size': 'small',
            'animation': 'none'
        }
        
        transition_settings = {
            'enabled': True,
            'transition_type': 'fade',
            'duration': 0.5
        }
        
        # 8. 创建模拟任务对象
        class MockTask:
            def __init__(self):
                self.id = "test_task_001"
        
        task = MockTask()
        
        # 9. 调用真实的视频合成服务
        output_path = "test_outputs/test_real_service_output.mp4"
        
        logger.info("🚀 开始调用真实的视频合成服务...")
        
        success = await VideoCompositionService.compose_video(
            task=task,
            materials=materials,
            background_music=background_music,
            audio_duration=target_duration,
            cover_image_path=str(cover_path),
            first_sentence_duration=3.0,
            subtitle_file_path=str(srt_path),
            output_path=output_path,
            video_settings=video_settings,
            audio_settings=audio_settings,
            subtitle_settings=subtitle_settings,
            cover_settings=cover_settings,
            transition_settings=transition_settings
        )
        
        if success:
            # 10. 检查输出结果
            output_file = Path("backend") / output_path
            if output_file.exists():
                # 分析输出视频
                probe = ffmpeg.probe(str(output_file))
                video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
                audio_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'audio'), None)
                
                if video_stream and audio_stream:
                    video_duration = float(video_stream.get('duration', 0))
                    audio_duration = float(audio_stream.get('duration', 0))
                    duration_diff = abs(video_duration - audio_duration)
                    
                    logger.info(f"🎉 真实服务测试成功!")
                    logger.info(f"   输出文件: {output_file}")
                    logger.info(f"   视频时长: {video_duration:.2f}s")
                    logger.info(f"   音频时长: {audio_duration:.2f}s")
                    logger.info(f"   时长差异: {duration_diff:.2f}s")
                    
                    if duration_diff < 0.5:
                        logger.info("✅ 时长匹配成功，修复有效!")
                        return True
                    else:
                        logger.warning("⚠️ 时长仍有较大差异")
                        return False
                else:
                    logger.error("❌ 无法分析输出视频流")
                    return False
            else:
                logger.error(f"❌ 输出文件不存在: {output_file}")
                return False
        else:
            logger.error("❌ 视频合成失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 真实服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    logger.info("开始真实服务测试...")
    
    success = await test_with_real_service()
    
    if success:
        logger.info("🎉 真实服务测试成功！修复有效！")
    else:
        logger.error("❌ 真实服务测试失败")

if __name__ == "__main__":
    asyncio.run(main())
