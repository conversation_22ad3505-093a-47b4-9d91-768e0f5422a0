#!/usr/bin/env python3
"""
调试转场问题 - 简化测试找出10秒后定格的根本原因
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_xfade_calculation():
    """调试xfade转场的时间计算"""
    
    logger.info("🔍 开始调试xfade转场时间计算...")
    
    # 模拟5段视频的时长
    segment_durations = [3.0, 4.0, 2.5, 3.5, 2.0]  # 总计15秒
    transition_duration = 0.5
    
    logger.info(f"视频片段时长: {segment_durations}")
    logger.info(f"转场时长: {transition_duration}s")
    logger.info(f"总原始时长: {sum(segment_durations)}s")
    
    # 计算转场时间点
    total_segments = len(segment_durations)
    transition_points = []
    cumulative_time = 0
    
    logger.info("\n=== 转场时间点计算 ===")
    
    for i in range(total_segments - 1):
        current_segment_duration = segment_durations[i]
        cumulative_time += current_segment_duration
        
        # 转场开始时间 = 累积时间 - 转场时长
        transition_start = max(0, cumulative_time - transition_duration)
        transition_points.append(transition_start)
        
        logger.info(f"转场 {i+1}:")
        logger.info(f"  片段{i+1}时长: {current_segment_duration}s")
        logger.info(f"  累积时间: {cumulative_time}s")
        logger.info(f"  转场开始: {transition_start}s")
        logger.info(f"  转场结束: {cumulative_time}s")
        
        # 减去转场重叠时间，为下一段做准备
        cumulative_time -= transition_duration
        logger.info(f"  调整后累积时间: {cumulative_time}s")
        logger.info("")
    
    # 计算最终时长
    total_original_duration = sum(segment_durations)
    total_transition_overlap = (total_segments - 1) * transition_duration
    expected_final_duration = total_original_duration - total_transition_overlap
    
    logger.info("=== 最终时长计算 ===")
    logger.info(f"原始总时长: {total_original_duration}s")
    logger.info(f"转场数量: {total_segments - 1}")
    logger.info(f"转场重叠时长: {total_transition_overlap}s")
    logger.info(f"预期最终时长: {expected_final_duration}s")
    
    # 检查10秒时应该播放什么
    logger.info("\n=== 10秒时播放内容分析 ===")
    
    # 模拟播放时间线
    timeline = []
    current_time = 0
    
    for i in range(total_segments):
        segment_duration = segment_durations[i]
        
        if i == 0:
            # 第一段：完整播放到转场开始
            segment_end = segment_duration - transition_duration
            timeline.append({
                'segment': i + 1,
                'start': current_time,
                'end': segment_end,
                'content': f'片段{i+1}完整播放'
            })
            current_time = segment_end
        else:
            # 后续段：从转场开始播放剩余部分
            segment_start = current_time
            segment_end = current_time + segment_duration - transition_duration
            timeline.append({
                'segment': i + 1,
                'start': segment_start,
                'end': segment_end,
                'content': f'片段{i+1}剩余部分'
            })
            current_time = segment_end
    
    logger.info("播放时间线:")
    for item in timeline:
        logger.info(f"  {item['start']:.1f}s - {item['end']:.1f}s: {item['content']}")
    
    # 检查10秒时的状态
    target_time = 10.0
    logger.info(f"\n在{target_time}秒时:")
    
    for item in timeline:
        if item['start'] <= target_time <= item['end']:
            logger.info(f"  应该播放: {item['content']}")
            logger.info(f"  片段{item['segment']}的第{target_time - item['start']:.1f}秒")
            break
    else:
        logger.warning(f"  {target_time}秒超出了预期播放时长！")
        logger.warning("  这可能是定格问题的原因！")
    
    return expected_final_duration

def test_simple_xfade():
    """测试简单的xfade转场"""
    
    logger.info("\n🎬 测试简单的3段xfade转场...")
    
    try:
        # 创建3个简单的测试视频
        colors = [('red', '#FF0000', 3.0), ('green', '#00FF00', 4.0), ('blue', '#0000FF', 2.5)]
        
        for name, color, duration in colors:
            output_path = f"debug_{name}.mp4"
            if not Path(output_path).exists():
                logger.info(f"创建{duration}s {name}视频")
                (
                    ffmpeg
                    .input(f'color={color}:size=640x480:duration={duration}:rate=30', f='lavfi')
                    .output(output_path, vcodec='libx264', pix_fmt='yuv420p')
                    .overwrite_output()
                    .run(quiet=True)
                )
        
        # 手动构建xfade命令
        logger.info("手动构建xfade转场...")
        
        # 读取视频流
        red_stream = ffmpeg.input('debug_red.mp4').filter('fps', fps=30)
        green_stream = ffmpeg.input('debug_green.mp4').filter('fps', fps=30)
        blue_stream = ffmpeg.input('debug_blue.mp4').filter('fps', fps=30)
        
        # 第一个转场：红色 -> 绿色
        # 红色3秒，在2.5秒开始0.5秒转场
        first_transition = ffmpeg.filter(
            [red_stream, green_stream],
            'xfade',
            transition='fade',
            duration=0.5,
            offset=2.5
        )
        
        # 第二个转场：结果 -> 蓝色
        # 第一个转场结果应该是6.5秒（3+4-0.5），在6秒开始0.5秒转场
        final_result = ffmpeg.filter(
            [first_transition, blue_stream],
            'xfade',
            transition='fade',
            duration=0.5,
            offset=6.0
        )
        
        # 输出
        out = ffmpeg.output(
            final_result,
            'debug_manual_xfade.mp4',
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        logger.info("执行手动xfade命令...")
        ffmpeg.run(out, quiet=True)
        
        if Path('debug_manual_xfade.mp4').exists():
            logger.info("✅ 手动xfade测试成功!")
            logger.info("预期时长: 8.5秒 (3+4+2.5-2*0.5)")
            logger.info("请检查10秒时是否还在播放")
        else:
            logger.error("❌ 手动xfade测试失败")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    finally:
        # 清理测试文件
        for name in ['red', 'green', 'blue']:
            file_path = f"debug_{name}.mp4"
            if Path(file_path).exists():
                Path(file_path).unlink()

if __name__ == "__main__":
    logger.info("🚀 开始调试转场问题")
    
    # 1. 分析时间计算
    expected_duration = debug_xfade_calculation()
    
    # 2. 测试简单xfade
    test_simple_xfade()
    
    logger.info(f"\n📊 调试总结:")
    logger.info(f"预期5段视频转场后时长: {expected_duration}s")
    logger.info(f"如果10秒后定格，可能是xfade的offset计算有误")
    logger.info(f"需要检查每个转场的offset是否正确对应视频的实际时间点")
