<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>封面模板管理 - Reddit故事视频生成器</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        [data-theme="green"] {
            --primary-color: #059669;
            --primary-hover: #047857;
            --primary-light: #d1fae5;
        }

        [data-theme="purple"] {
            --primary-color: #7c3aed;
            --primary-hover: #6d28d9;
            --primary-light: #ede9fe;
        }

        [data-theme="orange"] {
            --primary-color: #ea580c;
            --primary-hover: #c2410c;
            --primary-light: #fed7aa;
        }

        [data-theme="red"] {
            --primary-color: #dc2626;
            --primary-hover: #b91c1c;
            --primary-light: #fecaca;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-secondary);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .left-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .panel-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
        }

        .panel-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .panel-content {
            padding: 1.5rem;
        }

        .template-editor {
            min-height: 600px;
            display: flex;
            flex-direction: column;
        }

        .editor-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
        }

        .editor-tools {
            display: flex;
            gap: 0.5rem;
        }

        .tool-btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid var(--border-color);
            background: var(--bg-primary);
            border-radius: 4px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tool-btn:hover, .tool-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .canvas-container {
            flex: 1;
            position: relative;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }        .template-canvas {
            width: 320px;
            height: 180px;
            background: #667eea;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            cursor: crosshair;
            box-shadow: var(--shadow-lg);
        }

        .canvas-element {
            position: absolute;
            cursor: move;
            border: 2px dashed transparent;
            transition: all 0.2s;
        }

        .canvas-element:hover, .canvas-element.selected {
            border-color: var(--primary-color);
        }        .text-element {
            padding: 4px 8px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            user-select: none;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            line-height: 1.2;
        }

        .title-element {
            color: white;
            font-size: 18px;
            top: 20px;
            left: 20px;
            max-width: 280px;
        }

        .author-element {
            color: #fbbf24;
            font-size: 14px;
            bottom: 40px;
            left: 20px;
        }

        .timestamp-element {
            color: #d1d5db;
            font-size: 12px;
            bottom: 20px;
            left: 20px;
        }

        .properties-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
        }

        .property-group {
            margin-bottom: 1.5rem;
        }

        .property-label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .property-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.875rem;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .property-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .color-input {
            width: 100%;
            height: 40px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
        }

        .range-input {
            width: 100%;
        }

        .templates-library {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
        }

        .library-filters {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            background: var(--bg-primary);
            border-radius: 20px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn:hover, .filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .templates-grid {
            padding: 1rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .template-item {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.2s;
        }

        .template-item:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow);
        }

        .template-preview {
            width: 100%;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .template-info {
            padding: 0.5rem;
            font-size: 0.75rem;
        }

        .template-name {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .template-meta {
            color: var(--text-secondary);
            font-size: 0.625rem;
        }

        .templates-list {
            margin-top: 2rem;
        }

        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .list-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .list-actions {
            display: flex;
            gap: 0.5rem;
        }

        .templates-table {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 0.75rem 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table th {
            background: var(--bg-secondary);
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .table td {
            font-size: 0.875rem;
        }

        .table tbody tr:hover {
            background: var(--bg-secondary);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: var(--success-color);
        }

        .status-draft {
            background: #fef3c7;
            color: var(--warning-color);
        }

        .table-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-edit {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .action-delete {
            background: #fee2e2;
            color: var(--danger-color);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.125rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: 8px;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .right-panel {
                order: -1;
            }

            .template-canvas {
                width: 240px;
                height: 135px;
            }

            .templates-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body data-theme="blue">    <header class="header">
        <div class="header-content">
            <h1>封面模板管理</h1>
            <div class="header-actions">
                <button class="btn btn-secondary">← 返回</button>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h2 class="page-title">封面模板设计器</h2>
            <p class="page-description">创建和管理视频封面模板，支持自定义文本、颜色、位置等属性</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">12</div>
                <div class="stat-label">总模板数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">8</div>
                <div class="stat-label">已启用</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">4</div>
                <div class="stat-label">草稿</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">156</div>
                <div class="stat-label">使用次数</div>
            </div>
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 模板编辑器 -->
            <div class="left-panel">
                <div class="panel-header">
                    <h3 class="panel-title">模板编辑器</h3>
                </div>
                <div class="template-editor">
                    <div class="editor-toolbar">
                        <div class="editor-tools">
                            <button class="tool-btn active" data-tool="select">选择</button>
                            <button class="tool-btn" data-tool="text">文本</button>
                            <button class="tool-btn" data-tool="shape">形状</button>
                            <button class="tool-btn" data-tool="image">图片</button>
                        </div>
                        <div class="editor-actions">
                            <button class="btn btn-secondary btn-sm">撤销</button>
                            <button class="btn btn-secondary btn-sm">重做</button>
                            <button class="btn btn-primary btn-sm">保存</button>
                        </div>
                    </div>
                    <div class="canvas-container">                        <div class="template-canvas" id="templateCanvas">
                            <div class="canvas-element text-element title-element selected" data-element="title" style="width: 280px; text-align: left;">
                                这里是标题文本
                            </div>
                            <div class="canvas-element text-element author-element" data-element="author" style="width: 150px; text-align: left;">
                                u/作者名称
                            </div>
                            <div class="canvas-element text-element timestamp-element" data-element="timestamp" style="width: 100px; text-align: left;">
                                2小时前
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 属性面板 -->
                <div class="properties-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">属性设置</h3>
                    </div>                    <div class="panel-content">                        <!-- 文本属性组 -->
                        <div id="textProperties" style="display: none;">
                            <div class="property-group">
                                <label class="property-label">文本内容</label>
                                <textarea class="property-input" rows="3" id="textContent"></textarea>
                            </div>
                            <div class="property-group">
                                <label class="property-label">字体大小</label>
                                <input type="range" class="range-input" min="12" max="48" value="16" id="fontSize">
                                <span id="fontSizeValue">16px</span>
                            </div>
                            <div class="property-group">
                                <label class="property-label">文字颜色</label>
                                <input type="color" class="color-input" value="#ffffff" id="textColor">
                            </div>
                            <div class="property-group">
                                <label class="property-label">文本宽度</label>
                                <input type="number" class="property-input" min="50" max="280" value="200" id="textWidth">
                                <small style="color: var(--text-secondary); font-size: 0.75rem;">设置文本框宽度，超出将自动换行</small>
                            </div>
                            <div class="property-group">
                                <label class="property-label">文本对齐</label>
                                <select class="property-input" id="textAlign">
                                    <option value="left">左对齐</option>
                                    <option value="center">居中对齐</option>
                                    <option value="right">右对齐</option>
                                </select>
                            </div>
                        </div>

                        <!-- 形状属性组 -->
                        <div id="shapeProperties" style="display: none;">
                            <div class="property-group">
                                <label class="property-label">形状类型</label>
                                <select class="property-input" id="shapeType">
                                    <option value="rectangle">矩形</option>
                                    <option value="square">正方形</option>
                                    <option value="circle">圆形</option>
                                    <option value="triangle">三角形</option>
                                    <option value="star">星形</option>
                                </select>
                            </div>
                            <div class="property-group">
                                <label class="property-label">宽度</label>
                                <input type="number" class="property-input" min="10" max="300" value="50" id="shapeWidth">
                            </div>
                            <div class="property-group">
                                <label class="property-label">高度</label>
                                <input type="number" class="property-input" min="10" max="300" value="50" id="shapeHeight">
                            </div>
                            <div class="property-group">
                                <label class="property-label">填充颜色</label>
                                <input type="color" class="color-input" value="#ffffff" id="shapeFillColor">
                            </div>
                            <div class="property-group">
                                <label class="property-label">边框颜色</label>
                                <input type="color" class="color-input" value="#000000" id="shapeBorderColor">
                            </div>
                            <div class="property-group">
                                <label class="property-label">边框宽度</label>
                                <input type="range" class="range-input" min="0" max="10" value="0" id="shapeBorderWidth">
                                <span id="borderWidthValue">0px</span>
                            </div>
                        </div>

                        <!-- 图片属性组 -->
                        <div id="imageProperties" style="display: none;">
                            <div class="property-group">
                                <label class="property-label">图片形状</label>
                                <select class="property-input" id="imageShape">
                                    <option value="rectangle">矩形</option>
                                    <option value="square">正方形</option>
                                    <option value="circle">圆形</option>
                                </select>
                            </div>
                            <div class="property-group">
                                <label class="property-label">宽度</label>
                                <input type="number" class="property-input" min="20" max="300" value="80" id="imageWidth">
                            </div>
                            <div class="property-group">
                                <label class="property-label">高度</label>
                                <input type="number" class="property-input" min="20" max="300" value="60" id="imageHeight">
                            </div>
                            <div class="property-group">
                                <label class="property-label">上传图片</label>
                                <input type="file" class="property-input" accept="image/*" id="imageUpload">
                            </div>
                        </div>

                        <!-- 通用位置属性 -->
                        <div id="positionProperties" style="display: none;">
                            <div class="property-group">
                                <label class="property-label">位置调整</label>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                                    <input type="number" class="property-input" placeholder="X坐标" value="0" id="positionX">
                                    <input type="number" class="property-input" placeholder="Y坐标" value="0" id="positionY">
                                </div>
                            </div>
                        </div>

                        <!-- 背景属性组 -->
                        <div id="backgroundProperties">
                            <div class="property-group">
                                <label class="property-label">背景类型</label>
                                <select class="property-input" id="backgroundType">
                                    <option value="solid">纯色背景</option>
                                    <option value="gradient">渐变背景</option>
                                    <option value="image">图片背景</option>
                                </select>
                            </div>
                            <div class="property-group" id="solidColorGroup">
                                <label class="property-label">背景颜色</label>
                                <input type="color" class="color-input" value="#667eea" id="backgroundColor">
                            </div>
                            <div class="property-group" id="gradientGroup" style="display: none;">
                                <label class="property-label">渐变样式</label>
                                <select class="property-input" id="backgroundGradient">
                                    <option value="blue-purple">蓝紫渐变</option>
                                    <option value="sunset">日落渐变</option>
                                    <option value="ocean">海洋渐变</option>
                                    <option value="forest">森林渐变</option>
                                    <option value="fire">火焰渐变</option>
                                </select>
                            </div>
                            <div class="property-group" id="imageGroup" style="display: none;">
                                <label class="property-label">背景图片</label>
                                <input type="file" class="property-input" accept="image/*" id="backgroundImage">
                            </div>
                        </div>

                        <button class="btn btn-primary" style="width: 100%; margin-top: 1rem;" onclick="applyChanges()">应用更改</button>
                    </div>
                </div>

                <!-- 模板库 -->
                <div class="templates-library">
                    <div class="panel-header">
                        <h3 class="panel-title">模板库</h3>
                    </div>
                    <div class="library-filters">
                        <button class="filter-btn active">全部</button>
                        <button class="filter-btn">经典</button>
                        <button class="filter-btn">现代</button>
                        <button class="filter-btn">简约</button>
                        <button class="filter-btn">科技</button>
                    </div>
                    <div class="templates-grid">
                        <div class="template-item">
                            <div class="template-preview" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                经典样式
                            </div>
                            <div class="template-info">
                                <div class="template-name">经典蓝紫</div>
                                <div class="template-meta">使用23次</div>
                            </div>
                        </div>
                        <div class="template-item">
                            <div class="template-preview" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                                粉色主题
                            </div>
                            <div class="template-info">
                                <div class="template-name">粉色梦幻</div>
                                <div class="template-meta">使用18次</div>
                            </div>
                        </div>
                        <div class="template-item">
                            <div class="template-preview" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                清新蓝色
                            </div>
                            <div class="template-info">
                                <div class="template-name">清新海洋</div>
                                <div class="template-meta">使用31次</div>
                            </div>
                        </div>
                        <div class="template-item">
                            <div class="template-preview" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                暖色调
                            </div>
                            <div class="template-info">
                                <div class="template-name">温暖夕阳</div>
                                <div class="template-meta">使用15次</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模板列表 -->
        <div class="templates-list">
            <div class="list-header">
                <h3 class="list-title">我的模板</h3>
                <div class="list-actions">
                    <button class="btn btn-secondary">导入模板</button>
                    <button class="btn btn-primary" onclick="showCreateModal()">+ 新建模板</button>
                </div>
            </div>
            
            <div class="templates-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>预览</th>
                            <th>模板名称</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th>使用次数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="width: 60px; height: 34px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 4px;"></div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">经典蓝紫渐变</div>
                                <div style="font-size: 0.75rem; color: var(--text-secondary);">16:9 横版</div>
                            </td>
                            <td>2024-01-15</td>
                            <td>
                                <span class="status-badge status-active">
                                    <span style="width: 6px; height: 6px; background: currentColor; border-radius: 50%; display: inline-block;"></span>
                                    已启用
                                </span>
                            </td>
                            <td>23</td>
                            <td>
                                <div class="table-actions">
                                    <button class="action-btn action-edit">编辑</button>
                                    <button class="action-btn action-edit">复制</button>
                                    <button class="action-btn action-delete">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="width: 60px; height: 34px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 4px;"></div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">粉色梦幻风格</div>
                                <div style="font-size: 0.75rem; color: var(--text-secondary);">16:9 横版</div>
                            </td>
                            <td>2024-01-14</td>
                            <td>
                                <span class="status-badge status-active">
                                    <span style="width: 6px; height: 6px; background: currentColor; border-radius: 50%; display: inline-block;"></span>
                                    已启用
                                </span>
                            </td>
                            <td>18</td>
                            <td>
                                <div class="table-actions">
                                    <button class="action-btn action-edit">编辑</button>
                                    <button class="action-btn action-edit">复制</button>
                                    <button class="action-btn action-delete">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="width: 60px; height: 34px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 4px;"></div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">清新海洋蓝</div>
                                <div style="font-size: 0.75rem; color: var(--text-secondary);">16:9 横版</div>
                            </td>
                            <td>2024-01-13</td>
                            <td>
                                <span class="status-badge status-draft">
                                    <span style="width: 6px; height: 6px; background: currentColor; border-radius: 50%; display: inline-block;"></span>
                                    草稿
                                </span>
                            </td>
                            <td>0</td>
                            <td>
                                <div class="table-actions">
                                    <button class="action-btn action-edit">编辑</button>
                                    <button class="action-btn action-edit">复制</button>
                                    <button class="action-btn action-delete">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="width: 60px; height: 34px; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 4px;"></div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">温暖夕阳色</div>
                                <div style="font-size: 0.75rem; color: var(--text-secondary);">16:9 横版</div>
                            </td>
                            <td>2024-01-12</td>
                            <td>
                                <span class="status-badge status-active">
                                    <span style="width: 6px; height: 6px; background: currentColor; border-radius: 50%; display: inline-block;"></span>
                                    已启用
                                </span>
                            </td>
                            <td>15</td>
                            <td>
                                <div class="table-actions">
                                    <button class="action-btn action-edit">编辑</button>
                                    <button class="action-btn action-edit">复制</button>
                                    <button class="action-btn action-delete">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 新建模板模态框 -->
    <div class="modal" id="createModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">新建封面模板</h3>
                <button class="modal-close" onclick="hideCreateModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="property-group">
                    <label class="property-label">模板名称</label>
                    <input type="text" class="property-input" placeholder="请输入模板名称">
                </div>
                <div class="property-group">
                    <label class="property-label">模板分类</label>
                    <select class="property-input">
                        <option>经典风格</option>
                        <option>现代风格</option>
                        <option>简约风格</option>
                        <option>科技风格</option>
                    </select>
                </div>
                <div class="property-group">
                    <label class="property-label">画面比例</label>
                    <select class="property-input">
                        <option>16:9 (推荐)</option>
                        <option>9:16 (竖屏)</option>
                        <option>1:1 (正方形)</option>
                        <option>4:3 (传统)</option>
                    </select>
                </div>
                <div class="property-group">
                    <label class="property-label">模板描述</label>
                    <textarea class="property-input" rows="3" placeholder="请输入模板描述"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideCreateModal()">取消</button>
                <button class="btn btn-primary" onclick="createTemplate()">创建模板</button>
            </div>
        </div>
    </div>    <script>
        // 当前工具和状态
        let currentTool = 'select';
        let elementCounter = 0;
        let selectedElement = null;

        // 主题切换功能（从本地存储加载）
        const savedTheme = localStorage.getItem('theme') || 'blue';
        document.body.setAttribute('data-theme', savedTheme);

        // 编辑器工具切换
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                currentTool = this.dataset.tool;
                console.log('切换到工具:', currentTool);
                
                // 更改画布光标
                const canvas = document.getElementById('templateCanvas');
                if (currentTool === 'select') {
                    canvas.style.cursor = 'default';
                } else {
                    canvas.style.cursor = 'crosshair';
                }
            });
        });

        // 画布点击事件
        document.getElementById('templateCanvas').addEventListener('click', function(e) {
            if (currentTool === 'text') {
                addTextElement(e);
            } else if (currentTool === 'shape') {
                addShapeElement(e);
            } else if (currentTool === 'image') {
                addImagePlaceholder(e);
            }
        });        // 添加文本元素
        function addTextElement(e) {
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left - 50; // 偏移一点避免太靠边
            const y = e.clientY - rect.top - 10;
            
            elementCounter++;
            const textElement = document.createElement('div');
            textElement.className = 'canvas-element text-element';
            textElement.style.left = Math.max(0, x) + 'px';
            textElement.style.top = Math.max(0, y) + 'px';
            textElement.style.color = '#ffffff';
            textElement.style.fontSize = '16px';
            textElement.style.width = '200px';
            textElement.style.textAlign = 'left';
            textElement.textContent = '新文本 ' + elementCounter;
            textElement.dataset.element = 'text-' + elementCounter;
            
            // 添加点击事件
            textElement.addEventListener('click', function(e) {
                e.stopPropagation();
                selectElement(this);
            });
            
            e.currentTarget.appendChild(textElement);
            selectElement(textElement);
        }// 添加形状元素
        function addShapeElement(e) {
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left - 25;
            const y = e.clientY - rect.top - 25;
            
            elementCounter++;
            const shapeElement = document.createElement('div');
            shapeElement.className = 'canvas-element shape-element';
            shapeElement.style.left = Math.max(0, x) + 'px';
            shapeElement.style.top = Math.max(0, y) + 'px';
            shapeElement.style.width = '50px';
            shapeElement.style.height = '50px';
            shapeElement.style.backgroundColor = '#ffffff';
            shapeElement.style.borderRadius = '4px';
            shapeElement.dataset.element = 'shape-' + elementCounter;
            shapeElement.dataset.shapeType = 'rectangle';
            
            // 添加点击事件
            shapeElement.addEventListener('click', function(e) {
                e.stopPropagation();
                selectElement(this);
            });
            
            e.currentTarget.appendChild(shapeElement);
            selectElement(shapeElement);
        }        // 添加图片占位符
        function addImagePlaceholder(e) {
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left - 40;
            const y = e.clientY - rect.top - 30;
            
            elementCounter++;
            const imageElement = document.createElement('div');
            imageElement.className = 'canvas-element image-element';
            imageElement.style.left = Math.max(0, x) + 'px';
            imageElement.style.top = Math.max(0, y) + 'px';
            imageElement.style.width = '80px';
            imageElement.style.height = '60px';
            imageElement.style.backgroundColor = '#f3f4f6';
            imageElement.style.border = '2px dashed #9ca3af';
            imageElement.style.display = 'flex';
            imageElement.style.alignItems = 'center';
            imageElement.style.justifyContent = 'center';
            imageElement.style.fontSize = '12px';
            imageElement.style.color = '#6b7280';
            imageElement.style.borderRadius = '4px';
            imageElement.textContent = '图片';
            imageElement.dataset.element = 'image-' + elementCounter;
            imageElement.dataset.imageType = 'rectangle';
            
            // 添加点击事件
            imageElement.addEventListener('click', function(e) {
                e.stopPropagation();
                selectElement(this);
            });
            
            e.currentTarget.appendChild(imageElement);
            selectElement(imageElement);
        }

        // 选择元素
        function selectElement(element) {
            // 清除所有选中状态
            document.querySelectorAll('.canvas-element').forEach(el => el.classList.remove('selected'));
            
            // 选中当前元素
            element.classList.add('selected');
            selectedElement = element;
            
            // 切换回选择工具
            currentTool = 'select';
            document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
            document.querySelector('.tool-btn[data-tool="select"]').classList.add('active');
            document.getElementById('templateCanvas').style.cursor = 'default';
            
            // 更新属性面板
            updatePropertyPanel(element);
        }

        // 画布元素选择（已有元素）
        document.querySelectorAll('.canvas-element').forEach(element => {
            element.addEventListener('click', function(e) {
                e.stopPropagation();
                selectElement(this);
            });
        });        function updatePropertyPanel(element) {
            // 隐藏所有属性组
            document.getElementById('textProperties').style.display = 'none';
            document.getElementById('shapeProperties').style.display = 'none';
            document.getElementById('imageProperties').style.display = 'none';
            document.getElementById('positionProperties').style.display = 'none';
            
            // 总是显示位置属性
            document.getElementById('positionProperties').style.display = 'block';
            
            // 更新位置信息
            const positionX = document.getElementById('positionX');
            const positionY = document.getElementById('positionY');
            positionX.value = parseInt(element.style.left || 0);
            positionY.value = parseInt(element.style.top || 0);
              if (element.classList.contains('text-element')) {
                // 显示文本属性
                document.getElementById('textProperties').style.display = 'block';
                
                const textContent = document.getElementById('textContent');
                const fontSize = document.getElementById('fontSize');
                const textColor = document.getElementById('textColor');
                const textWidth = document.getElementById('textWidth');
                const textAlign = document.getElementById('textAlign');
                const fontSizeValue = document.getElementById('fontSizeValue');
                
                textContent.value = element.textContent.trim();
                
                // 获取计算样式
                const computedStyle = window.getComputedStyle(element);
                const currentFontSize = parseInt(computedStyle.fontSize);
                fontSize.value = currentFontSize;
                fontSizeValue.textContent = currentFontSize + 'px';
                
                // 更新颜色
                textColor.value = rgbToHex(computedStyle.color) || '#ffffff';
                
                // 更新宽度
                textWidth.value = parseInt(element.style.width || 200);
                
                // 更新对齐方式
                textAlign.value = element.style.textAlign || 'left';
                
            } else if (element.classList.contains('shape-element')) {
                // 显示形状属性
                document.getElementById('shapeProperties').style.display = 'block';
                
                const shapeType = document.getElementById('shapeType');
                const shapeWidth = document.getElementById('shapeWidth');
                const shapeHeight = document.getElementById('shapeHeight');
                const shapeFillColor = document.getElementById('shapeFillColor');
                const shapeBorderColor = document.getElementById('shapeBorderColor');
                const shapeBorderWidth = document.getElementById('shapeBorderWidth');
                const borderWidthValue = document.getElementById('borderWidthValue');
                
                // 设置形状类型
                shapeType.value = element.dataset.shapeType || 'rectangle';
                
                // 设置尺寸
                shapeWidth.value = parseInt(element.style.width || 50);
                shapeHeight.value = parseInt(element.style.height || 50);
                
                // 设置颜色
                const computedStyle = window.getComputedStyle(element);
                shapeFillColor.value = rgbToHex(computedStyle.backgroundColor) || '#ffffff';
                shapeBorderColor.value = rgbToHex(computedStyle.borderColor) || '#000000';
                
                // 设置边框宽度
                const borderWidth = parseInt(computedStyle.borderWidth || 0);
                shapeBorderWidth.value = borderWidth;
                borderWidthValue.textContent = borderWidth + 'px';
                
            } else if (element.classList.contains('image-element')) {
                // 显示图片属性
                document.getElementById('imageProperties').style.display = 'block';
                
                const imageShape = document.getElementById('imageShape');
                const imageWidth = document.getElementById('imageWidth');
                const imageHeight = document.getElementById('imageHeight');
                
                // 设置图片形状
                imageShape.value = element.dataset.imageType || 'rectangle';
                
                // 设置尺寸
                imageWidth.value = parseInt(element.style.width || 80);
                imageHeight.value = parseInt(element.style.height || 60);
            }
        }

        // RGB转HEX工具函数
        function rgbToHex(rgb) {
            if (!rgb) return '#ffffff';
            if (rgb.indexOf('#') === 0) return rgb;
            
            const result = rgb.match(/\d+/g);
            if (!result || result.length < 3) return '#ffffff';
            
            return "#" + ((1 << 24) + (parseInt(result[0]) << 16) + (parseInt(result[1]) << 8) + parseInt(result[2])).toString(16).slice(1);
        }        // 文本属性控制
        document.getElementById('textContent').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('text-element')) {
                selectedElement.textContent = this.value;
            }
        });

        document.getElementById('fontSize').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('text-element')) {
                selectedElement.style.fontSize = this.value + 'px';
                document.getElementById('fontSizeValue').textContent = this.value + 'px';
            }
        });        document.getElementById('textColor').addEventListener('change', function() {
            if (selectedElement && selectedElement.classList.contains('text-element')) {
                selectedElement.style.color = this.value;
            }
        });

        document.getElementById('textWidth').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('text-element')) {
                selectedElement.style.width = this.value + 'px';
            }
        });

        document.getElementById('textAlign').addEventListener('change', function() {
            if (selectedElement && selectedElement.classList.contains('text-element')) {
                selectedElement.style.textAlign = this.value;
            }
        });

        // 形状属性控制
        document.getElementById('shapeType').addEventListener('change', function() {
            if (selectedElement && selectedElement.classList.contains('shape-element')) {
                selectedElement.dataset.shapeType = this.value;
                applyShapeStyle(selectedElement, this.value);
            }
        });

        document.getElementById('shapeWidth').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('shape-element')) {
                selectedElement.style.width = this.value + 'px';
                if (selectedElement.dataset.shapeType === 'square' || selectedElement.dataset.shapeType === 'circle') {
                    selectedElement.style.height = this.value + 'px';
                    document.getElementById('shapeHeight').value = this.value;
                }
            }
        });

        document.getElementById('shapeHeight').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('shape-element')) {
                selectedElement.style.height = this.value + 'px';
                if (selectedElement.dataset.shapeType === 'square' || selectedElement.dataset.shapeType === 'circle') {
                    selectedElement.style.width = this.value + 'px';
                    document.getElementById('shapeWidth').value = this.value;
                }
            }
        });

        document.getElementById('shapeFillColor').addEventListener('change', function() {
            if (selectedElement && selectedElement.classList.contains('shape-element')) {
                selectedElement.style.backgroundColor = this.value;
            }
        });

        document.getElementById('shapeBorderColor').addEventListener('change', function() {
            if (selectedElement && selectedElement.classList.contains('shape-element')) {
                selectedElement.style.borderColor = this.value;
            }
        });

        document.getElementById('shapeBorderWidth').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('shape-element')) {
                selectedElement.style.borderWidth = this.value + 'px';
                selectedElement.style.borderStyle = 'solid';
                document.getElementById('borderWidthValue').textContent = this.value + 'px';
            }
        });

        // 图片属性控制
        document.getElementById('imageShape').addEventListener('change', function() {
            if (selectedElement && selectedElement.classList.contains('image-element')) {
                selectedElement.dataset.imageType = this.value;
                applyImageShape(selectedElement, this.value);
            }
        });

        document.getElementById('imageWidth').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('image-element')) {
                selectedElement.style.width = this.value + 'px';
                if (selectedElement.dataset.imageType === 'square' || selectedElement.dataset.imageType === 'circle') {
                    selectedElement.style.height = this.value + 'px';
                    document.getElementById('imageHeight').value = this.value;
                }
            }
        });

        document.getElementById('imageHeight').addEventListener('input', function() {
            if (selectedElement && selectedElement.classList.contains('image-element')) {
                selectedElement.style.height = this.value + 'px';
                if (selectedElement.dataset.imageType === 'square' || selectedElement.dataset.imageType === 'circle') {
                    selectedElement.style.width = this.value + 'px';
                    document.getElementById('imageWidth').value = this.value;
                }
            }
        });

        document.getElementById('imageUpload').addEventListener('change', function(e) {
            if (selectedElement && selectedElement.classList.contains('image-element')) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        selectedElement.style.backgroundImage = `url(${e.target.result})`;
                        selectedElement.style.backgroundSize = 'cover';
                        selectedElement.style.backgroundPosition = 'center';
                        selectedElement.textContent = '';
                    };
                    reader.readAsDataURL(file);
                }
            }
        });

        // 位置属性控制
        document.getElementById('positionX').addEventListener('input', function() {
            if (selectedElement) {
                selectedElement.style.left = Math.max(0, Math.min(270, this.value)) + 'px';
            }
        });

        document.getElementById('positionY').addEventListener('input', function() {
            if (selectedElement) {
                selectedElement.style.top = Math.max(0, Math.min(150, this.value)) + 'px';
            }
        });

        // 形状样式应用函数
        function applyShapeStyle(element, shapeType) {
            switch(shapeType) {
                case 'rectangle':
                    element.style.borderRadius = '4px';
                    element.style.clipPath = 'none';
                    break;
                case 'square':
                    element.style.borderRadius = '4px';
                    element.style.clipPath = 'none';
                    const size = Math.min(parseInt(element.style.width), parseInt(element.style.height));
                    element.style.width = size + 'px';
                    element.style.height = size + 'px';
                    break;
                case 'circle':
                    element.style.borderRadius = '50%';
                    element.style.clipPath = 'none';
                    const circleSize = Math.min(parseInt(element.style.width), parseInt(element.style.height));
                    element.style.width = circleSize + 'px';
                    element.style.height = circleSize + 'px';
                    break;
                case 'triangle':
                    element.style.borderRadius = '0';
                    element.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';
                    break;
                case 'star':
                    element.style.borderRadius = '0';
                    element.style.clipPath = 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)';
                    break;
            }
        }

        // 图片形状应用函数
        function applyImageShape(element, imageType) {
            switch(imageType) {
                case 'rectangle':
                    element.style.borderRadius = '4px';
                    break;
                case 'square':
                    element.style.borderRadius = '4px';
                    const size = Math.min(parseInt(element.style.width), parseInt(element.style.height));
                    element.style.width = size + 'px';
                    element.style.height = size + 'px';
                    break;
                case 'circle':
                    element.style.borderRadius = '50%';
                    const circleSize = Math.min(parseInt(element.style.width), parseInt(element.style.height));
                    element.style.width = circleSize + 'px';
                    element.style.height = circleSize + 'px';
                    break;
            }
        }

        // 背景类型切换
        document.getElementById('backgroundType').addEventListener('change', function() {
            const solidGroup = document.getElementById('solidColorGroup');
            const gradientGroup = document.getElementById('gradientGroup');
            const imageGroup = document.getElementById('imageGroup');
            
            // 隐藏所有组
            solidGroup.style.display = 'none';
            gradientGroup.style.display = 'none';
            imageGroup.style.display = 'none';
            
            // 显示对应组
            if (this.value === 'solid') {
                solidGroup.style.display = 'block';
            } else if (this.value === 'gradient') {
                gradientGroup.style.display = 'block';
            } else if (this.value === 'image') {
                imageGroup.style.display = 'block';
            }
        });

        // 背景颜色控制
        document.getElementById('backgroundColor').addEventListener('change', function() {
            const canvas = document.getElementById('templateCanvas');
            canvas.style.background = this.value;
        });

        document.getElementById('backgroundGradient').addEventListener('change', function() {
            const canvas = document.getElementById('templateCanvas');
            const gradients = {
                'blue-purple': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'sunset': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                'ocean': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'forest': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'fire': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
            };
            canvas.style.background = gradients[this.value];
        });

        // 背景图片上传
        document.getElementById('backgroundImage').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const canvas = document.getElementById('templateCanvas');
                    canvas.style.background = `url(${e.target.result}) center/cover`;
                };
                reader.readAsDataURL(file);
            }
        });

        // 模板库过滤
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                console.log('过滤模板:', this.textContent);
            });
        });

        // 模板项点击
        document.querySelectorAll('.template-item').forEach(item => {
            item.addEventListener('click', function() {
                const preview = this.querySelector('.template-preview');
                const canvas = document.getElementById('templateCanvas');
                canvas.style.background = preview.style.background;
                
                console.log('应用模板:', this.querySelector('.template-name').textContent);
            });
        });

        // 模态框函数
        function showCreateModal() {
            document.getElementById('createModal').classList.add('show');
        }

        function hideCreateModal() {
            document.getElementById('createModal').classList.remove('show');
        }

        function createTemplate() {
            const name = document.querySelector('#createModal input[type="text"]').value;
            if (!name.trim()) {
                alert('请输入模板名称');
                return;
            }
            
            console.log('创建新模板:', name);
            hideCreateModal();
            
            // 这里添加创建逻辑
            alert('模板创建成功！');
        }

        // 拖拽功能
        let isDragging = false;
        let currentElement = null;
        let startX, startY, startLeft, startTop;

        document.addEventListener('mousedown', function(e) {
            if (e.target.classList.contains('canvas-element') && currentTool === 'select') {
                isDragging = true;
                currentElement = e.target;
                startX = e.clientX;
                startY = e.clientY;
                startLeft = parseInt(currentElement.style.left || 0);
                startTop = parseInt(currentElement.style.top || 0);
                currentElement.style.cursor = 'grabbing';
                
                // 选中被拖拽的元素
                selectElement(currentElement);
            }
        });        document.addEventListener('mousemove', function(e) {
            if (isDragging && currentElement) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                const newLeft = Math.max(0, Math.min(270, startLeft + deltaX)); // 限制在画布内
                const newTop = Math.max(0, Math.min(150, startTop + deltaY));
                currentElement.style.left = newLeft + 'px';
                currentElement.style.top = newTop + 'px';
                
                // 更新位置输入框
                document.getElementById('positionX').value = newLeft;
                document.getElementById('positionY').value = newTop;
            }
        });

        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                if (currentElement) {
                    currentElement.style.cursor = 'move';
                    currentElement = null;
                }
            }
        });        // 点击画布空白处取消选择
        document.getElementById('templateCanvas').addEventListener('click', function(e) {
            if (e.target === this && currentTool === 'select') {
                document.querySelectorAll('.canvas-element').forEach(el => el.classList.remove('selected'));
                selectedElement = null;
                
                // 隐藏所有属性面板，只显示背景属性
                document.getElementById('textProperties').style.display = 'none';
                document.getElementById('shapeProperties').style.display = 'none';
                document.getElementById('imageProperties').style.display = 'none';
                document.getElementById('positionProperties').style.display = 'none';
            }
        });

        // 删除选中元素 (Delete键)
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Delete' && selectedElement) {
                selectedElement.remove();
                selectedElement = null;
                
                // 隐藏所有属性面板
                document.getElementById('textProperties').style.display = 'none';
                document.getElementById('shapeProperties').style.display = 'none';
                document.getElementById('imageProperties').style.display = 'none';
                document.getElementById('positionProperties').style.display = 'none';
            }
        });

        // 应用更改函数
        function applyChanges() {
            console.log('应用所有更改到模板');
            // 这里可以添加保存逻辑
        }

        // 初始化默认选中第一个文本元素
        const firstTextElement = document.querySelector('.canvas-element.text-element');
        if (firstTextElement) {
            selectElement(firstTextElement);
        }

        console.log('封面模板管理页面已加载 - 增强版编辑器');
    </script>
</body>
</html>
