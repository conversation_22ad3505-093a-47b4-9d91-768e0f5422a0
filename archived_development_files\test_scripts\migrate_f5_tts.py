#!/usr/bin/env python3
"""
F5-TTS数据库迁移脚本
添加F5-TTS相关的数据库字段和表
"""

import sys
import os
sys.path.append('src')

from sqlalchemy import create_engine, text, inspect
from src.core.database import engine, get_session_maker
from src.models import F5TTSVoice
from loguru import logger

def check_column_exists(engine, table_name, column_name):
    """检查表中是否存在指定列"""
    inspector = inspect(engine)
    columns = inspector.get_columns(table_name)
    return any(col['name'] == column_name for col in columns)

def check_table_exists(engine, table_name):
    """检查表是否存在"""
    inspector = inspect(engine)
    return table_name in inspector.get_table_names()

def migrate_settings_table(engine):
    """迁移settings表，添加F5-TTS字段"""
    logger.info("检查settings表的F5-TTS字段...")
    
    # 检查f5_tts_endpoint字段是否存在
    if not check_column_exists(engine, 'settings', 'f5_tts_endpoint'):
        logger.info("添加f5_tts_endpoint字段到settings表...")
        with engine.connect() as conn:
            conn.execute(text("""
                ALTER TABLE settings 
                ADD COLUMN f5_tts_endpoint VARCHAR(500) 
                COMMENT 'F5-TTS服务端点'
            """))
            conn.commit()
        logger.info("✅ f5_tts_endpoint字段添加成功")
    else:
        logger.info("✅ f5_tts_endpoint字段已存在")

def create_f5_tts_voices_table(engine):
    """创建F5-TTS音色表"""
    logger.info("检查f5_tts_voices表...")
    
    if not check_table_exists(engine, 'f5_tts_voices'):
        logger.info("创建f5_tts_voices表...")
        # 只创建F5TTSVoice表
        F5TTSVoice.__table__.create(engine)
        logger.info("✅ f5_tts_voices表创建成功")
    else:
        logger.info("✅ f5_tts_voices表已存在")

def create_sample_voice(session_maker):
    """创建示例音色（可选）"""
    logger.info("检查是否需要创建示例音色...")
    
    session = session_maker()
    try:
        # 检查是否已有音色
        existing_voices = session.query(F5TTSVoice).count()
        if existing_voices == 0:
            logger.info("创建示例音色...")
            
            sample_voice = F5TTSVoice(
                name="示例音色",
                description="这是一个F5-TTS示例音色，请替换为您自己的音色",
                language="zh-CN",
                gender="female",
                ref_audio_path="/path/to/sample.wav",
                ref_text="这是示例参考文本，请替换为实际的参考音频对应文本",
                is_built_in=True
            )
            
            session.add(sample_voice)
            session.commit()
            logger.info("✅ 示例音色创建成功")
        else:
            logger.info("✅ 已存在音色，跳过示例音色创建")
            
    except Exception as e:
        session.rollback()
        logger.error(f"创建示例音色失败: {e}")
    finally:
        session.close()

def main():
    """主迁移函数"""
    logger.info("🚀 开始F5-TTS数据库迁移...")

    try:
        # 使用现有的数据库引擎和会话
        session_maker = get_session_maker()

        logger.info("使用现有数据库连接")
        
        # 1. 迁移settings表
        migrate_settings_table(engine)
        
        # 2. 创建f5_tts_voices表
        create_f5_tts_voices_table(engine)
        
        # 3. 创建示例音色（可选）
        create_sample_voice(session_maker)
        
        logger.info("🎉 F5-TTS数据库迁移完成！")
        
        # 验证迁移结果
        logger.info("\n📊 迁移结果验证:")
        inspector = inspect(engine)
        
        # 检查settings表字段
        settings_columns = [col['name'] for col in inspector.get_columns('settings')]
        if 'f5_tts_endpoint' in settings_columns:
            logger.info("✅ settings表包含f5_tts_endpoint字段")
        else:
            logger.error("❌ settings表缺少f5_tts_endpoint字段")
        
        # 检查f5_tts_voices表
        if 'f5_tts_voices' in inspector.get_table_names():
            logger.info("✅ f5_tts_voices表存在")
            f5_voices_columns = [col['name'] for col in inspector.get_columns('f5_tts_voices')]
            logger.info(f"   表字段: {', '.join(f5_voices_columns)}")
        else:
            logger.error("❌ f5_tts_voices表不存在")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库迁移失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 迁移成功！现在可以重新启动应用了。")
        sys.exit(0)
    else:
        print("\n❌ 迁移失败！请检查错误信息。")
        sys.exit(1)
