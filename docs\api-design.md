# Reddit故事视频生成器 - 后端API接口设计

## 🎯 总览

基于前端Zustand状态管理结构，设计对应的后端API接口，确保前后端数据结构一致，支持完整的功能实现。

## 📋 目录

- [基础信息](#基础信息)
- [认证和授权](#认证和授权)
- [设置管理API](#设置管理api)
- [资源管理API](#资源管理api)
- [视频生成API](#视频生成api)
- [系统管理API](#系统管理api)
- [WebSocket接口](#websocket接口)
- [文件上传接口](#文件上传接口)
- [错误处理](#错误处理)
- [数据模型](#数据模型)

## 🔧 基础信息

### 基础URL
```
开发环境: http://localhost:8000
生产环境: https://api.your-domain.com
```

### 版本控制
```
API版本: v1
路径前缀: /api/v1
```

### 统一响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  timestamp: string
  requestId: string
}
```

### 错误响应格式
```typescript
interface ApiError {
  success: false
  error: string
  message: string
  details?: any
  status: number
  timestamp: string
  requestId: string
}
```

## 🔐 认证和授权

### 认证方式
- JWT Token认证（后期扩展）
- 当前阶段：开放访问，无需认证

### Headers
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer <token> (可选)
```

---

## ⚙️ 设置管理API

### 1. 获取所有设置
```http
GET /api/v1/settings
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "tts": {
      "provider": "openai",
      "apiKey": "sk-****",
      "endpoint": "",
      "voice": "alloy",
      "model": "tts-1",
      "speed": 1.0,
      "pitch": 1.0,
      "volume": 1.0
    },
    "llm": {
      "provider": "openai",
      "apiKey": "sk-****",
      "endpoint": "",
      "model": "gpt-3.5-turbo",
      "temperature": 0.7,
      "maxTokens": 2000,
      "systemPrompt": "你是一个专业的故事改写助手..."
    },
    "general": {
      "theme": "light",
      "language": "zh-CN",
      "autoSave": true,
      "showTips": true,
      "outputDirectory": "/app/output"
    }
  },
  "timestamp": "2024-01-20T10:30:00Z",
  "requestId": "req_abc123"
}
```

### 2. 更新TTS设置
```http
PUT /api/v1/settings/tts
```

**请求体：**
```json
{
  "provider": "openai",
  "apiKey": "sk-****",
  "voice": "nova",
  "speed": 1.2
}
```

### 3. 更新LLM设置
```http
PUT /api/v1/settings/llm
```

### 4. 更新通用设置
```http
PUT /api/v1/settings/general
```

### 5. 测试TTS配置
```http
POST /api/v1/settings/tts/test
```

**请求体：**
```json
{
  "text": "这是一段测试文本",
  "config": {
    "provider": "openai",
    "apiKey": "sk-****",
    "voice": "alloy"
  }
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "audioUrl": "/api/v1/temp/audio/test_abc123.mp3",
    "duration": 3.5,
    "status": "success"
  }
}
```

### 6. 测试LLM配置
```http
POST /api/v1/settings/llm/test
```

### 7. 重置设置
```http
POST /api/v1/settings/reset
```

---

## 📁 资源管理API

### 背景音乐管理

#### 1. 获取背景音乐列表
```http
GET /api/v1/resources/music
```

**查询参数：**
- `category` (可选): 音乐分类
- `search` (可选): 搜索关键词
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认20

**响应示例：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "music-001",
        "name": "舒缓钢琴",
        "filePath": "/uploads/music/piano-calm.mp3",
        "fileUrl": "/api/v1/files/music/music-001",
        "duration": 180,
        "fileSize": 5242880,
        "category": "舒缓",
        "tags": ["钢琴", "舒缓", "背景"],
        "isBuiltIn": true,
        "uploadedAt": "2024-01-15T08:00:00Z",
        "metadata": {
          "bitrate": "128kbps",
          "format": "mp3",
          "channels": 2
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3
    },
    "stats": {
      "totalCount": 45,
      "totalSize": "125.6 MB",
      "categories": {
        "舒缓": 15,
        "动感": 20,
        "古典": 10
      }
    }
  }
}
```

#### 2. 上传背景音乐
```http
POST /api/v1/resources/music
Content-Type: multipart/form-data
```

**表单数据：**
- `file`: 音频文件
- `name`: 音乐名称
- `category`: 分类
- `tags`: 标签（JSON数组字符串）

#### 3. 获取单个背景音乐
```http
GET /api/v1/resources/music/{musicId}
```

#### 4. 更新背景音乐信息
```http
PUT /api/v1/resources/music/{musicId}
```

#### 5. 删除背景音乐
```http
DELETE /api/v1/resources/music/{musicId}
```

### 视频素材管理

#### 1. 获取视频素材列表
```http
GET /api/v1/resources/videos
```

#### 2. 上传视频素材
```http
POST /api/v1/resources/videos
Content-Type: multipart/form-data
```

#### 3. 批量上传
```http
POST /api/v1/resources/videos/batch
Content-Type: multipart/form-data
```

### 提示词管理

#### 1. 获取提示词列表
```http
GET /api/v1/resources/prompts
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "prompt-001",
        "name": "Reddit恐怖故事改写",
        "content": "请将以下Reddit恐怖故事改写为更适合视频内容的版本...",
        "category": "恐怖",
        "description": "专门用于改写Reddit恐怖故事的提示词",
        "variables": ["story_content", "target_length"],
        "usage": 15,
        "isBuiltIn": true,
        "createdAt": "2024-01-10T10:00:00Z",
        "lastUsed": "2024-01-19T15:30:00Z",
        "tags": ["恐怖", "Reddit", "改写"]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 32,
      "totalPages": 2
    }
  }
}
```

#### 2. 创建提示词
```http
POST /api/v1/resources/prompts
```

#### 3. 测试提示词
```http
POST /api/v1/resources/prompts/{promptId}/test
```

### 账号管理

#### 1. 获取账号列表
```http
GET /api/v1/resources/accounts
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "account-001",
        "name": "Reddit主账号",
        "type": "reddit",
        "username": "reddit_user_001",
        "email": "<EMAIL>",
        "status": "unused",
        "description": "用于Reddit故事抓取的主账号",
        "createdAt": "2024-01-15T10:00:00Z",
        "lastUsed": null,
        "usageCount": 0,
        "tags": ["主账号", "Reddit"],
        "metadata": {
          "karma": 1250,
          "accountAge": "2 years",
          "isVerified": true
        }
      }
    ],
    "stats": {
      "total": 8,
      "unused": 5,
      "used": 2,
      "disabled": 1,
      "byType": {
        "reddit": 4,
        "youtube": 2,
        "tiktok": 2
      }
    }
  }
}
```

#### 2. 创建账号
```http
POST /api/v1/resources/accounts
```

#### 3. 更新账号状态
```http
PUT /api/v1/resources/accounts/{accountId}/status
```

#### 4. 批量操作
```http
POST /api/v1/resources/accounts/batch
```

### 封面模板管理

#### 1. 获取模板列表
```http
GET /api/v1/resources/cover-templates
```

#### 2. 创建模板
```http
POST /api/v1/resources/cover-templates
```

#### 3. 预览模板
```http
POST /api/v1/resources/cover-templates/{templateId}/preview
```

---

## 🎬 视频生成API

### 1. 创建生成任务
```http
POST /api/v1/generation/tasks
```

**请求体：**
```json
{
  "config": {
    "selectedPrompt": "prompt-001",
    "selectedAccount": "account-001",
    "selectedBackgroundMusic": "music-001",
    "selectedVideoMaterials": ["video-001", "video-002"],
    "selectedCoverTemplate": "template-001",
    "videoDuration": 60,
    "videoResolution": "1080p",
    "frameRate": 30,
    "backgroundMusicVolume": 0.3,
    "voiceVolume": 1.0,
    "showSubtitles": true,
    "subtitleStyle": {
      "fontFamily": "Arial",
      "fontSize": 24,
      "color": "#FFFFFF",
      "backgroundColor": "rgba(0, 0, 0, 0.7)",
      "position": "bottom"
    }
  },
  "priority": "normal",
  "batchConfig": {
    "count": 5,
    "namePattern": "Reddit故事_{index}",
    "randomizeMaterials": true
  }
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "taskId": "task-********-001",
    "batchTaskIds": [
      "task-********-001-1",
      "task-********-001-2",
      "task-********-001-3",
      "task-********-001-4",
      "task-********-001-5"
    ],
    "estimatedDuration": 900,
    "status": "queued"
  }
}
```

### 2. 获取任务列表
```http
GET /api/v1/generation/tasks
```

**查询参数：**
- `status` (可选): pending, processing, completed, failed
- `page`, `limit`: 分页参数

**响应示例：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "task-********-001",
        "config": { /* 生成配置 */ },
        "status": "processing",
        "progress": 45,
        "currentStep": "生成音频中...",
        "startTime": "2024-01-20T10:30:00Z",
        "estimatedEndTime": "2024-01-20T10:45:00Z",
        "priority": "normal",
        "logs": [
          {
            "timestamp": "2024-01-20T10:30:05Z",
            "level": "info",
            "step": "story_fetch",
            "message": "成功获取Reddit故事内容"
          },
          {
            "timestamp": "2024-01-20T10:30:15Z",
            "level": "info", 
            "step": "story_rewrite",
            "message": "故事改写完成，字数: 485"
          }
        ],
        "result": null,
        "error": null
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    },
    "stats": {
      "pending": 5,
      "processing": 2,
      "completed": 15,
      "failed": 3,
      "totalTime": "4.5小时",
      "averageTime": "12分钟"
    }
  }
}
```

### 3. 获取单个任务详情
```http
GET /api/v1/generation/tasks/{taskId}
```

### 4. 取消任务
```http
POST /api/v1/generation/tasks/{taskId}/cancel
```

### 5. 重试失败任务
```http
POST /api/v1/generation/tasks/{taskId}/retry
```

### 6. 删除任务
```http
DELETE /api/v1/generation/tasks/{taskId}
```

### 7. 批量操作任务
```http
POST /api/v1/generation/tasks/batch
```

**请求体：**
```json
{
  "action": "cancel", // cancel, retry, delete
  "taskIds": ["task-001", "task-002", "task-003"]
}
```

### 8. 获取任务日志
```http
GET /api/v1/generation/tasks/{taskId}/logs
```

### 9. 下载生成结果
```http
GET /api/v1/generation/tasks/{taskId}/download
```

---

## 🎛️ 系统管理API

### 1. 系统健康检查
```http
GET /api/v1/system/health
```

**响应：**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "2天 5小时 30分钟",
    "services": {
      "database": "connected",
      "redis": "connected",
      "storage": "available",
      "ai_services": {
        "openai_tts": "available",
        "openai_llm": "available"
      }
    },
    "resources": {
      "cpu": 25.4,
      "memory": 68.2,
      "disk": 45.8,
      "gpu": 15.3
    },
    "queue": {
      "pending": 3,
      "processing": 1,
      "failed": 0
    }
  }
}
```

### 2. 系统统计
```http
GET /api/v1/system/stats
```

### 3. 清理临时文件
```http
POST /api/v1/system/cleanup
```

### 4. 获取系统日志
```http
GET /api/v1/system/logs
```

### 5. 数据库备份
```http
POST /api/v1/system/backup
```

---

## 🔌 WebSocket接口

### 连接地址
```
ws://localhost:8000/api/v1/ws
```

### 消息格式
```typescript
interface WebSocketMessage {
  type: 'task_update' | 'system_status' | 'error' | 'ping'
  data: any
  timestamp: string
  requestId?: string
}
```

### 任务状态更新
```json
{
  "type": "task_update",
  "data": {
    "taskId": "task-********-001",
    "status": "processing",
    "progress": 65,
    "currentStep": "合成视频中...",
    "logs": [
      {
        "timestamp": "2024-01-20T10:35:00Z",
        "level": "info",
        "step": "video_synthesis",
        "message": "开始合成视频，预计需要3分钟"
      }
    ]
  },
  "timestamp": "2024-01-20T10:35:00Z"
}
```

### 系统状态更新
```json
{
  "type": "system_status",
  "data": {
    "cpu": 45.2,
    "memory": 72.8,
    "activeConnections": 5,
    "queueSize": 2
  },
  "timestamp": "2024-01-20T10:35:00Z"
}
```

---

## 📤 文件上传接口

### 1. 单文件上传
```http
POST /api/v1/upload
Content-Type: multipart/form-data
```

**表单字段：**
- `file`: 文件
- `type`: 文件类型 (music, video, image, document)
- `category`: 分类 (可选)

**响应：**
```json
{
  "success": true,
  "data": {
    "fileId": "file-********-001",
    "fileName": "background-music.mp3",
    "fileSize": 5242880,
    "fileType": "audio/mpeg",
    "filePath": "/uploads/music/file-********-001.mp3",
    "fileUrl": "/api/v1/files/file-********-001",
    "thumbnailUrl": "/api/v1/files/file-********-001/thumbnail",
    "metadata": {
      "duration": 180,
      "bitrate": "128kbps",
      "format": "mp3"
    },
    "uploadedAt": "2024-01-20T10:30:00Z"
  }
}
```

### 2. 多文件上传
```http
POST /api/v1/upload/batch
Content-Type: multipart/form-data
```

### 3. 获取文件
```http
GET /api/v1/files/{fileId}
```

### 4. 获取文件缩略图
```http
GET /api/v1/files/{fileId}/thumbnail
```

### 5. 删除文件
```http
DELETE /api/v1/files/{fileId}
```

---

## ❌ 错误处理

### HTTP状态码

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 成功 | 正常请求响应 |
| 201 | 创建成功 | 创建资源 |
| 400 | 请求错误 | 参数验证失败 |
| 401 | 未授权 | Token无效 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 查询不存在的资源 |
| 409 | 冲突 | 资源已存在 |
| 422 | 验证失败 | 数据格式错误 |
| 429 | 请求过频繁 | 触发限流 |
| 500 | 服务器错误 | 内部错误 |
| 503 | 服务不可用 | 系统维护 |

### 错误响应示例

```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "请求参数验证失败",
  "details": {
    "field": "email",
    "code": "INVALID_FORMAT",
    "expected": "有效的邮箱地址"
  },
  "status": 422,
  "timestamp": "2024-01-20T10:30:00Z",
  "requestId": "req_abc123"
}
```

### 错误代码

| 错误代码 | 说明 |
|----------|------|
| VALIDATION_ERROR | 参数验证失败 |
| RESOURCE_NOT_FOUND | 资源不存在 |
| RESOURCE_CONFLICT | 资源冲突 |
| PERMISSION_DENIED | 权限不足 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 |
| SERVICE_UNAVAILABLE | 服务不可用 |
| EXTERNAL_API_ERROR | 外部API错误 |
| FILE_TOO_LARGE | 文件过大 |
| UNSUPPORTED_FORMAT | 不支持的格式 |
| STORAGE_FULL | 存储空间不足 |

---

## 📊 数据模型

### 设置模型
```typescript
interface Settings {
  id: string
  userId?: string
  tts: TTSConfig
  llm: LLMConfig
  general: GeneralSettings
  createdAt: string
  updatedAt: string
}
```

### 资源模型
```typescript
interface BackgroundMusic {
  id: string
  name: string
  fileName: string
  filePath: string
  fileUrl: string
  fileSize: number
  duration: number
  category: string
  tags: string[]
  isBuiltIn: boolean
  uploadedAt: string
  metadata: AudioMetadata
}

interface VideoMaterial {
  id: string
  name: string
  fileName: string
  filePath: string
  fileUrl: string
  thumbnailUrl: string
  fileSize: number
  duration: number
  resolution: string
  category: string
  tags: string[]
  isBuiltIn: boolean
  uploadedAt: string
  metadata: VideoMetadata
}

interface Prompt {
  id: string
  name: string
  content: string
  category: string
  description?: string
  variables: string[]
  usage: number
  isBuiltIn: boolean
  createdAt: string
  lastUsed?: string
  tags: string[]
}

interface Account {
  id: string
  name: string
  type: AccountType
  username: string
  email?: string
  password?: string
  description?: string
  status: AccountStatus
  createdAt: string
  lastUsed?: string
  usageCount: number
  tags: string[]
  metadata: AccountMetadata
}
```

### 任务模型
```typescript
interface GenerationTask {
  id: string
  config: GenerationConfig
  status: TaskStatus
  progress: number
  currentStep: string
  startTime?: string
  endTime?: string
  estimatedEndTime?: string
  priority: TaskPriority
  logs: TaskLog[]
  result?: TaskResult
  error?: string
  createdAt: string
  updatedAt: string
}

interface TaskLog {
  timestamp: string
  level: 'info' | 'warning' | 'error'
  step: string
  message: string
  details?: any
}

interface TaskResult {
  outputPath: string
  outputUrl: string
  thumbnailUrl: string
  fileSize: number
  duration: number
  metadata: VideoMetadata
}
```

---

## 🚀 实施计划

### 阶段1：核心API开发 (2-3天)
1. **基础框架**
   - FastAPI项目结构
   - 数据库模型定义
   - 统一响应格式
   - 错误处理机制

2. **设置管理API**
   - CRUD操作
   - 配置验证
   - 测试接口

3. **基础资源API**
   - 文件上传
   - 资源管理
   - 分页查询

### 阶段2：生成任务API (2-3天)
1. **任务管理**
   - 任务创建
   - 状态更新
   - 进度跟踪

2. **WebSocket集成**
   - 实时状态推送
   - 连接管理
   - 消息格式

### 阶段3：系统优化 (1-2天)
1. **性能优化**
   - 数据库优化
   - 缓存策略
   - 异步处理

2. **监控和日志**
   - 系统监控
   - 错误日志
   - 性能指标

---

## 📝 注意事项

1. **数据一致性**：确保前后端数据结构完全一致
2. **性能考虑**：大文件上传、并发处理、内存管理
3. **安全性**：文件类型验证、大小限制、路径安全
4. **扩展性**：支持插件式AI服务、模块化设计
5. **监控**：完善的日志记录和错误追踪

这个API设计完全基于前端的Zustand状态管理结构，确保前后端数据流的一致性和完整性。
