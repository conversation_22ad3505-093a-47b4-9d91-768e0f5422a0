"""
调试版本的main.py - 逐步测试导入和启动
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

print("=" * 50)
print("调试启动过程...")
print("=" * 50)

try:
    # 第1步：基本依赖
    print("1. 导入基本依赖...")
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    print("✓ 基本依赖导入成功")
    
    # 第2步：配置
    print("2. 导入配置...")
    from src.core.config import get_settings
    settings = get_settings()
    print(f"✓ 配置导入成功 - 环境: {settings.environment}")
    
    # 第3步：数据库
    print("3. 导入数据库模块...")
    from src.core.database import get_db
    print("✓ 数据库模块导入成功")
    
    # 第4步：路由（逐个测试）
    print("4. 导入路由模块...")
    
    print("  4.1 导入settings路由...")
    from src.api.settings import router as settings_router
    print("  ✓ settings路由导入成功")
    
    print("  4.2 导入music路由...")
    from src.api.music import router as music_router
    print("  ✓ music路由导入成功")
    
    print("  4.3 导入video路由...")
    from src.api.video import router as video_router
    print("  ✓ video路由导入成功")
    
    print("  4.4 导入其他路由...")
    from src.api.video_categories import router as video_categories_router
    from src.api.prompt import router as prompt_router
    from src.api.accounts import router as accounts_router
    from src.api.cover_template import router as cover_template_router
    print("  ✓ 其他路由导入成功")
    
    # 第5步：创建应用
    print("5. 创建FastAPI应用...")
    app = FastAPI(
        title="Reddit Story Video Generator API",
        description="Backend API for generating Reddit story videos with AI",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    print("✓ FastAPI应用创建成功")
    
    # 第6步：添加中间件
    print("6. 添加中间件...")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    print("✓ 中间件添加成功")
    
    # 第7步：添加路由
    print("7. 添加路由...")
    app.include_router(settings_router, prefix="/api/settings", tags=["settings"])
    app.include_router(music_router)  # prefix="/api/background-music" 
    app.include_router(video_router)  # prefix="/api/video-materials" 
    app.include_router(video_categories_router)  # prefix="/api/video-categories"
    app.include_router(prompt_router)  # prefix="/api/prompts" 
    app.include_router(accounts_router)  # prefix="/api/accounts" 
    app.include_router(cover_template_router)  # prefix="/api/cover-templates"
    print("✓ 路由添加成功")
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "reddit-story-generator-backend"}
    
    @app.get("/api/health")
    async def api_health():
        return {"status": "healthy", "service": "reddit-story-generator-api"}
    
    print("✓ 健康检查端点添加成功")
    
    # 第8步：启动服务器
    print("8. 启动服务器...")
    print("服务器地址: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")
    print("=" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )

except Exception as e:
    print(f"✗ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
