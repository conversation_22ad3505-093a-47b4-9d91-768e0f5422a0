#!/usr/bin/env python3
"""
封面模板API调试脚本
用于测试封面模板的创建、获取、更新等功能
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000/api/cover-templates"

def test_api():
    print("=== 封面模板API调试 ===\n")
    
    # 1. 测试获取模板列表
    print("1. 测试获取模板列表...")
    try:
        response = requests.get(BASE_URL)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据结构: {type(data)}")
            print(f"响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查数据结构
            if isinstance(data, dict) and 'data' in data:
                templates_data = data['data']
                if isinstance(templates_data, dict) and 'templates' in templates_data:
                    templates = templates_data['templates']
                    print(f"模板数量: {len(templates)}")
                    if templates:
                        print(f"第一个模板: {json.dumps(templates[0], indent=2, ensure_ascii=False)}")
                else:
                    print(f"data字段类型: {type(templates_data)}")
            else:
                print(f"响应数据不是预期格式: {data}")
        else:
            print(f"获取模板列表失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 2. 测试创建新模板
    print("2. 测试创建新模板...")
    new_template = {
        "name": "调试测试模板",
        "category": "测试",
        "description": "用于调试的测试模板",
        "variables": [],
        "elements": [
            {
                "id": "text1",
                "type": "text",
                "x": 100,
                "y": 100,
                "width": 200,
                "height": 50,
                "content": "测试文本",
                "fontSize": 24,
                "color": "#000000"
            }
        ],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        },
        "is_built_in": False,
        "width": 1920,
        "height": 1080,
        "format": "png"
    }
    
    try:
        response = requests.post(BASE_URL, json=new_template)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"创建成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 提取模板ID用于后续测试
            template_id = None
            if isinstance(data, dict) and 'data' in data:
                template_id = data['data'].get('id')
            elif isinstance(data, dict) and 'id' in data:
                template_id = data['id']
                
            if template_id:
                print(f"新创建的模板ID: {template_id}")
                
                # 3. 测试获取单个模板
                print(f"\n3. 测试获取单个模板 (ID: {template_id})...")
                get_response = requests.get(f"{BASE_URL}/{template_id}")
                print(f"状态码: {get_response.status_code}")
                if get_response.status_code == 200:
                    get_data = get_response.json()
                    print(f"获取成功: {json.dumps(get_data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"获取失败: {get_response.text}")
                    
                # 4. 测试更新模板
                print(f"\n4. 测试更新模板 (ID: {template_id})...")
                update_data = {
                    "name": "更新后的测试模板",
                    "elements": [
                        {
                            "id": "text1",
                            "type": "text",
                            "x": 150,
                            "y": 150,
                            "width": 250,
                            "height": 60,
                            "content": "更新后的文本",
                            "fontSize": 28,
                            "color": "#ff0000"
                        }
                    ],
                    "background": {
                        "type": "solid",
                        "value": "#ffffff"
                    }
                }
                
                update_response = requests.put(f"{BASE_URL}/{template_id}", json=update_data)
                print(f"状态码: {update_response.status_code}")
                if update_response.status_code == 200:
                    update_result = update_response.json()
                    print(f"更新成功: {json.dumps(update_result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"更新失败: {update_response.text}")
            else:
                print("未能获取模板ID")
        else:
            print(f"创建模板失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 5. 再次获取模板列表查看变化
    print("5. 再次获取模板列表查看变化...")
    try:
        response = requests.get(BASE_URL)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"最终模板列表: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"获取失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    try:
        test_api()
    except KeyboardInterrupt:
        print("\n测试中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        sys.exit(1)
