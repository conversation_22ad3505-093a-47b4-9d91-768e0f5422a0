"""
简单的封面生成测试
"""

import asyncio
import os
import sys
from pathlib import Path

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

from backend.src.services.cover_screenshot_service import cover_screenshot_service

async def simple_test():
    """简单测试HTML渲染"""
    
    print("=== 简单封面生成测试 ===")
    
    # 简单的HTML内容
    html_content = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试封面</title>
        <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            #reddit-cover { 
                width: 1920px; 
                height: 1080px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                color: white;
                text-align: center;
            }
            .title { font-size: 48px; font-weight: bold; margin-bottom: 20px; }
            .subtitle { font-size: 24px; opacity: 0.8; }
        </style>
    </head>
    <body>
        <div id="reddit-cover">
            <div class="title">这是一个测试标题</div>
            <div class="subtitle">测试账号 - 测试频道</div>
        </div>
    </body>
    </html>
    '''
    
    try:
        # 生成截图
        result = await cover_screenshot_service.generate_cover_screenshot(
            template_id="test",  # 使用测试模板ID
            account_id="test",   # 使用测试账号ID
            output_path="test_outputs/simple_cover.png",
            custom_variables={"title": "这是一个测试标题", "account_name": "测试账号"}
        )
        
        if result:
            print(f"✅ 成功生成封面: {result}")
            # 检查文件是否存在
            if Path(result).exists():
                file_size = Path(result).stat().st_size
                print(f"   文件大小: {file_size} 字节")
            else:
                print("❌ 文件未生成")
        else:
            print("❌ 生成失败")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_test())
