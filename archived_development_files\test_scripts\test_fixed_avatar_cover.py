#!/usr/bin/env python3
"""
测试修复后的封面头像显示效果
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

async def test_fixed_avatar_cover():
    """测试修复后的头像封面"""
    try:
        from src.core.database import get_db_session
        from src.models.accounts import Account
        from src.models.resources import CoverTemplate
        from src.services.cover_screenshot_service import cover_screenshot_service
        
        print("=== 测试修复后的封面头像显示 ===")
        
        db = get_db_session()
        
        # 获取第一个账号
        account = db.query(Account).first()
        if not account:
            print("未找到账号")
            return
        
        # 获取第一个模板
        template = db.query(CoverTemplate).first()
        if not template:
            print("未找到封面模板")
            return
        
        print(f"账号: {account.name}")
        print(f"模板: {template.name}")
        
        # 生成修复后的封面
        output_path = "fixed_avatar_cover.png"
        title = "测试修复后的头像显示效果"
        
        print(f"\n生成修复后的封面...")
        print(f"输出路径: {output_path}")
        print(f"标题: {title}")
        
        success = await cover_screenshot_service.generate_cover_screenshot(
            template_id=template.id,
            account=account,
            title=title,
            output_path=output_path,
            db=db
        )
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"\n✅ 修复后封面生成成功！")
            print(f"封面文件: {output_path}")
            print(f"文件大小: {file_size} bytes")
            print(f"\n请查看生成的图片文件，确认头像现在是否正确显示为圆形头像")
        else:
            print(f"\n❌ 封面生成失败")
        
        db.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_fixed_avatar_cover())
