#!/usr/bin/env python3
"""
检查cover_templates表结构，并添加缺失的字段
"""

from src.models.resources import CoverTemplate
from src.core.database import engine
from sqlalchemy import text

def check_and_update_table():
    """检查并更新表结构"""
    with engine.connect() as conn:
        try:
            # 检查当前表结构
            result = conn.execute(text('PRAGMA table_info(cover_templates)'))
            columns = [row[1] for row in result.fetchall()]
            print('Current columns:', columns)
            
            # 检查是否需要添加新字段
            need_migration = False
            
            if 'elements' not in columns:
                print('Need to add elements column')
                conn.execute(text('ALTER TABLE cover_templates ADD COLUMN elements JSON DEFAULT "[]"'))
                need_migration = True
                
            if 'background' not in columns:
                print('Need to add background column')
                conn.execute(text('ALTER TABLE cover_templates ADD COLUMN background JSON'))
                need_migration = True
            
            if need_migration:
                conn.commit()
                print('Table structure updated successfully')
            else:
                print('Table structure is up to date')
                
        except Exception as e:
            print(f'Error checking/updating table: {e}')

if __name__ == '__main__':
    check_and_update_table()
