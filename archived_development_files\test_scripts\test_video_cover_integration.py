"""
测试视频生成中封面截图功能的集成
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

# 加载环境变量
load_dotenv(dotenv_path=backend_path / '.env')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate
from backend.src.services.video_generation_helpers import VideoGenerationServiceHelpers

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_video_cover_integration():
    """测试视频生成中的封面生成功能"""
    db = SessionLocal()
    try:
        print("=== 视频封面生成集成测试 ===")
        
        # 获取测试资源
        account = db.query(Account).first()
        template = db.query(CoverTemplate).first()
        
        if not account or not template:
            print("❌ 缺少必要的测试资源")
            return
        
        print(f"✅ 使用账号: {account.name}")
        print(f"✅ 使用模板: {template.name}")
        
        # 构建测试任务数据
        class MockTask:
            def __init__(self, account_id):
                self.id = "test-task-123"
                self.account_id = account_id
                self.generated_story = "这是一个测试故事，用来验证封面截图功能在视频生成流程中的集成效果。故事内容应该足够长以便测试标题提取功能。"
                self.first_sentence = "这是一个测试故事，用来验证封面截图功能。"
        
        task = MockTask(account.id)
        
        # 构建作业配置
        job_config = {
            "cover_template_id": template.id,
            "subtitle_settings": {"font": "Arial", "size": 24, "color": "#ffffff"}
        }
        
        print(f"🔄 开始生成封面...")
        
        # 创建服务实例
        helpers = VideoGenerationServiceHelpers(SessionLocal)
        
        # 调用封面生成函数
        cover_path = await helpers._generate_cover(
            task=task,
            job_config=job_config
        )
        
        if cover_path and Path(cover_path).exists():
            file_size = Path(cover_path).stat().st_size
            print(f"✅ 封面生成成功!")
            print(f"   输出路径: {cover_path}")
            print(f"   文件大小: {file_size} 字节")
            
            # 检查是否是网页截图生成的（路径包含特定标识）
            if "screenshot" in str(cover_path) or cover_path.endswith(".png"):
                print("✅ 使用了网页截图方式生成封面")
            else:
                print("ℹ️  使用了传统PIL方式生成封面")
                
        else:
            print("❌ 封面生成失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_video_cover_integration())
