#!/usr/bin/env python3
"""
最终验证：测试封面头像修复效果
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

async def final_avatar_test():
    """最终的头像修复验证"""
    try:
        from src.core.database import get_db_session
        from src.models.accounts import Account
        from src.models.resources import CoverTemplate
        from src.services.cover_screenshot_service import cover_screenshot_service
        
        print("=== 最终头像修复验证 ===")
        
        db = get_db_session()
        
        # 获取账号和模板
        account = db.query(Account).first()
        template = db.query(CoverTemplate).first()
        
        if not account or not template:
            print("缺少必要的数据")
            return
        
        print(f"测试账号: {account.name}")
        print(f"使用模板: {template.name}")
        
        # 检查头像文件
        if account.avatar_file_path:
            backend_dir = Path(__file__).parent / 'backend'
            avatar_path = backend_dir / account.avatar_file_path
            print(f"头像文件: {avatar_path}")
            print(f"文件存在: {avatar_path.exists()}")
            if avatar_path.exists():
                print(f"文件大小: {os.path.getsize(avatar_path)} bytes")
        
        # 生成最终测试封面
        output_path = "FINAL_AVATAR_TEST.png"
        title = "最终头像修复验证 - 应该显示正确的圆形头像"
        
        print(f"\n🎯 生成最终测试封面...")
        print(f"标题: {title}")
        print(f"输出: {output_path}")
        
        success = await cover_screenshot_service.generate_cover_screenshot(
            template_id=template.id,
            account=account,
            title=title,
            output_path=output_path,
            db=db
        )
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"\n✅ 最终测试封面生成成功！")
            print(f"📁 文件: {output_path}")
            print(f"📊 大小: {file_size} bytes")
            
            print(f"\n🔍 请检查生成的封面图片：")
            print(f"   - 头像应该是圆形的")
            print(f"   - 头像应该显示账号的真实头像图片")
            print(f"   - 头像不应该被渐变背景或其他元素遮挡")
            print(f"   - 头像应该清晰且居中显示")
            
            # 与之前的文件大小对比
            if os.path.exists("test_cover_with_avatar.png"):
                old_size = os.path.getsize("test_cover_with_avatar.png")
                print(f"\n📈 文件大小对比:")
                print(f"   修复前: {old_size} bytes")
                print(f"   修复后: {file_size} bytes")
                if file_size < old_size:
                    print(f"   ✅ 文件大小减少 {old_size - file_size} bytes (优化成功)")
            
        else:
            print(f"\n❌ 最终测试失败")
        
        db.close()
        
        print(f"\n🎉 头像修复验证完成！")
        print(f"📋 修复摘要:")
        print(f"   1. ✅ 修复了模板CSS中的头像样式冲突")
        print(f"   2. ✅ 移除了渐变背景和抽象图标")
        print(f"   3. ✅ 添加了专门的img标签样式")
        print(f"   4. ✅ 确保头像以Base64格式正确嵌入")
        print(f"   5. ✅ 生成了测试封面进行验证")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_avatar_test())
