/**
 * 视频素材状态管理
 * 管理视频素材文件的上传、存储、分类等功能
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
// 使用直接API客户端，不使用fetch
import { directVideoMaterialApi, VideoMaterialAPI, VideoMaterialCreateRequest } from '@/lib/api/directVideoMaterials'
import { directVideoCategoriesAPI, VideoCategory } from '@/lib/api/directVideoCategories'
// 引入全局loading状态管理
import { useGlobalLoadingStore } from './globalLoadingStore'

export interface VideoMaterial {
  id: string
  name: string
  type: 'video' | 'image' | 'gif'
  format: string
  size: string
  duration?: string // 视频/GIF 才有时长
  dimensions: {
    width: number
    height: number
  }
  aspectRatio: string
  path: string
  thumbnailPath?: string
  file?: File
  url?: string
  thumbnailUrl?: string
  tags: string[]
  category?: string
  createdAt: string
  lastModified: string
}

interface VideoMaterialState {
  // 状态
  materials: VideoMaterial[]
  isLoading: boolean
  error: string | null
  currentView: 'grid' | 'list'
  uploadProgress: number
  // 分类管理状态
  categories: string[]
  isCategoriesLoading: boolean

  // 操作方法 - 本地状态管理
  addMaterial: (material: VideoMaterial) => void
  addMaterials: (materials: VideoMaterial[]) => void
  removeMaterial: (id: string) => void
  updateMaterial: (id: string, updates: Partial<VideoMaterial>) => void
  clearAllMaterials: () => void
  
  // API操作方法 - 与后端交互
  loadMaterials: (params?: { category?: string; search?: string; skip?: number; limit?: number }) => Promise<void>
  createMaterial: (material: VideoMaterial, file: File) => Promise<void>
  updateMaterialOnServer: (id: string, updates: { name?: string; category?: string; tags?: string[] }) => Promise<void>
  deleteMaterialFromServer: (id: string) => Promise<void>
  uploadFile: (file: File, category?: string, onProgress?: (progress: number) => void) => Promise<VideoMaterial>
  bulkUploadFiles: (files: File[], category?: string) => Promise<{ success: VideoMaterial[]; failed: Array<{ name: string; error: string }> }>
  
  // 分类管理方法
  loadCategories: () => Promise<void>
  createCategory: (name: string, description?: string) => Promise<VideoCategory>
  updateCategory: (id: string, name?: string, description?: string) => Promise<VideoCategory>
  deleteCategory: (id: string) => Promise<void>
  
  // 查询方法
  getMaterialById: (id: string) => VideoMaterial | undefined
  getMaterialsByType: (type: VideoMaterial['type']) => VideoMaterial[]
  getMaterialsByCategory: (category: string) => VideoMaterial[]
  getTotalCount: () => number
  getTotalSize: () => string
  getTypeStats: () => Record<string, number>
  getCategoryStats: () => Record<string, number>
  
  // UI状态
  setView: (view: 'grid' | 'list') => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setUploadProgress: (progress: number) => void
}

// 格式化文件大小的工具函数
const formatFileSize = (totalBytes: number): string => {
  if (totalBytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(totalBytes) / Math.log(k))
  return parseFloat((totalBytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 将后端API数据转换为前端VideoMaterial格式
const convertApiToVideoMaterial = (apiData: VideoMaterialAPI): VideoMaterial => {
  // 后端已经返回前端格式，只需要少量转换
  return {
    id: apiData.id,
    name: apiData.name,
    type: apiData.type as 'video' | 'image' | 'gif',
    format: apiData.format,
    size: apiData.size,
    duration: apiData.duration,
    dimensions: apiData.dimensions,
    aspectRatio: apiData.aspectRatio,
    path: apiData.path,
    url: apiData.url,
    thumbnailPath: apiData.thumbnailPath || undefined,
    thumbnailUrl: apiData.thumbnailUrl || undefined,
    tags: apiData.tags,
    category: apiData.category,
    createdAt: apiData.createdAt || new Date().toISOString(),
    lastModified: apiData.lastModified || new Date().toISOString(),
    file: undefined
  }
}

// 将前端VideoMaterial转换为后端API创建请求格式
const convertVideoMaterialToApiCreate = (material: VideoMaterial, filePath: string): VideoMaterialCreateRequest => {
  // 解析时长字符串转换为秒
  let durationInSeconds = 0
  if (material.duration) {
    const [minutes, seconds] = material.duration.split(':').map(Number)
    durationInSeconds = minutes * 60 + seconds
  }

  return {
    name: material.name,
    file_path: filePath,
    duration: durationInSeconds,
    resolution: `${material.dimensions.width}x${material.dimensions.height}`,
    category: material.category || 'general',
    tags: material.tags,
    is_built_in: false,
    file_size: parseFileSizeToBytes(material.size),
    format: material.format,
    frame_rate: undefined, // 会在上传时从文件解析
    bitrate: undefined,    // 会在上传时从文件解析
    thumbnail_path: material.thumbnailPath
  }
}

// 解析文件大小字符串为字节数
const parseFileSizeToBytes = (sizeStr: string): number => {
  const match = sizeStr.match(/^([\d.]+)\s*([KMGT]?)B?$/i)
  if (!match) return 0
  
  const value = parseFloat(match[1])
  const unit = match[2].toUpperCase()
  
  const multipliers = { '': 1, 'K': 1024, 'M': 1024 ** 2, 'G': 1024 ** 3, 'T': 1024 ** 4 }
  const multiplier = multipliers[unit as keyof typeof multipliers] || 1
  
  return Math.round(value * multiplier)
}

// 计算总文件大小（字节）
const calculateTotalBytes = (materials: VideoMaterial[]): number => {
  return materials.reduce((total, material) => {
    // 从size字符串中提取数字和单位
    const sizeStr = material.size
    const match = sizeStr.match(/^([\d.]+)\s*([KMGT]?)B?$/i)
    if (!match) return total
    
    const value = parseFloat(match[1])
    const unit = match[2].toUpperCase()
    
    const multipliers = { '': 1, 'K': 1024, 'M': 1024 ** 2, 'G': 1024 ** 3, 'T': 1024 ** 4 }
    const multiplier = multipliers[unit as keyof typeof multipliers] || 1
    
    return total + (value * multiplier)
  }, 0)
}

// 计算宽高比
const calculateAspectRatio = (width: number, height: number): string => {
  const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
  const divisor = gcd(width, height)
  return `${width/divisor}:${height/divisor}`
}

export const useVideoMaterialStore = create<VideoMaterialState>()(
  persist(
    (set, get) => ({
      // 初始状态
      materials: [],
      isLoading: false,
      error: null,
      currentView: 'grid',
      uploadProgress: 0,
      categories: [],
      isCategoriesLoading: false,

      // 添加单个素材
      addMaterial: (material: VideoMaterial) => {
        set((state) => ({
          materials: [...state.materials, material],
          error: null
        }))
      },

      // 批量添加素材
      addMaterials: (materials: VideoMaterial[]) => {
        set((state) => ({
          materials: [...state.materials, ...materials],
          error: null
        }))
      },

      // 删除素材
      removeMaterial: (id: string) => {
        set((state) => {
          const materialToRemove = state.materials.find(m => m.id === id)
          if (materialToRemove?.url) {
            // 释放URL对象
            URL.revokeObjectURL(materialToRemove.url)
          }
          if (materialToRemove?.thumbnailUrl) {
            URL.revokeObjectURL(materialToRemove.thumbnailUrl)
          }
          return {
            materials: state.materials.filter(material => material.id !== id),
            error: null
          }
        })
      },

      // 更新素材信息
      updateMaterial: (id: string, updates: Partial<VideoMaterial>) => {
        set((state) => ({
          materials: state.materials.map(material =>
            material.id === id 
              ? { ...material, ...updates, lastModified: new Date().toISOString() } 
              : material
          ),
          error: null
        }))
      },

      // 清空所有素材
      clearAllMaterials: () => {
        const { materials } = get()
        // 释放所有URL对象
        materials.forEach(material => {
          if (material.url) {
            URL.revokeObjectURL(material.url)
          }
          if (material.thumbnailUrl) {
            URL.revokeObjectURL(material.thumbnailUrl)
          }
        })
        set({
          materials: [],
          error: null
        })
      },

      // 从服务器加载素材
      loadMaterials: async (params) => {
        set({ isLoading: true, error: null })
        console.log('🟢 Store.loadMaterials 被调用，参数:', params)
        
        try {
          const apiMaterials = await directVideoMaterialApi.getVideoMaterials(params)
          console.log('🟢 从API获取的素材:', apiMaterials)
          
          const materials = apiMaterials.map(convertApiToVideoMaterial)
          console.log('🟢 转换后的素材:', materials)
          
          set({ materials, isLoading: false })
        } catch (error) {
          console.error('🔴 加载素材失败:', error)
          set({ 
            error: error instanceof Error ? error.message : '加载素材失败',
            isLoading: false 
          })
        }
      },

      // 创建素材到服务器
      createMaterial: async (material: VideoMaterial, file: File) => {
        set({ isLoading: true, error: null })
        
        // 启动全局loading，根据文件大小预估时长
        const fileSizeInMB = file.size / (1024 * 1024)
        const estimatedDuration = Math.max(8, Math.min(fileSizeInMB * 2.5, 45)) // 8-45秒之间
        
        const globalLoading = useGlobalLoadingStore.getState()
        const taskId = globalLoading.startGlobalLoading({
          type: 'upload',
          title: '创建视频素材',
          description: `正在创建素材: ${material.name}`,
          progress: 0,
          estimatedDuration: estimatedDuration,
          simulateProgress: true
        })
        
        try {
          // 首先上传文件
          globalLoading.updateTaskProgress(taskId, 10, '正在上传文件...')
          const uploadResult = await directVideoMaterialApi.uploadVideoFile(file, material.category || 'general', '', (progress: number) => {
            set({ uploadProgress: progress })
            // 上传进度占总进度的70%
            const totalProgress = 10 + (progress * 0.7)
            globalLoading.updateTaskProgress(taskId, totalProgress, `正在上传文件... (${progress}%)`)
          })
          
          // 然后创建素材记录
          globalLoading.updateTaskProgress(taskId, 85, '正在创建素材记录...')
          const createRequest = convertVideoMaterialToApiCreate(material, uploadResult.filePath)
          const apiMaterial = await directVideoMaterialApi.createVideoMaterial({
            ...createRequest,
            file_size: uploadResult.fileSize,
            format: uploadResult.format,
            frame_rate: uploadResult.frameRate,
            bitrate: uploadResult.bitrate,
            thumbnail_path: uploadResult.thumbnailPath
          })
          
          const newMaterial = convertApiToVideoMaterial(apiMaterial)
          
          // 完成任务
          globalLoading.updateTaskProgress(taskId, 100, '素材创建完成!')
          setTimeout(() => {
            globalLoading.finishTask(taskId)
          }, 1500) // 延迟1.5秒让用户看到完成状态
          
          set((state) => ({
            materials: [...state.materials, newMaterial],
            isLoading: false,
            uploadProgress: 0
          }))
        } catch (error) {
          // 完成任务（即使失败也要清除loading）
          globalLoading.finishTask(taskId)
          set({ 
            error: error instanceof Error ? error.message : '创建素材失败',
            isLoading: false,
            uploadProgress: 0
          })
        }
      },

      // 更新服务器上的素材
      updateMaterialOnServer: async (id: string, updates) => {
        set({ isLoading: true, error: null })
        try {
          const apiMaterial = await directVideoMaterialApi.updateVideoMaterial(id, updates)
          const updatedMaterial = convertApiToVideoMaterial(apiMaterial)
          
          set((state) => ({
            materials: state.materials.map(material =>
              material.id === id ? updatedMaterial : material
            ),
            isLoading: false
          }))
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '更新素材失败',
            isLoading: false 
          })
        }
      },

      // 从服务器删除素材
      deleteMaterialFromServer: async (id: string) => {
        set({ isLoading: true, error: null })
        try {
          await directVideoMaterialApi.deleteVideoMaterial(id)
          // 同时从本地状态中删除
          get().removeMaterial(id)
          set({ isLoading: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '删除素材失败',
            isLoading: false 
          })
        }
      },

      // 上传单个文件
      uploadFile: async (file: File, category: string = 'general', onProgress?: (progress: number) => void) => {
        set({ error: null })
        console.log('🟢 Store.uploadFile 被调用:', {
          fileName: file.name,
          category: category,
          fileSize: file.size
        })
        
        // 启动全局loading，根据文件大小预估时长
        const fileSizeInMB = file.size / (1024 * 1024)
        const estimatedDuration = Math.max(5, Math.min(fileSizeInMB * 2, 30)) // 5-30秒之间
        
        const globalLoading = useGlobalLoadingStore.getState()
        const taskId = globalLoading.startGlobalLoading({
          type: 'upload',
          title: '上传视频素材',
          description: `正在上传文件: ${file.name}`,
          progress: 0,
          estimatedDuration: estimatedDuration,
          simulateProgress: true
        })
        
        try {
          const uploadResult = await directVideoMaterialApi.uploadVideoFile(file, category, '', (progress) => {
            set({ uploadProgress: progress })
            // 更新全局loading进度（如果有真实进度，覆盖模拟进度）
            if (progress > 0) {
              globalLoading.updateTaskProgress(taskId, progress, `正在上传文件: ${file.name} (${progress}%)`)
            }
            onProgress?.(progress)
          })
          
          console.log('🟢 上传API返回结果:', uploadResult)
          
          // 上传成功后，直接返回一个简单的material对象
          // 后续会通过 loadMaterials() 重新从后端获取完整数据
          const material: VideoMaterial = {
            id: uploadResult.id || '', // 从后端上传API获取的ID
            name: file.name,
            type: 'video', // 默认为视频类型，后续从后端获取准确类型
            format: uploadResult.format || 'MP4',
            size: formatFileSize(uploadResult.fileSize),
            duration: uploadResult.duration ? `${Math.floor(uploadResult.duration / 60)}:${Math.floor(uploadResult.duration % 60).toString().padStart(2, '0')}` : undefined,
            dimensions: { width: uploadResult.width, height: uploadResult.height },
            aspectRatio: calculateAspectRatio(uploadResult.width, uploadResult.height),
            path: uploadResult.filePath,
            thumbnailPath: uploadResult.thumbnailPath,
            tags: [],
            category: category,
            createdAt: new Date().toISOString(),
            lastModified: new Date().toISOString()
          }
          
          console.log('🟢 构建的material对象:', material)
          
          // 完成任务，设置为100%
          globalLoading.updateTaskProgress(taskId, 100, '上传完成!')
          setTimeout(() => {
            globalLoading.finishTask(taskId)
          }, 1000) // 延迟1秒让用户看到完成状态
          
          set({ uploadProgress: 0 })
          return material
        } catch (error) {
          console.error('🔴 上传文件失败:', error)
          // 完成任务（即使失败也要清除loading）
          globalLoading.finishTask(taskId)
          set({ 
            error: error instanceof Error ? error.message : '文件上传失败',
            uploadProgress: 0
          })
          throw error
        }
      },

      // 批量上传文件
      bulkUploadFiles: async (files: File[], category: string = 'general') => {
        set({ isLoading: true, error: null })
        console.log('🟢 Store.bulkUploadFiles 被调用:', {
          fileCount: files.length,
          fileNames: files.map(f => f.name),
          category: category
        })
        
        // 启动全局loading，根据文件数量和大小预估时长
        const totalSizeInMB = files.reduce((sum, file) => sum + file.size, 0) / (1024 * 1024)
        const estimatedDuration = Math.max(10, Math.min(totalSizeInMB * 2 + files.length * 3, 120)) // 10-120秒之间
        
        const globalLoading = useGlobalLoadingStore.getState()
        const taskId = globalLoading.startGlobalLoading({
          type: 'upload',
          title: '批量上传视频素材',
          description: `正在批量上传 ${files.length} 个文件`,
          progress: 0,
          estimatedDuration: estimatedDuration,
          simulateProgress: true
        })
        
        try {
          // 更新进度为开始状态
          globalLoading.updateTaskProgress(taskId, 5, '正在准备上传文件...')
          
          // 使用后端的批量上传API
          const result = await directVideoMaterialApi.bulkUploadVideoFiles(Array.from(files), category)
          
          console.log('🟢 批量上传API返回结果:', result)
          
          // 更新进度为处理结果
          globalLoading.updateTaskProgress(taskId, 95, '正在处理上传结果...')
          
          // 转换成功上传的材料为前端格式
          const successMaterials: VideoMaterial[] = result.success.map((apiMaterial: VideoMaterialAPI) => convertApiToVideoMaterial(apiMaterial))
          
          console.log('🟢 转换后的成功素材:', successMaterials)
          
          // 完成任务
          globalLoading.updateTaskProgress(taskId, 100, `批量上传完成! 成功: ${result.success.length}, 失败: ${result.failed.length}`)
          setTimeout(() => {
            globalLoading.finishTask(taskId)
          }, 2000) // 延迟2秒让用户看到完成状态
          
          set({ isLoading: false, uploadProgress: 0 })
          return { 
            success: successMaterials, 
            failed: result.failed 
          }
        } catch (error) {
          console.error('🔴 批量上传失败:', error)
          // 完成任务（即使失败也要清除loading）
          globalLoading.finishTask(taskId)
          set({ 
            error: error instanceof Error ? error.message : '批量上传失败',
            isLoading: false,
            uploadProgress: 0
          })
          return { 
            success: [], 
            failed: Array.from(files).map(file => ({
              name: file.name,
              error: error instanceof Error ? error.message : '上传失败'
            }))
          }
        }
      },

      // 分类管理方法
      loadCategories: async () => {
        set({ isCategoriesLoading: true, error: null })
        try {
          const response = await directVideoCategoriesAPI.getCategories()
          const categories = response.data?.map((cat: VideoCategory) => cat.name) || []
          set({ 
            categories,
            isCategoriesLoading: false 
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '加载分类列表失败',
            isCategoriesLoading: false
          })
        }
      },

      // 分类管理的CRUD方法
      createCategory: async (name: string, description?: string) => {
        try {
          const response = await directVideoCategoriesAPI.createCategory({ name, description })
          // 重新加载分类列表
          await get().loadCategories()
          return response.data!
        } catch (error) {
          set({ error: error instanceof Error ? error.message : '创建分类失败' })
          throw error
        }
      },

      updateCategory: async (id: string, name?: string, description?: string) => {
        try {
          const response = await directVideoCategoriesAPI.updateCategory(id, { name, description })
          // 重新加载分类列表
          await get().loadCategories()
          return response.data!
        } catch (error) {
          set({ error: error instanceof Error ? error.message : '更新分类失败' })
          throw error
        }
      },

      deleteCategory: async (id: string) => {
        try {
          await directVideoCategoriesAPI.deleteCategory(id)
          // 重新加载分类列表
          await get().loadCategories()
        } catch (error) {
          set({ error: error instanceof Error ? error.message : '删除分类失败' })
          throw error
        }
      },

      // 根据ID获取素材
      getMaterialById: (id: string) => {
        return get().materials.find(material => material.id === id)
      },

      // 根据类型获取素材
      getMaterialsByType: (type: VideoMaterial['type']) => {
        return get().materials.filter(material => material.type === type)
      },

      // 根据分类获取素材
      getMaterialsByCategory: (category: string) => {
        return get().materials.filter(material => material.category === category)
      },

      // 获取素材总数
      getTotalCount: () => {
        return get().materials.length
      },

      // 获取总文件大小（格式化字符串）
      getTotalSize: () => {
        const { materials } = get()
        const totalBytes = calculateTotalBytes(materials)
        return formatFileSize(totalBytes)
      },

      // 获取类型统计
      getTypeStats: () => {
        const { materials } = get()
        return materials.reduce((stats, material) => {
          const type = material.type
          stats[type] = (stats[type] || 0) + 1
          return stats
        }, {} as Record<string, number>)
      },

      // 获取分类统计 - 只统计视频文件
      getCategoryStats: () => {
        const { materials } = get()
        return materials
          .filter(material => material.type === 'video') // 只统计视频文件
          .reduce((stats, material) => {
            const category = material.category || 'general'
            stats[category] = (stats[category] || 0) + 1
            return stats
          }, {} as Record<string, number>)
      },

      // 设置视图模式
      setView: (view: 'grid' | 'list') => {
        set({ currentView: view })
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error })
      },

      // 设置上传进度
      setUploadProgress: (progress: number) => {
        set({ uploadProgress: progress })
      },
    }),
    {
      name: 'video-materials-storage',
      storage: createJSONStorage(() => localStorage),
      // 过滤掉不需要持久化的字段
      partialize: (state) => ({
        materials: state.materials.map(material => ({
          ...material,
          // 不持久化File对象和URL，重新打开时需要重新上传
          file: undefined,
          url: undefined,
          thumbnailUrl: undefined
        })),
        currentView: state.currentView,
        categories: state.categories
      }),
    }
  )
)

// 导出工具函数
export { calculateAspectRatio, formatFileSize }

// 导出类型
export type { VideoMaterialState }
