"""
测试修复后的模板创建功能
"""

import requests
import json

def test_template_creation():
    """测试模板创建功能"""
    url = "http://localhost:8000/api/cover-templates"
    
    # 使用与前端相同的数据格式
    template_data = {
        "name": "修复测试模板",
        "category": "测试",
        "description": "测试响应解析修复",
        "variables": [],
        "elements": [],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        },
        "is_built_in": False,
        "width": 1920,
        "height": 1080,
        "format": "png"
    }
    
    print("🧪 测试模板创建API响应格式")
    print("=" * 50)
    
    try:
        response = requests.post(url, json=template_data, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 请求成功!")
            print(f"📋 完整响应结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 验证响应结构
            if 'data' in data and data['data']:
                template_data = data['data']
                print(f"\n🎯 模板数据结构:")
                print(f"   ID: {template_data.get('id', 'Not found')}")
                print(f"   名称: {template_data.get('name', 'Not found')}")
                print(f"   分类: {template_data.get('category', 'Not found')}")
                
                # 验证前端需要的字段
                if 'id' in template_data and template_data['id']:
                    print(f"✅ ID字段存在且有值: {template_data['id']}")
                else:
                    print("❌ ID字段缺失或为空")
                    
                if 'name' in template_data:
                    print(f"✅ name字段存在: {template_data['name']}")
                else:
                    print("❌ name字段缺失")
                    
                if 'category' in template_data:
                    print(f"✅ category字段存在: {template_data['category']}")
                else:
                    print("❌ category字段缺失")
                    
                print(f"\n💡 前端解析测试:")
                print(f"   savedTemplate.data.id: {template_data.get('id', 'undefined')}")
                print(f"   savedTemplate.data.name: {template_data.get('name', 'undefined')}")
                print(f"   savedTemplate.data.category: {template_data.get('category', 'undefined')}")
                
            else:
                print("❌ 响应中缺少data字段或data为空")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_template_creation()
