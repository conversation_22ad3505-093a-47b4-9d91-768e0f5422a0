"""
FastAPI异步执行流程演示
展示await如何让出控制权，主线程如何调度多个请求
"""

import asyncio
import time
from datetime import datetime
from fastapi import FastAPI
import uvicorn
from loguru import logger

app = FastAPI()

def log_with_time(message: str, request_id: str = ""):
    """带时间戳的日志输出"""
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    thread_info = f"线程ID: {id(asyncio.current_task())}"
    logger.info(f"[{timestamp}] {request_id} {message} | {thread_info}")

@app.get("/quick")
async def quick_request():
    """快速请求，无异步等待"""
    request_id = f"QUICK-{int(time.time() * 1000) % 10000}"
    log_with_time("🚀 快速请求开始处理", request_id)
    
    # 模拟一些同步处理
    for i in range(3):
        log_with_time(f"  执行步骤 {i+1}", request_id)
        # 注意：这里没有await，不会让出控制权
    
    log_with_time("✅ 快速请求完成", request_id)
    return {"message": "快速完成", "request_id": request_id}

@app.get("/slow")
async def slow_request():
    """慢请求，包含异步等待"""
    request_id = f"SLOW-{int(time.time() * 1000) % 10000}"
    log_with_time("🐌 慢请求开始处理", request_id)
    
    # 步骤1：同步处理
    log_with_time("  步骤1: 同步处理开始", request_id)
    
    # 步骤2：异步等待（这里会让出控制权）
    log_with_time("  步骤2: 开始异步等待（让出控制权）", request_id)
    await asyncio.sleep(3)  # 模拟异步IO操作，比如数据库查询、文件IO等
    log_with_time("  步骤2: 异步等待完成（重新获得控制权）", request_id)
    
    # 步骤3：继续同步处理
    log_with_time("  步骤3: 继续同步处理", request_id)
    
    log_with_time("✅ 慢请求完成", request_id)
    return {"message": "慢请求完成", "request_id": request_id}

@app.get("/video_simulation")
async def video_generation_simulation():
    """模拟视频生成过程中的异步操作"""
    request_id = f"VIDEO-{int(time.time() * 1000) % 10000}"
    log_with_time("🎬 视频生成请求开始", request_id)
    
    # 1. 生成文案（快速）
    log_with_time("  1. 生成文案", request_id)
    
    # 2. TTS语音生成（异步）
    log_with_time("  2. 开始TTS语音生成（让出控制权）", request_id)
    await asyncio.sleep(2)  # 模拟TTS异步处理
    log_with_time("  2. TTS语音生成完成（重新获得控制权）", request_id)
    
    # 3. Whisper音频分析（异步）
    log_with_time("  3. 开始Whisper音频分析（让出控制权）", request_id)
    await asyncio.sleep(1.5)  # 模拟Whisper异步处理
    log_with_time("  3. Whisper音频分析完成（重新获得控制权）", request_id)
    
    # 4. 封面截图（异步）
    log_with_time("  4. 开始封面截图（让出控制权）", request_id)
    await asyncio.sleep(1)  # 模拟Playwright异步截图
    log_with_time("  4. 封面截图完成（重新获得控制权）", request_id)
    
    # 5. FFmpeg视频合成（异步）
    log_with_time("  5. 开始FFmpeg视频合成（让出控制权）", request_id)
    await asyncio.sleep(3)  # 模拟FFmpeg异步合成
    log_with_time("  5. FFmpeg视频合成完成（重新获得控制权）", request_id)
    
    log_with_time("✅ 视频生成完成", request_id)
    return {"message": "视频生成完成", "request_id": request_id, "duration": "7.5秒"}

@app.get("/status")
async def status_check():
    """状态检查请求（模拟其他API调用）"""
    request_id = f"STATUS-{int(time.time() * 1000) % 10000}"
    log_with_time("📊 状态检查请求", request_id)
    
    # 快速返回状态
    current_time = datetime.now().strftime("%H:%M:%S")
    log_with_time("✅ 状态检查完成", request_id)
    
    return {
        "status": "running", 
        "time": current_time,
        "request_id": request_id,
        "message": "服务正常运行"
    }

@app.get("/test_concurrent")
async def test_concurrent():
    """测试并发处理能力"""
    log_with_time("🧪 开始并发测试")
    
    # 模拟同时处理多个异步任务
    async def task(task_id: int, duration: float):
        log_with_time(f"  任务{task_id}开始（预计{duration}秒）")
        await asyncio.sleep(duration)
        log_with_time(f"  任务{task_id}完成")
        return f"任务{task_id}结果"
    
    # 并发执行多个任务
    results = await asyncio.gather(
        task(1, 1.0),
        task(2, 1.5),
        task(3, 0.8),
        task(4, 1.2)
    )
    
    log_with_time("✅ 并发测试完成")
    return {"results": results, "message": "所有任务并发完成"}

if __name__ == "__main__":
    print("🚀 启动FastAPI异步演示服务器")
    print("📝 测试说明：")
    print("   1. 访问 http://localhost:8001/slow 开始一个慢请求")
    print("   2. 在慢请求处理期间，立即访问 http://localhost:8001/quick 或 http://localhost:8001/status")
    print("   3. 观察日志输出，你会看到：")
    print("      - 慢请求遇到await时让出控制权")
    print("      - 主线程立即处理其他请求")
    print("      - 异步操作完成后，慢请求重新获得控制权")
    print("   4. 访问 http://localhost:8001/video_simulation 模拟视频生成流程")
    print("   5. 访问 http://localhost:8001/test_concurrent 测试并发能力")
    print()
    print("🔍 关键观察点：")
    print("   - 时间戳：看到请求是如何交替处理的")
    print("   - 线程ID：确认所有请求都在同一线程中处理")
    print("   - 控制权转移：await前后的日志时间差")
    print()
    
    # 启动服务器
    uvicorn.run(app, host="127.0.0.1", port=8001, log_level="warning")
