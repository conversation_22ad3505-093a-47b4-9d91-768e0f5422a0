"""
使用正确schema的API测试
"""

import requests
import json

def test_with_correct_schema():
    url = "http://localhost:8000/api/cover-templates"
    
    # 匹配后端CoverTemplateCreate schema的数据
    data = {
        "name": "测试模板正确Schema",
        "variables": [],  # 必需字段
        "category": "测试",
        "description": "使用正确schema的测试",
        "elements": [],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        },
        "is_built_in": False,
        "width": 1920,
        "height": 1080,
        "format": "png",
        "tags": []
    }
    
    print("🔗 测试正确的Schema格式")
    print(f"📦 数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        print("📤 发送请求...")
        response = requests.post(url, json=data, timeout=10)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 创建成功!")
            print(f"📋 返回数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 创建失败: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏱️ 请求超时")
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误 - 后端可能未启动")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_with_correct_schema()
