import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

try:
    # 测试完整的模板管理功能
    from src.services.template_import_service import template_import_service
    from src.services.app_init_service import app_init_service
    from src.core.database import get_db_session
    from src.models.resources import CoverTemplate
    
    print('=== 完整的封面模板管理功能测试 ===\n')
    
    # 1. 初始化
    print('1. 初始化应用...')
    app_init_service.startup()
    print('✓ 应用初始化完成\n')
    
    # 2. 测试HTML模板导入
    print('2. 测试HTML模板导入...')
    db = get_db_session()
    try:
        # 检查是否已存在
        existing = db.query(CoverTemplate).filter(
            CoverTemplate.name == "社交媒体帖子模板"
        ).first()
        
        if not existing:
            template = template_import_service.import_html_template(
                html_file_path="social_post_template.html",
                name="社交媒体帖子模板",
                description="适用于各种社交媒体平台的帖子封面模板，支持头像、用户名、标题和描述变量",
                category="社交媒体",
                db=db
            )
            print('✓ 成功导入社交媒体帖子模板')
        else:
            template = existing
            print('✓ 社交媒体帖子模板已存在')
        
        # 3. 获取模板变量
        print('\n3. 获取模板变量...')
        variables = template_import_service.list_template_variables(template.id, db)
        print(f'✓ 模板变量: {variables}')
        
        # 4. 测试模板渲染
        print('\n4. 测试模板渲染...')
        test_data = {
            "avatar": "https://example.com/avatar.jpg",
            "account_name": "测试用户名",
            "title": "这是一个测试标题，用于验证HTML模板渲染功能"
        }
        
        rendered_html = template_import_service.render_template(
            template_id=template.id,
            variables=test_data,
            db=db
        )
        print(f'✓ 模板渲染成功，HTML长度: {len(rendered_html)} 字符')
        
        # 验证变量替换
        for var_name, var_value in test_data.items():
            if var_value in rendered_html:
                print(f'✓ 变量 {var_name} 替换成功')
            else:
                print(f'✗ 变量 {var_name} 替换失败')
        
        # 5. 前端格式转换测试
        print('\n5. 前端格式转换测试...')
        frontend_data = template.to_frontend_format()
        print(f'✓ 模板ID: {frontend_data["id"]}')
        print(f'✓ 模板名称: {frontend_data["name"]}')
        print(f'✓ 模板类型: {frontend_data["templateType"]}')
        print(f'✓ 变量数量: {frontend_data["variableCount"]}')
        print(f'✓ 包含变量: {frontend_data["hasVariables"]}')
        print(f'✓ 模板路径: {frontend_data["templatePath"]}')
        
        # 6. 数据库中的所有模板
        print('\n6. 数据库中的所有模板:')
        all_templates = db.query(CoverTemplate).all()
        for i, tmpl in enumerate(all_templates, 1):
            tmpl_data = tmpl.to_frontend_format()
            print(f'  {i}. {tmpl.name} ({tmpl_data["templateType"]}) - {tmpl.category}')
            if tmpl_data["hasVariables"]:
                print(f'     变量: {tmpl.variables}')
        
        print(f'\n✅ 所有测试通过！共 {len(all_templates)} 个模板')
        
    finally:
        db.close()
        
except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()
