#!/usr/bin/env python3
"""
测试F5-TTS音色创建API
"""

import requests
import json
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_create_f5_tts_voice():
    """测试创建F5-TTS音色"""
    
    print("🚀 测试F5-TTS音色创建API...")
    
    # 准备测试数据
    form_data = {
        'name': 'API测试音色',
        'description': '通过API创建的测试音色',
        'language': 'zh-CN',
        'gender': 'female',
        'ref_text': '这是一个测试音色的参考文本，用于训练F5-TTS模型。',
        'remove_silence': 'false',
        'cross_fade_duration': '0.15',
        'nfe_value': '32',
        'randomize_seed': 'true'
    }
    
    # 创建一个简单的测试音频文件（实际应用中应该是真实的音频文件）
    test_audio_path = Path("test_audio.wav")
    if not test_audio_path.exists():
        # 创建一个空的音频文件用于测试
        with open(test_audio_path, "wb") as f:
            # 写入一个简单的WAV文件头（44字节）
            f.write(b'RIFF')
            f.write((36).to_bytes(4, 'little'))  # 文件大小-8
            f.write(b'WAVE')
            f.write(b'fmt ')
            f.write((16).to_bytes(4, 'little'))  # fmt chunk大小
            f.write((1).to_bytes(2, 'little'))   # 音频格式
            f.write((1).to_bytes(2, 'little'))   # 声道数
            f.write((44100).to_bytes(4, 'little'))  # 采样率
            f.write((88200).to_bytes(4, 'little'))  # 字节率
            f.write((2).to_bytes(2, 'little'))   # 块对齐
            f.write((16).to_bytes(2, 'little'))  # 位深度
            f.write(b'data')
            f.write((0).to_bytes(4, 'little'))   # 数据大小
    
    try:
        # 准备文件上传
        with open(test_audio_path, 'rb') as audio_file:
            files = {'ref_audio': ('test_audio.wav', audio_file, 'audio/wav')}
            
            # 发送POST请求
            response = requests.post(
                f"{BASE_URL}/api/f5-tts-voices",
                data=form_data,
                files=files
            )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 音色创建成功!")
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 验证返回的数据
            if data.get('success') and data.get('data'):
                voice_data = data['data']
                print(f"\n📋 创建的音色信息:")
                print(f"   ID: {voice_data.get('id')}")
                print(f"   名称: {voice_data.get('name')}")
                print(f"   语言: {voice_data.get('language')}")
                print(f"   性别: {voice_data.get('gender')}")
                print(f"   参考文本: {voice_data.get('ref_text')}")
                return True
            else:
                print(f"❌ 响应格式错误: {data}")
                return False
        else:
            print(f"❌ 音色创建失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    finally:
        # 清理测试文件
        if test_audio_path.exists():
            test_audio_path.unlink()

def test_get_voices():
    """测试获取音色列表"""
    print("\n📋 测试获取音色列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/f5-tts-voices")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取音色列表成功: {len(data.get('data', []))} 个音色")
            
            for voice in data.get('data', []):
                print(f"   - {voice.get('name')} ({voice.get('id')})")
            return True
        else:
            print(f"❌ 获取音色列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 F5-TTS API测试开始...\n")
    
    # 测试获取音色列表
    success1 = test_get_voices()
    
    # 测试创建音色
    success2 = test_create_f5_tts_voice()
    
    # 再次测试获取音色列表
    success3 = test_get_voices()
    
    print(f"\n📊 测试结果:")
    print(f"   获取音色列表: {'✅' if success1 else '❌'}")
    print(f"   创建音色: {'✅' if success2 else '❌'}")
    print(f"   验证音色列表: {'✅' if success3 else '❌'}")
    
    if all([success1, success2, success3]):
        print("\n🎉 所有测试通过！F5-TTS API工作正常。")
    else:
        print("\n❌ 部分测试失败，请检查问题。")
