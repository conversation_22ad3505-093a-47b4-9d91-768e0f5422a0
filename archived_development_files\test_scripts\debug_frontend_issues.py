#!/usr/bin/env python3
"""
针对性修复前端数据联动问题
逐一测试并修复每个报告的问题
"""

import requests
import json
import subprocess
import time
import os
from pathlib import Path

API_BASE = "http://localhost:8000/api"

def ensure_backend_running():
    """确保后端正在运行"""
    try:
        response = requests.get(f"{API_BASE}/health", timeout=2)
        if response.status_code == 200:
            print("✅ 后端服务正在运行")
            return True
    except:
        pass
    
    print("❌ 后端服务未运行，尝试启动...")
    
    # 尝试启动后端
    backend_dir = Path(__file__).parent / "backend"
    if backend_dir.exists():
        try:
            # 启动后端进程
            subprocess.Popen(
                ["python", "main.py"],
                cwd=backend_dir,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            print("正在启动后端，等待5秒...")
            time.sleep(5)
            
            # 再次检查
            response = requests.get(f"{API_BASE}/health", timeout=2)
            if response.status_code == 200:
                print("✅ 后端服务启动成功")
                return True
        except Exception as e:
            print(f"❌ 启动后端失败: {e}")
    
    return False

def test_issue_1_material_count():
    """测试问题1：视频素材分类的素材个数显示"""
    print("\n🧪 测试问题1：视频素材分类的素材个数")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE}/video-categories")
        print(f"API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应格式: {type(data)}")
            print(f"响应键: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
            
            # 检查是否有data字段
            categories = data.get('data', data) if isinstance(data, dict) else data
            
            if isinstance(categories, list) and len(categories) > 0:
                print(f"分类数量: {len(categories)}")
                first_category = categories[0]
                print(f"首个分类: {json.dumps(first_category, ensure_ascii=False, indent=2)}")
                
                # 检查material_count字段
                if 'material_count' in first_category:
                    print(f"✅ material_count字段存在: {first_category['material_count']}")
                else:
                    print("❌ material_count字段缺失")
                    print(f"可用字段: {list(first_category.keys())}")
                    
                return True
            else:
                print("❌ 没有分类数据")
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    return False

def test_issue_2_prompt_categories():
    """测试问题2：提示词分组匹配"""
    print("\n🧪 测试问题2：提示词分组")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE}/prompts")
        print(f"API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            prompts = data.get('data', data) if isinstance(data, dict) else data
            
            if isinstance(prompts, list) and len(prompts) > 0:
                print(f"提示词数量: {len(prompts)}")
                
                # 提取分类
                categories = list(set(prompt.get('category', 'unknown') for prompt in prompts))
                print(f"提取的分类: {categories}")
                
                # 显示每个分类的提示词
                for category in categories:
                    category_prompts = [p for p in prompts if p.get('category') == category]
                    print(f"  {category}: {len(category_prompts)} 个提示词")
                    if category_prompts:
                        print(f"    示例: {category_prompts[0].get('name', 'N/A')}")
                
                return True
            else:
                print("❌ 没有提示词数据")
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    return False

def test_issue_3_tts_voices():
    """测试问题3：TTS音色设置"""
    print("\n🧪 测试问题3：TTS音色设置")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE}/settings")
        print(f"API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            settings = data.get('data', data) if isinstance(data, dict) else data
            
            print(f"设置格式: {json.dumps(settings, ensure_ascii=False, indent=2)[:500]}...")
            
            # 检查TTS配置
            if 'tts' in settings:
                tts_config = settings['tts']
                print(f"TTS配置: {json.dumps(tts_config, ensure_ascii=False, indent=2)}")
                
                current_voice = tts_config.get('voice')
                provider = tts_config.get('provider')
                
                print(f"当前音色: {current_voice}")
                print(f"当前提供商: {provider}")
                
                return True
            else:
                print("❌ 没有TTS配置")
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    return False

def test_issue_4_background_music():
    """测试问题4：背景音乐配置"""
    print("\n🧪 测试问题4：背景音乐配置")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE}/background-music")
        print(f"API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            music = data.get('data', data) if isinstance(data, dict) else data
            
            if isinstance(music, list):
                print(f"音乐数量: {len(music)}")
                if len(music) > 0:
                    print(f"首个音乐: {json.dumps(music[0], ensure_ascii=False, indent=2)}")
                    
                    # 提取分类
                    categories = list(set(m.get('category', 'unknown') for m in music))
                    print(f"音乐分类: {categories}")
                else:
                    print("❌ 没有音乐数据")
            else:
                print(f"❌ 音乐数据格式错误: {type(music)}")
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    return False

def test_issue_5_cover_templates():
    """测试问题5：封面模板"""
    print("\n🧪 测试问题5：封面模板")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE}/cover-templates")
        print(f"API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            templates = data.get('data', data) if isinstance(data, dict) else data
            
            if isinstance(templates, list):
                print(f"模板数量: {len(templates)}")
                if len(templates) > 0:
                    print(f"首个模板: {json.dumps(templates[0], ensure_ascii=False, indent=2)}")
                else:
                    print("❌ 没有模板数据")
            else:
                print(f"❌ 模板数据格式错误: {type(templates)}")
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    return False

def test_issue_6_accounts():
    """测试问题6：账号配置"""
    print("\n🧪 测试问题6：账号配置")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE}/accounts")
        print(f"API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('data', data) if isinstance(data, dict) else data
            
            if isinstance(accounts, list):
                print(f"账号数量: {len(accounts)}")
                if len(accounts) > 0:
                    print(f"首个账号: {json.dumps(accounts[0], ensure_ascii=False, indent=2)}")
                else:
                    print("❌ 没有账号数据")
            else:
                print(f"❌ 账号数据格式错误: {type(accounts)}")
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    return False

def main():
    """主测试流程"""
    print("🚀 前端数据联动问题诊断")
    print("=" * 60)
    
    # 确保后端运行
    if not ensure_backend_running():
        print("❌ 无法启动后端服务，测试终止")
        return
    
    # 测试各个问题
    issues = [
        ("视频素材个数显示", test_issue_1_material_count),
        ("提示词分组匹配", test_issue_2_prompt_categories),
        ("TTS音色设置", test_issue_3_tts_voices),
        ("背景音乐配置", test_issue_4_background_music),
        ("封面模板数据", test_issue_5_cover_templates),
        ("账号配置数据", test_issue_6_accounts),
    ]
    
    results = []
    
    for issue_name, test_func in issues:
        try:
            result = test_func()
            results.append((issue_name, result))
        except Exception as e:
            print(f"❌ {issue_name} 测试失败: {e}")
            results.append((issue_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    for issue_name, success in results:
        status = "✅ 正常" if success else "❌ 异常"
        print(f"{status} {issue_name}")
    
    print(f"\n总体状况: {success_count}/{total_count} 正常")
    
    if success_count < total_count:
        print("\n📝 下一步修复建议:")
        for issue_name, success in results:
            if not success:
                print(f"  - 修复 {issue_name}")

if __name__ == "__main__":
    main()
