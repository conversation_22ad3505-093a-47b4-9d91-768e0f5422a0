#!/usr/bin/env python3
"""
基于API真实响应格式分析前端数据联动问题
"""
import requests
import json

API_BASE = "http://localhost:8000/api"

def test_api(endpoint, name, print_raw_data=False):
    """测试API端点并解析返回格式"""
    try:
        # 先尝试不带斜杠
        response = requests.get(f"{API_BASE}{endpoint}")
        if response.status_code == 307:
            # 重定向，使用带斜杠的版本
            response = requests.get(f"{API_BASE}{endpoint}/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n✅ {name}")
            print(f"   状态码: {response.status_code}")
            
            # 如果需要打印原始数据
            if print_raw_data:
                print(f"   📄 原始返回数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return data
            
            # 检查是否是标准格式 {success, data, message}
            if isinstance(data, dict) and 'success' in data and 'data' in data:
                print(f"   格式: 标准格式 - success: {data['success']}")
                actual_data = data['data']
                if data.get('message'):
                    print(f"   消息: {data['message']}")
            else:
                print(f"   格式: 直接数据")
                actual_data = data
            
            # 分析数据结构
            if isinstance(actual_data, list):
                print(f"   数据: 列表，{len(actual_data)} 项")
                if actual_data:
                    first_item = actual_data[0]
                    if isinstance(first_item, dict):
                        print(f"   字段: {list(first_item.keys())}")
                        # 显示重要字段的值
                        for key in ['id', 'name', 'category', 'material_count', 'provider', 'voice']:
                            if key in first_item:
                                print(f"   {key}: {first_item[key]}")
                    return actual_data
            elif isinstance(actual_data, dict):
                print(f"   数据: 对象")
                print(f"   字段: {list(actual_data.keys())}")
                return actual_data
            else:
                print(f"   数据: {type(actual_data).__name__}")
                return actual_data
        else:
            print(f"\n❌ {name}")
            print(f"   状态码: {response.status_code}")
            print(f"   错误: {response.text[:200]}")
            return None
            
    except Exception as e:
        print(f"\n❌ {name} - 异常: {e}")
        return None

def main():
    print("🔍 前端数据联动问题分析 - 基于真实API响应")
    print("=" * 60)
    
    # 测试所有关键API - 其他API打印原始数据
    video_categories = test_api("/video-categories", "视频素材分类API", print_raw_data=True)
    prompts = test_api("/prompts", "提示词API", print_raw_data=True) 
    settings = test_api("/settings", "系统设置API", print_raw_data=True)
    
    # 测试背景音乐相关API
    print("\n" + "🎵" * 20 + " 背景音乐API测试 " + "🎵" * 20)
    background_music = test_api("/background-music", "背景音乐列表API", print_raw_data=True)
    music_categories = test_api("/background-music/categories/list", "背景音乐分类API", print_raw_data=True)
    
    # 测试封面模板相关API
    print("\n" + "🖼️" * 20 + " 封面模板API测试 " + "🖼️" * 20)
    cover_templates = test_api("/cover-templates", "封面模板列表API", print_raw_data=True)
    template_categories = test_api("/cover-templates/categories/list", "封面模板分类API", print_raw_data=True)
    
    # 测试账号相关API
    print("\n" + "👥" * 20 + " 账号管理API测试 " + "👥" * 20)
    accounts = test_api("/accounts", "账号列表API", print_raw_data=True)
    account_platforms = test_api("/accounts/platforms/list", "账号平台API", print_raw_data=True)
    
    print("\n" + "=" * 60)
    print("🔍 前端数据联动问题分析")
    print("=" * 60)
    
    # 问题1: 素材分类个数显示
    print("\n1️⃣ 素材分类个数显示问题")
    if video_categories and isinstance(video_categories, dict):
        if 'success' in video_categories and 'data' in video_categories:
            categories_data = video_categories['data']
            if isinstance(categories_data, list) and categories_data:
                first_cat = categories_data[0]
                if 'material_count' in first_cat:
                    print(f"   ✅ API包含material_count字段: {first_cat['material_count']}")
                    print("   🔧 前端应该显示: {name} ({material_count}个素材)")
                else:
                    print("   ❌ API缺少material_count字段")
                if 'name' in first_cat:
                    print(f"   ✅ API包含name字段: {first_cat['name']}")
                else:
                    print("   ❌ API缺少name字段")
            else:
                print("   ⚠️  分类列表为空")
        else:
            print("   ❌ API返回格式不是标准格式")
    elif video_categories and isinstance(video_categories, list) and video_categories:
        first_cat = video_categories[0]
        if 'material_count' in first_cat:
            print(f"   ✅ API包含material_count字段: {first_cat['material_count']}")
            print("   🔧 前端应该显示: {name} ({material_count}个素材)")
        else:
            print("   ❌ API缺少material_count字段")
        if 'name' in first_cat:
            print(f"   ✅ API包含name字段: {first_cat['name']}")
        else:
            print("   ❌ API缺少name字段")
    else:
        print("   ❌ 素材分类API返回格式异常")
    
    # 问题2: 提示词分组
    print("\n2️⃣ 提示词分组问题")  
    prompts_data = None
    if prompts and isinstance(prompts, dict) and 'data' in prompts:
        prompts_data = prompts['data']
    elif prompts and isinstance(prompts, list):
        prompts_data = prompts
    
    if prompts_data and isinstance(prompts_data, list) and prompts_data:
        categories = set()
        for prompt in prompts_data:
            if isinstance(prompt, dict) and 'category' in prompt:
                categories.add(prompt['category'])
        
        print(f"   📂 真实分组: {sorted(list(categories))}")
        print("   🔧 前端应该从prompts提取category字段进行分组")
        
        # 检查是否有详情字段
        first_prompt = prompts_data[0]
        detail_fields = ['content', 'template', 'description']
        for field in detail_fields:
            if field in first_prompt:
                print(f"   ⚠️  包含详情字段'{field}'，前端不应显示")
    else:
        print("   ❌ 提示词API返回格式异常")
    
    # 问题3: TTS音色配置
    print("\n3️⃣ TTS音色配置问题")
    settings_data = None
    if settings and isinstance(settings, dict) and 'data' in settings:
        settings_data = settings['data']
    elif settings and isinstance(settings, dict):
        settings_data = settings
        
    if settings_data and isinstance(settings_data, dict):
        if 'tts' in settings_data:
            tts_config = settings_data['tts']
            print(f"   📢 TTS配置: {tts_config}")
            provider = tts_config.get('provider')
            voice = tts_config.get('voice')
            print(f"   🔧 前端应该根据provider='{provider}'生成音色列表")
            print(f"   🔧 当前音色: '{voice}'")
        else:
            print("   ❌ settings中缺少tts配置")
    else:
        print("   ❌ 设置API返回格式异常")
    
    # 问题4: 背景音乐分类
    print("\n4️⃣ 背景音乐分类问题")
    music_data = None
    if background_music and isinstance(background_music, dict) and 'data' in background_music:
        music_data = background_music['data']
        if isinstance(music_data, dict) and 'items' in music_data:
            music_data = music_data['items']
    elif background_music and isinstance(background_music, list):
        music_data = background_music
    
    if music_data and isinstance(music_data, list) and music_data:
        categories = set()
        for music in music_data:
            if isinstance(music, dict) and 'category' in music:
                categories.add(music['category'])
        
        print(f"   📂 真实音乐分类: {sorted(list(categories))}")
        print("   🔧 前端应该从background-music.data.items数组提取category字段")
        
        # 显示音乐字段
        first_music = music_data[0]
        print(f"   🎵 音乐对象字段: {list(first_music.keys())}")
    else:
        print("   ❌ 背景音乐API返回格式异常")
        print(f"   🔍 music_data类型: {type(music_data)}")
        if background_music:
            print(f"   🔍 原始数据结构: {list(background_music.keys()) if isinstance(background_music, dict) else type(background_music)}")
        
    # 检查音乐分类API
    if music_categories:
        print("   🔧 音乐分类API返回格式分析:")
        if isinstance(music_categories, dict) and 'data' in music_categories:
            print(f"      分类数据: {music_categories['data']}")
        else:
            print(f"      直接数据: {music_categories}")
    
    # 问题5: 封面模板
    print("\n5️⃣ 封面模板显示问题")
    templates_data = None
    if cover_templates and isinstance(cover_templates, dict) and 'data' in cover_templates:
        templates_data = cover_templates['data']
        if isinstance(templates_data, dict) and 'templates' in templates_data:
            templates_data = templates_data['templates']
    elif cover_templates and isinstance(cover_templates, list):
        templates_data = cover_templates
    
    if templates_data and isinstance(templates_data, list):
        print(f"   📄 返回模板数量: {len(templates_data)}")
        if templates_data:
            first_template = templates_data[0]
            print(f"   🔬 模板对象字段: {list(first_template.keys())}")
            if 'id' in first_template and 'name' in first_template:
                print(f"   ✅ 包含必需字段 - id: {first_template['id']}, name: {first_template['name']}")
            else:
                print("   ❌ 缺少必需字段id或name")
            print(f"   🔧 前端应从cover-templates.data.templates数组中解析模板数据")
        else:
            print("   ⚠️  模板列表为空")
    else:
        print("   ❌ 封面模板API返回格式异常")
        print(f"   🔍 templates_data类型: {type(templates_data)}")
        if cover_templates:
            print(f"   🔍 原始数据结构: {list(cover_templates.keys()) if isinstance(cover_templates, dict) else type(cover_templates)}")
        
    # 检查模板分类API
    if template_categories:
        print("   🔧 模板分类API返回格式分析:")
        if isinstance(template_categories, dict) and 'data' in template_categories:
            print(f"      分类数据: {template_categories['data']}")
        else:
            print(f"      直接数据: {template_categories}")
    
    # 问题6: 账号配置
    print("\n6️⃣ 账号配置问题")
    accounts_data = None
    if accounts and isinstance(accounts, dict):
        if 'success' in accounts and 'data' in accounts:
            accounts_data = accounts['data']
        elif 'accounts' in accounts:
            # 账号API返回格式: {"accounts": [...], "total": 1, "page": 1, "page_size": 20}
            accounts_data = accounts['accounts']
    elif accounts and isinstance(accounts, list):
        accounts_data = accounts
    
    if accounts_data and isinstance(accounts_data, list):
        print(f"   👥 返回账号数量: {len(accounts_data)}")
        if accounts_data:
            first_account = accounts_data[0]
            print(f"   🔬 账号对象字段: {list(first_account.keys())}")
            if 'id' in first_account:
                print(f"   ✅ 包含id字段: {first_account['id']}")
            else:
                print("   ❌ 缺少id字段")
            if 'name' in first_account:
                print(f"   ✅ 包含name字段: {first_account['name']}")
            else:
                print("   ❌ 缺少name字段")
            if 'platform' in first_account:
                print(f"   ✅ 包含platform字段: {first_account['platform']}")
            else:
                print("   ❌ 缺少platform字段")
            print(f"   🔧 前端应从accounts.accounts数组中解析账号数据")
        else:
            print("   ⚠️  账号列表为空")
    else:
        print("   ❌ 账号API返回格式异常")
        print(f"   🔍 实际返回数据类型: {type(accounts)}")
        if accounts:
            print(f"   🔍 返回数据内容: {accounts}")
        
    # 检查账号平台API
    if account_platforms:
        print("   🔧 账号平台API返回格式分析:")
        if isinstance(account_platforms, dict) and 'data' in account_platforms:
            print(f"      平台数据: {account_platforms['data']}")
        else:
            print(f"      直接数据: {account_platforms}")
    else:
        print("   ⚠️  账号平台API无数据或失败")

if __name__ == "__main__":
    main()
