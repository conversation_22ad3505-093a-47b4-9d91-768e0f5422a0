#!/usr/bin/env python3
"""
检查数据库中的is_deleted字段
"""

import sqlite3

def check_is_deleted():
    try:
        conn = sqlite3.connect('D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db')
        cursor = conn.cursor()
        
        # 检查所有记录的is_deleted状态
        cursor.execute('SELECT id, name, is_deleted FROM video_materials')
        rows = cursor.fetchall()
        
        print(f"找到 {len(rows)} 条记录:")
        print("ID (前8位) | 名称 | is_deleted")
        print("-" * 60)
        
        for row in rows:
            print(f"{row[0][:8]}... | {row[1][:30]:<30} | {row[2]}")
            
        # 检查is_deleted=0的记录数量
        cursor.execute('SELECT COUNT(*) FROM video_materials WHERE is_deleted = 0')
        not_deleted_count = cursor.fetchone()[0]
        print(f"\nis_deleted=0 (未删除) 的记录数量: {not_deleted_count}")
        
        # 检查is_deleted=1的记录数量
        cursor.execute('SELECT COUNT(*) FROM video_materials WHERE is_deleted = 1')
        deleted_count = cursor.fetchone()[0]
        print(f"is_deleted=1 (已删除) 的记录数量: {deleted_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    check_is_deleted()
