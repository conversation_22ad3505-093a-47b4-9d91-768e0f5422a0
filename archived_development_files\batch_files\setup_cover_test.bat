@echo off
echo =============================================
echo 封面生成测试环境设置
echo =============================================

echo.
echo 1. 安装Playwright...
pip install playwright

echo.
echo 2. 安装Chromium浏览器...
playwright install chromium

echo.
echo 3. 安装可选依赖...
pip install psutil

echo.
echo =============================================
echo 安装完成！
echo =============================================
echo.
echo 现在可以运行测试:
echo   python cover_test_independent.py
echo.
pause
