/**
 * 视频素材管理 - 直接API客户端 (不使用fetch)
 * 所有API调用都直接访问后端，无超时限制
 */

import DirectHttpClient from './directHttpClient'
import { ApiResponse } from '@/types/store'

// 保持原有接口类型不变，确保兼容性
export interface VideoMaterialAPI {
  id: string
  name: string
  type: string  // 'video', 'image', 'gif'
  format: string  // 格式字符串 (如 'MP4')
  size: string  // 格式化的文件大小字符串 (如 '1000.0 KB')
  duration?: string  // 格式化的时长字符串 (如 '2:00')
  dimensions: {
    width: number
    height: number
  }
  aspectRatio: string
  path: string
  url: string
  thumbnailUrl?: string | null
  thumbnailPath?: string | null
  filePath: string  // 兼容字段
  category: string
  tags: string[]
  isBuiltIn: boolean
  createdAt?: string
  lastModified?: string
  metadata: {
    fileSize?: number
    format?: string
    frameRate?: number
    bitrate?: number
    thumbnailPath?: string
  }
}

export interface VideoMaterialCreateRequest {
  name: string
  file_path: string
  duration: number
  resolution: string
  category?: string
  tags?: string[]
  is_built_in?: boolean
  file_size?: number
  format?: string
  frame_rate?: number
  bitrate?: number
  thumbnail_path?: string
}

export interface VideoMaterialUpdateRequest {
  name?: string
  category?: string
  tags?: string[]
}

export interface VideoMaterialQueryParams {
  category?: string
  search?: string
  skip?: number
  limit?: number
}

export interface BulkVideoMaterialResponse {
  success: VideoMaterialAPI[]
  failed: Array<{ name: string; error: string }>
  total: number
  success_count: number
  failed_count: number
}

class DirectVideoMaterialApiClient {
  private client: DirectHttpClient

  constructor() {
    this.client = new DirectHttpClient('/api/video-materials')
  }

  /**
   * 获取视频素材列表
   */
  async getVideoMaterials(params?: VideoMaterialQueryParams): Promise<VideoMaterialAPI[]> {
    const searchParams = new URLSearchParams()
    if (params?.category) searchParams.set('category', params.category)
    if (params?.search) searchParams.set('search', params.search)
    if (params?.skip !== undefined) searchParams.set('skip', params.skip.toString())
    if (params?.limit !== undefined) searchParams.set('limit', params.limit.toString())

    const url = searchParams.toString() ? `/?${searchParams.toString()}` : '/'
    return this.client.get<VideoMaterialAPI[]>(url)
  }

  /**
   * 获取单个视频素材
   */
  async getVideoMaterial(id: string): Promise<VideoMaterialAPI> {
    return this.client.get<VideoMaterialAPI>(`/${id}`)
  }

  /**
   * 创建视频素材
   */
  async createVideoMaterial(data: VideoMaterialCreateRequest): Promise<VideoMaterialAPI> {
    return this.client.post<VideoMaterialAPI>('/', data)
  }

  /**
   * 批量创建视频素材
   */
  async bulkCreateVideoMaterials(materials: VideoMaterialCreateRequest[]): Promise<BulkVideoMaterialResponse> {
    return this.client.post<BulkVideoMaterialResponse>('/bulk', materials)
  }

  /**
   * 更新视频素材
   */
  async updateVideoMaterial(id: string, data: VideoMaterialUpdateRequest): Promise<VideoMaterialAPI> {
    return this.client.put<VideoMaterialAPI>(`/${id}`, data)
  }

  /**
   * 删除视频素材
   */
  async deleteVideoMaterial(id: string): Promise<ApiResponse> {
    return this.client.delete<ApiResponse>(`/${id}`)
  }

  /**
   * 批量删除视频素材
   */
  async bulkDeleteVideoMaterials(ids: string[]): Promise<ApiResponse> {
    const searchParams = new URLSearchParams()
    ids.forEach(id => searchParams.append('material_ids', id))
    
    return this.client.delete<ApiResponse>(`/bulk?${searchParams.toString()}`)
  }

  /**
   * 获取视频素材分类列表
   */
  async getVideoMaterialCategories(): Promise<{ categories: string[] }> {
    const result = await this.client.get<{ data?: { categories: string[] } }>('/categories/list')
    return result.data || { categories: [] }
  }

  /**
   * 上传视频文件 - 无超时限制
   */
  async uploadVideoFile(
    file: File, 
    category: string = 'general',
    tags: string = '',
    onProgress?: (progress: number) => void
  ): Promise<{ 
    id: string
    filePath: string
    thumbnailPath?: string
    duration: number
    resolution: string
    fileSize: number
    format: string
    frameRate?: number
    bitrate?: number
    width: number
    height: number
  }> {
    console.log('🟡 DirectAPI客户端.uploadVideoFile 被调用:', {
      fileName: file.name,
      category: category,
      tags: tags,
      fileSize: file.size
    })
    
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', category)
    formData.append('tags', tags)

    console.log('🟡 FormData内容:', {
      file: file.name,
      category: category,
      tags: tags
    })

    const response = await this.client.uploadFile<{ data: any }>('/upload', formData, onProgress)
    
    console.log('🟡 上传成功，服务器响应:', response)
    return response.data
  }

  /**
   * 批量上传视频文件 - 无超时限制
   */
  async bulkUploadVideoFiles(
    files: File[], 
    category: string = 'general'
  ): Promise<BulkVideoMaterialResponse> {
    console.log('🟡 DirectAPI客户端.bulkUploadVideoFiles 被调用:', {
      fileCount: files.length,
      fileNames: files.map(f => f.name),
      category: category
    })
    
    const formData = new FormData()
    files.forEach(file => formData.append('files', file))
    formData.append('category', category)

    console.log('🟡 批量上传 FormData 内容:', {
      filesCount: files.length,
      category: category
    })

    const result = await this.client.uploadFile<BulkVideoMaterialResponse>('/upload/bulk', formData)
    
    console.log('🟡 批量上传服务器响应:', result)
    
    return result
  }
}

export const directVideoMaterialApi = new DirectVideoMaterialApiClient()
