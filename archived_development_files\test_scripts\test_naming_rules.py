#!/usr/bin/env python3
"""
简单测试新的文件命名系统
"""

import re

def generate_standard_filename_test(account_name: str, job_name: str, sequence: int, extension: str) -> str:
    """
    模拟新的文件命名规则
    格式：账号名_任务名称_视频N.扩展名
    """
    # 清理文件名中的特殊字符，保留中文、英文、数字、下划线和连字符
    safe_account_name = re.sub(r'[^\w\u4e00-\u9fff\-]', '_', account_name)
    safe_job_name = re.sub(r'[^\w\u4e00-\u9fff\-]', '_', job_name)
    
    # 生成文件名：账号名_任务名称_视频N.扩展名
    filename = f"{safe_account_name}_{safe_job_name}_视频{sequence}.{extension}"
    
    return filename

def test_naming_rules():
    """测试命名规则"""
    print("🔧 测试新的文件命名规则")
    print("=" * 50)
    
    # 测试案例
    test_cases = [
        ("张三", "搞笑故事生成", 1, "mp4"),
        ("李四@账号", "教育内容!", 2, "mp3"),
        ("小红💖", "美食分享#热门", 1, "txt"),
        ("Tech User", "AI科技讲解", 3, "srt"),
        ("测试用户123", "多语言Content", 1, "png"),
    ]
    
    print("📋 命名规则测试结果:")
    for account, job, seq, ext in test_cases:
        filename = generate_standard_filename_test(account, job, seq, ext)
        print(f"   账号: {account}")
        print(f"   作业: {job}")
        print(f"   序号: {seq}")
        print(f"   生成: {filename}")
        print()
    
    print("📂 批次中多个视频的命名示例:")
    account = "小明"
    job = "日常Vlog"
    
    for i in range(1, 4):
        video_file = generate_standard_filename_test(account, job, i, "mp4")
        audio_file = generate_standard_filename_test(account, job, i, "mp3")
        story_file = generate_standard_filename_test(account, job, i, "txt")
        
        print(f"   第{i}个视频:")
        print(f"     - 文案: {story_file}")
        print(f"     - 语音: {audio_file}")
        print(f"     - 视频: {video_file}")
        print()
    
    print("✅ 命名规则验证完成!")
    print("\n📋 新命名系统特点:")
    print("   1. 格式: 账号名_任务名称_视频N.扩展名")
    print("   2. N是该账号在当前批次中的序号（从1开始）")
    print("   3. 自动清理特殊字符，保留中文、英文、数字")
    print("   4. 同一任务的文案、语音、视频使用相同基础名称")
    print("   5. 统一导出到exports目录")

if __name__ == "__main__":
    test_naming_rules()
