"""
账号管理API路由
提供账号的增删改查和文件上传功能
"""

import os
import uuid
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from ..core.database import get_db
from ..models.accounts import Account
from ..schemas.accounts import (
    AccountCreate, AccountUpdate, AccountResponse, 
    AccountList, AccountStats, PlatformType, AccountStatus
)

router = APIRouter(prefix="/accounts", tags=["accounts"])

# 文件上传配置
UPLOAD_DIR = "uploads/avatars"
ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".webp"}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

# 确保上传目录存在
os.makedirs(UPLOAD_DIR, exist_ok=True)


@router.get("/", response_model=AccountList)
async def get_accounts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    platform: Optional[PlatformType] = Query(None, description="平台筛选"),
    status: Optional[AccountStatus] = Query(None, description="状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db)
):
    """获取账号列表"""
    query = db.query(Account)
    
    # 筛选条件
    if platform:
        query = query.filter(Account.platform == platform)
    if status:
        query = query.filter(Account.status == status)
    if search:
        query = query.filter(Account.name.contains(search))
    
    # 总数
    total = query.count()
    
    # 分页和排序
    accounts = query.order_by(desc(Account.created_at)).offset((page - 1) * page_size).limit(page_size).all()
    
    return AccountList(
        accounts=[AccountResponse.from_orm(account) for account in accounts],
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/stats", response_model=AccountStats)
async def get_account_stats(db: Session = Depends(get_db)):
    """获取账号统计信息"""
    total_accounts = db.query(Account).count()
    
    # 按平台统计
    platform_stats = db.query(Account.platform, func.count(Account.id)).group_by(Account.platform).all()
    by_platform = {platform: count for platform, count in platform_stats}
    
    # 按状态统计
    status_stats = db.query(Account.status, func.count(Account.id)).group_by(Account.status).all()
    by_status = {status: count for status, count in status_stats}
    
    # 使用最多的账号
    most_used = db.query(Account).order_by(desc(Account.usage_count)).first()
    
    # 最近创建的账号
    recently_created = db.query(Account).order_by(desc(Account.created_at)).limit(5).all()
    
    return AccountStats(
        total_accounts=total_accounts,
        by_platform=by_platform,
        by_status=by_status,
        most_used=AccountResponse.from_orm(most_used) if most_used else None,
        recently_created=[AccountResponse.from_orm(acc) for acc in recently_created]
    )


@router.post("/", response_model=AccountResponse)
async def create_account(account: AccountCreate, db: Session = Depends(get_db)):
    """创建新账号"""
    # 检查账号名称是否已存在
    existing = db.query(Account).filter(Account.name == account.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="账号名称已存在")
    
    # 创建账号
    db_account = Account(
        name=account.name,
        description=account.description,
        platform=account.platform,
        avatar_url=account.avatar_url,
        brand_color=account.brand_color,
        font_style=account.font_style,
        content_style=account.content_style,
        platform_settings=account.platform_settings
    )
    
    db.add(db_account)
    db.commit()
    db.refresh(db_account)
    
    return AccountResponse.from_orm(db_account)


@router.get("/{account_id}", response_model=AccountResponse)
async def get_account(account_id: int, db: Session = Depends(get_db)):
    """获取单个账号详情"""
    account = db.query(Account).filter(Account.id == account_id).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    return AccountResponse.from_orm(account)


@router.put("/{account_id}", response_model=AccountResponse)
async def update_account(
    account_id: int, 
    account_update: AccountUpdate, 
    db: Session = Depends(get_db)
):
    """更新账号信息"""
    account = db.query(Account).filter(Account.id == account_id).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    # 如果更新名称，检查是否重复
    if account_update.name and account_update.name != account.name:
        existing = db.query(Account).filter(Account.name == account_update.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="账号名称已存在")
    
    # 更新字段
    update_data = account_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(account, field, value)
    
    db.commit()
    db.refresh(account)
    
    return AccountResponse.from_orm(account)


@router.delete("/{account_id}")
async def delete_account(account_id: int, db: Session = Depends(get_db)):
    """删除账号"""
    account = db.query(Account).filter(Account.id == account_id).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    # 删除关联的头像文件
    if account.avatar_file_path and os.path.exists(account.avatar_file_path):
        try:
            os.remove(account.avatar_file_path)
        except OSError:
            pass  # 忽略文件删除错误
    
    db.delete(account)
    db.commit()
    
    return {"success": True, "message": "账号删除成功"}


@router.post("/{account_id}/avatar")
async def upload_avatar(
    account_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传账号头像"""
    account = db.query(Account).filter(Account.id == account_id).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    # 验证文件类型
    file_ext = os.path.splitext(file.filename or "")[1].lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件类型。支持的格式：{', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    # 验证文件大小
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="文件大小超过5MB限制")
    
    # 生成唯一文件名
    unique_filename = f"{account_id}_{uuid.uuid4().hex[:8]}{file_ext}"
    file_path = os.path.join(UPLOAD_DIR, unique_filename)
    
    # 删除旧头像文件
    if account.avatar_file_path and os.path.exists(account.avatar_file_path):
        try:
            os.remove(account.avatar_file_path)
        except OSError:
            pass
    
    # 保存新文件
    with open(file_path, "wb") as f:
        f.write(content)
    
    # 更新数据库
    account.avatar_file_path = file_path
    account.avatar_url = f"/api/accounts/{account_id}/avatar/file"
    db.commit()
    
    return {
        "success": True,
        "message": "头像上传成功",
        "avatar_url": account.avatar_url
    }


@router.get("/{account_id}/avatar/file")
async def get_avatar_file(account_id: int, db: Session = Depends(get_db)):
    """获取账号头像文件"""
    account = db.query(Account).filter(Account.id == account_id).first()
    if not account or not account.avatar_file_path:
        raise HTTPException(status_code=404, detail="头像文件不存在")
    
    if not os.path.exists(account.avatar_file_path):
        raise HTTPException(status_code=404, detail="头像文件不存在")
    
    return FileResponse(account.avatar_file_path)


@router.post("/{account_id}/use")
async def use_account(account_id: int, db: Session = Depends(get_db)):
    """标记账号为已使用"""
    account = db.query(Account).filter(Account.id == account_id).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    account.status = AccountStatus.USED
    account.usage_count += 1
    account.last_used_at = datetime.utcnow()
    
    db.commit()
    db.refresh(account)
    
    return AccountResponse.from_orm(account)


@router.post("/bulk/delete")
async def bulk_delete_accounts(
    account_ids: List[int],
    db: Session = Depends(get_db)
):
    """批量删除账号"""
    accounts = db.query(Account).filter(Account.id.in_(account_ids)).all()
    
    # 删除头像文件
    for account in accounts:
        if account.avatar_file_path and os.path.exists(account.avatar_file_path):
            try:
                os.remove(account.avatar_file_path)
            except OSError:
                pass
    
    # 删除数据库记录
    db.query(Account).filter(Account.id.in_(account_ids)).delete(synchronize_session=False)
    db.commit()
    
    return {
        "success": True,
        "message": f"成功删除 {len(accounts)} 个账号"
    }


@router.post("/bulk/status")
async def bulk_update_status(
    account_ids: List[int],
    status: AccountStatus,
    db: Session = Depends(get_db)
):
    """批量更新账号状态"""
    updated = db.query(Account).filter(Account.id.in_(account_ids)).update(
        {"status": status}, 
        synchronize_session=False
    )
    db.commit()
    
    return {
        "success": True,
        "message": f"成功更新 {updated} 个账号状态为 {status.value}"
    }
