"""
检查系统中现有的资源数据
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def check_resources():
    """检查系统中现有的资源"""
    print("🔍 检查系统资源...")
    
    endpoints = [
        ("/api/accounts", "账号"),
        ("/api/video-categories", "视频分组"),
        ("/api/background-music/categories", "音乐分组"),
        ("/api/prompts/categories", "提示词分组"),
        ("/api/cover-templates", "封面模板"),
        ("/api/prompts", "提示词"),
    ]
    
    resources = {}
    
    for endpoint, name in endpoints:
        try:
            print(f"\n📋 获取{name}...")
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    data = data['data']
                
                print(f"✅ {name}: 共 {len(data)} 个")
                if data:
                    for item in data[:3]:  # 只显示前3个
                        id_field = item.get('id') or item.get('name') or item.get('category')
                        name_field = item.get('name') or item.get('title') or item.get('category') or str(id_field)
                        print(f"   - {id_field}: {name_field}")
                    if len(data) > 3:
                        print(f"   ... 还有 {len(data) - 3} 个")
                
                resources[name] = data
            else:
                print(f"❌ {name}: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ {name}: {e}")
    
    return resources

if __name__ == "__main__":
    resources = check_resources()
    
    # 保存到文件供后续使用
    with open('available_resources.json', 'w', encoding='utf-8') as f:
        json.dump(resources, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 资源信息已保存到 available_resources.json")
