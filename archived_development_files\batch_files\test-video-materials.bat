@echo off
echo 开始测试视频素材管理功能...

echo.
echo 1. 启动后端服务器...
cd backend
start "Backend Server" cmd /k "python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000"

echo.
echo 等待服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo 2. 启动前端开发服务器...
cd ..\frontend
start "Frontend Server" cmd /k "npm run dev"

echo.
echo 等待前端服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo 3. 打开浏览器...
timeout /t 5 /nobreak >nul
start "" "http://localhost:3000/videos"

echo.
echo ✅ 服务器已启动！
echo 📌 后端API: http://localhost:8000
echo 📌 前端页面: http://localhost:3000
echo 📌 视频素材管理: http://localhost:3000/videos
echo.
echo 按任意键退出...
pause >nul
