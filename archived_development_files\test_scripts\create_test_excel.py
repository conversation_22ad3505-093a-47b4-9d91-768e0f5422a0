#!/usr/bin/env python3
"""
创建测试Excel文件，包含文案和标题两列
"""

import pandas as pd

# 测试数据
data = {
    '文案': [
        '今天发生了一件有趣的事情，我在公园里遇到了一只会说话的鹦鹉。',
        '昨天我去了一家新开的咖啡店，那里的咖啡味道真的很棒。',
        '这是一个关于友谊的故事，两个陌生人因为一本书而成为了好朋友。',
        '我最近学会了一项新技能，就是制作手工皂，过程很有趣。',
        '周末我和家人一起去爬山，山顶的风景美得让人难忘。'
    ],
    '标题': [
        '会说话的鹦鹉奇遇记',
        '新咖啡店探店体验',
        '一本书引发的友谊',
        '手工皂制作日记',
        ''  # 这一行没有标题，测试默认行为
    ]
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为Excel文件
df.to_excel('test_excel_with_titles.xlsx', index=False)

print("测试Excel文件创建成功！")
print("文件内容：")
print(df)
