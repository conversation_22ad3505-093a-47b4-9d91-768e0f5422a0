/**
 * LLM API 客户端
 * 用于测试提示词功能
 */

import { LLMConfig } from '@/types/store';
import { DirectHttpClient } from './directHttpClient';

export interface TestPromptRequest {
  prompt: string;
  llmConfig?: LLMConfig; // 可选，如果不提供则使用系统设置
  variables?: Record<string, string>;
}

export interface TestPromptResponse {
  success: boolean;
  result?: string;
  error?: string;
}

/**
 * 测试提示词
 */
export async function testPrompt(request: TestPromptRequest): Promise<TestPromptResponse> {
  try {
    const requestBody: any = {
      prompt: request.prompt,
      variables: request.variables || {}
    };

    // 只有当提供了LLM配置时才添加到请求体中
    if (request.llmConfig) {
      requestBody.llm_config = {
        provider: request.llmConfig.provider,
        api_key: request.llmConfig.apiKey,
        endpoint: request.llmConfig.endpoint,
        model: request.llmConfig.model,
        temperature: request.llmConfig.temperature,
        max_tokens: request.llmConfig.maxTokens,
        system_prompt: request.llmConfig.systemPrompt,
      };
    }

    const client = new DirectHttpClient('/api/llm');
    
    try {
      const data: any = await client.post('/test-prompt', requestBody);
      
      if (data.success) {
        return {
          success: true,
          result: data.data?.result || data.result
        };
      } else {
        return {
          success: false,
          error: data.message || '测试失败'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || `请求失败`
      };
    }
  } catch (error) {
    console.error('测试提示词失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络请求失败'
    };
  }
}
