#!/usr/bin/env python3
"""
检查账号头像数据
"""

import sys
import os

# 添加backend路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

from backend.src.core.database import get_db_session
from backend.src.models.accounts import Account

def main():
    db = get_db_session()
    try:
        accounts = db.query(Account).all()
        print('=== 账号头像信息 ===')
        for acc in accounts:
            print(f'ID: {acc.id}')
            print(f'名称: {acc.name}')
            print(f'头像URL: {acc.avatar_url}')
            print(f'头像URL是否为None: {acc.avatar_url is None}')
            print('---')
        
        if not accounts:
            print('未找到任何账号')
            
    except Exception as e:
        print(f'错误: {e}')
    finally:
        db.close()

if __name__ == "__main__":
    main()
