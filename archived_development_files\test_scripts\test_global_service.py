"""
验证任务队列修复
检查全局服务实例是否正确工作
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent / "backend"))

from src.core.services import get_video_generation_service, set_video_generation_service
from src.core.database import get_session_maker
from src.services.video_generation_service import VideoGenerationService
from loguru import logger

async def test_global_service():
    """测试全局服务实例"""
    print("🧪 测试全局视频生成服务实例...")
    
    # 1. 创建session_maker
    session_maker = get_session_maker()
    
    # 2. 创建服务实例并设置为全局实例
    service1 = VideoGenerationService(session_maker)
    set_video_generation_service(service1)
    
    # 3. 启动任务队列
    await service1.start_task_queue()
    print(f"✅ 任务队列启动状态: {service1._task_queue_running}")
    
    # 4. 获取全局实例
    service2 = get_video_generation_service()
    
    # 5. 验证是同一个实例
    print(f"📋 实例比较:")
    print(f"   service1 == service2: {service1 is service2}")
    print(f"   service1任务队列状态: {service1._task_queue_running}")
    print(f"   service2任务队列状态: {service2._task_queue_running}")
    
    # 6. 再次获取应该还是同一个实例
    service3 = get_video_generation_service(session_maker)
    print(f"   service1 == service3: {service1 is service3}")
    
    if service1 is service2 is service3:
        print("✅ 全局服务实例工作正常！")
    else:
        print("❌ 全局服务实例有问题")
    
    # 停止任务队列
    await service1.stop_task_queue()

if __name__ == "__main__":
    asyncio.run(test_global_service())
