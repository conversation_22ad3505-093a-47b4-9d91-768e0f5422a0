#!/usr/bin/env python3
"""
视频拼接问题排查脚本
按步骤测试：
1. 基础视频拼接（无转场）
2. 带转场效果的视频拼接
3. 添加字幕
4. 添加音频
"""

import os
import sys
import asyncio
import ffmpeg
import logging
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置路径
MATERIALS_DIR = Path("backend/uploads/video_materials")
OUTPUT_DIR = Path("test_outputs")
OUTPUT_DIR.mkdir(exist_ok=True)

async def run_ffmpeg_async(stream, description="FFmpeg操作"):
    """异步运行FFmpeg命令"""
    try:
        logger.info(f"开始执行: {description}")
        cmd = ffmpeg.compile(stream)
        logger.info(f"FFmpeg命令: {' '.join(cmd)}")

        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate()

        if process.returncode == 0:
            logger.info(f"✅ {description} 成功完成")
            return True
        else:
            logger.error(f"❌ {description} 失败")
            logger.error(f"错误输出: {stderr.decode('utf-8', errors='ignore')}")
            return False

    except Exception as e:
        logger.error(f"❌ {description} 执行异常: {e}")
        return False

def get_video_info(video_path):
    """获取视频信息"""
    try:
        probe = ffmpeg.probe(str(video_path))
        video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
        if video_stream:
            duration = float(video_stream.get('duration', 0))
            width = int(video_stream.get('width', 0))
            height = int(video_stream.get('height', 0))
            fps = eval(video_stream.get('r_frame_rate', '30/1'))
            return {
                'duration': duration,
                'width': width,
                'height': height,
                'fps': fps
            }
    except Exception as e:
        logger.error(f"获取视频信息失败: {e}")
        return None

def select_test_videos(target_duration=35):
    """选择测试视频，确保总时长超过目标时长"""
    video_files = []
    total_duration = 0
    
    # 获取所有mp4文件
    for file_path in MATERIALS_DIR.glob("*.mp4"):
        if "thumb_" not in file_path.name:  # 排除缩略图
            info = get_video_info(file_path)
            if info and info['duration'] > 0:
                video_files.append({
                    'path': file_path,
                    'duration': info['duration'],
                    'info': info
                })
                total_duration += info['duration']
                logger.info(f"选择视频: {file_path.name}, 时长: {info['duration']:.2f}s")
                
                if total_duration >= target_duration:
                    break
    
    logger.info(f"总共选择 {len(video_files)} 个视频，总时长: {total_duration:.2f}s")
    return video_files

async def test_basic_concat(video_files, output_path):
    """测试1: 基础视频拼接（无转场）"""
    logger.info("=== 测试1: 基础视频拼接（无转场） ===")
    
    try:
        # 创建输入流
        input_streams = []
        for video_file in video_files:
            stream = ffmpeg.input(str(video_file['path']))
            # 统一分辨率和帧率
            stream = stream.filter('scale', 1080, 1920).filter('fps', fps=30)
            input_streams.append(stream)
        
        # 简单拼接
        concatenated = ffmpeg.concat(*input_streams, v=1, a=0)
        
        # 输出
        output_stream = ffmpeg.output(concatenated, str(output_path), vcodec='libx264', preset='fast', pix_fmt='yuv420p')
        
        success = await run_ffmpeg_async(output_stream.overwrite_output(), "基础视频拼接")
        if not success:
            return False
        
        # 检查输出文件
        if output_path.exists():
            info = get_video_info(output_path)
            if info:
                logger.info(f"✅ 基础拼接成功: {output_path.name}, 时长: {info['duration']:.2f}s")
                return True
            else:
                logger.error("❌ 无法获取输出视频信息")
                return False
        else:
            logger.error("❌ 输出文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 基础拼接失败: {e}")
        return False

def create_transitions(input_streams, transition_duration=0.5, durations=None):
    """创建带转场效果的视频拼接"""
    if len(input_streams) < 2:
        return input_streams[0]

    if durations is None:
        durations = [5.0] * len(input_streams)

    # 统一帧率
    normalized_streams = []
    for stream in input_streams:
        normalized = stream.filter('fps', fps=30)
        normalized_streams.append(normalized)

    # 逐步构建转场链
    result = normalized_streams[0]
    current_result_duration = durations[0]

    for i in range(1, len(normalized_streams)):
        next_segment_duration = durations[i]
        offset = max(0, current_result_duration - transition_duration)

        logger.info(f"转场 {i}: offset={offset:.2f}s, duration={transition_duration}s")

        # 应用转场
        result = ffmpeg.filter(
            [result, normalized_streams[i]],
            'xfade',
            transition='fade',
            duration=transition_duration,
            offset=offset
        )

        # 更新结果时长
        current_result_duration = offset + next_segment_duration

    return result

async def test_transition_concat(video_files, output_path):
    """测试2: 带转场效果的视频拼接"""
    logger.info("=== 测试2: 带转场效果的视频拼接 ===")

    try:
        # 创建输入流
        input_streams = []
        durations = []

        for video_file in video_files:
            stream = ffmpeg.input(str(video_file['path']))
            # 统一分辨率和帧率
            stream = stream.filter('scale', 1080, 1920).filter('fps', fps=30)
            input_streams.append(stream)
            durations.append(video_file['duration'])

        # 使用转场效果
        concatenated = create_transitions(input_streams, 0.5, durations)

        # 输出
        output_stream = ffmpeg.output(concatenated, str(output_path), vcodec='libx264', preset='fast', pix_fmt='yuv420p')

        success = await run_ffmpeg_async(output_stream.overwrite_output(), "转场视频拼接")
        if not success:
            return False

        # 检查输出文件
        if output_path.exists():
            info = get_video_info(output_path)
            if info:
                logger.info(f"✅ 转场拼接成功: {output_path.name}, 时长: {info['duration']:.2f}s")
                return True
            else:
                logger.error("❌ 无法获取输出视频信息")
                return False
        else:
            logger.error("❌ 输出文件不存在")
            return False

    except Exception as e:
        logger.error(f"❌ 转场拼接失败: {e}")
        return False

async def test_with_subtitle(input_video_path, output_path):
    """测试3: 添加字幕"""
    logger.info("=== 测试3: 添加字幕 ===")
    
    try:
        # 创建简单的SRT字幕文件
        srt_content = """1
00:00:00,000 --> 00:00:05,000
这是第一段测试字幕

2
00:00:05,000 --> 00:00:10,000
这是第二段测试字幕

3
00:00:10,000 --> 00:00:15,000
这是第三段测试字幕

4
00:00:15,000 --> 00:00:20,000
这是第四段测试字幕

5
00:00:20,000 --> 00:00:25,000
这是第五段测试字幕

6
00:00:25,000 --> 00:00:30,000
这是第六段测试字幕

7
00:00:30,000 --> 00:00:35,000
这是第七段测试字幕
"""
        
        srt_path = OUTPUT_DIR / "test_subtitle.srt"
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        # 添加字幕
        input_stream = ffmpeg.input(str(input_video_path))
        video_with_subtitle = input_stream.filter('subtitles', filename=str(srt_path))
        
        # 输出
        output_stream = ffmpeg.output(video_with_subtitle, str(output_path), vcodec='libx264', preset='fast', pix_fmt='yuv420p')
        
        success = await run_ffmpeg_async(output_stream.overwrite_output(), "添加字幕")
        if not success:
            return False
        
        # 检查输出文件
        if output_path.exists():
            info = get_video_info(output_path)
            if info:
                logger.info(f"✅ 字幕添加成功: {output_path.name}, 时长: {info['duration']:.2f}s")
                return True
            else:
                logger.error("❌ 无法获取输出视频信息")
                return False
        else:
            logger.error("❌ 输出文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 字幕添加失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始视频拼接问题排查...")
    
    # 选择测试视频
    video_files = select_test_videos(35)
    if not video_files:
        logger.error("没有找到合适的测试视频")
        return
    
    # 测试1: 基础拼接
    basic_output = OUTPUT_DIR / "test_basic_concat.mp4"
    success1 = await test_basic_concat(video_files, basic_output)
    
    if success1:
        # 测试2: 转场拼接
        transition_output = OUTPUT_DIR / "test_transition_concat.mp4"
        success2 = await test_transition_concat(video_files, transition_output)
        
        if success2:
            # 测试3: 添加字幕
            subtitle_output = OUTPUT_DIR / "test_with_subtitle.mp4"
            success3 = await test_with_subtitle(transition_output, subtitle_output)
            
            if success3:
                logger.info("🎉 所有测试都通过了！")
            else:
                logger.error("字幕测试失败")
        else:
            logger.error("转场测试失败")
    else:
        logger.error("基础拼接测试失败")

if __name__ == "__main__":
    asyncio.run(main())
