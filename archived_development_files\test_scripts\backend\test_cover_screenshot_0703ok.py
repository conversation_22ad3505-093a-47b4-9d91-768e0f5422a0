#!/usr/bin/env python3
"""
封面截图服务测试脚本
用于测试真实模板的截图生成功能
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from src.core.database import get_db
from src.services.cover_screenshot_service import cover_screenshot_service
from src.models.accounts import Account
from src.models.resources import CoverTemplate
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

def setup_test_database():
    """设置测试数据库连接"""
    # 这里使用你项目的数据库配置
    DATABASE_URL = "sqlite:///./reddit_story_generator.db"  # 根据实际情况修改
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()

def create_test_account():
    """创建测试账号对象"""
    # 创建一个模拟的账号对象 - 使用简单对象避免SQLAlchemy类型问题
    class TestAccount:
        def __init__(self):
            self.id = 1
            self.name = "测试账号"
            self.platform = "reddit"
            # 如果有真实的头像文件，设置这个路径
            self.avatar_file_path = "uploads/avatars/1_acad3cfb.png"  # 根据实际情况修改
    
    return TestAccount()

async def test_cover_screenshot():
    """测试封面截图功能"""
    print("🧪 开始测试封面截图服务...")
    
    try:
        # 设置数据库
        db = setup_test_database()
        
        # 查询可用的封面模板
        templates = db.query(CoverTemplate).filter(CoverTemplate.id == 'a3689686-5945-456a-93f4-16397f75d418').all()
        if not templates:
            print("❌ 没有找到封面模板，请先导入模板")
            return False
        
        print(f"📋 找到 {len(templates)} 个模板:")
        for i, template in enumerate(templates):
            print(f"  {i+1}. {template.name} (ID: {template.id})")
        
        # 选择第一个模板进行测试
        template = templates[0]
        print(f"\n🎯 使用模板: {template.name}")
        
        # 创建测试账号
        account = create_test_account()
        print(f"👤 使用账号: {account.name}")
        
        # 测试标题
        test_title = "这是一个测试的Reddit故事标题，用来验证封面生成功能是否正常工作"
        
        # 输出路径
        output_dir = backend_dir / "test_outputs"
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / f"test_cover_{template.id}.png"
        
        print(f"📁 输出路径: {output_path}")
        print(f"📝 测试标题: {test_title}")
        
        # 执行截图
        print("\n🔄 开始生成封面截图...")
        success = await cover_screenshot_service.generate_cover_screenshot(
            template_id=str(template.id),
            account=account,  # type: ignore
            title=test_title,
            output_path=str(output_path),
            db=db
        )
        
        if success:
            if output_path.exists():
                file_size = output_path.stat().st_size
                print(f"✅ 截图生成成功!")
                print(f"   文件路径: {output_path}")
                print(f"   文件大小: {file_size} bytes")
                return True
            else:
                print("❌ 截图生成报告成功，但文件不存在")
                return False
        else:
            print("❌ 截图生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理数据库连接
        try:
            if 'db' in locals():
                db.close()
        except:
            pass

async def test_all_templates():
    """测试所有模板"""
    print("🧪 开始测试所有模板...")
    
    db = None
    try:
        db = setup_test_database()
        templates = db.query(CoverTemplate).all()
        
        if not templates:
            print("❌ 没有找到封面模板")
            return
        
        account = create_test_account()
        test_title = "Reddit故事标题测试"
        output_dir = backend_dir / "test_outputs"
        output_dir.mkdir(exist_ok=True)
        
        success_count = 0
        total_count = len(templates)
        
        for template in templates:
            print(f"\n📋 测试模板: {template.name} (ID: {template.id})")
            output_path = output_dir / f"test_cover_{template.id}_{template.name.replace(' ', '_')}.png"
            
            try:
                success = await cover_screenshot_service.generate_cover_screenshot(
                    template_id=str(template.id),
                    account=account,  # type: ignore
                    title=test_title,
                    output_path=str(output_path),
                    db=db
                )
                
                if success and output_path.exists():
                    file_size = output_path.stat().st_size
                    print(f"   ✅ 成功 - 文件大小: {file_size} bytes")
                    success_count += 1
                else:
                    print(f"   ❌ 失败")
                    
            except Exception as e:
                print(f"   ❌ 错误: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{total_count} 个模板测试成功")
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
    finally:
        if db:
            try:
                db.close()
            except:
                pass

def main():
    """主函数"""
    print("🚀 封面截图服务测试工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright 已安装")
    except ImportError:
        print("❌ Playwright 未安装，请运行: pip install playwright")
        print("   然后运行: playwright install chromium")
        return
    
    # 检查数据库
    try:
        db = setup_test_database()
        db.close()
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    print("\n选择测试模式:")
    print("1. 测试单个模板")
    print("2. 测试所有模板")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_cover_screenshot())
    elif choice == "2":
        asyncio.run(test_all_templates())
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
