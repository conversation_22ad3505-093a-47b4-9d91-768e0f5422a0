# Production Environment Configuration for Frontend
# This file will be used when building for production deployment with EXE

# API Configuration - Points to the same host as the EXE
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_API_TIMEOUT=30000

# Production settings
NODE_ENV=production
PORT=3000

# 构建配置 - 跳过检查以避免构建错误
SKIP_ESLINT=true
SKIP_TYPE_CHECK=false

# 调试配置 (生产环境关闭)
NEXT_PUBLIC_DEBUG=false
NEXT_PUBLIC_LOG_LEVEL=error

# Disable telemetry for production builds
NEXT_TELEMETRY_DISABLED=1
NEXT_TELEMETRY_DISABLED=1
