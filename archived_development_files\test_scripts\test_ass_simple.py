#!/usr/bin/env python3
"""简化的ASS字幕测试"""

def html_color_to_ass(html_color):
    if html_color.startswith('#'):
        html_color = html_color[1:]
    r = int(html_color[0:2], 16)
    g = int(html_color[2:4], 16) 
    b = int(html_color[4:6], 16)
    return f"&H00{b:02X}{g:02X}{r:02X}"

def create_ass_subtitle_file(srt_path: str, ass_path: str, font_name: str, font_size: int, font_color: str, position: str):
    """创建ASS字幕文件"""
    try:
        ass_color = html_color_to_ass(font_color)
        
        # 设置对齐方式
        if position == 'top':
            alignment = 8  # 上方居中
            margin_v = 30  # 上边距
        elif position == 'center':
            alignment = 5  # 中间居中  
            margin_v = 0
        else:  # bottom
            alignment = 2  # 下方居中
            margin_v = 30  # 下边距
        
        # 读取SRT文件
        with open(srt_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        # 创建ASS文件头部
        ass_content = f"""[Script Info]
Title: Generated Subtitle
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{ass_color},&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,{alignment},10,10,{margin_v},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # 解析SRT并转换为ASS格式
        srt_blocks = srt_content.strip().split('\n\n')
        for block in srt_blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 跳过序号
                time_line = lines[1]
                text_lines = lines[2:]
                
                # 转换时间格式
                if ' --> ' in time_line:
                    start_time, end_time = time_line.split(' --> ')
                    start_time = start_time.replace(',', '.')[:-1]  # 移除最后一位毫秒
                    end_time = end_time.replace(',', '.')[:-1]
                    
                    # 移除开头的0
                    start_time = start_time.lstrip('0:').lstrip('0')
                    end_time = end_time.lstrip('0:').lstrip('0')
                    
                    text = '\\N'.join(text_lines)  # ASS使用\N换行
                    
                    ass_content += f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{text}\n"
        
        # 写入ASS文件
        with open(ass_path, 'w', encoding='utf-8') as f:
            f.write(ass_content)
        
        print(f"成功创建ASS字幕文件: {ass_path}")
        return True
        
    except Exception as e:
        print(f"创建ASS字幕文件失败: {e}")
        return False

# 创建测试SRT文件
test_srt_content = """1
00:00:01,000 --> 00:00:05,000
这是第一行字幕

2
00:00:06,000 --> 00:00:10,000
这是第二行字幕
颜色应该是红色

3
00:00:11,000 --> 00:00:15,000
这是第三行字幕
位置应该在顶部
"""

# 写入测试SRT文件
with open('test_subtitle.srt', 'w', encoding='utf-8') as f:
    f.write(test_srt_content)

# 测试不同配置
configs = [
    ('Arial', 24, '#FF0000', 'top'),     # 红色，顶部
    ('Arial', 30, '#00FF00', 'center'),  # 绿色，居中
    ('Arial', 32, '#0000FF', 'bottom'),  # 蓝色，底部
]

for i, (font, size, color, pos) in enumerate(configs):
    ass_path = f'test_subtitle_{pos}.ass'
    print(f'\n测试配置 {i+1}: 字体={font}, 大小={size}, 颜色={color}, 位置={pos}')
    
    success = create_ass_subtitle_file('test_subtitle.srt', ass_path, font, size, color, pos)
    
    if success:
        print(f'✅ 成功创建: {ass_path}')
        # 读取并显示ASS文件头部
        with open(ass_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line.startswith('Style:'):
                    print(f'样式行: {line[:100]}...')  # 只显示前100字符
                elif line.startswith('Dialogue:'):
                    print(f'对话行: {line[:80]}...')  # 只显示前80字符

print('\n颜色转换测试:')
colors = ['#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00']
for color in colors:
    ass_color = html_color_to_ass(color)
    print(f'{color} -> {ass_color}')
