<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频生成 - Reddit故事视频生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 2rem;
            align-items: start;
        }

        .config-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
            max-height: 90vh;
            overflow-y: auto;
        }

        .panel-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
        }

        .panel-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .panel-content {
            padding: 1.5rem;
        }

        .config-section {
            margin-bottom: 2rem;
        }

        .config-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.875rem;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .range-group {
            margin-bottom: 1rem;
        }

        .range-input {
            width: 100%;
            margin: 0.5rem 0;
        }

        .range-display {
            text-align: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
            background: var(--bg-secondary);
            padding: 0.25rem;
            border-radius: 4px;
        }

        .multi-select {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            max-height: 150px;
            overflow-y: auto;
            background: var(--bg-primary);
        }

        .multi-select-item {
            padding: 0.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .multi-select-item:hover {
            background: var(--bg-secondary);
        }

        .multi-select-item:last-child {
            border-bottom: none;
        }

        .subtitle-config {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 1rem;
            background: var(--bg-secondary);
        }

        .subtitle-row {
            display: grid;
            grid-template-columns: 1fr 80px;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .color-input {
            width: 40px;
            height: 40px;
            padding: 0;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
        }

        .preview-section {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .preview-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: between;
        }

        .preview-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .preview-content {
            padding: 1.5rem;
        }

        .preview-container {
            background: #000;
            border-radius: 8px;
            aspect-ratio: 9/16;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .preview-placeholder {
            text-align: center;
            color: #9ca3af;
        }

        .preview-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .preview-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            max-width: 80%;
        }

        .preview-controls {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.625rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-primary);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
        }

        .queue-preview {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .queue-title {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .queue-summary {
            font-size: 0.75rem;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .generate-section {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .account-summary {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .account-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .account-row:last-child {
            margin-bottom: 0;
        }

        .account-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .video-count {
            color: var(--text-secondary);
        }

        .help-text {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
            line-height: 1.4;
        }

        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .config-panel {
                max-height: none;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .subtitle-row {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .preview-container {
                aspect-ratio: 16/9;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">视频生成</h1>
            <p class="page-description">配置视频生成参数，批量创建Reddit故事视频内容</p>
        </div>

        <!-- 主布局 -->
        <div class="main-layout">
            <!-- 配置面板 -->
            <div class="config-panel">
                <div class="panel-header">
                    <h2 class="panel-title">生成配置</h2>
                </div>

                <div class="panel-content">
                    <!-- 基础服务配置 -->
                    <div class="config-section">
                        <h3 class="section-title">基础服务</h3>
                        
                        <div class="form-group">
                            <label class="form-label">语音服务</label>
                            <select class="form-select" id="ttsService">
                                <option value="openai">OpenAI TTS</option>
                                <option value="azure">Azure 语音服务</option>
                                <option value="google">Google TTS</option>
                                <option value="f5-tts">F5-TTS (本地)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">大模型服务</label>
                            <select class="form-select" id="llmService">
                                <option value="openai">OpenAI GPT</option>
                                <option value="claude">Claude</option>
                                <option value="gemini">Google Gemini</option>
                                <option value="local">本地模型</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">提示词模板</label>
                            <select class="form-select" id="promptTemplate">
                                <option value="horror">恐怖故事模板</option>
                                <option value="funny">搞笑故事模板</option>
                                <option value="emotional">情感故事模板</option>
                                <option value="mystery">悬疑故事模板</option>
                                <option value="custom">自定义模板</option>
                            </select>
                        </div>

                        <div class="form-group" id="customPromptGroup" style="display: none;">
                            <label class="form-label">自定义提示词</label>
                            <textarea class="form-textarea" id="customPrompt" placeholder="输入自定义提示词..."></textarea>
                        </div>
                    </div>

                    <!-- 音频配置 -->
                    <div class="config-section">
                        <h3 class="section-title">音频配置</h3>
                        
                        <div class="form-group">
                            <label class="form-label">语音倍速</label>
                            <div class="range-group">
                                <input type="range" class="range-input" id="speechSpeed" min="0.5" max="3" step="0.1" value="1.5">
                                <div class="range-display" id="speedDisplay">1.5x</div>
                            </div>
                            <div class="help-text">调整语音播放速度，影响视频总时长</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">背景音乐</label>
                            <select class="form-select" id="backgroundMusic">
                                <option value="random">随机选择</option>
                                <option value="music1">悬疑氛围.mp3</option>
                                <option value="music2">轻松背景.mp3</option>
                                <option value="music3">紧张节奏.mp3</option>
                                <option value="none">无背景音乐</option>
                            </select>
                        </div>
                    </div>

                    <!-- 视频素材配置 -->
                    <div class="config-section">
                        <h3 class="section-title">视频素材</h3>
                        
                        <div class="form-group">
                            <label class="form-label">素材选择模式</label>
                            <div class="checkbox-group">
                                <input type="radio" name="materialMode" value="random" id="randomMaterials" checked>
                                <label for="randomMaterials">随机选择</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="radio" name="materialMode" value="manual" id="manualMaterials">
                                <label for="manualMaterials">手动选择</label>
                            </div>
                        </div>

                        <div class="form-group" id="materialSelection" style="display: none;">
                            <label class="form-label">选择视频素材</label>
                            <div class="multi-select">
                                <div class="multi-select-item">
                                    <input type="checkbox" value="video1">
                                    <span>城市夜景.mp4 (1080p, 30s)</span>
                                </div>
                                <div class="multi-select-item">
                                    <input type="checkbox" value="video2">
                                    <span>自然风光.mp4 (1080p, 45s)</span>
                                </div>
                                <div class="multi-select-item">
                                    <input type="checkbox" value="video3">
                                    <span>室内场景.mp4 (720p, 25s)</span>
                                </div>
                                <div class="multi-select-item">
                                    <input type="checkbox" value="video4">
                                    <span>动态图案.mp4 (1080p, 60s)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 字幕配置 -->
                    <div class="config-section">
                        <h3 class="section-title">字幕设置</h3>
                        
                        <div class="subtitle-config">
                            <div class="form-group">
                                <label class="form-label">字体</label>
                                <select class="form-select" id="subtitleFont">
                                    <option value="arial">Arial</option>
                                    <option value="helvetica">Helvetica</option>
                                    <option value="microsoft-yahei">微软雅黑</option>
                                    <option value="simhei">黑体</option>
                                </select>
                            </div>

                            <div class="subtitle-row">
                                <div class="form-group">
                                    <label class="form-label">字体大小</label>
                                    <input type="number" class="form-input" id="subtitleSize" value="24" min="12" max="60">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">颜色</label>
                                    <input type="color" class="color-input" id="subtitleColor" value="#ffffff">
                                </div>
                            </div>

                            <div class="help-text">字幕将显示在视频垂直居中位置，第一句话用封面替代</div>
                        </div>
                    </div>

                    <!-- 封面模板 -->
                    <div class="config-section">
                        <h3 class="section-title">封面模板</h3>
                        
                        <div class="form-group">
                            <label class="form-label">模板选择</label>
                            <select class="form-select" id="coverTemplate">
                                <option value="random">随机选择</option>
                                <option value="template1">经典模板</option>
                                <option value="template2">现代模板</option>
                                <option value="template3">简约模板</option>
                            </select>
                        </div>
                    </div>

                    <!-- 账号和输出配置 -->
                    <div class="config-section">
                        <h3 class="section-title">账号和输出</h3>
                        
                        <div class="form-group">
                            <label class="form-label">选择账号</label>
                            <div class="multi-select">
                                <div class="multi-select-item">
                                    <input type="checkbox" value="account1" checked>
                                    <span>恐怖故事君</span>
                                </div>
                                <div class="multi-select-item">
                                    <input type="checkbox" value="account2" checked>
                                    <span>搞笑日常</span>
                                </div>
                                <div class="multi-select-item">
                                    <input type="checkbox" value="account3">
                                    <span>情感故事</span>
                                </div>
                                <div class="multi-select-item">
                                    <input type="checkbox" value="account4">
                                    <span>悬疑探案</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">每个账号视频数量</label>
                            <input type="number" class="form-input" id="videosPerAccount" value="1" min="1" max="10">
                        </div>

                        <div class="form-group">
                            <label class="form-label">输出目录</label>
                            <input type="text" class="form-input" id="outputDirectory" value="D:/Videos/Output" placeholder="选择输出目录">
                            <div class="help-text">视频文件名格式：账号名_yyyyMMddHHmmss.mp4</div>
                        </div>
                    </div>

                    <!-- 生成按钮 -->
                    <div class="generate-section">
                        <div class="account-summary" id="accountSummary">
                            <div class="account-row">
                                <span class="account-name">恐怖故事君</span>
                                <span class="video-count">1 个视频</span>
                            </div>
                            <div class="account-row">
                                <span class="account-name">搞笑日常</span>
                                <span class="video-count">1 个视频</span>
                            </div>
                        </div>

                        <button class="btn btn-success btn-large" id="generateBtn" style="width: 100%;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polygon points="5,3 19,12 5,21"/>
                            </svg>
                            开始生成 (共 2 个视频)
                        </button>
                    </div>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="preview-section">
                <div class="preview-header">
                    <h3 class="preview-title">预览区域</h3>
                </div>

                <div class="preview-content">
                    <div class="preview-container" id="previewContainer">
                        <div class="preview-placeholder">
                            <h3>9:16 竖屏预览</h3>
                            <p>1080P 高清输出</p>
                            <p>配置完成后可预览效果</p>
                        </div>
                        
                        <!-- 模拟封面预览 -->
                        <div class="preview-overlay" id="coverPreview" style="display: none;">
                            <div style="margin-bottom: 1rem;">
                                <div style="width: 40px; height: 40px; background: #ddd; border-radius: 50%; margin: 0 auto 0.5rem;"></div>
                                <div style="font-weight: bold; margin-bottom: 0.25rem;">账号名称</div>
                                <div style="font-size: 0.875rem; opacity: 0.8;">故事简述预览</div>
                            </div>
                        </div>
                    </div>

                    <div class="preview-controls">
                        <button class="btn btn-secondary" id="previewCoverBtn">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <circle cx="8.5" cy="8.5" r="1.5"/>
                                <polyline points="21,15 16,10 5,21"/>
                            </svg>
                            预览封面
                        </button>
                        <button class="btn btn-secondary" id="previewSubtitleBtn">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/>
                                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/>
                                <line x1="8" y1="6" x2="16" y2="6"/>
                                <line x1="8" y1="10" x2="16" y2="10"/>
                                <line x1="8" y1="14" x2="13" y2="14"/>
                            </svg>
                            预览字幕
                        </button>
                    </div>

                    <!-- 队列预览 -->
                    <div class="queue-preview">
                        <div class="queue-title">生成队列预览</div>
                        <div class="queue-summary" id="queueSummary">
                            选择了 2 个账号，每个账号生成 1 个视频<br>
                            预计总时长：约 6-8 分钟<br>
                            输出格式：1080P MP4 (9:16)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 语音倍速控制
        const speechSpeedSlider = document.getElementById('speechSpeed');
        const speedDisplay = document.getElementById('speedDisplay');

        speechSpeedSlider.addEventListener('input', function() {
            speedDisplay.textContent = this.value + 'x';
        });

        // 提示词模板切换
        document.getElementById('promptTemplate').addEventListener('change', function() {
            const customGroup = document.getElementById('customPromptGroup');
            if (this.value === 'custom') {
                customGroup.style.display = 'block';
            } else {
                customGroup.style.display = 'none';
            }
        });

        // 素材选择模式切换
        document.querySelectorAll('input[name="materialMode"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const materialSelection = document.getElementById('materialSelection');
                if (this.value === 'manual') {
                    materialSelection.style.display = 'block';
                } else {
                    materialSelection.style.display = 'none';
                }
            });
        });

        // 预览功能
        document.getElementById('previewCoverBtn').addEventListener('click', function() {
            const coverPreview = document.getElementById('coverPreview');
            const placeholder = document.querySelector('.preview-placeholder');
            
            if (coverPreview.style.display === 'none') {
                coverPreview.style.display = 'block';
                placeholder.style.display = 'none';
                this.textContent = '隐藏封面';
            } else {
                coverPreview.style.display = 'none';
                placeholder.style.display = 'block';
                this.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                        <circle cx="8.5" cy="8.5" r="1.5"/>
                        <polyline points="21,15 16,10 5,21"/>
                    </svg>
                    预览封面
                `;
            }
        });

        document.getElementById('previewSubtitleBtn').addEventListener('click', function() {
            alert('字幕预览功能');
        });

        // 账号选择更新
        function updateAccountSummary() {
            const selectedAccounts = document.querySelectorAll('.multi-select input[type="checkbox"]:checked');
            const videosPerAccount = document.getElementById('videosPerAccount').value;
            const accountSummary = document.getElementById('accountSummary');
            const generateBtn = document.getElementById('generateBtn');
            const queueSummary = document.getElementById('queueSummary');
            
            accountSummary.innerHTML = '';
            let totalVideos = 0;
            
            selectedAccounts.forEach(checkbox => {
                if (checkbox.value.startsWith('account')) {
                    const accountName = checkbox.nextElementSibling.textContent;
                    const accountRow = document.createElement('div');
                    accountRow.className = 'account-row';
                    accountRow.innerHTML = `
                        <span class="account-name">${accountName}</span>
                        <span class="video-count">${videosPerAccount} 个视频</span>
                    `;
                    accountSummary.appendChild(accountRow);
                    totalVideos += parseInt(videosPerAccount);
                }
            });
            
            generateBtn.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5,3 19,12 5,21"/>
                </svg>
                开始生成 (共 ${totalVideos} 个视频)
            `;
            
            queueSummary.innerHTML = `
                选择了 ${selectedAccounts.length} 个账号，每个账号生成 ${videosPerAccount} 个视频<br>
                预计总时长：约 ${totalVideos * 3}-${totalVideos * 4} 分钟<br>
                输出格式：1080P MP4 (9:16)
            `;
        }

        // 监听账号选择变化
        document.querySelectorAll('.multi-select input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', updateAccountSummary);
        });

        document.getElementById('videosPerAccount').addEventListener('input', updateAccountSummary);

        // 生成按钮点击
        document.getElementById('generateBtn').addEventListener('click', function() {
            const selectedAccounts = document.querySelectorAll('.multi-select input[type="checkbox"]:checked');
            const videosPerAccount = document.getElementById('videosPerAccount').value;
            
            if (selectedAccounts.length === 0) {
                alert('请至少选择一个账号');
                return;
            }
            
            const totalVideos = selectedAccounts.length * parseInt(videosPerAccount);
            
            if (confirm(`确定要生成 ${totalVideos} 个视频吗？\n\n这将创建 ${totalVideos} 个任务加入到队列中。`)) {
                alert('视频生成任务已添加到队列！\n\n请前往任务队列管理页面查看进度。');
            }
        });

        // 输出目录选择
        document.getElementById('outputDirectory').addEventListener('click', function() {
            // 模拟目录选择对话框
            const newPath = prompt('请输入输出目录路径：', this.value);
            if (newPath) {
                this.value = newPath;
            }
        });

        // 初始化
        updateAccountSummary();
    </script>
</body>
</html>
