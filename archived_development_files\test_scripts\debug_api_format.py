"""
测试后端API实际返回格式
"""

import requests
import json

def test_actual_api_response():
    """测试实际的API响应格式"""
    url = "http://localhost:8000/api/cover-templates"
    
    print("🔍 测试实际API响应格式")
    print("=" * 50)
    
    try:
        response = requests.get(url, timeout=10)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 请求成功!")
            print(f"📋 完整响应结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            print(f"\n🔍 响应分析:")
            print(f"   有 'success' 字段: {'success' in data}")
            print(f"   有 'data' 字段: {'data' in data}")
            print(f"   有 'message' 字段: {'message' in data}")
            
            if 'data' in data:
                data_content = data['data']
                print(f"   data类型: {type(data_content)}")
                
                if isinstance(data_content, dict):
                    print(f"   data是字典，包含字段: {list(data_content.keys())}")
                    if 'templates' in data_content:
                        templates = data_content['templates']
                        print(f"   templates类型: {type(templates)}")
                        print(f"   templates长度: {len(templates) if isinstance(templates, list) else 'N/A'}")
                        
                        if isinstance(templates, list) and templates:
                            print(f"   第一个模板示例:")
                            template = templates[0]
                            print(f"     类型: {type(template)}")
                            if isinstance(template, dict):
                                print(f"     字段: {list(template.keys())}")
                elif isinstance(data_content, list):
                    print(f"   data是数组，长度: {len(data_content)}")
                    
            print(f"\n🎯 前端应该使用的访问路径:")
            if 'data' in data and isinstance(data['data'], dict) and 'templates' in data['data']:
                print(f"   responseData.data.templates")
            elif 'data' in data and isinstance(data['data'], list):
                print(f"   responseData.data")
            else:
                print(f"   需要进一步分析...")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_actual_api_response()
