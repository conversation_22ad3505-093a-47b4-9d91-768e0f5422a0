"""
测试模板列表获取功能
"""

import requests
import json

def test_template_list_api():
    """测试获取模板列表API"""
    url = "http://localhost:8000/api/cover-templates"
    
    print("🔍 测试模板列表获取功能")
    print("=" * 50)
    
    try:
        response = requests.get(url, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取成功!")
            print(f"📋 响应结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查响应格式
            if 'data' in data:
                templates = data['data']
                print(f"\n📦 模板列表:")
                print(f"   总数: {len(templates)}")
                
                if templates:
                    print(f"   第一个模板示例:")
                    template = templates[0]
                    print(f"     ID: {template.get('id', 'N/A')}")
                    print(f"     名称: {template.get('name', 'N/A')}")
                    print(f"     分类: {template.get('category', 'N/A')}")
                    print(f"     描述: {template.get('description', 'N/A')}")
                    print(f"     使用次数: {template.get('usage_count', template.get('usageCount', 'N/A'))}")
                    print(f"     创建时间: {template.get('created_at', template.get('createdAt', 'N/A'))}")
                else:
                    print("   ℹ️ 当前没有模板")
                    
            else:
                print("⚠️ 响应格式可能不正确，缺少data字段")
                
        else:
            print(f"❌ 获取失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_create_then_list():
    """测试创建模板后获取列表"""
    print("\n🔄 测试创建模板后获取列表")
    print("=" * 50)
    
    # 先创建一个模板
    create_url = "http://localhost:8000/api/cover-templates"
    template_data = {
        "name": "测试列表功能模板",
        "category": "测试",
        "description": "用于测试列表获取功能",
        "variables": [],
        "elements": [],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        },
        "is_built_in": False,
        "width": 1920,
        "height": 1080,
        "format": "png"
    }
    
    try:
        # 创建模板
        print("1. 创建测试模板...")
        create_response = requests.post(create_url, json=template_data, timeout=10)
        
        if create_response.status_code == 200:
            create_data = create_response.json()
            template_id = create_data.get('data', {}).get('id')
            print(f"   ✅ 创建成功，ID: {template_id}")
            
            # 获取列表
            print("2. 获取模板列表...")
            list_response = requests.get(create_url, timeout=10)
            
            if list_response.status_code == 200:
                list_data = list_response.json()
                templates = list_data.get('data', [])
                print(f"   ✅ 列表获取成功，共{len(templates)}个模板")
                
                # 查找刚创建的模板
                found = False
                for template in templates:
                    if template.get('id') == template_id:
                        found = True
                        print(f"   ✅ 找到刚创建的模板: {template.get('name')}")
                        break
                
                if not found:
                    print(f"   ⚠️ 未找到刚创建的模板 (ID: {template_id})")
                    
            else:
                print(f"   ❌ 列表获取失败: {list_response.status_code}")
                
            # 清理测试数据
            if template_id:
                print("3. 清理测试模板...")
                delete_response = requests.delete(f"{create_url}/{template_id}")
                if delete_response.status_code == 200:
                    print("   ✅ 测试模板已删除")
                else:
                    print(f"   ⚠️ 删除测试模板失败: {delete_response.status_code}")
                    
        else:
            print(f"   ❌ 创建失败: {create_response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_template_list_api()
    test_create_then_list()
    
    print("\n🎯 测试完成!")
    print("💡 现在前端应该能正确获取和显示模板列表了")
