@echo off
REM Quick build script for development testing

echo Building frontend for EXE testing...
cd frontend

REM Build frontend
call npm run build:production
if errorlevel 1 (
    echo Failed to build frontend
    pause
    exit /b 1
)

REM Copy to backend
if exist "..\backend\frontend_dist" (
    rmdir /s /q "..\backend\frontend_dist"
)
mkdir "..\backend\frontend_dist"
xcopy /E /I /Y "out\*" "..\backend\frontend_dist\"

echo.
echo Frontend built and copied to backend/frontend_dist/
echo You can now test the static file serving by running:
echo   cd backend
echo   set ENVIRONMENT=production
echo   python main.py
echo.
pause
