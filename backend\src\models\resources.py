"""
资源管理数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, Float, DateTime, Text, JSON
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, List, Optional
from . import BaseModel
import uuid

class MusicCategory(BaseModel):
    """音乐分类模型"""
    __tablename__ = "music_categories"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    
    def to_frontend_format(self) -> Dict[str, Any]:
        """转换为前端格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "createdAt": self.created_at.isoformat() if getattr(self, 'created_at', None) else None,
            "updatedAt": self.updated_at.isoformat() if getattr(self, 'updated_at', None) else None
        }

class VideoCategory(BaseModel):
    """视频分类模型"""
    __tablename__ = "video_categories"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    
    def to_frontend_format(self) -> Dict[str, Any]:
        """转换为前端格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "createdAt": self.created_at.isoformat() if getattr(self, 'created_at', None) else None,
            "updatedAt": self.updated_at.isoformat() if getattr(self, 'updated_at', None) else None
        }

class BackgroundMusic(BaseModel):
    """背景音乐模型"""
    __tablename__ = "background_music"
    
    id = Column(String, primary_key=True)
    name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    duration = Column(Float, nullable=False)  # 秒
    category = Column(String(100), nullable=False, default="general")
    tags = Column(JSON, nullable=False, default=list)  # 标签列表
    is_built_in = Column(Boolean, nullable=False, default=False)
    
    # 音频元数据
    file_size = Column(Integer)  # 文件大小(字节)
    format = Column(String(20))  # 音频格式
    bitrate = Column(Integer)   # 比特率
    sample_rate = Column(Integer)  # 采样率
    
    def to_frontend_format(self) -> Dict[str, Any]:
        """转换为前端格式"""
        # 计算文件大小的字符串表示
        size_str = "0 B"
        file_size_value = getattr(self, 'file_size', None)
        if file_size_value:
            if file_size_value < 1024:
                size_str = f"{file_size_value} B"
            elif file_size_value < 1024 * 1024:
                size_str = f"{file_size_value / 1024:.1f} KB"
            elif file_size_value < 1024 * 1024 * 1024:
                size_str = f"{file_size_value / (1024 * 1024):.1f} MB"
            else:
                size_str = f"{file_size_value / (1024 * 1024 * 1024):.1f} GB"
        
        return {
            "id": self.id,
            "name": self.name,
            "duration": self.duration,
            "size": size_str,
            "format": self.format or "unknown",
            "filePath": self.file_path,
            "url": None,  # 播放URL会通过API端点提供
            "category": self.category,
            "tags": getattr(self, 'tags', None) or [],
            "isBuiltIn": self.is_built_in,
            "metadata": {
                "fileSize": file_size_value,
                "bitrate": getattr(self, 'bitrate', None),
                "sampleRate": getattr(self, 'sample_rate', None)
            },
            "createdAt": self.created_at.isoformat() if getattr(self, 'created_at', None) else None,
            "updatedAt": self.updated_at.isoformat() if getattr(self, 'updated_at', None) else None
        }

class VideoMaterial(BaseModel):
    """视频素材模型"""
    __tablename__ = "video_materials"
    
    id = Column(String, primary_key=True)
    name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    duration = Column(Float, nullable=False)  # 秒
    category = Column(String(100), nullable=False, default="general")
    resolution = Column(String(20), nullable=False)  # 如: 1920x1080
    tags = Column(JSON, nullable=False, default=list)
    is_built_in = Column(Boolean, nullable=False, default=False)
    
    # 视频元数据
    file_size = Column(Integer)
    format = Column(String(20))
    frame_rate = Column(Float)  # 帧率
    bitrate = Column(Integer)   # 比特率
    thumbnail_path = Column(String(500))  # 缩略图路径
    
    def to_frontend_format(self) -> Dict[str, Any]:
        """转换为前端格式"""
        # 计算文件大小的字符串表示
        size_str = "0 B"
        file_size_value = getattr(self, 'file_size', None)
        if file_size_value:
            if file_size_value < 1024:
                size_str = f"{file_size_value} B"
            elif file_size_value < 1024 * 1024:
                size_str = f"{file_size_value / 1024:.1f} KB"
            elif file_size_value < 1024 * 1024 * 1024:
                size_str = f"{file_size_value / (1024 * 1024):.1f} MB"
            else:
                size_str = f"{file_size_value / (1024 * 1024 * 1024):.1f} GB"
        
        # 格式化时长为前端格式 (mm:ss)
        duration_str = None
        duration_value = getattr(self, 'duration', None)
        if duration_value:
            minutes = int(duration_value // 60)
            seconds = int(duration_value % 60)
            duration_str = f"{minutes}:{seconds:02d}"
        
        # 解析分辨率
        dimensions = {"width": 0, "height": 0}
        resolution_value = getattr(self, 'resolution', None)
        if resolution_value:
            try:
                width, height = resolution_value.split('x')
                dimensions = {"width": int(width), "height": int(height)}
            except:
                pass
        
        # 计算宽高比
        aspect_ratio = "unknown"
        if dimensions["width"] > 0 and dimensions["height"] > 0:
            def gcd(a, b):
                while b:
                    a, b = b, a % b
                return a
            divisor = gcd(dimensions["width"], dimensions["height"])
            aspect_ratio = f"{dimensions['width']//divisor}:{dimensions['height']//divisor}"
        
        # 确定文件类型
        format_value = getattr(self, 'format', None)
        format_lower = (format_value or "").lower()
        if format_lower in ['mp4', 'mov', 'avi', 'webm', 'mkv']:
            file_type = 'video'
        elif format_lower in ['gif']:
            file_type = 'gif' 
        else:
            file_type = 'image'
        
        return {
            "id": self.id,
            "name": self.name,
            "type": file_type,
            "format": (format_value or "unknown").upper(),
            "size": size_str,
            "duration": duration_str,  # 前端格式的时长字符串
            "dimensions": dimensions,
            "aspectRatio": aspect_ratio,
            "path": self.file_path,
            "url": f"/api/video-materials/file/{self.id}",  # HTTP端点URL
            "thumbnailUrl": f"/api/video-materials/thumbnail/{self.id}" if getattr(self, 'thumbnail_path', None) else None,  # HTTP端点URL
            "thumbnailPath": getattr(self, 'thumbnail_path', None),
            "filePath": self.file_path,  # 兼容前端
            "category": self.category,
            "tags": getattr(self, 'tags', None) or [],
            "isBuiltIn": self.is_built_in,
            "createdAt": self.created_at.isoformat() if getattr(self, 'created_at', None) else None,
            "lastModified": self.updated_at.isoformat() if getattr(self, 'updated_at', None) else None,
            "metadata": {
                "fileSize": file_size_value,
                "format": format_value,
                "frameRate": getattr(self, 'frame_rate', None),
                "bitrate": getattr(self, 'bitrate', None),
                "thumbnailPath": getattr(self, 'thumbnail_path', None)
            }
        }

class Prompt(BaseModel):
    """提示词模板模型"""
    __tablename__ = "prompts"
    
    id = Column(String, primary_key=True)
    name = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    category = Column(String(100), nullable=False, default="general")
    variables = Column(JSON, nullable=False, default=list)  # 变量列表
    is_built_in = Column(Boolean, nullable=False, default=False)
    
    # 提示词元数据
    description = Column(Text)  # 描述
    example_output = Column(Text)  # 示例输出
    usage_count = Column(Integer, default=0)  # 使用次数
    
    def to_frontend_format(self) -> Dict[str, Any]:
        """转换为前端格式"""
        return {
            "id": self.id,
            "name": self.name,
            "content": self.content,
            "category": self.category,
            "variables": getattr(self, 'variables', None) or [],
            "isBuiltIn": self.is_built_in,
            "metadata": {
                "description": self.description,
                "exampleOutput": self.example_output,
                "usageCount": self.usage_count
            },
            "createdAt": self.created_at.isoformat() if getattr(self, 'created_at', None) else None,
            "updatedAt": self.updated_at.isoformat() if getattr(self, 'updated_at', None) else None
        }

class CoverTemplate(BaseModel):
    """封面模板模型"""
    __tablename__ = "cover_templates"
    
    id = Column(String, primary_key=True)
    name = Column(String(255), nullable=False)
    preview_path = Column(String(500), nullable=False)  # 预览图路径
    template_path = Column(String(500), nullable=False)  # 模板文件路径
    variables = Column(JSON, nullable=False, default=list)  # 可变元素列表
    is_built_in = Column(Boolean, nullable=False, default=False)
    
    # 模板元数据
    description = Column(Text)
    category = Column(String(100), default="general")
    tags = Column(JSON, default=list)
    usage_count = Column(Integer, default=0)
    
    # 模板规格
    width = Column(Integer)  # 宽度
    height = Column(Integer)  # 高度
    format = Column(String(20))  # 输出格式
    
    # 模板内容 - 存储画布元素和背景信息
    elements = Column(JSON, nullable=False, default=list)  # 画布元素列表
    background = Column(JSON, nullable=True)  # 背景配置
    
    def to_frontend_format(self) -> Dict[str, Any]:
        """转换为前端格式"""
        variables_list = getattr(self, 'variables', None) or []
        elements_list = getattr(self, 'elements', None) or []
        background_config = getattr(self, 'background', None) or {
            "type": "gradient",
            "value": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        }
        
        # 检查是否为HTML模板
        template_path = getattr(self, 'template_path', '')
        is_html_template = template_path.endswith('.html')
        
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "category": self.category or "general",
            "width": self.width or 1920,
            "height": self.height or 1080,
            "background": background_config,
            "elements": elements_list,  # 返回实际的元素数据
            "variables": variables_list,
            "thumbnailPath": self.preview_path,
            "thumbnailUrl": self.preview_path,
            "isPublic": not bool(self.is_built_in),  # 临时逻辑，内置模板为公开
            "templateType": "html" if is_html_template else "canvas",  # 模板类型
            "templatePath": self.template_path,
            "createdAt": self.created_at.isoformat() if getattr(self, 'created_at', None) else None,
            "updatedAt": self.updated_at.isoformat() if getattr(self, 'updated_at', None) else None,
            "elementCount": len(elements_list),
            "variableCount": len(variables_list),
            "hasVariables": len(variables_list) > 0 or any(
                elem.get('variableBinding', {}).get('enabled', False) for elem in elements_list
            ),
            "canvasSize": f"{self.width or 1920}×{self.height or 1080}"
        }
