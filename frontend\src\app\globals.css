@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom global styles */
body {
  font-family: system-ui, -apple-system, sans-serif;
}

/* Custom utilities */
.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* 弹窗动画 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 全局Loading动画 */
@keyframes shimmer {
  0% { 
    transform: translateX(-100%); 
  }
  100% { 
    transform: translateX(100%); 
  }
}

@keyframes progressPulse {
  0%, 100% { 
    opacity: 0.4; 
  }
  50% { 
    opacity: 0.8; 
  }
}

@keyframes loadingBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 应用动画类 */
.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.animate-progress-pulse {
  animation: progressPulse 1.5s ease-in-out infinite;
}

.animate-loading-bounce {
  animation: loadingBounce 1.4s ease-in-out infinite;
}

/* 确保弹窗完全居中 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 主内容区域内的弹窗（排除侧边栏） */
.modal-overlay-main-content {
  position: fixed;
  left: 260px; /* 侧边栏宽度 */
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 弹窗内容容器 */
.modal-content {
  max-width: calc(100vw - 320px); /* 桌面端：排除侧边栏和边距 */
}

/* 响应式：在小屏幕上全屏显示 */
@media (max-width: 768px) {
  .modal-overlay-main-content {
    left: 0;
  }
  
  .modal-content {
    max-width: 95vw; /* 移动端：使用95vw */
  }
}
