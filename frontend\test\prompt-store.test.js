#!/usr/bin/env node
/**
 * 提示词管理功能测试脚本
 */

import { usePromptStore } from '../src/store/promptStore.js';

console.log('🧪 开始测试提示词管理功能...\n');

// 模拟测试环境
const { getState, setState } = usePromptStore;

try {
  // 测试基本功能
  console.log('✅ 测试 1: 基本数据结构');
  const initialState = getState();
  console.log(`   - 初始提示词数量: ${initialState.prompts.length}`);
  console.log(`   - 当前分类: ${initialState.currentCategory}`);
  
  // 测试统计功能
  console.log('\n✅ 测试 2: 统计功能');
  const stats = initialState.getStats();
  console.log(`   - 总数: ${stats.total}`);
  console.log(`   - 系统/故事/旁白/描述: ${stats.systemCount}/${stats.storyCount}/${stats.narrationCount}/${stats.descriptionCount}`);
  console.log(`   - 标签数: ${stats.tagCount}`);
  console.log(`   - 总测试次数: ${stats.totalTestCount}`);
  
  // 测试过滤功能
  console.log('\n✅ 测试 3: 过滤功能');
  const allPrompts = initialState.getFilteredPrompts();
  console.log(`   - 全部提示词: ${allPrompts.length}`);
  
  // 切换到故事分类
  initialState.setCurrentCategory('story');
  const storyPrompts = initialState.getFilteredPrompts();
  console.log(`   - 故事分类: ${storyPrompts.length}`);
  
  // 测试标签功能
  console.log('\n✅ 测试 4: 标签功能');
  const allTags = initialState.getAllTags();
  console.log(`   - 所有标签: ${allTags.join(', ')}`);
  
  console.log('\n🎉 所有测试通过！提示词管理功能正常工作。');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  process.exit(1);
}
