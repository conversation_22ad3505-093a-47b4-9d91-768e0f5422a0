#!/usr/bin/env python3
"""
验证分类关联修复效果的测试脚本
"""

import requests
import tempfile
import os
from pathlib import Path

def create_test_video():
    """创建测试视频文件"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
    temp_file.write(b'fake video content for category testing')
    temp_file.close()
    return temp_file.name

def test_category_fix():
    """测试分类修复"""
    API_BASE = "http://localhost:8000"
    UPLOAD_URL = f"{API_BASE}/api/video-materials/upload"
    QUERY_URL = f"{API_BASE}/api/video-materials"
    
    test_categories = ["action", "comedy", "drama", "test-special-category"]
    uploaded_ids = []
    
    print("🧪 开始测试分类关联修复效果...")
    
    for category in test_categories:
        test_file = create_test_video()
        
        try:
            print(f"\n📤 测试上传到分类: '{category}'")
            
            with open(test_file, "rb") as f:
                files = {"file": (f"test_{category}.mp4", f, "video/mp4")}
                data = {
                    "category": category,
                    "tags": "test,category-fix"
                }
                
                response = requests.post(UPLOAD_URL, files=files, data=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    if "data" in result:
                        uploaded_id = result["data"]["id"]
                        uploaded_ids.append(uploaded_id)
                        print(f"✅ 上传成功: ID={uploaded_id}")
                    else:
                        print(f"⚠️ 上传成功但响应格式异常: {result}")
                else:
                    print(f"❌ 上传失败: {response.status_code} - {response.text}")
        
        except Exception as e:
            print(f"❌ 上传异常: {e}")
        
        finally:
            if os.path.exists(test_file):
                os.unlink(test_file)
    
    # 验证查询结果
    print(f"\n🔍 验证查询结果...")
    
    try:
        response = requests.get(QUERY_URL, timeout=10)
        if response.status_code == 200:
            materials = response.json()
            print(f"📊 查询到 {len(materials)} 个素材")
            
            # 检查我们上传的文件
            test_materials = [m for m in materials if m.get('id') in uploaded_ids]
            
            print(f"\n📋 测试结果:")
            for material in test_materials:
                expected_category = None
                for cat in test_categories:
                    if f"test_{cat}" in material.get('name', ''):
                        expected_category = cat
                        break
                
                actual_category = material.get('category')
                status = "✅" if actual_category == expected_category else "❌"
                
                print(f"{status} {material.get('name')}: 期望='{expected_category}', 实际='{actual_category}'")
            
            return len([m for m in test_materials if m.get('category') != 'general']) > 0
            
        else:
            print(f"❌ 查询失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        return False

def check_server():
    """检查服务器状态"""
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    if not check_server():
        print("❌ 后端服务器未运行，请先启动服务器")
        print("运行: cd backend && python main.py")
        exit(1)
    
    success = test_category_fix()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 分类关联修复验证成功！")
        print("✅ 上传的文件正确关联到指定分类")
    else:
        print("⚠️ 分类关联可能仍有问题")
        print("请检查后端日志和前端控制台")
    print(f"{'='*50}")
