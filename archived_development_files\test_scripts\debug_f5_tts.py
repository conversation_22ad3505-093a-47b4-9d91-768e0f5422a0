#!/usr/bin/env python3
"""
调试F5-TTS API调用
"""

import requests
import json

def test_f5_tts_api_directly():
    """直接测试F5-TTS音色测试API并显示详细错误"""
    
    BASE_URL = "http://localhost:8000"
    voice_id = "7eb2cf47-056f-4d3a-8061-0725c0caf965"
    
    print("🔍 直接测试F5-TTS音色测试API...")
    
    try:
        # 准备测试数据
        form_data = {
            'test_text': '这是一个简短的测试。'
        }
        
        print(f"📤 发送请求到: {BASE_URL}/api/f5-tts-voices/{voice_id}/test")
        print(f"📝 测试文本: {form_data['test_text']}")
        
        # 发送测试请求
        response = requests.post(
            f"{BASE_URL}/api/f5-tts-voices/{voice_id}/test",
            data=form_data,
            timeout=60  # 增加超时时间
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📄 响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            # 如果有错误信息，显示详细信息
            if not response_data.get('success', True):
                error_msg = response_data.get('message', '未知错误')
                print(f"\n❌ 错误详情: {error_msg}")
                
                # 检查是否是配置问题
                if "F5-TTS服务端点未配置" in error_msg:
                    print("💡 解决方案: 请在设置页面配置F5-TTS服务端点")
                elif "无法连接到F5-TTS服务" in error_msg:
                    print("💡 解决方案: 请检查F5-TTS服务是否正常运行")
                elif "参考音频文件" in error_msg:
                    print("💡 解决方案: 请检查音色的参考音频文件是否存在")
                else:
                    print("💡 建议: 检查后端日志获取更详细的错误信息")
            else:
                print("✅ API调用成功!")
                
        except json.JSONDecodeError:
            print(f"❌ 响应不是有效的JSON:")
            print(response.text)
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，F5-TTS服务可能响应较慢")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请检查服务是否运行")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def check_voice_details():
    """检查音色详细信息"""
    
    BASE_URL = "http://localhost:8000"
    voice_id = "7eb2cf47-056f-4d3a-8061-0725c0caf965"
    
    print("\n🔍 检查音色详细信息...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/f5-tts-voices/{voice_id}")
        
        if response.status_code == 200:
            voice_data = response.json()
            print("✅ 音色信息:")
            print(json.dumps(voice_data, indent=2, ensure_ascii=False))
            
            # 检查关键字段
            if voice_data.get('data'):
                voice = voice_data['data']
                ref_audio_path = voice.get('ref_audio_path')
                ref_text = voice.get('ref_text')
                
                print(f"\n📋 关键信息:")
                print(f"   参考音频路径: {ref_audio_path}")
                print(f"   参考文本: {ref_text}")
                
                # 检查文件是否存在
                if ref_audio_path:
                    import os
                    if os.path.exists(ref_audio_path):
                        file_size = os.path.getsize(ref_audio_path)
                        print(f"   ✅ 参考音频文件存在，大小: {file_size} 字节")
                    else:
                        print(f"   ❌ 参考音频文件不存在: {ref_audio_path}")
                        
        else:
            print(f"❌ 获取音色信息失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ 检查音色信息失败: {e}")

if __name__ == "__main__":
    print("🧪 F5-TTS API调试工具\n")
    
    # 检查音色详细信息
    check_voice_details()
    
    # 直接测试API
    test_f5_tts_api_directly()
    
    print("\n📝 调试完成！")
    print("如果仍有问题，请检查:")
    print("1. F5-TTS服务是否正常运行")
    print("2. 服务端点配置是否正确")
    print("3. 音色的参考音频文件是否存在")
    print("4. 后端日志中的详细错误信息")
