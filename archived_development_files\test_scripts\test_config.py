#!/usr/bin/env python3
"""
测试 EXE 配置和路径解析
运行此脚本以验证所有配置是否正确设置
"""

import sys
import os
from pathlib import Path

# 添加 backend 到 Python 路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def test_config():
    """测试配置系统"""
    print("=" * 60)
    print("Reddit Story Video Generator - 配置测试")
    print("=" * 60)
    print()
    
    try:
        # 导入配置
        from src.core.config import get_settings, get_app_base_dir
        
        print("✅ 配置模块导入成功")
        
        # 测试基础目录检测
        base_dir = get_app_base_dir()
        print(f"📁 应用基础目录: {base_dir}")
        print(f"   是否为 EXE 环境: {getattr(sys, 'frozen', False)}")
        print()
        
        # 测试设置加载
        settings = get_settings()
        print("✅ 设置加载成功")
        print(f"   环境: {settings.environment}")
        print(f"   调试模式: {settings.debug}")
        print(f"   API 主机: {settings.api_host}")
        print(f"   API 端口: {settings.api_port}")
        print()
        
        # 测试路径解析
        print("📂 路径解析测试:")
        print(f"   数据库URL: {settings.resolved_database_url}")
        print(f"   上传目录: {settings.resolved_upload_dir}")
        print(f"   前端静态目录: {settings.resolved_frontend_static_dir}")
        print(f"   FFmpeg 路径: {settings.resolved_ffmpeg_path}")
        print()
        
        # 测试 CORS 配置
        print("🌐 CORS 配置:")
        print(f"   允许的源: {settings.cors_origins_list}")
        print(f"   允许凭据: {settings.cors_allow_credentials}")
        print()
        
        # 检查关键文件和目录
        print("📋 文件和目录检查:")
        
        checks = [
            ("配置文件 .env", backend_dir / ".env"),
            ("生产配置 .env.production", backend_dir / ".env.production"),
            ("主程序 main.py", backend_dir / "main.py"),
            ("构建规格 build.spec", backend_dir / "build.spec"),
            ("前端构建目录", settings.resolved_frontend_static_dir),
            ("工具目录", backend_dir / "tools"),
            ("模板目录", backend_dir / "templates"),
        ]
        
        for name, path in checks:
            if path.exists():
                print(f"   ✅ {name}: {path}")
            else:
                print(f"   ❌ {name}: {path} (不存在)")
        
        print()
        
        # 测试目录创建
        print("🔧 测试目录创建...")
        try:
            settings.ensure_directories()
            print("   ✅ 目录创建成功")
        except Exception as e:
            print(f"   ❌ 目录创建失败: {e}")
        
        print()
        print("=" * 60)
        print("配置测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_frontend_build():
    """检查前端构建"""
    print("\n🎨 前端构建检查:")
    
    frontend_dir = Path(__file__).parent / "frontend"
    frontend_dist = Path(__file__).parent / "backend" / "frontend_dist"
    
    checks = [
        ("前端目录", frontend_dir),
        ("package.json", frontend_dir / "package.json"),
        ("next.config.js", frontend_dir / "next.config.js"),
        ("前端生产配置", frontend_dir / ".env.production"),
        ("前端构建输出", frontend_dist),
    ]
    
    for name, path in checks:
        if path.exists():
            print(f"   ✅ {name}: {path}")
        else:
            print(f"   ❌ {name}: {path} (不存在)")
    
    if frontend_dist.exists():
        files = list(frontend_dist.rglob("*"))
        print(f"   📁 构建文件数量: {len(files)}")
        
        important_files = ["index.html", "_next"]
        for file_name in important_files:
            if any(file_name in str(f) for f in files):
                print(f"   ✅ 包含 {file_name}")
            else:
                print(f"   ❌ 缺少 {file_name}")


def test_build_scripts():
    """检查构建脚本"""
    print("\n🔨 构建脚本检查:")
    
    project_root = Path(__file__).parent
    scripts = [
        ("主构建脚本", project_root / "build_exe.bat"),
        ("前端构建脚本", project_root / "build_frontend.bat"),
        ("生产测试脚本", project_root / "test_production.bat"),
        ("部署指南", project_root / "EXE_DEPLOYMENT_GUIDE.md"),
    ]
    
    for name, path in scripts:
        if path.exists():
            print(f"   ✅ {name}: {path}")
        else:
            print(f"   ❌ {name}: {path} (不存在)")


if __name__ == "__main__":
    print("开始配置测试...")
    print()
    
    success = test_config()
    test_frontend_build()
    test_build_scripts()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！配置看起来正常。")
        print("\n下一步:")
        print("1. 运行 'build_frontend.bat' 构建前端")
        print("2. 运行 'test_production.bat' 测试生产配置")
        print("3. 运行 'build_exe.bat' 构建完整的 EXE")
    else:
        print("❌ 测试失败，请检查配置。")
    print("=" * 60)
