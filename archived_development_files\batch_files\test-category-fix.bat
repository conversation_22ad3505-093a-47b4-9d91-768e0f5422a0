@echo off
REM 测试视频素材分类关联修复

echo 🔧 启动视频素材分类关联功能测试...

REM 启动后端服务器
echo 启动后端服务器...
cd backend
start "Backend Server" cmd /k "python main.py"

REM 等待服务器启动
echo 等待后端服务器启动...
timeout /t 5 /nobreak > nul

REM 启动前端开发服务器
echo 启动前端开发服务器...
cd ..\frontend
start "Frontend Server" cmd /k "npm run dev"

REM 等待前端服务器启动
echo 等待前端服务器启动...
timeout /t 10 /nobreak > nul

echo 📋 测试指南:
echo.
echo 1. 打开浏览器访问 http://localhost:3000/videos
echo 2. 点击"批量导入"按钮
echo 3. 在分类选择框中选择一个非默认分类（如创建一个新分类）
echo 4. 点击"选择文件"并上传一个测试视频文件
echo 5. 查看开发者工具控制台的调试日志
echo 6. 验证上传后的文件是否显示正确的分类
echo.
echo 🔍 调试信息:
echo - 前端会输出详细的分类选择和参数传递日志
echo - 后端会在终端显示接收到的参数
echo - 上传完成后检查素材列表中的分类标签
echo.
echo 按任意键关闭测试环境...
pause > nul

REM 清理进程
taskkill /f /im node.exe > nul 2>&1
taskkill /f /im python.exe > nul 2>&1

echo 测试环境已关闭
