<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页仪表板 - Reddit故事视频生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .sidebar-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        /* 统计卡片 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
        }

        .stat-card.primary::before { background: var(--primary-color); }
        .stat-card.success::before { background: var(--success-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.danger::before { background: var(--danger-color); }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

        .stat-icon.primary { background: var(--primary-light); color: var(--primary-color); }
        .stat-icon.success { background: #dcfce7; color: var(--success-color); }
        .stat-icon.warning { background: #fef3c7; color: var(--warning-color); }
        .stat-icon.danger { background: #fee2e2; color: var(--danger-color); }

        .stat-info {
            flex: 1;
        }

        .stat-title {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-change {
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }

        .stat-change.positive {
            color: var(--success-color);
        }

        .stat-change.negative {
            color: var(--danger-color);
        }

        /* 面板组件 */
        .panel {
            background: var(--bg-primary);
            border-radius: 8px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .panel-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: between;
        }

        .panel-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .panel-action {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .panel-action:hover {
            color: var(--primary-hover);
        }

        .panel-content {
            padding: 1.5rem;
        }

        /* 快速操作 */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .action-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: var(--text-primary);
        }

        .action-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
            border-color: var(--primary-color);
        }

        .action-icon {
            width: 3rem;
            height: 3rem;
            margin: 0 auto 1rem;
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .action-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* 最近任务 */
        .task-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .task-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .task-status-dot.running {
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        .task-status-dot.completed {
            background: var(--primary-color);
        }

        .task-status-dot.failed {
            background: var(--danger-color);
        }

        .task-status-dot.pending {
            background: var(--warning-color);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .task-info {
            flex: 1;
        }

        .task-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .task-meta {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .task-time {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-align: right;
        }

        /* 系统状态 */
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .status-item {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 0.75rem;
            background: var(--bg-secondary);
            border-radius: 6px;
        }

        .status-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-online {
            color: var(--success-color);
        }

        .status-online .status-dot {
            background: var(--success-color);
        }

        .status-offline {
            color: var(--danger-color);
        }

        .status-offline .status-dot {
            background: var(--danger-color);
        }

        /* 图表容器 */
        .chart-container {
            height: 250px;
            background: var(--bg-secondary);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            border: 2px dashed var(--border-color);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }

        .empty-icon {
            width: 3rem;
            height: 3rem;
            margin: 0 auto 1rem;
            opacity: 0.5;
        }

        .empty-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .stats-overview {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">系统概览</h1>
            <p class="page-subtitle">欢迎使用Reddit故事视频生成器，这里是您的工作台</p>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card primary">
                <div class="stat-header">
                    <div class="stat-icon primary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                            <line x1="12" y1="22.08" x2="12" y2="12"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-title">今日生成视频</div>
                        <div class="stat-value">28</div>
                        <div class="stat-change positive">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"/>
                                <polyline points="17,6 23,6 23,12"/>
                            </svg>
                            +12% 比昨天
                        </div>
                    </div>
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-header">
                    <div class="stat-icon success">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9,11 12,14 22,4"/>
                            <path d="m21,12v7a2,2 0 0,1-2,2H5a2,2 0 0,1-2-2V5a2,2 0 0,1,2-2h11"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-title">成功率</div>
                        <div class="stat-value">94.2%</div>
                        <div class="stat-change positive">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"/>
                                <polyline points="17,6 23,6 23,12"/>
                            </svg>
                            +2.1% 比上周
                        </div>
                    </div>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-header">
                    <div class="stat-icon warning">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-title">队列任务</div>
                        <div class="stat-value">7</div>
                        <div class="stat-change">等待处理</div>
                    </div>
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-header">
                    <div class="stat-icon danger">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m21.73,18-8-14a2,2 0 0,0-3.48,0l-8,14A2,2 0 0,0,4,21H20a2,2 0 0,0,1.73-3Z"/>
                            <line x1="12" y1="9" x2="12" y2="13"/>
                            <line x1="12" y1="17" x2="12.01" y2="17"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-title">失败任务</div>
                        <div class="stat-value">2</div>
                        <div class="stat-change negative">需要处理</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="dashboard-grid">
            <!-- 左侧主内容 -->
            <div class="main-content">
                <!-- 快速操作 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title">快速操作</h3>
                    </div>
                    <div class="panel-content">
                        <div class="quick-actions">
                            <a href="8-video-generator.html" class="action-card">
                                <div class="action-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polygon points="5,3 19,12 5,21"/>
                                    </svg>
                                </div>
                                <div class="action-title">生成视频</div>
                                <div class="action-desc">创建新的故事视频</div>
                            </a>

                            <a href="5-prompts.html" class="action-card">
                                <div class="action-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                        <polyline points="10,9 9,9 8,9"/>
                                    </svg>
                                </div>
                                <div class="action-title">管理提示词</div>
                                <div class="action-desc">编辑故事生成模板</div>
                            </a>

                            <a href="4-video-materials.html" class="action-card">
                                <div class="action-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                        <circle cx="8.5" cy="8.5" r="1.5"/>
                                        <polyline points="21,15 16,10 5,21"/>
                                    </svg>
                                </div>
                                <div class="action-title">视频素材</div>
                                <div class="action-desc">管理背景视频文件</div>
                            </a>

                            <a href="6-accounts-fixed.html" class="action-card">
                                <div class="action-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                </div>
                                <div class="action-title">账号管理</div>
                                <div class="action-desc">管理频道账号信息</div>
                            </a>

                            <a href="2-settings.html" class="action-card">
                                <div class="action-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="3"/>
                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                    </svg>
                                </div>
                                <div class="action-title">系统设置</div>
                                <div class="action-desc">配置API和服务</div>
                            </a>

                            <a href="9-task-queue.html" class="action-card">
                                <div class="action-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                                        <line x1="9" y1="12" x2="15" y2="12"/>
                                        <line x1="9" y1="16" x2="15" y2="16"/>
                                    </svg>
                                </div>
                                <div class="action-title">任务队列</div>
                                <div class="action-desc">查看生成进度</div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 最近任务 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title">最近任务</h3>
                        <a href="9-task-queue.html" class="panel-action">
                            查看全部
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </a>
                    </div>
                    <div class="panel-content">
                        <div class="task-list">
                            <div class="task-item">
                                <div class="task-status-dot running"></div>
                                <div class="task-info">
                                    <div class="task-title">Reddit惊悚故事：午夜敲门声</div>
                                    <div class="task-meta">恐怖故事君 • 720p • 进度 75%</div>
                                </div>
                                <div class="task-time">
                                    <div>15:30</div>
                                    <div>执行中</div>
                                </div>
                            </div>

                            <div class="task-item">
                                <div class="task-status-dot completed"></div>
                                <div class="task-info">
                                    <div class="task-title">Reddit恐怖故事合集：深夜回家</div>
                                    <div class="task-meta">恐怖故事君 • 1080p • 已完成</div>
                                </div>
                                <div class="task-time">
                                    <div>13:45</div>
                                    <div>完成</div>
                                </div>
                            </div>

                            <div class="task-item">
                                <div class="task-status-dot failed"></div>
                                <div class="task-info">
                                    <div class="task-title">Reddit情感故事：失恋的夜晚</div>
                                    <div class="task-meta">情感故事 • 1080p • TTS API限制</div>
                                </div>
                                <div class="task-time">
                                    <div>14:55</div>
                                    <div>失败</div>
                                </div>
                            </div>

                            <div class="task-item">
                                <div class="task-status-dot pending"></div>
                                <div class="task-info">
                                    <div class="task-title">Reddit奇怪故事：神秘邻居</div>
                                    <div class="task-meta">悬疑探案 • 720p • 队列第3位</div>
                                </div>
                                <div class="task-time">
                                    <div>16:20</div>
                                    <div>等待中</div>
                                </div>
                            </div>

                            <div class="task-item">
                                <div class="task-status-dot completed"></div>
                                <div class="task-info">
                                    <div class="task-title">Reddit搞笑故事：学校糗事</div>
                                    <div class="task-meta">搞笑日常 • 720p • 已完成</div>
                                </div>
                                <div class="task-time">
                                    <div>12:30</div>
                                    <div>完成</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧边栏 -->
            <div class="sidebar-content">
                <!-- 系统状态 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title">系统状态</h3>
                    </div>
                    <div class="panel-content">
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="status-label">TTS服务</span>
                                <div class="status-indicator status-online">
                                    <span class="status-dot"></span>
                                    在线
                                </div>
                            </div>

                            <div class="status-item">
                                <span class="status-label">大模型</span>
                                <div class="status-indicator status-online">
                                    <span class="status-dot"></span>
                                    在线
                                </div>
                            </div>

                            <div class="status-item">
                                <span class="status-label">数据库</span>
                                <div class="status-indicator status-online">
                                    <span class="status-dot"></span>
                                    正常
                                </div>
                            </div>

                            <div class="status-item">
                                <span class="status-label">队列</span>
                                <div class="status-indicator status-online">
                                    <span class="status-dot"></span>
                                    运行中
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 资源统计 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title">资源统计</h3>
                    </div>
                    <div class="panel-content">
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="status-label">视频素材</span>
                                <span style="color: var(--text-primary); font-weight: 500;">142 个</span>
                            </div>

                            <div class="status-item">
                                <span class="status-label">背景音乐</span>
                                <span style="color: var(--text-primary); font-weight: 500;">28 个</span>
                            </div>

                            <div class="status-item">
                                <span class="status-label">提示词模板</span>
                                <span style="color: var(--text-primary); font-weight: 500;">15 个</span>
                            </div>

                            <div class="status-item">
                                <span class="status-label">封面模板</span>
                                <span style="color: var(--text-primary); font-weight: 500;">8 个</span>
                            </div>

                            <div class="status-item">
                                <span class="status-label">管理账号</span>
                                <span style="color: var(--text-primary); font-weight: 500;">12 个</span>
                            </div>

                            <div class="status-item">
                                <span class="status-label">存储空间</span>
                                <span style="color: var(--warning-color); font-weight: 500;">68% 已用</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 生成统计图表 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title">本周生成趋势</h3>
                    </div>
                    <div class="panel-content">
                        <div class="chart-container">
                            <div style="text-align: center;">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                                    <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"/>
                                    <polyline points="17,6 23,6 23,12"/>
                                </svg>
                                <div style="margin-top: 0.5rem;">图表数据正在加载...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟实时数据更新
        function updateStats() {
            // 模拟统计数据的动态更新
            const statValues = document.querySelectorAll('.stat-value');
            
            // 随机更新数据（演示用）
            setInterval(() => {
                const todayVideos = document.querySelector('.stat-card.primary .stat-value');
                const currentValue = parseInt(todayVideos.textContent);
                
                // 模拟新视频生成
                if (Math.random() > 0.7) {
                    todayVideos.textContent = currentValue + 1;
                }
            }, 10000);
        }

        // 模拟任务状态更新
        function updateTaskStatus() {
            const runningTasks = document.querySelectorAll('.task-status-dot.running');
            
            runningTasks.forEach(dot => {
                // 模拟任务完成
                if (Math.random() > 0.9) {
                    dot.classList.remove('running');
                    dot.classList.add('completed');
                    
                    const taskItem = dot.closest('.task-item');
                    const metaElement = taskItem.querySelector('.task-meta');
                    metaElement.textContent = metaElement.textContent.replace('进度 75%', '已完成');
                    
                    const timeElement = taskItem.querySelector('.task-time div:last-child');
                    timeElement.textContent = '完成';
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            
            // 定时更新任务状态
            setInterval(updateTaskStatus, 5000);
            
            // 添加快速操作卡片的点击动画
            document.querySelectorAll('.action-card').forEach(card => {
                card.addEventListener('click', function(e) {
                    // 简单的点击反馈动画
                    this.style.transform = 'translateY(-1px) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });

        // 显示当前时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
            
            // 可以在页面上显示当前时间
            console.log('当前时间:', timeString);
        }

        setInterval(updateTime, 1000);
    </script>
</body>
</html>
