#!/usr/bin/env python3
"""
检查API路由注册情况
"""

import sys
from pathlib import Path

# 添加backend目录到路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def check_routes():
    """检查API路由注册"""
    try:
        from main import app
        
        print('🔍 Checking API route registration...')
        
        # 检查路由是否正确注册
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        print(f'📋 Found {len(routes)} total routes')
        
        # 过滤API路由
        api_routes = [route for route in routes if '/api/v1' in route]
        
        print(f'\n🎯 API v1 routes ({len(api_routes)} found):')
        for route in sorted(api_routes):
            print(f'  ✅ {route}')
        
        print(f'\n🔍 Expected API endpoints:')
        expected = [
            '/api/v1/health',
            '/api/v1/settings',
            '/api/v1/background-music',
            '/api/v1/video-materials', 
            '/api/v1/prompts',
            '/api/v1/accounts',
            '/api/v1/cover-templates'
        ]
        
        missing = []
        found = []
        
        for endpoint in expected:
            # 检查是否有匹配的路由
            matches = [route for route in api_routes if route.startswith(endpoint)]
            if matches:
                found.append(endpoint)
                print(f'  ✅ {endpoint} - FOUND')
            else:
                missing.append(endpoint)
                print(f'  ❌ {endpoint} - MISSING')
        
        print(f'\n📊 Summary:')
        print(f'  Found: {len(found)}/{len(expected)}')
        print(f'  Missing: {len(missing)}')
        
        if missing:
            print(f'\n⚠️ Missing endpoints: {missing}')
            return False
        else:
            print(f'\n🎉 All expected endpoints are registered!')
            return True
            
    except Exception as e:
        print(f'❌ Error checking routes: {e}')
        return False

if __name__ == "__main__":
    success = check_routes()
    sys.exit(0 if success else 1)
