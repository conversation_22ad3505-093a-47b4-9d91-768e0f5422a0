#!/usr/bin/env python3
"""
简单测试 - 验证修复
"""

import requests
import time

def simple_test():
    print("🧪 简单验证测试...")
    backend_url = "http://localhost:8000"
    
    # 等待后端启动
    print("⏳ 等待后端启动...")
    for i in range(10):
        try:
            response = requests.get(f"{backend_url}/health", timeout=2)
            if response.status_code == 200:
                print("✅ 后端已启动")
                break
        except:
            pass
        time.sleep(1)
        print(f"尝试 {i+1}/10...")
    else:
        print("❌ 后端启动超时")
        return False
    
    # 测试两个路径
    test_paths = [
        "/api/cover-templates/import-html",    # 无斜杠
        "/api/cover-templates/import-html/"    # 有斜杠
    ]
    
    for path in test_paths:
        url = backend_url + path
        print(f"\n🚀 测试: {url}")
        
        try:
            # 发送空的POST请求来测试路由是否存在
            response = requests.post(url, timeout=5)
            
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 422:  # FastAPI validation error - 说明路由存在
                print("✅ 路由存在（422是缺少必需参数，这是正常的）")
            elif response.status_code == 404:
                print("❌ 路由不存在（404 Not Found）")
            elif response.status_code == 500:
                print("❌ 服务器错误（可能是路由匹配问题）")
            else:
                print(f"ℹ️ 其他状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    return True

if __name__ == "__main__":
    simple_test()
