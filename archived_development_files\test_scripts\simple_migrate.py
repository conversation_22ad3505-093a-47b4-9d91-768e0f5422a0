#!/usr/bin/env python3
"""
简单的F5-TTS数据库迁移脚本
"""

import sqlite3
import os
from pathlib import Path

def main():
    # 数据库文件路径
    db_path = Path("reddit_story_generator.db")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    print("🚀 开始F5-TTS数据库迁移...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 1. 检查并添加f5_tts_endpoint字段到settings表
        print("检查settings表的f5_tts_endpoint字段...")
        
        # 检查字段是否存在
        cursor.execute("PRAGMA table_info(settings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'f5_tts_endpoint' not in columns:
            print("添加f5_tts_endpoint字段...")
            cursor.execute("""
                ALTER TABLE settings 
                ADD COLUMN f5_tts_endpoint VARCHAR(500)
            """)
            print("✅ f5_tts_endpoint字段添加成功")
        else:
            print("✅ f5_tts_endpoint字段已存在")
        
        # 2. 创建f5_tts_voices表
        print("检查f5_tts_voices表...")
        
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='f5_tts_voices'
        """)
        
        if not cursor.fetchone():
            print("创建f5_tts_voices表...")
            cursor.execute("""
                CREATE TABLE f5_tts_voices (
                    id VARCHAR(36) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    language VARCHAR(10) NOT NULL DEFAULT 'zh-CN',
                    gender VARCHAR(10) NOT NULL,
                    ref_audio_path VARCHAR(500) NOT NULL,
                    ref_text TEXT NOT NULL,
                    remove_silence BOOLEAN DEFAULT 0,
                    cross_fade_duration DECIMAL(3,2) DEFAULT 0.15,
                    nfe_value INTEGER DEFAULT 32,
                    randomize_seed BOOLEAN DEFAULT 1,
                    is_active BOOLEAN DEFAULT 1,
                    is_built_in BOOLEAN DEFAULT 0,
                    usage_count INTEGER DEFAULT 0,
                    last_used_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✅ f5_tts_voices表创建成功")
        else:
            print("✅ f5_tts_voices表已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证结果
        print("\n📊 迁移结果验证:")
        
        # 检查settings表字段
        cursor.execute("PRAGMA table_info(settings)")
        settings_columns = [column[1] for column in cursor.fetchall()]
        if 'f5_tts_endpoint' in settings_columns:
            print("✅ settings表包含f5_tts_endpoint字段")
        else:
            print("❌ settings表缺少f5_tts_endpoint字段")
        
        # 检查f5_tts_voices表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='f5_tts_voices'
        """)
        if cursor.fetchone():
            print("✅ f5_tts_voices表存在")
            cursor.execute("PRAGMA table_info(f5_tts_voices)")
            f5_voices_columns = [column[1] for column in cursor.fetchall()]
            print(f"   表字段: {', '.join(f5_voices_columns)}")
        else:
            print("❌ f5_tts_voices表不存在")
        
        conn.close()
        print("\n🎉 F5-TTS数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 迁移成功！现在可以重新启动应用了。")
    else:
        print("\n❌ 迁移失败！请检查错误信息。")
