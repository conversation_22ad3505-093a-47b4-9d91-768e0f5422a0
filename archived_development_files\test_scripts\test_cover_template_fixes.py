"""
测试封面模板样式修复
验证头像显示、圆角效果、截图质量等
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
backend_path = Path(__file__).parent / "backend" / "src"
sys.path.append(str(backend_path))

from services.cover_screenshot_service import cover_screenshot_service
from core.database import get_session_maker
from models.accounts import Account
from models.resources import CoverTemplate

async def test_cover_template_fixes():
    """测试封面模板样式修复"""
    print("🧪 开始测试封面模板样式修复...")
    
    # 获取数据库会话
    session_maker = get_session_maker()
    db = session_maker()
    
    try:
        # 获取测试账号
        test_account = db.query(Account).first()
        if not test_account:
            print("❌ 没有找到测试账号")
            return
        
        print(f"📋 使用测试账号: {test_account.name}")
        print(f"📋 头像路径: {test_account.avatar_file_path}")
        
        # 获取封面模板
        templates = db.query(CoverTemplate).all()
        if not templates:
            print("❌ 没有找到封面模板")
            return
        
        print(f"📋 找到 {len(templates)} 个模板")
        
        # 测试每个模板
        for i, template in enumerate(templates[:3]):  # 只测试前3个模板
            print(f"\n🎨 测试模板 {i+1}: {template.id}")
            
            # 生成测试封面
            output_path = f"test_cover_fix_{i+1}.png"
            test_title = "这是一个测试标题，用来验证封面模板的样式修复效果"
            
            success = await cover_screenshot_service.generate_cover_for_video_task(
                template_id=template.id,
                account=test_account,
                title=test_title,
                output_path=output_path,
                db=db
            )
            
            if success:
                print(f"   ✅ 封面生成成功: {output_path}")
                
                # 检查文件是否存在
                if Path(output_path).exists():
                    file_size = Path(output_path).stat().st_size
                    print(f"   📊 文件大小: {file_size / 1024:.2f} KB")
                    
                    if file_size > 10 * 1024:  # 大于10KB说明有内容
                        print(f"   ✅ 文件大小正常")
                    else:
                        print(f"   ⚠️  文件可能太小")
                else:
                    print(f"   ❌ 文件不存在")
            else:
                print(f"   ❌ 封面生成失败")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
    
    print("\n🎉 封面模板样式修复测试完成！")
    print("\n📋 修复内容总结:")
    print("   ✅ 添加了 .avatar img 样式，确保头像正确显示")
    print("   ✅ 设置了 object-fit: cover 保持图片比例")
    print("   ✅ 添加了 border-radius: 50% 确保圆角效果")
    print("   ✅ 设置了 overflow: hidden 确保容器圆角")
    print("   ✅ 优化了截图分辨率为 1080x1920")
    print("   ✅ 提升了截图质量设置")
    print("   ✅ 添加了图片加载等待时间")

if __name__ == "__main__":
    asyncio.run(test_cover_template_fixes())
