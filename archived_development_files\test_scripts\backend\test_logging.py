#!/usr/bin/env python3
"""
测试日志配置是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# 配置日志系统
from loguru import logger
import logging

# --- Loguru Configuration ---
logger.remove()
logger.add(sys.stderr, level="DEBUG")

# 配置标准logging模块的日志也发送到loguru
class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists.
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message.
        frame, depth = sys._getframe(6), 6
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

# 设置所有标准logging的日志级别为INFO，并使用InterceptHandler
logging.basicConfig(handlers=[InterceptHandler()], level=logging.INFO, force=True)

# 测试标准logging
std_logger = logging.getLogger("test_standard_logging")
std_logger.info("这是一条来自标准logging的INFO日志")
std_logger.warning("这是一条来自标准logging的WARNING日志")
std_logger.error("这是一条来自标准logging的ERROR日志")

# 测试loguru
logger.info("这是一条来自loguru的INFO日志")
logger.warning("这是一条来自loguru的WARNING日志")
logger.error("这是一条来自loguru的ERROR日志")

# 测试模拟的视频生成服务日志
video_logger = logging.getLogger("src.services.video_generation_service")
video_logger.info("任务 12345: 开始执行")
video_logger.info("任务 12345: 素材选择完成. 选择了 3 个素材.")
video_logger.info("任务 12345: 文案生成完成. 文案长度: 150")
video_logger.warning("任务 12345: Whisper未能提取任何单词时间戳.")
video_logger.error("任务 12345: 素材选择失败: 文件未找到")

print("日志测试完成。如果上面有日志输出，说明配置正确。")
