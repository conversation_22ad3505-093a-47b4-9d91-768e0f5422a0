@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul

echo =========================
echo Creating Next.js Distribution Package
echo =========================

:: 设置目标目录
set "DIST_DIR=./frontend-dist"

:: 首先执行构建
echo Building production version...
echo Running: npm run build:production
call npm run build:production
if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)
echo Build completed successfully!
echo.

:: 清理目标目录
if exist "%DIST_DIR%" (
    echo Cleaning old distribution directory...
    rmdir /s /q "%DIST_DIR%"
)

:: 创建分发目录
mkdir "%DIST_DIR%"

echo Copying necessary files...

:: 复制核心文件
copy package.json "%DIST_DIR%\"
copy package-lock.json "%DIST_DIR%\"
copy next.config.js "%DIST_DIR%\"
copy tsconfig.json "%DIST_DIR%\"
copy postcss.config.js "%DIST_DIR%\"
copy tailwind.config.js "%DIST_DIR%\"

:: 复制环境变量文件
if exist .env.production copy .env.production "%DIST_DIR%\"
if exist .env.local copy .env.local "%DIST_DIR%\"

:: 复制启动脚本
if exist start-prod.js copy start-prod.js "%DIST_DIR%\"

:: 复制构建输出
echo Copying build output directory...
if exist .next (
    xcopy /E /I /Y .next\* "%DIST_DIR%\.next\"
) else (
    echo WARNING: .next directory not found! Make sure build completed successfully.
)

:: 复制静态资源
echo Copying static resources directory...
if exist public (
    xcopy /E /I /Y public\* "%DIST_DIR%\public\"
) else (
    echo WARNING: public directory not found!
)

:: 创建启动脚本
echo Creating startup script...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo Starting Next.js Application...
echo echo.
echo echo Please ensure Node.js and npm are installed
echo echo.
echo if not exist node_modules ^(
echo     echo Installing dependencies...
echo     npm install --production
echo     echo.
echo ^)
echo echo Starting application...
echo npm run start:prod:optimized
echo pause
) > "%DIST_DIR%\start.bat"

:: 创建安装脚本
echo Creating installation script...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo Installing dependencies...
echo echo.
echo npm install --production
echo echo.
echo echo Dependencies installed successfully!
echo echo Run start.bat to launch the application
echo pause
) > "%DIST_DIR%\install.bat"

:: 创建README文件
echo Creating usage instructions...
(
echo # Reddit Story Video Generator - Frontend
echo.
echo ## Installation and Running
echo.
echo ### Method 1: Using Batch Files
echo 1. Double-click `install.bat` to install dependencies
echo 2. Double-click `start.bat` to start the application
echo.
echo ### Method 2: Using Command Line
echo 1. Open command prompt
echo 2. Install dependencies: `npm install --production`
echo 3. Start application: `npm run start:prod`
echo.
echo ## Access Application
echo After starting, visit: http://localhost:3000
echo.
echo ## Requirements
echo - Node.js 16.0 or higher
echo - npm 7.0 or higher
echo.
echo ## Configuration
echo To modify backend API address, edit `.env.production`:
echo ```
echo NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
echo ```
) > "%DIST_DIR%\README.md"

echo.
echo =========================
echo Distribution Package Created Successfully!
echo =========================
echo Distribution directory: %DIST_DIR%
echo.
echo To distribute the application:
echo 1. Copy the entire %DIST_DIR% directory to target environment
echo 2. Run install.bat to install dependencies
echo 3. Run start.bat to launch the application
echo.
echo Files included:
echo - Built Next.js application (.next directory)
echo - Package configuration files
echo - Static resources (public directory)
echo - Installation and startup scripts
echo - Usage documentation (README.md)
echo.
pause
