# 后端API开发快速开始指南

## 🚀 立即开始

基于前端Zustand状态管理结构，快速启动后端API开发。

## 📋 准备工作

### 1. 环境检查
```bash
# 检查Python版本 (需要3.8+)
python --version

# 检查pip
pip --version

# 检查Git
git --version
```

### 2. 项目初始化
```bash
# 创建后端目录
mkdir backend
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate
```

### 3. 依赖安装
```bash
# 创建requirements.txt
cat > requirements.txt << EOF
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
pymysql==1.1.0
psycopg2-binary==2.9.9

# 数据验证
pydantic==2.5.0
pydantic-settings==2.1.0

# 异步支持
aiofiles==23.2.1
httpx==0.25.2

# 任务队列
celery==5.3.4
redis==5.0.1

# 文件处理
pillow==10.1.0
python-multipart==0.0.6

# AI服务
openai==1.3.7

# 音视频处理
ffmpeg-python==0.2.0
mutagen==1.47.0

# 工具库
python-jose[cryptography]==3.3.0
python-dotenv==1.0.0
email-validator==2.1.0
bcrypt==4.1.2

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1
EOF

# 安装依赖
pip install -r requirements.txt
```

## 🏗️ 项目结构创建

### 创建基础目录结构
```bash
# 创建项目结构
mkdir -p app/{api/v1,core,models,schemas,services,utils,middleware,worker}
mkdir -p tests/{test_api,test_services,test_utils}
mkdir -p migrations/versions

# 创建__init__.py文件
touch app/__init__.py
touch app/api/__init__.py
touch app/api/v1/__init__.py
touch app/core/__init__.py
touch app/models/__init__.py
touch app/schemas/__init__.py
touch app/services/__init__.py
touch app/utils/__init__.py
touch app/middleware/__init__.py
touch app/worker/__init__.py
touch tests/__init__.py
```

## 📄 核心文件创建

### 1. 主应用文件 (app/main.py)
```python
"""
Reddit故事视频生成器 - 后端API主入口
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import uuid

from app.core.config import settings
from app.api.v1.router import api_router
from app.middleware.error_handler import setup_error_handlers

# 创建FastAPI应用
app = FastAPI(
    title="Reddit故事视频生成器 API",
    description="用于生成Reddit故事视频的后端API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求ID中间件
@app.middleware("http")
async def add_request_id(request: Request, call_next):
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    response.headers["X-Request-ID"] = request_id
    response.headers["X-Process-Time"] = str(process_time)
    
    return response

# 注册错误处理器
setup_error_handlers(app)

# 注册路由
app.include_router(api_router, prefix="/api/v1")

# 健康检查
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "reddit-story-generator-api",
        "version": "1.0.0"
    }

# 根路径
@app.get("/")
async def root():
    return {
        "message": "Reddit故事视频生成器 API",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
```

### 2. 配置文件 (app/core/config.py)
```python
"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os

class Settings(BaseSettings):
    # 应用设置
    APP_NAME: str = "Reddit故事视频生成器 API"
    DEBUG: bool = True
    SECRET_KEY: str = "your-secret-key-here"
    
    # 服务器设置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库设置
    DATABASE_URL: str = "sqlite:///./reddit_generator.db"
    
    # Redis设置
    REDIS_URL: str = "redis://localhost:6379"
    
    # 文件存储设置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    TEMP_DIR: str = "temp"
    
    # AI服务设置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"
    
    # 任务队列设置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    MAX_CONCURRENT_TASKS: int = 3
    
    # 日志设置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建设置实例
settings = Settings()

# 确保必要目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.TEMP_DIR, exist_ok=True)
os.makedirs("logs", exist_ok=True)
```

### 3. 统一响应格式 (app/core/responses.py)
```python
"""
统一响应格式
"""

from typing import Any, Optional, Dict
from fastapi import Request
from fastapi.responses import JSONResponse
from datetime import datetime
import uuid

class APIResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "操作成功",
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """成功响应"""
        return {
            "success": True,
            "data": data,
            "message": message,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "requestId": request_id or str(uuid.uuid4())
        }
    
    @staticmethod
    def error(
        message: str = "操作失败",
        error: str = "UNKNOWN_ERROR",
        details: Any = None,
        status_code: int = 400,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """错误响应"""
        return {
            "success": False,
            "error": error,
            "message": message,
            "details": details,
            "status": status_code,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "requestId": request_id or str(uuid.uuid4())
        }

def success_response(
    data: Any = None,
    message: str = "操作成功",
    request: Optional[Request] = None
) -> JSONResponse:
    """创建成功响应"""
    request_id = getattr(request.state, 'request_id', None) if request else None
    content = APIResponse.success(data, message, request_id)
    return JSONResponse(content=content, status_code=200)

def error_response(
    message: str = "操作失败",
    error: str = "UNKNOWN_ERROR",
    details: Any = None,
    status_code: int = 400,
    request: Optional[Request] = None
) -> JSONResponse:
    """创建错误响应"""
    request_id = getattr(request.state, 'request_id', None) if request else None
    content = APIResponse.error(message, error, details, status_code, request_id)
    return JSONResponse(content=content, status_code=status_code)
```

### 4. 数据库连接 (app/core/database.py)
```python
"""
数据库连接管理
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def init_db():
    """初始化数据库"""
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("数据库初始化完成")
```

### 5. 基础模型 (app/models/base.py)
```python
"""
基础数据模型
"""

from sqlalchemy import Column, String, DateTime, Boolean
from sqlalchemy.sql import func
from app.core.database import Base
import uuid

class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_deleted = Column(Boolean, default=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
```

### 6. 设置模型 (app/models/settings.py)
```python
"""
设置相关数据模型
"""

from sqlalchemy import Column, String, Text, Decimal, Boolean, Integer
from app.models.base import BaseModel

class Settings(BaseModel):
    """用户设置表"""
    __tablename__ = "settings"
    
    user_id = Column(String(36), nullable=True, comment="用户ID")
    
    # TTS配置
    tts_provider = Column(String(50), nullable=False, default="openai", comment="TTS提供商")
    tts_api_key = Column(Text, comment="TTS API密钥")
    tts_endpoint = Column(String(500), comment="TTS API端点")
    tts_voice = Column(String(100), nullable=False, default="alloy", comment="TTS语音")
    tts_model = Column(String(100), comment="TTS模型")
    tts_speed = Column(Decimal(3, 2), nullable=False, default=1.0, comment="TTS语速")
    tts_pitch = Column(Decimal(3, 2), nullable=False, default=1.0, comment="TTS音调")
    tts_volume = Column(Decimal(3, 2), nullable=False, default=1.0, comment="TTS音量")
    
    # LLM配置
    llm_provider = Column(String(50), nullable=False, default="openai", comment="LLM提供商")
    llm_api_key = Column(Text, comment="LLM API密钥")
    llm_endpoint = Column(String(500), comment="LLM API端点")
    llm_model = Column(String(100), nullable=False, default="gpt-3.5-turbo", comment="LLM模型")
    llm_temperature = Column(Decimal(3, 2), nullable=False, default=0.7, comment="LLM温度")
    llm_max_tokens = Column(Integer, nullable=False, default=2000, comment="LLM最大Token数")
    llm_system_prompt = Column(Text, comment="LLM系统提示词")
    
    # 通用设置
    theme = Column(String(20), nullable=False, default="light", comment="主题")
    language = Column(String(10), nullable=False, default="zh-CN", comment="语言")
    auto_save = Column(Boolean, nullable=False, default=True, comment="自动保存")
    show_tips = Column(Boolean, nullable=False, default=True, comment="显示提示")
    output_directory = Column(String(500), comment="输出目录")
```

### 7. 错误处理中间件 (app/middleware/error_handler.py)
```python
"""
错误处理中间件
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError
import logging
from app.core.responses import APIResponse

logger = logging.getLogger(__name__)

def setup_error_handlers(app: FastAPI):
    """设置错误处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理"""
        request_id = getattr(request.state, 'request_id', None)
        
        logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
        
        return JSONResponse(
            status_code=exc.status_code,
            content=APIResponse.error(
                message=exc.detail,
                error="HTTP_ERROR",
                status_code=exc.status_code,
                request_id=request_id
            )
        )
    
    @app.exception_handler(SQLAlchemyError)
    async def database_exception_handler(request: Request, exc: SQLAlchemyError):
        """数据库异常处理"""
        request_id = getattr(request.state, 'request_id', None)
        
        logger.error(f"数据库错误: {str(exc)}")
        
        return JSONResponse(
            status_code=500,
            content=APIResponse.error(
                message="数据库操作失败",
                error="DATABASE_ERROR",
                details=str(exc) if app.debug else None,
                status_code=500,
                request_id=request_id
            )
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理"""
        request_id = getattr(request.state, 'request_id', None)
        
        logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content=APIResponse.error(
                message="服务器内部错误",
                error="INTERNAL_SERVER_ERROR",
                details=str(exc) if app.debug else None,
                status_code=500,
                request_id=request_id
            )
        )
```

### 8. 主路由 (app/api/v1/router.py)
```python
"""
API v1 主路由
"""

from fastapi import APIRouter
from app.api.v1 import settings, system

api_router = APIRouter()

# 注册子路由
api_router.include_router(settings.router, prefix="/settings", tags=["设置管理"])
api_router.include_router(system.router, prefix="/system", tags=["系统管理"])

# 根路由
@api_router.get("/")
async def api_root():
    return {
        "message": "Reddit故事视频生成器 API v1",
        "version": "1.0.0",
        "endpoints": {
            "settings": "/api/v1/settings",
            "system": "/api/v1/system",
            "docs": "/docs"
        }
    }
```

### 9. 设置API路由 (app/api/v1/settings.py)
```python
"""
设置管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.responses import success_response
from app.models.settings import Settings
from app.schemas.settings import SettingsResponse, TTSConfigUpdate

router = APIRouter()

@router.get("/", response_model=SettingsResponse)
async def get_settings(db: Session = Depends(get_db)):
    """获取所有设置"""
    settings = db.query(Settings).first()
    
    if not settings:
        # 创建默认设置
        settings = Settings()
        db.add(settings)
        db.commit()
        db.refresh(settings)
    
    return success_response(data=settings.to_dict())

@router.put("/tts")
async def update_tts_config(
    config: TTSConfigUpdate,
    db: Session = Depends(get_db)
):
    """更新TTS设置"""
    settings = db.query(Settings).first()
    
    if not settings:
        settings = Settings()
        db.add(settings)
    
    # 更新TTS配置
    for field, value in config.dict(exclude_unset=True).items():
        setattr(settings, f"tts_{field}", value)
    
    db.commit()
    db.refresh(settings)
    
    return success_response(
        data=settings.to_dict(),
        message="TTS配置更新成功"
    )

@router.post("/tts/test")
async def test_tts_config():
    """测试TTS配置"""
    # TODO: 实现TTS测试逻辑
    return success_response(
        data={"status": "success", "message": "TTS配置测试通过"},
        message="TTS测试完成"
    )
```

### 10. 环境配置文件 (.env)
```bash
# 应用设置
APP_NAME="Reddit故事视频生成器 API"
DEBUG=true
SECRET_KEY="your-secret-key-change-this-in-production"

# 服务器设置
HOST=0.0.0.0
PORT=8000

# 数据库设置
DATABASE_URL="sqlite:///./reddit_generator.db"

# Redis设置  
REDIS_URL="redis://localhost:6379"

# 文件存储
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=104857600
TEMP_DIR="temp"

# AI服务
OPENAI_API_KEY=""
OPENAI_BASE_URL="https://api.openai.com/v1"

# 任务队列
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"
MAX_CONCURRENT_TASKS=3

# 日志
LOG_LEVEL="INFO"
LOG_FILE="logs/app.log"
```

## 🚀 启动项目

### 1. 启动开发服务器
```bash
# 确保在backend目录且虚拟环境已激活
cd backend
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 启动FastAPI开发服务器
python app/main.py

# 或使用uvicorn命令
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 验证API
```bash
# 健康检查
curl http://localhost:8000/health

# API根路径
curl http://localhost:8000/api/v1/

# 获取设置
curl http://localhost:8000/api/v1/settings/

# 查看API文档
# 浏览器访问: http://localhost:8000/docs
```

### 3. 测试前后端连接
```bash
# 在前端目录启动开发服务器
cd ../frontend
npm run dev

# 访问前端设置页面，测试与后端API的连接
# http://localhost:3000/settings
```

## 📋 下一步开发任务

### 优先级1：立即实现
1. **完善设置API** - 实现所有设置相关的CRUD操作
2. **文件上传系统** - 实现基础的文件上传和管理
3. **前后端集成测试** - 确保前端状态管理与后端API完全对接

### 优先级2：核心功能
1. **资源管理API** - 实现音乐、视频、提示词、账号等资源管理
2. **任务队列系统** - 实现生成任务的创建和管理
3. **WebSocket集成** - 实现实时状态更新

### 优先级3：完善功能
1. **系统监控** - 实现健康检查和统计功能
2. **错误处理** - 完善错误处理和日志记录
3. **性能优化** - 数据库优化和缓存策略

## 🛠️ 开发工具

### 代码质量
```bash
# 格式化代码
black app/

# 代码检查
flake8 app/

# 类型检查
mypy app/
```

### 测试
```bash
# 运行测试
pytest tests/

# 测试覆盖率
pytest --cov=app tests/
```

### 数据库迁移
```bash
# 初始化Alembic
alembic init migrations

# 创建迁移
alembic revision --autogenerate -m "Initial migration"

# 应用迁移
alembic upgrade head
```

按照这个指南，你可以快速启动后端API开发，并逐步完善功能与前端的完美对接。
