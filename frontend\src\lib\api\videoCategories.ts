/**
 * 视频分类管理 API 客户端
 */

import { ApiResponse } from '@/types/store'

export interface VideoCategory {
  id: string
  name: string
  description?: string
  createdAt?: string
  updatedAt?: string
}

export interface VideoCategoryCreateRequest {
  name: string
  description?: string
}

export interface VideoCategoryUpdateRequest {
  name?: string
  description?: string
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

export class VideoCategoriesAPI {
  private static instance: VideoCategoriesAPI
  
  static getInstance(): VideoCategoriesAPI {
    if (!VideoCategoriesAPI.instance) {
      VideoCategoriesAPI.instance = new VideoCategoriesAPI()
    }
    return VideoCategoriesAPI.instance
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/video-categories${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error(`API请求失败: ${endpoint}`, error)
      throw error
    }
  }

  /**
   * 获取所有视频分类
   */
  async getCategories(): Promise<ApiResponse<VideoCategory[]>> {
    return this.request<VideoCategory[]>('/')
  }

  /**
   * 创建新的视频分类
   */
  async createCategory(data: VideoCategoryCreateRequest): Promise<ApiResponse<VideoCategory>> {
    return this.request<VideoCategory>('/', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  /**
   * 更新视频分类
   */
  async updateCategory(id: string, data: VideoCategoryUpdateRequest): Promise<ApiResponse<VideoCategory>> {
    return this.request<VideoCategory>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  /**
   * 删除视频分类
   */
  async deleteCategory(id: string): Promise<ApiResponse<null>> {
    return this.request<null>(`/${id}`, {
      method: 'DELETE',
    })
  }

  /**
   * 获取单个视频分类详情
   */
  async getCategory(id: string): Promise<ApiResponse<VideoCategory>> {
    return this.request<VideoCategory>(`/${id}`)
  }
}

export const videoCategoriesAPI = VideoCategoriesAPI.getInstance()
