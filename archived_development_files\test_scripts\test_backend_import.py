#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的后端启动测试
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def test_imports():
    """测试关键模块导入"""
    try:
        print("1. 测试基础导入...")
        from src.services.video_generation_service import VideoGenerationService
        print("✅ VideoGenerationService 导入成功")
        
        print("2. 测试API路由导入...")
        from src.api.video_generation import router
        print("✅ video_generation router 导入成功")
        
        print("3. 测试主应用导入...")
        from src.api.routes import api_router
        print("✅ api_router 导入成功")
        
        print("✅ 所有关键模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 后端启动测试")
    print("=" * 50)
    
    success = test_imports()
    
    if success:
        print("\n✅ 测试通过，后端应该可以正常启动")
    else:
        print("\n❌ 测试失败，需要修复导入问题")
