#!/usr/bin/env python3
"""
视频素材管理API测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import aiohttp
import json

# API基础URL
API_BASE = "http://localhost:8000/api"

async def test_video_materials_api():
    """测试视频素材管理API"""
    async with aiohttp.ClientSession() as session:
        print("🧪 开始测试视频素材管理API...")
        
        # 1. 测试获取空列表
        print("\n1. 测试获取视频素材列表...")
        try:
            async with session.get(f"{API_BASE}/video-materials/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取列表成功，素材数量: {len(data)}")
                else:
                    print(f"❌ 获取列表失败，状态码: {response.status}")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        # 2. 测试获取分类列表
        print("\n2. 测试获取分类列表...")
        try:
            async with session.get(f"{API_BASE}/video-materials/categories/list") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 获取分类列表成功: {data}")
                else:
                    print(f"❌ 获取分类列表失败，状态码: {response.status}")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        # 3. 测试创建视频素材
        print("\n3. 测试创建视频素材...")
        test_material = {
            "name": "测试视频素材",
            "file_path": "/test/path/video.mp4",
            "duration": 120.5,
            "resolution": "1920x1080",
            "category": "test",
            "tags": ["测试", "示例"],
            "is_built_in": False,
            "file_size": 1024000,
            "format": "mp4",
            "frame_rate": 30.0,
            "bitrate": 5000000
        }
        
        try:
            async with session.post(
                f"{API_BASE}/video-materials/",
                json=test_material,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 创建视频素材成功，ID: {data.get('id')}")
                    material_id = data.get('id')
                    
                    # 4. 测试获取单个素材
                    print(f"\n4. 测试获取单个素材...")
                    async with session.get(f"{API_BASE}/video-materials/{material_id}") as get_response:
                        if get_response.status == 200:
                            get_data = await get_response.json()
                            print(f"✅ 获取单个素材成功: {get_data.get('name')}")
                        else:
                            print(f"❌ 获取单个素材失败，状态码: {get_response.status}")
                    
                    # 5. 测试更新素材
                    print(f"\n5. 测试更新素材...")
                    update_data = {
                        "name": "更新后的视频素材",
                        "tags": ["更新", "测试"]
                    }
                    async with session.put(
                        f"{API_BASE}/video-materials/{material_id}",
                        json=update_data,
                        headers={"Content-Type": "application/json"}
                    ) as update_response:
                        if update_response.status == 200:
                            update_result = await update_response.json()
                            print(f"✅ 更新素材成功: {update_result.get('name')}")
                        else:
                            print(f"❌ 更新素材失败，状态码: {update_response.status}")
                    
                    # 6. 测试删除素材
                    print(f"\n6. 测试删除素材...")
                    async with session.delete(f"{API_BASE}/video-materials/{material_id}") as delete_response:
                        if delete_response.status == 200:
                            delete_result = await delete_response.json()
                            print(f"✅ 删除素材成功: {delete_result.get('message', '删除成功')}")
                        else:
                            print(f"❌ 删除素材失败，状态码: {delete_response.status}")
                            
                else:
                    error_text = await response.text()
                    print(f"❌ 创建视频素材失败，状态码: {response.status}, 错误: {error_text}")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        print("\n🏁 视频素材管理API测试完成")

async def test_upload_api():
    """测试文件上传API"""
    print("\n🧪 开始测试文件上传API...")
    
    # 创建一个测试文件
    test_file_content = b"test video content"
    
    async with aiohttp.ClientSession() as session:
        # 测试视频上传端点
        print("\n1. 测试视频上传接口...")
        try:
            form_data = aiohttp.FormData()
            form_data.add_field('file', test_file_content, filename='test_video.mp4', content_type='video/mp4')
            
            async with session.post(f"{API_BASE}/upload/video", data=form_data) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 上传接口响应成功: {data}")
                else:
                    error_text = await response.text()
                    print(f"❌ 上传接口失败，状态码: {response.status}, 错误: {error_text}")
        except Exception as e:
            print(f"❌ 上传请求失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始API测试...")
    
    # 检查服务器是否运行
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{API_BASE}/health") as response:
                if response.status == 200:
                    print("✅ 服务器运行正常")
                else:
                    print(f"❌ 服务器状态异常，状态码: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            print("请确保后端服务器正在运行在 http://localhost:8000")
            return
    
    await test_video_materials_api()
    await test_upload_api()
    
    print("\n✨ 所有测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
