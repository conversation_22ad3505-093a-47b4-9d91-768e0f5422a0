#!/usr/bin/env python3
"""
检查数据库中的音乐文件数据
"""

import sys
import os
sys.path.append('src')

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from src.models.resources import BackgroundMusic, MusicCategory
from src.models import Base

# 创建数据库连接
DATABASE_URL = "sqlite:///D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def main():
    session = SessionLocal()
    try:
        # 查询音乐文件
        musics = session.query(BackgroundMusic).all()
        print(f"数据库中的音乐文件数量: {len(musics)}")
        
        if musics:
            print("\n音乐文件列表:")
            for i, music in enumerate(musics[:10], 1):  # 只显示前10个
                print(f"{i}. ID: {music.id}")
                print(f"   Name: {music.name}")
                print(f"   Category: {music.category}")
                print(f"   File Path: {music.file_path}")
                print(f"   Duration: {music.duration}")
                print(f"   Format: {music.format}")
                print(f"   Is Deleted: {music.is_deleted}")  # 新增：显示软删除状态
                print()
        
        # 查询分类
        categories = session.query(MusicCategory).all()
        print(f"数据库中的分类数量: {len(categories)}")
        
        if categories:
            print("\n分类列表:")
            for cat in categories:
                print(f"- {cat.name}")
        
        # 直接查询表结构
        print("\n数据库表结构:")
        with engine.connect() as connection:
            tables = connection.execute(text("SELECT name FROM sqlite_master WHERE type='table';")).fetchall()
            for table in tables:
                print(f"- {table[0]}")
        
            # 检查background_music表的列
            try:
                columns = connection.execute(text("PRAGMA table_info(background_music);")).fetchall()
                print(f"\nbackground_music表结构:")
                for col in columns:
                    print(f"  {col[1]} ({col[2]})")
            except Exception as e:
                print(f"检查表结构失败: {e}")
            
    except Exception as e:
        print(f"查询失败: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    main()
