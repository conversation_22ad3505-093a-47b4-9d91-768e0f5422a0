#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试封面模板的完整保存和加载流程
验证前后端数据传输和持久化功能
"""

import subprocess
import time
import requests
import json
import os

def test_template_persistence():
    """测试模板的持久化功能"""
    
    print("🧪 测试封面模板持久化功能")
    print("=" * 60)
    
    # 1. 启动后端服务器
    print("\n1️⃣ 启动后端服务器...")
    try:
        # 检查服务器是否已经运行
        response = requests.get("http://localhost:8000/health", timeout=5)
        print("   ✅ 后端服务器已在运行")
    except:
        print("   🔄 启动后端服务器...")
        backend_process = subprocess.Popen(
            ["python", "main.py"],
            cwd="backend",
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        time.sleep(5)
        
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            print("   ✅ 后端服务器启动成功")
        except:
            print("   ❌ 后端服务器启动失败")
            return False
    
    # 2. 测试创建模板
    print("\n2️⃣ 测试创建模板...")
    create_data = {
        "name": "测试模板",
        "description": "这是一个测试模板",
        "category": "测试",
        "elements": [
            {
                "id": "text_1",
                "type": "text",
                "x": 50,
                "y": 30,
                "width": 200,
                "height": 30,
                "content": "测试文本",
                "fontSize": 18,
                "color": "#ffffff",
                "variableBinding": {
                    "enabled": True,
                    "variableName": "title",
                    "propertyPath": "content"
                }
            },
            {
                "id": "shape_1",
                "type": "shape",
                "x": 100,
                "y": 80,
                "width": 80,
                "height": 40,
                "backgroundColor": "#3b82f6",
                "shapeType": "rectangle"
            }
        ],
        "background": {
            "type": "gradient",
            "value": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/cover-templates/",
            json=create_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            template_id = result["data"]["id"]
            print(f"   ✅ 模板创建成功，ID: {template_id}")
        else:
            print(f"   ❌ 模板创建失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 创建模板请求失败: {str(e)}")
        return False
    
    # 3. 测试获取模板
    print("\n3️⃣ 测试获取模板...")
    try:
        response = requests.get(f"http://localhost:8000/api/cover-templates/{template_id}")
        
        if response.status_code == 200:
            result = response.json()
            template_data = result["data"]
            
            print(f"   ✅ 模板获取成功:")
            print(f"      名称: {template_data['name']}")
            print(f"      元素数量: {len(template_data.get('elements', []))}")
            print(f"      背景类型: {template_data.get('background', {}).get('type', 'unknown')}")
            
            # 验证数据完整性
            if template_data.get('elements') and template_data.get('background'):
                print("   ✅ 元素和背景数据完整")
            else:
                print("   ❌ 元素或背景数据丢失")
                return False
                
        else:
            print(f"   ❌ 获取模板失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 获取模板请求失败: {str(e)}")
        return False
    
    # 4. 测试更新模板
    print("\n4️⃣ 测试更新模板...")
    update_data = {
        "name": "更新后的测试模板",
        "elements": [
            {
                "id": "text_1",
                "type": "text",
                "x": 60,
                "y": 40,
                "width": 220,
                "height": 25,
                "content": "更新后的文本",
                "fontSize": 20,
                "color": "#ff0000",
                "variableBinding": {
                    "enabled": True,
                    "variableName": "author",
                    "propertyPath": "content"
                }
            },
            {
                "id": "image_1",
                "type": "image",
                "x": 150,
                "y": 100,
                "width": 60,
                "height": 60,
                "imageType": "circle",
                "variableBinding": {
                    "enabled": True,
                    "variableName": "avatar",
                    "propertyPath": "imageUrl"
                }
            }
        ],
        "background": {
            "type": "solid",
            "value": "#4ade80"
        }
    }
    
    try:
        response = requests.put(
            f"http://localhost:8000/api/cover-templates/{template_id}",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            updated_template = result["data"]
            
            print(f"   ✅ 模板更新成功:")
            print(f"      新名称: {updated_template['name']}")
            print(f"      新元素数量: {len(updated_template.get('elements', []))}")
            print(f"      新背景类型: {updated_template.get('background', {}).get('type', 'unknown')}")
        else:
            print(f"   ❌ 更新模板失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 更新模板请求失败: {str(e)}")
        return False
    
    # 5. 再次获取模板验证更新
    print("\n5️⃣ 验证更新结果...")
    try:
        response = requests.get(f"http://localhost:8000/api/cover-templates/{template_id}")
        
        if response.status_code == 200:
            result = response.json()
            final_template = result["data"]
            
            # 验证更新是否生效
            if final_template['name'] == "更新后的测试模板":
                print("   ✅ 模板名称更新成功")
            else:
                print("   ❌ 模板名称更新失败")
                return False
                
            if len(final_template.get('elements', [])) == 2:
                print("   ✅ 元素数量更新成功")
            else:
                print("   ❌ 元素数量更新失败")
                return False
                
            if final_template.get('background', {}).get('type') == 'solid':
                print("   ✅ 背景配置更新成功")
            else:
                print("   ❌ 背景配置更新失败")
                return False
                
        else:
            print(f"   ❌ 验证获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 验证请求失败: {str(e)}")
        return False
    
    # 6. 测试模板列表
    print("\n6️⃣ 测试模板列表...")
    try:
        response = requests.get("http://localhost:8000/api/cover-templates/")
        
        if response.status_code == 200:
            result = response.json()
            templates = result["data"]["templates"]
            
            # 查找我们创建的模板
            our_template = None
            for template in templates:
                if template["id"] == template_id:
                    our_template = template
                    break
            
            if our_template:
                print(f"   ✅ 在模板列表中找到我们的模板")
                print(f"      名称: {our_template['name']}")
                print(f"      分类: {our_template.get('category', 'unknown')}")
            else:
                print("   ❌ 在模板列表中未找到我们的模板")
                return False
        else:
            print(f"   ❌ 获取模板列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 获取模板列表请求失败: {str(e)}")
        return False
    
    # 7. 清理测试数据
    print("\n7️⃣ 清理测试数据...")
    try:
        response = requests.delete(f"http://localhost:8000/api/cover-templates/{template_id}")
        
        if response.status_code == 200:
            print("   ✅ 测试模板删除成功")
        else:
            print(f"   ⚠️  删除测试模板失败: {response.status_code}")
    except Exception as e:
        print(f"   ⚠️  删除测试模板请求失败: {str(e)}")
    
    return True

def test_frontend_integration():
    """测试前端集成"""
    
    print("\n8️⃣ 前端集成测试指南:")
    print("   1. 启动前端服务器: cd frontend && npm run dev")
    print("   2. 访问: http://localhost:3000/covers")
    print("   3. 点击'新建模板'创建模板")
    print("   4. 在编辑器中:")
    print("      - 添加文本和图片元素")
    print("      - 配置背景颜色/渐变")
    print("      - 设置变量绑定")
    print("      - 点击'保存'按钮")
    print("   5. 返回列表页面，再次编辑同一模板")
    print("   6. 验证所有设置都被正确加载")

def main():
    """主函数"""
    print("🚀 封面模板持久化功能测试")
    print("=" * 60)
    
    # 测试后端持久化
    if test_template_persistence():
        print("\n🎉 后端持久化测试全部通过！")
        test_frontend_integration()
        
        print("\n" + "=" * 60)
        print("✨ 模板现在可以正确保存到数据库并重新加载！")
        print("📝 主要改进:")
        print("   ✅ 模板保存到数据库而非localStorage")
        print("   ✅ 进入编辑器时自动加载模板数据")
        print("   ✅ 支持元素、背景、变量绑定的完整保存")
        print("   ✅ 保存状态显示和错误处理")
        print("   ✅ 前后端数据格式统一")
    else:
        print("\n❌ 后端持久化测试失败")
        print("请检查后端服务器是否正常运行")

if __name__ == "__main__":
    main()
