"""
资源管理相关的 Pydantic schemas
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

# ================================
# 背景音乐相关 Schemas
# ================================

class BackgroundMusicBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="音乐名称")
    category: str = Field(default="general", max_length=100, description="分类")
    tags: List[str] = Field(default=[], description="标签列表")

class BackgroundMusicCreate(BackgroundMusicBase):
    file_path: str = Field(..., description="文件路径")
    duration: float = Field(..., gt=0, description="时长(秒)")
    is_built_in: bool = Field(default=False, description="是否为内置音乐")
    
    # 可选的元数据
    file_size: Optional[int] = Field(None, description="文件大小")
    format: Optional[str] = Field(None, description="音频格式")
    bitrate: Optional[int] = Field(None, description="比特率")
    sample_rate: Optional[int] = Field(None, description="采样率")

class BackgroundMusicUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = Field(None)

class BackgroundMusicResponse(BackgroundMusicBase):
    id: str
    filePath: str
    duration: float
    isBuiltIn: bool
    metadata: Dict[str, Any]
    createdAt: Optional[str]
    updatedAt: Optional[str]

# ================================
# 视频分类相关 Schemas
# ================================

class VideoCategoryBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")

class VideoCategoryCreate(VideoCategoryBase):
    pass

class VideoCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None)

class VideoCategoryResponse(VideoCategoryBase):
    id: str
    createdAt: Optional[str]
    updatedAt: Optional[str]

    class Config:
        from_attributes = True

# ================================
# 视频素材相关 Schemas
# ================================

class VideoMaterialBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="素材名称")
    category: str = Field(default="general", max_length=100, description="分类")
    tags: List[str] = Field(default=[], description="标签列表")

class VideoMaterialCreate(VideoMaterialBase):
    file_path: str = Field(..., description="文件路径")
    duration: float = Field(..., gt=0, description="时长(秒)")
    resolution: str = Field(..., description="分辨率")
    is_built_in: bool = Field(default=False, description="是否为内置素材")
    
    # 可选的元数据
    file_size: Optional[int] = Field(None, description="文件大小")
    format: Optional[str] = Field(None, description="视频格式")
    frame_rate: Optional[float] = Field(None, description="帧率")
    bitrate: Optional[int] = Field(None, description="比特率")
    thumbnail_path: Optional[str] = Field(None, description="缩略图路径")

class VideoMaterialUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = Field(None)

class VideoMaterialResponse(BaseModel):
    id: str
    name: str
    type: str  # 'video', 'image', 'gif'
    format: str
    size: str  # 格式化的文件大小字符串
    duration: Optional[str] = None  # 格式化的时长字符串 (mm:ss)
    dimensions: Dict[str, int]  # {width: int, height: int}
    aspectRatio: str
    path: str
    url: str  # 播放URL
    thumbnailUrl: Optional[str] = None
    thumbnailPath: Optional[str] = None
    filePath: str  # 兼容字段
    category: str
    tags: List[str]
    isBuiltIn: bool
    createdAt: Optional[str]
    lastModified: Optional[str]
    metadata: Dict[str, Any]

    class Config:
        from_attributes = True

# ================================
# 提示词相关 Schemas
# ================================

class PromptBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="提示词名称")
    content: str = Field(..., min_length=1, description="提示词内容")
    category: str = Field(default="general", max_length=100, description="分类")
    variables: List[str] = Field(default=[], description="变量列表")

class PromptCreate(PromptBase):
    is_built_in: bool = Field(default=False, description="是否为内置提示词")
    description: Optional[str] = Field(None, description="描述")
    example_output: Optional[str] = Field(None, description="示例输出")

class PromptUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = Field(None, min_length=1)
    category: Optional[str] = Field(None, max_length=100)
    variables: Optional[List[str]] = Field(None)
    description: Optional[str] = Field(None)
    example_output: Optional[str] = Field(None)

class PromptResponse(PromptBase):
    id: str
    isBuiltIn: bool
    metadata: Dict[str, Any]
    createdAt: Optional[str]
    updatedAt: Optional[str]

class PromptTestRequest(BaseModel):
    content: str = Field(..., description="要测试的提示词内容")
    variables: Dict[str, str] = Field(default={}, description="变量值")

class PromptTestResponse(BaseModel):
    success: bool
    result: Optional[str] = Field(None, description="生成结果")
    error: Optional[str] = Field(None, description="错误信息")
    usage: Optional[Dict[str, Any]] = Field(None, description="使用统计")

# ================================
# 账户相关 Schemas
# ================================

class AccountBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="账户名称")
    platform: str = Field(..., description="平台名称")

class AccountCreate(AccountBase):
    is_active: bool = Field(default=True, description="是否激活")
    config: Dict[str, Any] = Field(default={}, description="配置信息")
    credentials: Optional[Dict[str, str]] = Field(None, description="凭证信息")

class AccountUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    is_active: Optional[bool] = Field(None)
    config: Optional[Dict[str, Any]] = Field(None)
    credentials: Optional[Dict[str, str]] = Field(None)

class AccountResponse(AccountBase):
    id: str
    isActive: bool
    config: Dict[str, Any]
    metadata: Dict[str, Any]
    createdAt: Optional[str]
    updatedAt: Optional[str]

# ================================
# 封面模板相关 Schemas
# ================================

class CoverTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="模板名称")
    variables: List[str] = Field(default=[], description="可变元素列表")

class CoverTemplateCreate(CoverTemplateBase):
    preview_path: Optional[str] = Field(None, description="预览图路径")
    template_path: Optional[str] = Field(None, description="模板文件路径")
    is_built_in: bool = Field(default=False, description="是否为内置模板")
    description: Optional[str] = Field(None, description="描述")
    category: str = Field(default="general", description="分类")
    tags: List[str] = Field(default=[], description="标签")
    width: Optional[int] = Field(None, description="宽度")
    height: Optional[int] = Field(None, description="高度")
    format: Optional[str] = Field(None, description="输出格式")
    # 新增字段以匹配前端
    background: Optional[Dict[str, Any]] = Field(None, description="背景配置")
    elements: Optional[List[Dict[str, Any]]] = Field(default=[], description="元素列表")
    isPublic: Optional[bool] = Field(default=False, description="是否公开")

class CoverTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    variables: Optional[List[str]] = Field(None)
    description: Optional[str] = Field(None)
    category: Optional[str] = Field(None)
    tags: Optional[List[str]] = Field(None)
    # 新增字段以支持画布内容更新
    elements: Optional[List[Dict[str, Any]]] = Field(None, description="画布元素列表")
    background: Optional[Dict[str, Any]] = Field(None, description="背景配置")
    updatedAt: Optional[str] = Field(None, description="更新时间")

class CoverTemplateResponse(CoverTemplateBase):
    id: str
    previewPath: str
    templatePath: str
    isBuiltIn: bool
    metadata: Dict[str, Any]
    createdAt: Optional[str]
    updatedAt: Optional[str]

class CoverTemplatePreviewRequest(BaseModel):
    variables: Dict[str, str] = Field(default={}, description="变量值")

class CoverTemplatePreviewResponse(BaseModel):
    success: bool
    previewUrl: Optional[str] = Field(None, description="预览图URL")
    error: Optional[str] = Field(None, description="错误信息")

# ================================
# 通用响应 Schemas
# ================================

class ResourceListResponse(BaseModel):
    """资源列表响应"""
    total: int = Field(..., description="总数量")
    items: List[Union[
        BackgroundMusicResponse,
        VideoMaterialResponse,
        PromptResponse,
        AccountResponse,
        CoverTemplateResponse
    ]] = Field(..., description="资源列表")

class ResourceQueryParams(BaseModel):
    """资源查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    category: Optional[str] = Field(None, description="分类过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    is_built_in: Optional[bool] = Field(None, description="是否内置")
    tags: Optional[List[str]] = Field(None, description="标签过滤")

class BatchOperationRequest(BaseModel):
    """批量操作请求"""
    ids: List[str] = Field(..., min_length=1, description="资源ID列表")
    operation: str = Field(..., description="操作类型")
    params: Optional[Dict[str, Any]] = Field(None, description="操作参数")
    
    @validator('ids')
    def validate_ids_not_empty(cls, v):
        if not v:
            raise ValueError('资源ID列表不能为空')
        return v

class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    success_count: int = Field(..., description="成功数量")
    error_count: int = Field(..., description="失败数量")
    errors: List[Dict[str, str]] = Field(default=[], description="错误详情")

# ================================
# 查询参数 Schemas
# ================================

class VideoMaterialQuery(BaseModel):
    """视频素材查询参数"""
    category: Optional[str] = Field(None, description="分类过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    skip: Optional[int] = Field(None, ge=0, description="跳过数量")
    limit: Optional[int] = Field(None, ge=1, le=100, description="限制数量")

class BackgroundMusicQuery(BaseModel):
    """背景音乐查询参数"""
    category: Optional[str] = Field(None, description="分类过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    skip: Optional[int] = Field(None, ge=0, description="跳过数量")
    limit: Optional[int] = Field(None, ge=1, le=100, description="限制数量")

class PromptQuery(BaseModel):
    """提示词查询参数"""
    category: Optional[str] = Field(None, description="分类过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    skip: Optional[int] = Field(None, ge=0, description="跳过数量")
    limit: Optional[int] = Field(None, ge=1, le=100, description="限制数量")

class AccountQuery(BaseModel):
    """账户查询参数"""
    platform: Optional[str] = Field(None, description="平台过滤")
    is_active: Optional[bool] = Field(None, description="是否激活")
    search: Optional[str] = Field(None, description="搜索关键词")
    skip: Optional[int] = Field(None, ge=0, description="跳过数量")
    limit: Optional[int] = Field(None, ge=1, le=100, description="限制数量")

class CoverTemplateQuery(BaseModel):
    """封面模板查询参数"""
    category: Optional[str] = Field(None, description="分类过滤")
    search: Optional[str] = Field(None, description="搜索关键词")
    skip: Optional[int] = Field(None, ge=0, description="跳过数量")
    limit: Optional[int] = Field(None, ge=1, le=100, description="限制数量")

# ================================
# 批量操作响应 Schemas
# ================================

class BulkVideoMaterialResponse(BaseModel):
    """批量视频素材响应"""
    success: List[VideoMaterialResponse] = Field(..., description="成功创建的素材")
    failed: List[Dict[str, str]] = Field(..., description="失败的素材")
    total: int = Field(..., description="总数")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")

class BulkBackgroundMusicResponse(BaseModel):
    """批量背景音乐响应"""
    success: List[BackgroundMusicResponse] = Field(..., description="成功创建的音乐")
    failed: List[Dict[str, str]] = Field(..., description="失败的音乐")
    total: int = Field(..., description="总数")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")

class BulkPromptResponse(BaseModel):
    """批量提示词响应"""
    success: List[PromptResponse] = Field(..., description="成功创建的提示词")
    failed: List[Dict[str, str]] = Field(..., description="失败的提示词")
    total: int = Field(..., description="总数")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")

class BulkAccountResponse(BaseModel):
    """批量账户响应"""
    success: List[AccountResponse] = Field(..., description="成功创建的账户")
    failed: List[Dict[str, str]] = Field(..., description="失败的账户")
    total: int = Field(..., description="总数")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")

class BulkCoverTemplateResponse(BaseModel):
    """批量封面模板响应"""
    success: List[CoverTemplateResponse] = Field(..., description="成功创建的模板")
    failed: List[Dict[str, str]] = Field(..., description="失败的模板")
    total: int = Field(..., description="总数")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
