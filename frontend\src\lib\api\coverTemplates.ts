/**
 * 封面模板管理API客户端
 */

import { DirectHttpClient } from './directHttpClient';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  code?: string;
}

export interface TemplateListQuery {
  category?: string;
  isPublic?: boolean;
  search?: string;
  page?: number;
  pageSize?: number;
  hasVariables?: boolean;
}

export interface CreateTemplateData {
  name: string;
  description?: string;
  category?: string;
  width?: number;
  height?: number;
  background?: any;
  elements?: any[];
  variables?: any[];
  isPublic?: boolean;
}

export interface UpdateTemplateData extends Partial<CreateTemplateData> {}

export interface PreviewData {
  avatar?: string;
  nickname?: string;
  title?: string;
  [key: string]: any;
}

export interface GenerateCoverData extends PreviewData {}

// 获取所有模板
export async function getAllTemplates(params: TemplateListQuery = {}): Promise<ApiResponse> {
  const searchParams = new URLSearchParams();
  
  if (params.category) searchParams.append('category', params.category);
  if (params.isPublic !== undefined) searchParams.append('is_public', params.isPublic.toString());
  if (params.search) searchParams.append('search', params.search);
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.pageSize) searchParams.append('page_size', params.pageSize.toString());

  const client = new DirectHttpClient('/api/cover-templates');
  const queryString = searchParams.toString();
  const endpoint = queryString ? `/?${queryString}` : '/';
  
  return await client.get(endpoint);
}

// 根据ID获取模板
export async function getTemplateById(id: string): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.get(`/${id}`);
}

// 创建模板
export async function createTemplate(data: CreateTemplateData): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.post('/', data);
}

// 更新模板
export async function updateTemplate(id: string, data: UpdateTemplateData): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.put(`/${id}`, data);
}

// 删除模板
export async function deleteTemplate(id: string): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.delete(`/${id}`);
}

// 获取模板统计信息
export async function getTemplateStats(): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.get('/stats');
}

// 获取可用变量  
export async function getAvailableVariables(): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.get('/variables');
}

// 预览模板
export async function previewTemplate(id: string, data: PreviewData = {}): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.post(`/${id}/preview`, data);
}

// 生成封面
export async function generateCover(templateId: string, data: GenerateCoverData): Promise<ApiResponse> {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.post('/generate/', {
    template_id: templateId,
    variables: data
  });
}

// 上传模板文件
export async function uploadTemplate(file: File): Promise<ApiResponse> {
  const formData = new FormData();
  formData.append('file', file);

  const client = new DirectHttpClient('/api/cover-templates');
  return await client.uploadFile('/upload/', formData);
}

// 获取模板缩略图URL  
export function getTemplateThumbnailUrl(templateId: string): string {
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
  return `${API_BASE}/api/cover-templates/thumbnail/${templateId}`;
}

// 获取模板预览URL
export function getTemplatePreviewUrl(templateId: string): string {
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
  return `${API_BASE}/api/cover-templates/preview/${templateId}`;
}

// 导入HTML模板
export const importHtmlTemplate = async (
  file: File, 
  name: string, 
  description: string = "", 
  category: string = "custom"
): Promise<ApiResponse> => {
  console.log('🚀 importHtmlTemplate 开始执行');
  console.log('📄 文件信息:', {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified,
    fileObject: file
  });
  console.log('📝 表单数据:', { name, description, category });

  const formData = new FormData();
  formData.append('file', file);
  formData.append('name', name);
  formData.append('description', description);
  formData.append('category', category);

  console.log('📦 FormData 已创建，包含文件和表单字段');
  
  // 调试：打印 FormData 内容
  console.log('🔍 FormData 内容检查:');
  console.log('  file:', formData.get('file'));
  console.log('  name:', formData.get('name'));
  console.log('  description:', formData.get('description'));
  console.log('  category:', formData.get('category'));
  
  const fileValue = formData.get('file');
  if (fileValue instanceof File) {
    console.log(`  文件详情: ${fileValue.name}, 大小: ${fileValue.size}, 类型: ${fileValue.type}`);
  }

  const client = new DirectHttpClient('/api/cover-templates');
  console.log('🌐 发送请求到: /api/cover-templates/import-html');
  
  try {
    const result = await client.uploadFile('/import-html', formData) as ApiResponse;
    console.log('✅ 请求成功:', result);
    return result;
  } catch (error: any) {
    console.error('❌ 请求失败:', error);
    // 如果有响应数据，打印详细错误信息
    if (error.response) {
      console.error('❌ 响应状态:', error.response.status);
      console.error('❌ 响应数据:', error.response.data);
    }
    throw error;
  }
};

// 渲染模板
export const renderTemplate = async (
  templateId: string, 
  variables: Record<string, string> = {}
): Promise<ApiResponse> => {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.post(`/${templateId}/render`, variables);
};

// 获取模板变量
export const getTemplateVariables = async (templateId: string): Promise<ApiResponse> => {
  const client = new DirectHttpClient('/api/cover-templates');
  return await client.get(`/${templateId}/variables`);
};
