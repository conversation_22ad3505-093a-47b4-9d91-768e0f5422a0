"""
诊断视频生成任务队列状态
检查任务是否真正启动和运行
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent / "backend"))

from sqlalchemy.orm import sessionmaker
from src.core.database import get_session_maker
from src.services.video_generation_service import VideoGenerationService
from src.models import VideoGenerationJob, VideoGenerationTask, TaskStatus
from loguru import logger

async def diagnose_task_queue():
    """诊断任务队列状态"""
    print("🔍 开始诊断视频生成任务队列...")
    
    # 1. 检查数据库连接
    try:
        session_maker = get_session_maker()
        db = session_maker()
        
        # 查询任务状态
        running_jobs = db.query(VideoGenerationJob).filter(
            VideoGenerationJob.status == TaskStatus.RUNNING
        ).all()
        
        pending_tasks = db.query(VideoGenerationTask).filter(
            VideoGenerationTask.status == TaskStatus.PENDING
        ).all()
        
        print(f"📊 数据库状态:")
        print(f"   正在运行的作业: {len(running_jobs)}")
        print(f"   待处理的任务: {len(pending_tasks)}")
        
        if running_jobs:
            print(f"\n📋 正在运行的作业详情:")
            for job in running_jobs:
                print(f"   作业 {job.id}: {job.name}")
                print(f"   创建时间: {job.created_at}")
                print(f"   开始时间: {job.started_at}")
                
                job_tasks = db.query(VideoGenerationTask).filter(
                    VideoGenerationTask.job_id == job.id
                ).all()
                
                for task in job_tasks:
                    print(f"     任务 {task.id}: {task.task_name} ({task.status})")
        
        if pending_tasks:
            print(f"\n⏳ 待处理任务详情:")
            for task in pending_tasks:
                print(f"   任务 {task.id}: {task.task_name}")
                print(f"   作业 {task.job_id}")
                print(f"   创建时间: {task.created_at}")
                print(f"   开始时间: {task.started_at}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    # 2. 创建并测试任务服务
    try:
        print(f"\n🚀 创建视频生成服务...")
        task_service = VideoGenerationService(session_maker)
        
        # 检查任务队列状态
        print(f"   任务队列运行状态: {task_service._task_queue_running}")
        print(f"   运行中的作业: {len(task_service.running_jobs)}")
        
        # 尝试启动任务队列
        print(f"\n▶️  尝试启动任务队列...")
        await task_service.start_task_queue()
        
        print(f"   任务队列运行状态: {task_service._task_queue_running}")
        
        # 等待一段时间观察任务处理
        print(f"\n⏱️  等待5秒观察任务处理...")
        await asyncio.sleep(5)
        
        # 再次检查数据库状态
        db = session_maker()
        
        updated_pending_tasks = db.query(VideoGenerationTask).filter(
            VideoGenerationTask.status == TaskStatus.PENDING
        ).all()
        
        running_tasks = db.query(VideoGenerationTask).filter(
            VideoGenerationTask.status == TaskStatus.RUNNING
        ).all()
        
        print(f"\n📊 5秒后的状态:")
        print(f"   待处理任务: {len(updated_pending_tasks)}")
        print(f"   运行中任务: {len(running_tasks)}")
        
        if running_tasks:
            print(f"\n🏃 运行中的任务:")
            for task in running_tasks:
                print(f"   任务 {task.id}: {task.task_name}")
                print(f"   当前步骤: {task.current_step}")
                print(f"   进度: {task.progress}%")
        
        # 停止任务队列
        await task_service.stop_task_queue()
        db.close()
        
    except Exception as e:
        print(f"❌ 任务服务测试失败: {e}")
        import traceback
        traceback.print_exc()

async def manual_trigger_task():
    """手动触发一个任务"""
    print(f"\n🔧 手动触发任务处理...")
    
    try:
        session_maker = get_session_maker()
        task_service = VideoGenerationService(session_maker)
        
        # 查找第一个待处理任务
        db = session_maker()
        pending_task = db.query(VideoGenerationTask).join(VideoGenerationJob).filter(
            VideoGenerationJob.status == TaskStatus.RUNNING,
            VideoGenerationTask.status == TaskStatus.PENDING
        ).first()
        
        if not pending_task:
            print("❌ 没有找到待处理的任务")
            db.close()
            return
        
        print(f"✅ 找到待处理任务: {pending_task.id} - {pending_task.task_name}")
        
        # 手动执行任务
        try:
            job_config = pending_task.job.config
            print(f"🚀 开始执行任务...")
            
            await task_service._execute_task(pending_task.id, job_config)
            print(f"✅ 任务执行完成")
            
        except Exception as e:
            print(f"❌ 任务执行失败: {e}")
            import traceback
            traceback.print_exc()
        
        db.close()
        
    except Exception as e:
        print(f"❌ 手动触发失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("="*60)
    print("🔧 视频生成任务队列诊断工具")
    print("="*60)
    
    await diagnose_task_queue()
    
    # 询问是否要手动触发任务
    print(f"\n❓ 是否要手动触发一个待处理任务？(y/n)")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            await manual_trigger_task()
    except KeyboardInterrupt:
        print(f"\n👋 用户取消")
    
    print(f"\n✅ 诊断完成")

if __name__ == "__main__":
    asyncio.run(main())
