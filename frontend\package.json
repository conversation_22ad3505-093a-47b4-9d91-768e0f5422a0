{"name": "reddit-story-generator-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start:prod:optimized": "set NODE_ENV=production && next start -p 3000", "build:prod:fast": "set NODE_ENV=production&& set SKIP_ESLINT=true&& set SKIP_TYPE_CHECK=true&& next build", "lint": "next lint", "export": "next build && next export", "build:production": "set NODE_ENV=production && next build", "build:exe": "npm run build:production && npm run copy-to-backend", "copy-to-backend": "xcopy /E /I /Y out\\* ..\\backend\\frontend_dist\\", "type-check": "tsc --noEmit"}, "dependencies": {"@heroicons/react": "^2.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "lucide-react": "^0.292.0", "next": "15.0.3", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "zustand": "^4.5.7"}, "devDependencies": {"@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}}