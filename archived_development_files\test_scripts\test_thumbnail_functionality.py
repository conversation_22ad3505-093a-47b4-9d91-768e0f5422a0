#!/usr/bin/env python3
"""
测试视频缩略图功能
验证缩略图生成、显示和居中效果
"""

import time
import os
import requests
from pathlib import Path

def test_thumbnail_functionality():
    """测试缩略图功能"""
    print("🖼️ 开始测试视频缩略图功能...")
    
    # 检查后端API端点
    print("\n📋 检查后端API端点...")
    
    backend_file = Path("backend/src/api/video.py")
    if not backend_file.exists():
        print("❌ 后端API文件不存在")
        return False
    
    content = backend_file.read_text(encoding='utf-8')
    
    # 检查缩略图生成函数
    if 'def generate_thumbnail(' in content:
        print("✅ 缩略图生成函数存在")
    else:
        print("❌ 缩略图生成函数缺失")
        return False
    
    # 检查缩略图服务端点
    if '/thumbnail/{material_id}' in content:
        print("✅ 缩略图服务端点存在")
    else:
        print("❌ 缩略图服务端点缺失")
        return False
    
    # 检查ffmpeg调用
    if 'ffmpeg' in content and '-ss' in content and '-vframes' in content:
        print("✅ FFmpeg缩略图生成命令正确")
    else:
        print("❌ FFmpeg缩略图生成命令有误")
        return False
    
    # 检查前端代码
    print("\n📋 检查前端代码...")
    
    frontend_file = Path("frontend/src/app/videos/page.tsx")
    if not frontend_file.exists():
        print("❌ 前端页面文件不存在")
        return False
    
    frontend_content = frontend_file.read_text(encoding='utf-8')
    
    # 检查缩略图URL构建
    if 'localhost:8000${material.thumbnailUrl}' in frontend_content:
        print("✅ 缩略图URL构建正确")
    else:
        print("❌ 缩略图URL构建有误")
        return False
    
    # 检查缩略图错误处理
    if 'onError=' in frontend_content and '缩略图加载失败' in frontend_content:
        print("✅ 缩略图错误处理存在")
    else:
        print("❌ 缩略图错误处理缺失")
        return False
    
    # 检查居中CSS样式
    centering_styles = [
        'display: flex',
        'align-items: center',
        'justify-content: center'
    ]
    
    preview_found = False
    for i, line in enumerate(frontend_content.split('\n')):
        if '.material-preview {' in line:
            preview_found = True
            # 检查接下来的几行是否包含居中样式
            next_lines = frontend_content.split('\n')[i:i+15]  # 增加检查范围
            styles_found = []
            for style in centering_styles:
                style_found = any(style in l for l in next_lines)
                if style_found:
                    styles_found.append(style)
                    print(f"✅ 缩略图容器样式: {style}")
                else:
                    print(f"❌ 缩略图容器样式缺失: {style}")
            
            # 只要找到所有样式就认为通过
            if len(styles_found) == len(centering_styles):
                break
            else:
                return False
    
    if not preview_found:
        print("❌ .material-preview 样式未找到")
        return False
    
    # 检查缩略图CSS
    if 'object-fit: cover' in frontend_content:
        print("✅ 缩略图object-fit样式正确")
    else:
        print("❌ 缩略图object-fit样式缺失")
        return False
    
    # 检查模型字段
    print("\n📋 检查数据模型...")
    
    model_file = Path("backend/src/models/resources.py")
    if not model_file.exists():
        print("❌ 数据模型文件不存在")
        return False
    
    model_content = model_file.read_text(encoding='utf-8')
    
    # 检查thumbnailUrl字段生成
    if '/api/video-materials/thumbnail/' in model_content:
        print("✅ thumbnailUrl字段生成正确")
    else:
        print("❌ thumbnailUrl字段生成有误")
        return False
    
    # 检查thumbnail_path字段
    if 'thumbnail_path = Column(String' in model_content:
        print("✅ thumbnail_path数据库字段存在")
    else:
        print("❌ thumbnail_path数据库字段缺失")
        return False
    
    print("\n🎯 缩略图功能验证详情:")
    print("📐 后端使用FFmpeg生成视频第1秒的缩略图")
    print("📐 缩略图存储在uploads目录，文件名格式: thumb_*.jpg")
    print("📐 thumbnailUrl指向HTTP端点: /api/video-materials/thumbnail/{id}")
    print("📐 前端使用flexbox实现缩略图容器居中")
    print("📐 缩略图使用object-fit: cover保持比例填充")
    print("📐 支持竖屏视频缩略图居中显示")
    print("📐 包含缩略图加载失败的降级处理")
    
    print("\n✅ 所有缩略图功能测试通过！")
    return True

def print_test_scenarios():
    """打印测试场景说明"""
    print("\n📋 手动测试场景:")
    print("1. 上传不同比例的视频文件:")
    print("   - 竖屏视频 (9:16) - 缩略图应居中显示")
    print("   - 横屏视频 (16:9) - 缩略图应正常显示")
    print("   - 方形视频 (1:1) - 缩略图应正常显示")
    
    print("\n2. 验证缩略图生成:")
    print("   - 检查uploads目录是否生成thumb_*.jpg文件")
    print("   - 验证缩略图是否为视频第1秒的画面")
    print("   - 确认缩略图文件大小合理(通常几KB到几十KB)")
    
    print("\n3. 验证前端显示:")
    print("   - 视频卡片应显示真实缩略图而非图标")
    print("   - 竖屏视频缩略图应在卡片中居中")
    print("   - 缩略图加载失败时应降级显示图标")
    
    print("\n4. 验证HTTP端点:")
    print("   - 直接访问: http://localhost:8000/api/video-materials/thumbnail/{id}")
    print("   - 应返回JPEG图片而不是404错误")
    print("   - 检查浏览器网络面板的缩略图请求")

def print_troubleshooting():
    """打印故障排除指南"""
    print("\n🔧 故障排除指南:")
    print("1. 如果缩略图不显示:")
    print("   - 检查FFmpeg是否正确安装: ffmpeg -version")
    print("   - 查看后端日志是否有缩略图生成错误")
    print("   - 检查uploads目录权限和磁盘空间")
    print("   - 验证视频文件格式是否被FFmpeg支持")
    
    print("\n2. 如果缩略图不居中:")
    print("   - 检查浏览器开发者工具CSS样式")
    print("   - 确认.material-preview有flexbox居中样式")
    print("   - 验证缩略图的object-fit属性")
    
    print("\n3. 如果HTTP端点返回404:")
    print("   - 确认缩略图文件确实存在于磁盘")
    print("   - 检查数据库中thumbnail_path字段不为空")
    print("   - 验证路由注册是否正确")
    
    print("\n4. 性能优化建议:")
    print("   - 考虑为缩略图添加HTTP缓存头")
    print("   - 可以异步生成缩略图避免阻塞上传")
    print("   - 大文件可以考虑压缩缩略图尺寸")

def main():
    """主函数"""
    print("🖼️ 视频缩略图功能测试")
    print("=" * 50)
    
    # 运行测试
    success = test_thumbnail_functionality()
    
    # 打印测试场景
    print_test_scenarios()
    
    # 打印故障排除指南
    print_troubleshooting()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有缩略图功能测试通过！")
        print("\n🎯 主要改进:")
        print("- 添加了缩略图HTTP服务端点")
        print("- 修复了thumbnailUrl字段生成")
        print("- 优化了缩略图容器居中显示")
        print("- 增强了缩略图加载错误处理")
        print("- 确保竖屏视频缩略图居中显示")
        
        print("\n📝 接下来测试:")
        print("1. 上传一些不同比例的视频文件")
        print("2. 验证缩略图是否正确生成和显示")
        print("3. 特别关注竖屏视频的缩略图居中效果")
    else:
        print("❌ 测试失败！需要修复缩略图功能")
    
    return success

if __name__ == "__main__":
    main()
