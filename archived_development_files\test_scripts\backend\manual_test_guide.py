#!/usr/bin/env python3
"""
前后端联调手动测试清单
详细的测试步骤和验证要点
"""

import webbrowser
import time
import subprocess
import sys
from pathlib import Path

class ManualTestGuide:
    def __init__(self):
        self.frontend_url = "http://localhost:3000"
        self.backend_url = "http://localhost:8000"
        self.api_docs_url = f"{self.backend_url}/docs"
        
    def print_header(self, title: str):
        """打印标题"""
        print("\n" + "="*60)
        print(f"  {title}")
        print("="*60)
    
    def print_section(self, title: str):
        """打印章节"""
        print(f"\n📋 {title}")
        print("-"*40)
    
    def wait_for_user(self, message: str = "Press Enter to continue..."):
        """等待用户确认"""
        try:
            input(f"\n⏸️ {message}")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)
    
    def open_url(self, url: str, description: str):
        """打开URL"""
        print(f"🌐 Opening {description}: {url}")
        try:
            webbrowser.open(url)
            time.sleep(1)
        except Exception as e:
            print(f"❌ Failed to open {url}: {e}")
            print(f"Please manually open: {url}")
    
    def run_test_guide(self):
        """运行测试指南"""
        
        self.print_header("Reddit Story Video Generator - Manual Test Guide")
        
        print("""
🎯 This guide will walk you through manually testing the integration
   between the frontend and backend components.

📋 What we'll test:
   1. Server startup and accessibility
   2. Frontend page loading and navigation
   3. API connectivity and data flow
   4. Settings management
   5. Resource management (music, videos, prompts, etc.)
   6. Error handling and edge cases
""")
        
        self.wait_for_user("Ready to start? Press Enter...")
        
        # 1. 服务器启动检查
        self.print_section("1. Server Startup Check")
        print("""
✅ Tasks to verify:
   - Backend server is running on http://localhost:8000
   - Frontend server is running on http://localhost:3000
   - No startup errors in console
   - All dependencies are installed
""")
        
        print("🚀 If servers are not running, start them with:")
        print("   Backend: cd backend && python start_server.py")
        print("   Frontend: cd frontend && npm run dev")
        
        self.wait_for_user("Are both servers running? Press Enter when ready...")
        
        # 2. 打开应用
        self.print_section("2. Application Access")
        
        print("🌐 Opening applications...")
        self.open_url(self.frontend_url, "Frontend Application")
        self.open_url(self.api_docs_url, "API Documentation")
        
        print("""
✅ Verify:
   - Frontend loads without errors
   - No console errors in browser
   - Navigation menu is visible
   - API docs are accessible
""")
        
        self.wait_for_user("Can you see both applications? Press Enter...")
        
        # 3. 前端导航测试
        self.print_section("3. Frontend Navigation Test")
        
        pages_to_test = [
            ("Dashboard", "/dashboard", "Main overview page"),
            ("Settings", "/settings", "Application settings"),
            ("Background Music", "/background-music", "Music library management"),
            ("Video Materials", "/video-materials", "Video assets management"),
            ("Prompts", "/prompts", "AI prompt templates"),
            ("Accounts", "/accounts", "Social media accounts"),
            ("Cover Templates", "/cover-templates", "Video cover designs"),
            ("Video Generator", "/video-generator", "Video generation interface"),
            ("Task Queue", "/task-queue", "Generation task monitoring")
        ]
        
        print("🎯 Navigate through each page and verify:")
        for page_name, path, description in pages_to_test:
            print(f"   • {page_name} ({path}) - {description}")
        
        print("""
✅ For each page, check:
   - Page loads without errors
   - Layout renders correctly
   - Navigation links work
   - No console errors
   - Data loads (where applicable)
""")
        
        self.wait_for_user("Tested all pages? Press Enter...")
        
        # 4. API连接测试
        self.print_section("4. API Connectivity Test")
        
        print("🔗 Test API endpoints through the frontend:")
        print("""
Settings Page:
   ✅ Settings load automatically
   ✅ Can modify and save settings
   ✅ Changes persist after page refresh

Background Music Page:
   ✅ Music list loads
   ✅ Can filter by category
   ✅ Can add/edit/delete music items
   ✅ File upload works (if implemented)

Video Materials Page:
   ✅ Video list loads
   ✅ Can filter by category
   ✅ Can add/edit/delete video items
   ✅ Thumbnail displays correctly

Prompts Page:
   ✅ Prompt list loads
   ✅ Can filter by category
   ✅ Can add/edit/delete prompts
   ✅ Template preview works

Accounts Page:
   ✅ Account list loads
   ✅ Can filter by platform
   ✅ Can add/edit/delete accounts
   ✅ Authentication status displays

Cover Templates Page:
   ✅ Template list loads
   ✅ Can filter by category
   ✅ Can add/edit/delete templates
   ✅ Template preview works
""")
        
        self.wait_for_user("Tested API connectivity? Press Enter...")
        
        # 5. 数据持久化测试
        self.print_section("5. Data Persistence Test")
        
        print("""
🔄 Test data persistence:

1. Add New Items:
   - Add a background music item
   - Add a video material
   - Add a custom prompt
   - Add an account
   - Add a cover template

2. Modify Existing Items:
   - Edit item details
   - Change categories
   - Update descriptions

3. Refresh Browser:
   - Reload the page
   - Navigate away and back
   - Verify changes persist

4. Delete Items:
   - Delete test items
   - Verify removal persists
""")
        
        self.wait_for_user("Tested data persistence? Press Enter...")
        
        # 6. 错误处理测试
        self.print_section("6. Error Handling Test")
        
        print("""
🚨 Test error scenarios:

Network Issues:
   - Stop backend server temporarily
   - Try to load data / save changes
   - Verify error messages appear
   - Restart backend and verify recovery

Invalid Data:
   - Submit forms with missing required fields
   - Enter invalid data formats
   - Verify validation messages

Edge Cases:
   - Try to delete non-existent items
   - Submit very long text inputs
   - Test with special characters
   - Verify graceful handling
""")
        
        self.wait_for_user("Tested error handling? Press Enter...")
        
        # 7. 性能和用户体验测试
        self.print_section("7. Performance & UX Test")
        
        print("""
⚡ Performance checks:

Loading Speed:
   ✅ Pages load within 2-3 seconds
   ✅ Data fetching is reasonably fast
   ✅ No noticeable lag in navigation

User Experience:
   ✅ Loading indicators appear during API calls
   ✅ Success/error messages are clear
   ✅ Forms are intuitive and well-labeled
   ✅ Responsive design works on different screen sizes

Browser Console:
   ✅ No JavaScript errors
   ✅ No network errors (except intentional tests)
   ✅ No performance warnings
""")
        
        self.wait_for_user("Checked performance and UX? Press Enter...")
        
        # 8. 生成测试报告
        self.print_section("8. Test Report")
        
        print("""
📊 Manual Test Summary:

Based on your testing, please evaluate:

✅ PASS / ❌ FAIL - Server startup and accessibility
✅ PASS / ❌ FAIL - Frontend page loading and navigation  
✅ PASS / ❌ FAIL - API connectivity and data flow
✅ PASS / ❌ FAIL - Settings management
✅ PASS / ❌ FAIL - Resource management functionality
✅ PASS / ❌ FAIL - Data persistence
✅ PASS / ❌ FAIL - Error handling
✅ PASS / ❌ FAIL - Performance and UX

🔍 Common Issues to Look For:
   - CORS errors in browser console
   - 404 errors for API endpoints
   - Hydration mismatches in React
   - TypeScript type errors
   - Database connection issues
   - Missing environment variables

📝 Next Steps:
   - If any tests failed, check the console logs
   - Review the API documentation at {self.api_docs_url}
   - Check network tab for failed requests
   - Verify backend server logs for errors

🎉 If all tests passed:
   - The integration is working correctly!
   - Ready for production deployment
   - Consider adding automated tests for CI/CD
""")
        
        print(f"\n🌟 Congratulations! You've completed the manual integration test.")
        print(f"🔗 Keep these URLs handy for future testing:")
        print(f"   Frontend: {self.frontend_url}")
        print(f"   Backend: {self.backend_url}")
        print(f"   API Docs: {self.api_docs_url}")

def main():
    """主函数"""
    guide = ManualTestGuide()
    
    try:
        guide.run_test_guide()
    except KeyboardInterrupt:
        print("\n\n👋 Test guide interrupted. Goodbye!")
    except Exception as e:
        print(f"\n💥 Error running test guide: {e}")

if __name__ == "__main__":
    main()
