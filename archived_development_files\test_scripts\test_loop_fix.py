#!/usr/bin/env python3
"""
测试修复无限循环问题
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_loop_fix():
    """测试修复无限循环问题"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试修复无限循环问题...")
    logger.info("关键修复：限制loop循环次数，添加enable时间限制")
    
    # 测试配置 - 使用较短的时间快速验证
    test_configs = [
        {
            'name': '修复循环-快速淡入测试',
            'animation': 'fade_in',
            'duration': 5.0,  # 5秒显示
            'animation_duration': 2.0,  # 2秒淡入动画
            'output': 'loop_fix_fade_in.mp4'
        },
        {
            'name': '修复循环-快速淡出测试', 
            'animation': 'fade_out',
            'duration': 5.0,  # 5秒显示
            'animation_duration': 2.0,  # 2秒淡出动画
            'output': 'loop_fix_fade_out.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.6)  # 较小的封面，减少处理时间
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"loop_fix_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    logger.info("开始应用封面叠加效果...")
                    import time
                    start_time = time.time()
                    
                    # 应用封面叠加效果（使用修复后的代码）
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    total_duration = config['duration'] + 1  # 多1秒缓冲
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='ultrafast',  # 使用最快预设
                        pix_fmt='yuv420p',
                        t=total_duration
                    ).overwrite_output()
                    
                    logger.info(f"开始生成{total_duration}秒的测试视频...")
                    
                    # 执行，设置超时
                    import subprocess
                    
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
                    
                    # 使用subprocess执行，设置超时
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    try:
                        # 等待最多60秒
                        stdout, stderr = process.communicate(timeout=60)
                        
                        if process.returncode == 0:
                            end_time = time.time()
                            processing_time = end_time - start_time
                            
                            # 检查结果
                            if Path(config['output']).exists():
                                file_size = Path(config['output']).stat().st_size
                                logger.info(f"✅ {config['name']} 测试成功!")
                                logger.info(f"   文件大小: {file_size} bytes")
                                logger.info(f"   处理时间: {processing_time:.2f}秒")
                                success_count += 1
                            else:
                                logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        else:
                            logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                            logger.error(f"stderr: {stderr.decode('utf-8', errors='ignore')}")
                            
                    except subprocess.TimeoutExpired:
                        logger.error(f"❌ {config['name']} 测试超时（60秒），可能仍有无限循环问题")
                        process.kill()
                        process.communicate()
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"\n=== 修复循环测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count > 0:
            logger.info("\n📋 修复循环测试文件:")
            logger.info("- loop_fix_fade_in.mp4 (修复循环-快速淡入)")
            logger.info("- loop_fix_fade_out.mp4 (修复循环-快速淡出)")
            
            logger.info("\n🔍 关键修复点:")
            logger.info("1. 限制loop循环次数：避免loop=-1无限循环")
            logger.info("2. 添加enable时间限制：确保在指定时间内结束")
            logger.info("3. 使用ultrafast预设：加快处理速度")
            logger.info("4. 设置处理超时：防止卡死")
            
            logger.info("\n🎬 如果测试在60秒内完成且有fade效果，")
            logger.info("说明无限循环问题已解决！")
            
            return True
        else:
            logger.error("❌ 所有测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    import time
    
    logger.info("🚀 开始修复无限循环测试")
    logger.info("本次修复：限制loop循环次数，添加enable时间限制")
    
    success = test_loop_fix()
    
    if success:
        logger.info("\n🎉 修复循环测试完成!")
        logger.info("如果测试快速完成且有fade效果，说明无限循环问题已解决！")
        logger.info("现在可以重新生成你的视频，应该不会再卡死了。")
    else:
        logger.error("\n❌ 修复循环测试失败")
        logger.error("可能需要进一步调整loop参数")
        sys.exit(1)
