@echo off
echo === 视频素材管理页面集成测试 ===

cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator"

echo 1. 启动后端服务器...
cd backend
start "Backend Server" python main.py
cd ..

echo 2. 等待后端启动...
timeout /t 5 /nobreak > nul

echo 3. 启动前端开发服务器...
cd frontend
start "Frontend Server" npm run dev
cd ..

echo.
echo === 服务器已启动 ===
echo 后端API: http://localhost:8000
echo 前端页面: http://localhost:3000
echo 视频素材管理页面: http://localhost:3000/videos
echo API文档: http://localhost:8000/docs
echo.
echo === 测试说明 ===
echo 1. 在浏览器中打开 http://localhost:3000/videos
echo 2. 测试单文件上传功能
echo 3. 测试批量上传功能
echo 4. 检查分类选择功能
echo 5. 检查视频素材展示功能
echo.
echo 按任意键关闭所有服务器...
pause > nul

echo 关闭服务器...
taskkill /f /im node.exe 2>nul
taskkill /f /im python.exe 2>nul
echo 所有服务器已关闭
