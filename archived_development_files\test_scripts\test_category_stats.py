#!/usr/bin/env python3
"""
测试分类数量统计功能
验证前端分类统计是否正确工作
"""

import requests
import json
import time

def test_category_stats():
    """测试分类统计功能"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试分类数量统计功能")
    print("=" * 50)
    
    try:
        # 1. 获取所有视频素材
        print("1. 获取所有视频素材...")
        response = requests.get(f"{base_url}/api/video-materials")
        if response.status_code != 200:
            print(f"❌ 获取视频素材失败: {response.status_code}")
            return False
        
        data = response.json()
        materials = data.get('data', [])
        print(f"✅ 获取到 {len(materials)} 个视频素材")
        
        # 2. 统计分类数量
        category_stats = {}
        video_materials = [m for m in materials if m.get('type') == 'video']
        
        for material in video_materials:
            category = material.get('category', 'general')
            if not category:  # 处理空字符串或None
                category = 'general'
            category_stats[category] = category_stats.get(category, 0) + 1
        
        print("\n2. 分类统计结果:")
        print(f"总视频数量: {len(video_materials)}")
        for category, count in category_stats.items():
            print(f"  - {category}: {count} 个视频")
        
        # 3. 获取分类列表
        print("\n3. 获取分类列表...")
        response = requests.get(f"{base_url}/api/video-categories")
        if response.status_code == 200:
            categories_data = response.json()
            categories = [cat.get('name') for cat in categories_data.get('data', [])]
            print(f"✅ 系统分类: {categories}")
        else:
            print(f"⚠️ 获取分类列表失败: {response.status_code}")
            categories = list(category_stats.keys())
        
        # 4. 验证每个分类的数量
        print("\n4. 验证分类数量:")
        for category in categories:
            actual_count = category_stats.get(category, 0)
            print(f"  - {category}: {actual_count} 个视频")
        
        print(f"\n✅ 分类统计功能测试完成!")
        print(f"📊 前端应该显示:")
        print(f"  - 全部视频 ({len(video_materials)})")
        for category in categories:
            count = category_stats.get(category, 0)
            display_name = category if category else '默认分类'
            print(f"  - {display_name} ({count})")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保服务器已启动")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_category_stats()
    if not success:
        exit(1)
