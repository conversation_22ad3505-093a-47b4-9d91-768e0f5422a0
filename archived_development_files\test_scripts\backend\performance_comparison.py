#!/usr/bin/env python3
"""
截图服务性能比较测试
比较原版服务与优化版服务的性能差异
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from src.core.database import get_db
from src.services.cover_screenshot_service import cover_screenshot_service
from src.services.optimized_cover_screenshot_service import optimized_cover_screenshot_service
from src.models.accounts import Account
from src.models.resources import CoverTemplate
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

class PerformanceComparison:
    """性能比较工具"""
    
    def __init__(self):
        self.backend_dir = backend_dir
        self.output_dir = backend_dir / "performance_test_outputs"
        self.output_dir.mkdir(exist_ok=True)
        
    def setup_database(self):
        """设置数据库连接"""
        DATABASE_URL = "sqlite:///./reddit_story_generator.db"
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        return SessionLocal()
    
    def create_test_account(self):
        """创建测试账号"""
        class TestAccount:
            def __init__(self):
                self.id = 1
                self.name = "性能测试账号"
                self.platform = "reddit"
                self.avatar_file_path = "uploads/avatars/1_acad3cfb.png"
        return TestAccount()
    
    async def test_service_performance(self, service, service_name: str, template_id: str, test_count: int = 3):
        """测试单个服务的性能"""
        print(f"\n🔄 测试 {service_name} 性能...")
        
        db = self.setup_database()
        account = self.create_test_account()
        test_title = f"性能测试标题 - {service_name} - 包含中文字符测试"
        
        results = []
        
        try:
            for i in range(test_count):
                print(f"  第 {i+1}/{test_count} 次测试...", end=" ")
                
                output_path = self.output_dir / f"{service_name}_test_{i+1}_{template_id[:8]}.png"
                
                start_time = time.time()
                
                # 根据服务类型调用不同的方法
                if hasattr(service, 'generate_cover_screenshot'):
                    if 'optimized' in service_name.lower():
                        # 优化版服务支持fast_mode
                        success = await service.generate_cover_screenshot(
                            template_id=template_id,
                            account=account,
                            title=test_title,
                            output_path=str(output_path),
                            db=db,
                            fast_mode=(i == test_count - 1)  # 最后一次测试使用快速模式
                        )
                    else:
                        success = await service.generate_cover_screenshot(
                            template_id=template_id,
                            account=account,
                            title=test_title,
                            output_path=str(output_path),
                            db=db
                        )
                else:
                    print("❌ 服务不支持生成截图方法")
                    continue
                
                end_time = time.time()
                duration = end_time - start_time
                
                # 检查文件
                file_size = 0
                if output_path.exists():
                    file_size = output_path.stat().st_size
                
                result = {
                    "test_number": i + 1,
                    "success": success,
                    "duration": duration,
                    "file_size": file_size,
                    "output_path": str(output_path)
                }
                
                results.append(result)
                
                if success:
                    print(f"✅ {duration:.2f}s ({file_size} bytes)")
                else:
                    print(f"❌ {duration:.2f}s")
        
        finally:
            db.close()
        
        return results
    
    async def compare_services(self, template_id: str, test_count: int = 3):
        """比较两个服务的性能"""
        print(f"🏁 开始性能比较测试")
        print(f"📋 模板ID: {template_id}")
        print(f"🔢 测试次数: {test_count}")
        
        # 测试原版服务
        original_results = await self.test_service_performance(
            cover_screenshot_service,
            "原版服务",
            template_id,
            test_count
        )
        
        # 测试优化版服务
        optimized_results = await self.test_service_performance(
            optimized_cover_screenshot_service,
            "优化版服务",
            template_id,
            test_count
        )
        
        # 分析结果
        self.analyze_results(original_results, optimized_results)
        
        return original_results, optimized_results
    
    def analyze_results(self, original_results, optimized_results):
        """分析比较结果"""
        print("\n" + "="*60)
        print("📊 性能比较分析")
        print("="*60)
        
        # 计算统计数据
        def calculate_stats(results, name):
            successful_results = [r for r in results if r["success"]]
            if not successful_results:
                print(f"❌ {name}: 所有测试都失败了")
                return None
            
            durations = [r["duration"] for r in successful_results]
            file_sizes = [r["file_size"] for r in successful_results]
            
            stats = {
                "name": name,
                "success_rate": len(successful_results) / len(results) * 100,
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "avg_file_size": sum(file_sizes) / len(file_sizes),
                "total_tests": len(results),
                "successful_tests": len(successful_results)
            }
            
            return stats
        
        original_stats = calculate_stats(original_results, "原版服务")
        optimized_stats = calculate_stats(optimized_results, "优化版服务")
        
        # 打印统计结果
        for stats in [original_stats, optimized_stats]:
            if stats:
                print(f"\n🔹 {stats['name']}:")
                print(f"  成功率: {stats['success_rate']:.1f}% ({stats['successful_tests']}/{stats['total_tests']})")
                print(f"  平均耗时: {stats['avg_duration']:.2f}秒")
                print(f"  最短耗时: {stats['min_duration']:.2f}秒")
                print(f"  最长耗时: {stats['max_duration']:.2f}秒")
                print(f"  平均文件大小: {stats['avg_file_size']:.0f} bytes")
        
        # 性能比较
        if original_stats and optimized_stats:
            print(f"\n🏆 性能改进:")
            
            if optimized_stats['success_rate'] > original_stats['success_rate']:
                improvement = optimized_stats['success_rate'] - original_stats['success_rate']
                print(f"  成功率提升: +{improvement:.1f}%")
            elif optimized_stats['success_rate'] < original_stats['success_rate']:
                decline = original_stats['success_rate'] - optimized_stats['success_rate']
                print(f"  成功率下降: -{decline:.1f}%")
            else:
                print(f"  成功率相同: {optimized_stats['success_rate']:.1f}%")
            
            if optimized_stats['avg_duration'] < original_stats['avg_duration']:
                improvement = (original_stats['avg_duration'] - optimized_stats['avg_duration']) / original_stats['avg_duration'] * 100
                print(f"  平均速度提升: {improvement:.1f}% (节省 {original_stats['avg_duration'] - optimized_stats['avg_duration']:.2f}秒)")
            else:
                decline = (optimized_stats['avg_duration'] - original_stats['avg_duration']) / original_stats['avg_duration'] * 100
                print(f"  平均速度下降: {decline:.1f}% (增加 {optimized_stats['avg_duration'] - original_stats['avg_duration']:.2f}秒)")
            
            file_size_diff = optimized_stats['avg_file_size'] - original_stats['avg_file_size']
            file_size_change = file_size_diff / original_stats['avg_file_size'] * 100
            if abs(file_size_change) > 5:  # 只报告显著差异
                if file_size_diff > 0:
                    print(f"  文件大小增加: {file_size_change:.1f}% (+{file_size_diff:.0f} bytes)")
                else:
                    print(f"  文件大小减少: {abs(file_size_change):.1f}% ({file_size_diff:.0f} bytes)")

async def main():
    """主函数"""
    print("⚡ 截图服务性能比较工具")
    print("=" * 50)
    
    comparison = PerformanceComparison()
    
    # 检查依赖
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright 已安装")
    except ImportError:
        print("❌ Playwright 未安装，请运行:")
        print("   pip install playwright")
        print("   playwright install chromium")
        return
    
    # 检查数据库和模板
    try:
        db = comparison.setup_database()
        templates = db.query(CoverTemplate).all()
        db.close()
        print(f"✅ 数据库连接正常，找到 {len(templates)} 个模板")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    if not templates:
        print("❌ 没有找到封面模板，请先导入模板")
        return
    
    # 选择测试模板
    print(f"\n📋 可用模板 (显示前5个):")
    for i, template in enumerate(templates[:5], 1):
        print(f"  {i}. {template.name} ({template.id})")
    
    template_choice = input(f"\n选择模板 (1-{min(5, len(templates))}) 或输入模板ID: ").strip()
    
    if template_choice.isdigit():
        template_index = int(template_choice) - 1
        if 0 <= template_index < len(templates):
            template_id = str(templates[template_index].id)
        else:
            print("❌ 无效的模板选择")
            return
    else:
        # 假设输入的是模板ID
        template_id = template_choice
    
    # 设置测试次数
    test_count_input = input("输入测试次数 (默认3次): ").strip()
    test_count = int(test_count_input) if test_count_input.isdigit() else 3
    
    # 执行性能比较
    await comparison.compare_services(template_id, test_count)
    
    print(f"\n📁 测试输出文件保存在: {comparison.output_dir}")

if __name__ == "__main__":
    asyncio.run(main())
