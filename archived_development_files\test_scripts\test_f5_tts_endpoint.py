#!/usr/bin/env python3
"""
测试F5-TTS服务端点连接
"""

import requests
from gradio_client import Client

def test_f5_tts_endpoint():
    """测试F5-TTS服务端点"""
    
    endpoint = "http://application-e4ee63ebd100431995375a4a07ab41b320250817000003.ssh-hdc.xingluan.cn:1800/"
    
    print(f"🔍 测试F5-TTS服务端点: {endpoint}")
    
    # 1. 测试HTTP连接
    print("\n1. 测试HTTP连接...")
    try:
        response = requests.get(endpoint, timeout=10)
        print(f"✅ HTTP连接成功，状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
    except requests.exceptions.Timeout:
        print("❌ HTTP连接超时")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ HTTP连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ HTTP连接错误: {e}")
        return False
    
    # 2. 测试Gradio客户端连接
    print("\n2. 测试Gradio客户端连接...")
    try:
        client = Client(endpoint)
        print("✅ Gradio客户端连接成功")
        
        # 3. 获取API信息
        print("\n3. 获取API信息...")
        try:
            # 尝试查看可用的API端点
            print(f"API端点信息: {client.endpoints}")
            return True
        except Exception as e:
            print(f"⚠️  无法获取API信息: {e}")
            return True  # 连接成功但无法获取API信息
            
    except Exception as e:
        print(f"❌ Gradio客户端连接失败: {e}")
        return False

def test_gradio_predict():
    """测试Gradio预测功能"""
    
    endpoint = "http://application-e4ee63ebd100431995375a4a07ab41b320250817000003.ssh-hdc.xingluan.cn:1800/"
    
    print(f"\n🧪 测试Gradio预测功能...")
    
    try:
        from gradio_client import Client, handle_file
        
        client = Client(endpoint)
        
        # 使用测试音频文件
        ref_audio_path = "data/f5_tts_voices/e8e22b5f-4b2c-40cd-ac4c-e61b92286506.MP3"
        ref_text = "what hidden truth made you regret hating your parent for years when I was 11 my mom started working nights and weekends barely coming home"
        gen_text = "这是一个测试。"
        
        print(f"参考音频: {ref_audio_path}")
        print(f"参考文本: {ref_text}")
        print(f"生成文本: {gen_text}")
        
        # 调用预测
        result = client.predict(
            ref_audio_input=handle_file(ref_audio_path),
            ref_text_input=ref_text,
            gen_text_input=gen_text,
            remove_silence=False,
            randomize_seed=True,
            seed_input=0,
            cross_fade_duration_slider=0.15,
            nfe_slider=32,
            speed_slider=1.0,
            api_name="/basic_tts"
        )
        
        print(f"✅ 预测成功!")
        print(f"结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🧪 F5-TTS服务端点测试\n")
    
    # 测试端点连接
    connection_ok = test_f5_tts_endpoint()
    
    if connection_ok:
        # 测试预测功能
        predict_ok = test_gradio_predict()
        
        print(f"\n📊 测试结果:")
        print(f"   端点连接: {'✅' if connection_ok else '❌'}")
        print(f"   预测功能: {'✅' if predict_ok else '❌'}")
        
        if connection_ok and predict_ok:
            print("\n🎉 F5-TTS服务工作正常！")
        else:
            print("\n❌ F5-TTS服务存在问题。")
    else:
        print("\n❌ 无法连接到F5-TTS服务，请检查服务是否运行。")
