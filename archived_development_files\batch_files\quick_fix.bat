@echo off
REM 快速解决方案 - 针对当前 npm 检测失败的问题
echo ============================================================
echo 快速解决方案 - 环境问题修复
echo ============================================================
echo.

REM 检查环境
echo 🔍 正在检查环境...
python check_environment.py

echo.
echo 🤔 根据您的情况，这里有几个解决方案:
echo.

REM 选项 1: 检查已有前端构建
if exist "backend\frontend_dist" (
    echo ✅ 选项 1: 使用现有前端构建 (推荐)
    echo    发现已有前端构建产物，可以直接使用
    echo.
) else (
    echo ❌ 选项 1: 使用现有前端构建
    echo    未发现前端构建产物
    echo.
)

echo 🟢 选项 2: 安装 Node.js
echo    下载地址: https://nodejs.org/
echo    安装后重新运行构建
echo.

echo ⚡ 选项 3: 仅构建后端 (快速测试)
echo    跳过前端，仅打包后端代码
echo.

echo 🔧 选项 4: 手动构建前端
echo    cd frontend
echo    npm install
echo    npm run build:exe
echo.

set /p choice="请选择 [1-现有前端/2-安装Node.js/3-仅后端/4-手动前端]: "

if "%choice%"=="1" (
    if exist "backend\frontend_dist" (
        echo ✅ 使用现有前端构建，跳过前端构建步骤...
        python build_config\exe_builder.py --protect pyc --skip-frontend
    ) else (
        echo ❌ 未找到现有前端构建产物
        echo 请选择其他选项
        pause
        exit /b 1
    )
)

if "%choice%"=="2" (
    echo 📥 正在打开 Node.js 下载页面...
    start https://nodejs.org/
    echo.
    echo 安装完成后请重新运行: build.bat
    pause
    exit /b 0
)

if "%choice%"=="3" (
    echo ⚡ 仅构建后端...
    python build_config\exe_builder.py --protect pyc --skip-frontend
)

if "%choice%"=="4" (
    echo 🔧 手动构建前端的步骤:
    echo.
    echo 1. 打开新的命令提示符
    echo 2. cd frontend
    echo 3. npm install
    echo 4. npm run build:exe
    echo 5. 完成后重新运行: build.bat
    echo.
    pause
    exit /b 0
)

if errorlevel 1 (
    echo.
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo 🎉 构建完成！
echo 📁 输出目录: dist\RedditStoryVideoGenerator_Portable\
echo.
set /p open="打开输出目录? (y/n): "
if "%open%"=="y" (
    explorer "dist\RedditStoryVideoGenerator_Portable"
)

pause
