@echo off
echo ====================================
echo 截图服务测试套件
echo ====================================
echo.

cd /d "%~dp0"

echo 当前目录: %CD%
echo.

echo 选择测试模式:
echo 1. 快速验证 (推荐新用户)
echo 2. 基础功能测试
echo 3. 增强诊断测试
echo 4. 性能比较测试
echo 5. 清理测试文件
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto quick_test
if "%choice%"=="2" goto basic_test
if "%choice%"=="3" goto diagnostic_test
if "%choice%"=="4" goto performance_test
if "%choice%"=="5" goto cleanup
goto invalid_choice

:quick_test
echo.
echo 🚀 开始快速验证...
python quick_screenshot_verification.py
pause
goto end

:basic_test
echo.
echo 🧪 开始基础功能测试...
python test_cover_screenshot.py
pause
goto end

:diagnostic_test
echo.
echo 🔬 开始增强诊断测试...
python enhanced_screenshot_diagnostic.py
pause
goto end

:performance_test
echo.
echo ⚡ 开始性能比较测试...
python performance_comparison.py
pause
goto end

:cleanup
echo.
echo 🧹 清理测试文件...
if exist quick_test_outputs rmdir /s /q quick_test_outputs
if exist test_outputs rmdir /s /q test_outputs
if exist diagnostic_outputs rmdir /s /q diagnostic_outputs
if exist performance_test_outputs rmdir /s /q performance_test_outputs
echo 清理完成!
pause
goto end

:invalid_choice
echo ❌ 无效选择
pause
goto end

:end
echo.
echo 测试完成！
