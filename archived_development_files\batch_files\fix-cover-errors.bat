@echo off
echo 🔧 修复封面模板API错误...

echo.
echo 1. 停止现有进程...
taskkill /f /im python.exe 2>nul
taskkill /f /im node.exe 2>nul

echo.
echo 2. 清理并重启后端...
cd backend
start "Fixed Backend" cmd /k "python main.py"
cd ..

echo.
echo 3. 等待后端启动...
timeout /t 5

echo.
echo 4. 测试API修复...
python test_api_fix.py

echo.
echo 5. 启动前端...
cd frontend
start "Fixed Frontend" cmd /k "npm run dev"
cd ..

echo.
echo ✅ 修复完成!
echo.
echo 📍 现在可以访问:
echo    前端: http://localhost:3000/covers
echo    API文档: http://localhost:8001/docs
echo.
pause
