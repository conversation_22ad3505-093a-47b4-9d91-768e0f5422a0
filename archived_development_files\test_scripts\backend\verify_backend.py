#!/usr/bin/env python
"""
后端验证脚本 - 完整测试
"""
import sys
import os
from pathlib import Path

# 设置Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def verify_file_exists():
    """验证关键文件是否存在"""
    print("🔍 验证文件存在性...")
    
    files_to_check = [
        "main.py",
        "requirements.txt", 
        "src/core/config.py",
        "src/core/database.py",
        "src/core/responses.py",
        "src/models/__init__.py",
        "src/models/settings.py",
        "src/schemas/__init__.py",
        "src/schemas/settings.py",
        "src/api/__init__.py",
        "src/api/routes.py",
        "src/api/settings.py"
    ]
    
    missing_files = []
    for file_path in files_to_check:
        full_path = backend_dir / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - 文件缺失")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def verify_imports():
    """验证模块导入"""
    print("\n🔧 验证模块导入...")
    
    try:
        # 测试配置
        from src.core.config import get_settings
        settings = get_settings()
        print(f"  ✅ 配置加载成功: {settings.environment}")
        
        # 测试数据库
        from src.core.database import get_db, init_db
        print("  ✅ 数据库模块导入成功")
        
        # 测试响应格式
        from src.core.responses import ApiResponse
        print("  ✅ 响应格式模块导入成功")
        
        # 测试模型
        from src.models.settings import Settings
        print("  ✅ 设置模型导入成功")
        
        # 测试Schema
        from src.schemas.settings import SettingsResponse, TTSConfig, LLMConfig
        print("  ✅ Schema模块导入成功")
        
        # 测试API路由
        from src.api.settings import router as settings_router
        from src.api.routes import api_router
        print("  ✅ API路由导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def verify_fastapi_app():
    """验证FastAPI应用"""
    print("\n🚀 验证FastAPI应用...")
    
    try:
        from main import app
        print("  ✅ FastAPI应用创建成功")
          # 检查路由
        print(f"  ✅ FastAPI应用包含 {len(app.routes)} 个路由")
        
        # 查找健康检查路由
        health_route_exists = any(hasattr(route, 'path') and '/health' in str(route) for route in app.routes)
        if health_route_exists:
            print("  ✅ 健康检查路由存在")
        
        return True
        
    except Exception as e:
        print(f"  ❌ FastAPI应用验证失败: {e}")
        return False

def verify_settings_model():
    """验证设置模型功能"""
    print("\n⚙️ 验证设置模型...")
    
    try:
        from src.models.settings import Settings
        
        # 创建设置实例
        settings = Settings()
        print("  ✅ 设置模型实例创建成功")
        
        # 测试前端格式转换
        frontend_data = settings.to_frontend_format()
        print("  ✅ 前端格式转换成功")
        
        # 验证数据结构
        required_keys = ['tts', 'llm', 'general']
        for key in required_keys:
            if key in frontend_data:
                print(f"    ✅ {key}: {type(frontend_data[key])}")
            else:
                print(f"    ❌ 缺少 {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 设置模型验证失败: {e}")
        return False

def create_test_request():
    """创建测试请求数据"""
    print("\n📝 创建测试请求...")
    
    try:
        from src.schemas.settings import SettingsUpdateRequest, TTSConfigUpdate
          # 创建测试更新请求
        update_request = SettingsUpdateRequest(
            tts=TTSConfigUpdate(
                provider="openai",
                voice="alloy",
                speed=1.2
            )
        )
        
        print("  ✅ 测试请求创建成功")
        if update_request.tts:
            print(f"    TTS Provider: {update_request.tts.provider}")
            print(f"    Voice: {update_request.tts.voice}")
            print(f"    Speed: {update_request.tts.speed}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试请求创建失败: {e}")
        return False

def main():
    """主验证流程"""
    print("🚀 Reddit Story Video Generator - 后端验证\n")
    
    tests = [
        ("文件存在性检查", verify_file_exists),
        ("模块导入测试", verify_imports),
        ("FastAPI应用验证", verify_fastapi_app),
        ("设置模型验证", verify_settings_model),
        ("测试请求创建", create_test_request)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "="*50)
    print("📊 验证结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有验证通过! 后端准备就绪!")
        print("\n📝 下一步操作:")
        print("1. 运行: uvicorn main:app --reload --host 0.0.0.0 --port 8000")
        print("2. 访问: http://localhost:8000/docs")
        print("3. 测试: http://localhost:8000/api/v1/settings")
    else:
        print("⚠️ 部分验证失败，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
