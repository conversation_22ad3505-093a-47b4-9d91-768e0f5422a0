#!/usr/bin/env python3
"""
封面截图服务增强诊断工具
提供详细的模板分析、图片资源检查和截图质量评估
"""

import sys
import os
import asyncio
from pathlib import Path
import json
import subprocess
import tempfile
from typing import Dict, List, Optional, Tuple
import time

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from src.core.database import get_db
from src.services.cover_screenshot_service import cover_screenshot_service
from src.services.template_import_service import template_import_service
from src.models.accounts import Account
from src.models.resources import CoverTemplate
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

class ScreenshotDiagnosticTool:
    """截图诊断工具"""
    
    def __init__(self):
        self.backend_dir = backend_dir
        self.output_dir = backend_dir / "diagnostic_outputs"
        self.output_dir.mkdir(exist_ok=True)
        
    def setup_database(self):
        """设置数据库连接"""
        DATABASE_URL = "sqlite:///./reddit_story_generator.db"
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        return SessionLocal()
    
    def create_test_account(self):
        """创建测试账号"""
        class TestAccount:
            def __init__(self):
                self.id = 1
                self.name = "DiagnosticTest"
                self.platform = "reddit"
                # 测试不同类型的头像
                self.avatar_file_path = "uploads/avatars/1_acad3cfb.png"
        return TestAccount()
    
    def analyze_template_resources(self, template_id: str, db) -> Dict:
        """分析模板中的资源使用情况"""
        print(f"\n🔍 分析模板资源: {template_id}")
        
        try:
            # 获取模板
            template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
            if not template:
                return {"error": "模板不存在"}
            
            # 渲染模板获取HTML
            account = self.create_test_account()
            variables = {
                'avatar': 'https://via.placeholder.com/50/666666/FFFFFF?text=U',
                'account_name': account.name,
                'title': "测试标题用于资源分析",
                'description': "测试描述",
            }
            
            rendered_html = template_import_service.render_template(
                template_id=template_id,
                variables=variables,
                db=db,
                base_url="http://localhost:8000"
            )
            
            # 分析HTML中的资源
            analysis = self._analyze_html_resources(rendered_html)
            analysis["template_name"] = template.name
            analysis["template_id"] = template.id
            
            return analysis
            
        except Exception as e:
            return {"error": str(e)}
    
    def _analyze_html_resources(self, html_content: str) -> Dict:
        """分析HTML中的资源"""
        import re
        from urllib.parse import urlparse
        
        analysis = {
            "images": [],
            "external_resources": [],
            "css_links": [],
            "fonts": [],
            "statistics": {}
        }
        
        # 查找图片
        img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
        for match in re.finditer(img_pattern, html_content, re.IGNORECASE):
            src = match.group(1)
            img_info = {
                "src": src,
                "type": self._classify_image_source(src),
                "accessible": self._check_resource_accessibility(src)
            }
            analysis["images"].append(img_info)
        
        # 查找CSS链接
        css_pattern = r'<link[^>]+href=["\']([^"\']+\.css)["\'][^>]*>'
        for match in re.finditer(css_pattern, html_content, re.IGNORECASE):
            href = match.group(1)
            analysis["css_links"].append({
                "href": href,
                "accessible": self._check_resource_accessibility(href)
            })
        
        # 查找字体
        font_patterns = [
            r'@import[^;]+fonts\.googleapis\.com[^;]+;',
            r'font-family[^;]*:[^;]*["\']([^"\']+)["\'][^;]*;'
        ]
        for pattern in font_patterns:
            for match in re.finditer(pattern, html_content, re.IGNORECASE):
                analysis["fonts"].append(match.group(0))
        
        # 统计信息
        analysis["statistics"] = {
            "total_images": len(analysis["images"]),
            "base64_images": sum(1 for img in analysis["images"] if img["type"] == "base64"),
            "external_images": sum(1 for img in analysis["images"] if img["type"] == "external"),
            "local_images": sum(1 for img in analysis["images"] if img["type"] == "local"),
            "total_css": len(analysis["css_links"]),
            "total_fonts": len(analysis["fonts"])
        }
        
        return analysis
    
    def _classify_image_source(self, src: str) -> str:
        """分类图片来源"""
        if src.startswith('data:'):
            return "base64"
        elif src.startswith(('http://', 'https://')):
            return "external"
        elif src.startswith('file://'):
            return "local_file"
        else:
            return "local"
    
    def _check_resource_accessibility(self, url: str) -> bool:
        """检查资源可访问性"""
        try:
            if url.startswith('data:'):
                return True
            elif url.startswith(('http://', 'https://')):
                import requests
                response = requests.head(url, timeout=5)
                return response.status_code == 200
            elif url.startswith('file://'):
                file_path = Path(url[7:])  # 移除 'file://' 前缀
                return file_path.exists()
            else:
                # 本地相对路径
                return True  # 假设可访问，实际需要结合base_url检查
        except:
            return False
    
    def create_enhanced_screenshot_script(self, html_content: str, output_path: str, debug_mode: bool = True) -> str:
        """创建增强的截图脚本，包含详细诊断"""
        script_content = f'''
import sys
import os
import json
import time
from playwright.sync_api import sync_playwright
from pathlib import Path

# 确保输出使用UTF-8编码
if os.name == 'nt':  # Windows系统
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def log_debug(message):
    if {str(debug_mode).lower()}:
        print(f"[DEBUG] {{message}}", file=sys.stderr)

def main():
    html_content = """{html_content.replace('"""', '\\"\\"\\"').replace('\\', '\\\\')}"""
    output_path = r"{output_path}"
    debug_output = r"{output_path.replace('.png', '_debug.json')}"
    
    debug_info = {{
        "start_time": time.time(),
        "browser_info": {{}},
        "loading_stages": [],
        "network_requests": [],
        "image_analysis": [],
        "errors": [],
        "final_status": "unknown"
    }}
    
    try:
        with sync_playwright() as p:
            log_debug("启动浏览器...")
            debug_info["loading_stages"].append({{"stage": "browser_launch", "time": time.time()}})
            
            browser = p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox', 
                    '--disable-setuid-sandbox', 
                    '--disable-dev-shm-usage',
                    '--force-device-scale-factor=2',
                    '--disable-web-security',
                    '--allow-running-insecure-content',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-gpu',  # 添加更多稳定性选项
                    '--no-first-run',
                    '--disable-background-timer-throttling'
                ]
            )
            
            page = browser.new_page()
            debug_info["browser_info"] = {{
                "user_agent": page.evaluate("navigator.userAgent"),
                "viewport": page.viewport_size
            }}
            
            # 监听所有网络请求
            def handle_request(request):
                debug_info["network_requests"].append({{
                    "url": request.url,
                    "method": request.method,
                    "resource_type": request.resource_type,
                    "timestamp": time.time()
                }})
            
            def handle_request_failed(request):
                error_info = {{
                    "type": "network_failure",
                    "url": request.url,
                    "failure": request.failure,
                    "timestamp": time.time()
                }}
                debug_info["errors"].append(error_info)
                log_debug(f"网络请求失败: {{request.url}} - {{request.failure}}")
            
            page.on("request", handle_request)
            page.on("requestfailed", handle_request_failed)
            
            # 设置视口
            log_debug("设置视口尺寸...")
            page.set_viewport_size({{"width": 1080, "height": 1920}})
            debug_info["loading_stages"].append({{"stage": "viewport_set", "time": time.time()}})
            
            # 加载HTML内容
            log_debug("加载HTML内容...")
            page.set_content(html_content, wait_until="domcontentloaded", timeout=20000)
            debug_info["loading_stages"].append({{"stage": "content_loaded", "time": time.time()}})
            
            # 预加载和分析图片
            log_debug("分析页面图片...")
            image_analysis = page.evaluate("""
                () => {{
                    const images = Array.from(document.querySelectorAll('img'));
                    const analysis = images.map((img, index) => {{
                        const rect = img.getBoundingClientRect();
                        return {{
                            index: index,
                            src: img.src,
                            alt: img.alt || '',
                            width: img.width,
                            height: img.height,
                            naturalWidth: img.naturalWidth,
                            naturalHeight: img.naturalHeight,
                            complete: img.complete,
                            visible: rect.width > 0 && rect.height > 0,
                            position: {{
                                x: rect.x,
                                y: rect.y,
                                width: rect.width,
                                height: rect.height
                            }},
                            computed_style: {{
                                display: getComputedStyle(img).display,
                                visibility: getComputedStyle(img).visibility,
                                opacity: getComputedStyle(img).opacity
                            }}
                        }};
                    }});
                    
                    // 强制加载所有图片
                    images.forEach(img => {{
                        if (!img.complete && img.src) {{
                            const newImg = new Image();
                            newImg.src = img.src;
                        }}
                    }});
                    
                    return analysis;
                }}
            """)
            
            debug_info["image_analysis"] = image_analysis
            log_debug(f"发现 {{len(image_analysis)}} 张图片")
            
            for img in image_analysis:
                log_debug(f"图片 {{img['index']}}: src={{img['src'][:100]}}... complete={{img['complete']}} visible={{img['visible']}}")
            
            # 等待图片加载
            log_debug("等待图片加载完成...")
            try:
                page.wait_for_function("""
                    () => {{
                        const images = Array.from(document.querySelectorAll('img'));
                        if (images.length === 0) return true;
                        
                        let loaded = 0;
                        let total = images.length;
                        
                        images.forEach(img => {{
                            if (img.complete && (img.naturalWidth > 0 || img.src.startsWith('data:'))) {{
                                loaded++;
                            }}
                        }});
                        
                        console.log(`图片加载进度: ${{loaded}}/${{total}}`);
                        return loaded === total;
                    }}
                """, timeout=30000)
                debug_info["loading_stages"].append({{"stage": "images_loaded", "time": time.time()}})
                log_debug("所有图片加载完成")
            except Exception as e:
                debug_info["errors"].append({{"type": "image_loading_timeout", "message": str(e), "timestamp": time.time()}})
                log_debug(f"图片加载超时: {{e}}")
            
            # 等待渲染稳定
            log_debug("等待渲染稳定...")
            page.wait_for_timeout(3000)
            debug_info["loading_stages"].append({{"stage": "render_stable", "time": time.time()}})
            
            # 最终图片状态检查
            final_image_status = page.evaluate("""
                () => {{
                    const images = Array.from(document.querySelectorAll('img'));
                    return images.map(img => ({{
                        src: img.src.substring(0, 50),
                        complete: img.complete,
                        naturalWidth: img.naturalWidth,
                        naturalHeight: img.naturalHeight,
                        displayed_size: {{width: img.width, height: img.height}}
                    }}));
                }}
            """)
            debug_info["final_image_status"] = final_image_status
            
            # 查找目标元素并截图
            log_debug("查找截图目标元素...")
            cover_element = page.query_selector("#reddit-cover")
            
            if not cover_element:
                log_debug("未找到 #reddit-cover 元素，截图整个页面")
                debug_info["screenshot_type"] = "full_page"
                Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                page.screenshot(path=output_path, type='png', full_page=True)
            else:
                log_debug("找到 #reddit-cover 元素，截图该元素")
                debug_info["screenshot_type"] = "element"
                
                # 获取元素信息
                element_info = cover_element.evaluate("""
                    el => {{
                        const rect = el.getBoundingClientRect();
                        return {{
                            width: rect.width,
                            height: rect.height,
                            x: rect.x,
                            y: rect.y
                        }};
                    }}
                """)
                debug_info["element_info"] = element_info
                
                Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                cover_element.screenshot(
                    path=output_path,
                    type='png',
                    omit_background=False,
                    animations='disabled'
                )
            
            debug_info["loading_stages"].append({{"stage": "screenshot_taken", "time": time.time()}})
            debug_info["final_status"] = "success"
            
            browser.close()
            
            # 保存诊断信息
            debug_info["end_time"] = time.time()
            debug_info["total_duration"] = debug_info["end_time"] - debug_info["start_time"]
            
            with open(debug_output, 'w', encoding='utf-8') as f:
                json.dump(debug_info, f, indent=2, ensure_ascii=False)
            
            log_debug(f"截图完成，诊断信息保存到: {{debug_output}}")
            print(f"SUCCESS: {{output_path}}")
            sys.exit(0)
            
    except Exception as e:
        debug_info["final_status"] = "error"
        debug_info["errors"].append({{"type": "fatal_error", "message": str(e), "timestamp": time.time()}})
        
        # 保存错误诊断信息
        with open(debug_output, 'w', encoding='utf-8') as f:
            json.dump(debug_info, f, indent=2, ensure_ascii=False)
        
        print(f"ERROR: {{e}}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        
        # 创建临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(script_content)
            return f.name
    
    async def enhanced_screenshot_test(self, template_id: str) -> Dict:
        """增强的截图测试"""
        print(f"\n🎯 开始增强截图测试: {template_id}")
        
        db = None
        script_path = None
        
        try:
            db = self.setup_database()
            
            # 分析模板资源
            resource_analysis = self.analyze_template_resources(template_id, db)
            print(f"📊 资源分析完成: {resource_analysis.get('statistics', {})}")
            
            # 生成测试HTML
            account = self.create_test_account()
            variables = {
                'avatar': cover_screenshot_service._get_avatar_path(account),
                'account_name': account.name,
                'title': "这是一个用于增强诊断测试的Reddit故事标题，包含中文字符测试",
                'description': "测试描述内容",
                'timestamp': '2小时前',
                'subreddit': 'r/stories'
            }
            
            rendered_html = template_import_service.render_template(
                template_id=template_id,
                variables=variables,
                db=db,
                base_url="http://localhost:8000"
            )
            
            # 创建输出路径
            template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
            template_name = template.name if template else "unknown"
            safe_name = "".join(c for c in template_name if c.isalnum() or c in (' ', '-', '_')).strip()
            
            output_path = self.output_dir / f"enhanced_test_{safe_name}_{template_id[:8]}.png"
            
            # 创建增强截图脚本
            script_path = self.create_enhanced_screenshot_script(
                rendered_html, 
                str(output_path), 
                debug_mode=True
            )
            
            # 执行截图
            print("🔄 执行增强截图...")
            start_time = time.time()
            
            result = subprocess.run([
                sys.executable, script_path
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=90)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 分析结果
            debug_file = str(output_path).replace('.png', '_debug.json')
            debug_info = {}
            
            if Path(debug_file).exists():
                try:
                    with open(debug_file, 'r', encoding='utf-8') as f:
                        debug_info = json.load(f)
                except:
                    pass
            
            test_result = {
                "template_id": template_id,
                "template_name": template_name,
                "success": result.returncode == 0 and output_path.exists(),
                "duration": duration,
                "output_path": str(output_path),
                "debug_file": debug_file,
                "file_size": output_path.stat().st_size if output_path.exists() else 0,
                "resource_analysis": resource_analysis,
                "debug_info": debug_info,
                "script_output": {
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode
                }
            }
            
            print(f"✅ 测试完成: 成功={test_result['success']}, 耗时={duration:.2f}秒")
            
            return test_result
            
        except Exception as e:
            print(f"❌ 增强测试失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e), "traceback": traceback.format_exc()}
            
        finally:
            if db:
                try:
                    db.close()
                except:
                    pass
            if script_path and os.path.exists(script_path):
                try:
                    os.remove(script_path)
                except:
                    pass
    
    def generate_diagnostic_report(self, test_results: List[Dict]) -> str:
        """生成诊断报告"""
        report_file = self.output_dir / f"diagnostic_report_{int(time.time())}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 封面截图服务诊断报告\n\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 汇总统计
            total_tests = len(test_results)
            successful_tests = sum(1 for r in test_results if r.get('success', False))
            
            f.write(f"## 测试汇总\n\n")
            f.write(f"- 总测试数: {total_tests}\n")
            f.write(f"- 成功数: {successful_tests}\n")
            f.write(f"- 失败数: {total_tests - successful_tests}\n")
            f.write(f"- 成功率: {successful_tests/total_tests*100:.1f}%\n\n")
            
            # 详细结果
            f.write("## 详细测试结果\n\n")
            
            for i, result in enumerate(test_results, 1):
                f.write(f"### 测试 {i}: {result.get('template_name', 'Unknown')}\n\n")
                f.write(f"- **模板ID**: {result.get('template_id', 'N/A')}\n")
                f.write(f"- **测试结果**: {'✅ 成功' if result.get('success') else '❌ 失败'}\n")
                f.write(f"- **执行时间**: {result.get('duration', 0):.2f} 秒\n")
                f.write(f"- **文件大小**: {result.get('file_size', 0)} bytes\n")
                
                # 资源分析
                resource_stats = result.get('resource_analysis', {}).get('statistics', {})
                if resource_stats:
                    f.write(f"- **资源统计**:\n")
                    f.write(f"  - 图片总数: {resource_stats.get('total_images', 0)}\n")
                    f.write(f"  - Base64图片: {resource_stats.get('base64_images', 0)}\n")
                    f.write(f"  - 外部图片: {resource_stats.get('external_images', 0)}\n")
                    f.write(f"  - CSS链接: {resource_stats.get('total_css', 0)}\n")
                
                # 调试信息
                debug_info = result.get('debug_info', {})
                if debug_info:
                    f.write(f"- **加载阶段**: {len(debug_info.get('loading_stages', []))} 个\n")
                    f.write(f"- **网络请求**: {len(debug_info.get('network_requests', []))} 个\n")
                    f.write(f"- **发现图片**: {len(debug_info.get('image_analysis', []))} 张\n")
                    f.write(f"- **错误数量**: {len(debug_info.get('errors', []))} 个\n")
                
                # 错误信息
                if not result.get('success'):
                    script_output = result.get('script_output', {})
                    if script_output.get('stderr'):
                        f.write(f"- **错误信息**: \n```\n{script_output['stderr']}\n```\n")
                
                f.write("\n---\n\n")
            
            # 问题分析和建议
            f.write("## 问题分析和建议\n\n")
            
            failed_tests = [r for r in test_results if not r.get('success', False)]
            if failed_tests:
                f.write("### 失败测试分析\n\n")
                for result in failed_tests:
                    debug_info = result.get('debug_info', {})
                    errors = debug_info.get('errors', [])
                    if errors:
                        f.write(f"**{result.get('template_name')}** 的错误:\n")
                        for error in errors:
                            f.write(f"- {error.get('type', 'unknown')}: {error.get('message', 'N/A')}\n")
                        f.write("\n")
            
            # 性能分析
            f.write("### 性能分析\n\n")
            durations = [r.get('duration', 0) for r in test_results if r.get('success')]
            if durations:
                avg_duration = sum(durations) / len(durations)
                max_duration = max(durations)
                min_duration = min(durations)
                f.write(f"- 平均执行时间: {avg_duration:.2f} 秒\n")
                f.write(f"- 最长执行时间: {max_duration:.2f} 秒\n")
                f.write(f"- 最短执行时间: {min_duration:.2f} 秒\n")
        
        print(f"📄 诊断报告已生成: {report_file}")
        return str(report_file)

async def main():
    """主函数"""
    print("🔬 封面截图服务增强诊断工具")
    print("=" * 60)
    
    tool = ScreenshotDiagnosticTool()
    
    # 检查依赖
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright 已安装")
    except ImportError:
        print("❌ Playwright 未安装，请运行:")
        print("   pip install playwright")
        print("   playwright install chromium")
        return
    
    # 检查数据库
    try:
        db = tool.setup_database()
        templates = db.query(CoverTemplate).all()
        db.close()
        print(f"✅ 数据库连接正常，找到 {len(templates)} 个模板")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    if not templates:
        print("❌ 没有找到封面模板，请先导入模板")
        return
    
    print(f"\n📋 可用模板:")
    for i, template in enumerate(templates[:10], 1):  # 只显示前10个
        print(f"  {i}. {template.name} ({template.id})")
    if len(templates) > 10:
        print(f"  ... 还有 {len(templates) - 10} 个模板")
    
    print("\n选择测试模式:")
    print("1. 测试单个模板（增强诊断）")
    print("2. 测试所有模板（批量诊断）")
    print("3. 分析模板资源（不截图）")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 单个模板测试
        template_id = input("请输入模板ID（或回车使用第一个）: ").strip()
        if not template_id:
            template_id = str(templates[0].id)
        
        result = await tool.enhanced_screenshot_test(template_id)
        print("\n" + "="*50)
        print("🎯 单个模板测试完成")
        
        if result.get('success'):
            print(f"✅ 成功生成截图: {result['output_path']}")
            print(f"📊 文件大小: {result['file_size']} bytes")
            print(f"⏱️ 执行时间: {result['duration']:.2f} 秒")
        
        if result.get('debug_file'):
            print(f"🔍 调试信息: {result['debug_file']}")
        
        # 生成单个报告
        tool.generate_diagnostic_report([result])
    
    elif choice == "2":
        # 批量测试
        print("\n🔄 开始批量诊断...")
        results = []
        
        for i, template in enumerate(templates, 1):
            print(f"\n[{i}/{len(templates)}] 测试模板: {template.name}")
            result = await tool.enhanced_screenshot_test(str(template.id))
            results.append(result)
            
            # 简单进度显示
            if result.get('success'):
                print(f"  ✅ 成功 ({result.get('duration', 0):.1f}s)")
            else:
                print(f"  ❌ 失败")
        
        # 生成综合报告
        report_file = tool.generate_diagnostic_report(results)
        print(f"\n📄 综合诊断报告: {report_file}")
    
    elif choice == "3":
        # 资源分析
        template_id = input("请输入模板ID（或回车使用第一个）: ").strip()
        if not template_id:
            template_id = str(templates[0].id)
        
        db = tool.setup_database()
        try:
            analysis = tool.analyze_template_resources(template_id, db)
            print("\n📊 模板资源分析结果:")
            print(json.dumps(analysis, indent=2, ensure_ascii=False))
        finally:
            db.close()
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    asyncio.run(main())
