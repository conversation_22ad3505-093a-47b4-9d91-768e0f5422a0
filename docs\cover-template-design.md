# 封面模板管理功能设计文档

## 功能概述

封面模板管理系统允许用户创建、编辑和管理视频封面模板，支持变量绑定功能，可以动态替换头像、昵称、标题等内容，实现个性化封面生成。

## 核心功能

### 1. 模板管理
- 创建新模板
- 编辑已有模板
- 删除模板
- 模板预览
- 模板分类管理

### 2. 变量绑定系统
支持的变量类型：
- **头像（avatar）**: 图片类型，绑定账号头像
- **昵称（nickname）**: 文本类型，绑定账号名称
- **标题（title）**: 文本类型，绑定视频标题

### 3. 元素类型
- **文本元素**: 支持字体、大小、颜色、对齐方式
- **图片元素**: 支持尺寸、位置、圆角、边框
- **背景元素**: 支持纯色、渐变、图片背景
- **形状元素**: 矩形、圆形、线条等

### 4. 动态预览
- 实时预览模板效果
- 变量替换预览
- 不同账号数据预览

## 数据模型

### CoverTemplate 模型
```python
class CoverTemplate:
    id: str
    name: str                    # 模板名称
    description: str             # 模板描述
    category: str                # 分类
    width: int                   # 画布宽度
    height: int                  # 画布高度
    background: dict             # 背景配置
    elements: List[Element]      # 元素列表
    variables: List[Variable]    # 变量定义
    thumbnail: str               # 缩略图路径
    is_public: bool             # 是否公开
    created_at: datetime
    updated_at: datetime
```

### Element 模型
```python
class Element:
    id: str
    type: str                    # text, image, shape, background
    name: str                    # 元素名称
    x: float                     # X坐标
    y: float                     # Y坐标
    width: float                 # 宽度
    height: float                # 高度
    rotation: float              # 旋转角度
    opacity: float               # 透明度
    z_index: int                 # 层级
    properties: dict             # 元素特定属性
    variable_binding: str        # 绑定的变量名（可选）
```

### Variable 模型
```python
class Variable:
    name: str                    # 变量名 (avatar, nickname, title)
    type: str                    # 变量类型 (text, image)
    label: str                   # 显示标签
    default_value: str           # 默认值
    required: bool               # 是否必需
```

## API 设计

### 模板管理 API
```
GET /api/cover-templates/           # 获取模板列表
POST /api/cover-templates/          # 创建新模板
GET /api/cover-templates/{id}       # 获取模板详情
PUT /api/cover-templates/{id}       # 更新模板
DELETE /api/cover-templates/{id}    # 删除模板
```

### 预览生成 API
```
POST /api/cover-templates/{id}/preview   # 生成预览图
POST /api/cover-templates/generate-cover # 生成最终封面
```

### 元素操作 API
```
POST /api/cover-templates/{id}/elements     # 添加元素
PUT /api/cover-templates/{id}/elements/{eid} # 更新元素
DELETE /api/cover-templates/{id}/elements/{eid} # 删除元素
```

## 前端界面设计

### 1. 模板列表页面
- 网格展示模板缩略图
- 搜索和筛选功能
- 创建新模板按钮
- 模板操作菜单（编辑、删除、复制）

### 2. 模板编辑器
#### 左侧工具栏
- 元素库（文本、图片、形状）
- 图层管理
- 变量管理

#### 中间画布区域
- 可视化编辑画布
- 元素拖拽和调整
- 网格和参考线
- 缩放和平移

#### 右侧属性面板
- 元素属性编辑
- 变量绑定设置
- 样式配置

#### 顶部工具栏
- 保存、预览、导出
- 撤销重做
- 画布设置

### 3. 预览面板
- 实时预览效果
- 变量数据输入
- 不同账号数据切换

## 变量绑定实现

### 1. 变量定义
```typescript
interface Variable {
  name: 'avatar' | 'nickname' | 'title'
  type: 'text' | 'image'
  label: string
  defaultValue?: string
  required: boolean
}

const AVAILABLE_VARIABLES: Variable[] = [
  {
    name: 'avatar',
    type: 'image',
    label: '头像',
    required: false
  },
  {
    name: 'nickname',
    type: 'text',
    label: '昵称',
    required: false
  },
  {
    name: 'title',
    type: 'text',
    label: '标题',
    required: false
  }
]
```

### 2. 元素绑定
在元素属性中添加变量绑定选项：
```typescript
interface ElementProperties {
  // 基础属性
  x: number
  y: number
  width: number
  height: number
  
  // 变量绑定
  variableBinding?: {
    variable: string     // 绑定的变量名
    property: string     // 绑定的属性 (text, src, etc.)
  }
  
  // 其他属性...
}
```

### 3. 渲染逻辑
```typescript
function renderElement(element: Element, variables: Record<string, any>) {
  let props = { ...element.properties }
  
  // 应用变量绑定
  if (element.variableBinding) {
    const { variable, property } = element.variableBinding
    if (variables[variable]) {
      props[property] = variables[variable]
    }
  }
  
  return createElementWithProps(element.type, props)
}
```

## 技术实现

### 后端技术栈
- **FastAPI**: Web框架
- **SQLAlchemy**: ORM
- **Pillow**: 图像处理
- **Canvas API**: 服务端渲染（可选）

### 前端技术栈
- **React**: UI框架
- **Fabric.js**: 画布编辑器
- **Zustand**: 状态管理
- **React DnD**: 拖拽功能

### 文件存储
- 模板文件存储在 `uploads/templates/`
- 缩略图存储在 `uploads/thumbnails/`
- 生成的封面存储在 `uploads/covers/`

## 开发优先级

### 第一阶段：基础功能
1. 后端模型和API
2. 前端模板列表页面
3. 基础模板编辑器

### 第二阶段：编辑器增强
1. 拖拽编辑功能
2. 属性面板
3. 图层管理

### 第三阶段：变量系统
1. 变量绑定功能
2. 动态预览
3. 数据注入API

### 第四阶段：优化完善
1. 性能优化
2. 用户体验优化
3. 错误处理和测试

## 成功标准

1. **功能完整性**：所有核心功能正常工作
2. **用户体验**：编辑器操作流畅，预览准确
3. **变量绑定**：变量替换功能正确无误
4. **性能表现**：编辑和预览响应及时
5. **代码质量**：结构清晰，易于维护

---

*设计文档版本：v1.0*
*创建时间：2025-06-28*
