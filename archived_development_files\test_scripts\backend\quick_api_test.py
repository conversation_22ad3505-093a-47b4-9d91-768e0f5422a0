#!/usr/bin/env python
"""
快速API功能验证脚本
"""
import requests
import json

def test_api_quick():
    base_url = "http://localhost:8000"
    
    print("🧪 Reddit Story Video Generator - 快速API测试\n")
    
    # 测试1: 健康检查
    print("1. 健康检查...", end=" ")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 通过")
        else:
            print(f"❌ 失败 ({response.status_code})")
            return
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return
    
    # 测试2: 获取设置
    print("2. 获取设置...", end=" ")
    try:
        response = requests.get(f"{base_url}/api/v1/settings")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 通过")
            else:
                print(f"❌ API错误: {data}")
        else:
            print(f"❌ 请求失败 ({response.status_code})")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    # 测试3: 更新设置
    print("3. 更新设置...", end=" ")
    try:
        update_data = {
            "tts": {"provider": "openai", "voice": "nova", "speed": 1.1},
            "general": {"theme": "dark"}
        }
        response = requests.put(f"{base_url}/api/v1/settings", json=update_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 通过")
            else:
                print(f"❌ 更新失败: {data}")
        else:
            print(f"❌ 请求失败 ({response.status_code})")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    # 测试4: 验证设置
    print("4. 验证设置...", end=" ")
    try:
        response = requests.get(f"{base_url}/api/v1/settings/validate")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 通过")
            else:
                print(f"❌ 验证失败: {data}")
        else:
            print(f"❌ 请求失败 ({response.status_code})")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    # 测试5: 重置设置
    print("5. 重置设置...", end=" ")
    try:
        response = requests.post(f"{base_url}/api/v1/settings/reset")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 通过")
            else:
                print(f"❌ 重置失败: {data}")
        else:
            print(f"❌ 请求失败 ({response.status_code})")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    print("\n🎉 API功能测试完成!")

if __name__ == "__main__":
    test_api_quick()
