#!/usr/bin/env python3
"""
检查前端页面与API的当前对接状况
验证所有数据流是否正确联动
"""
import requests
import json
import sys

API_BASE = "http://localhost:8000/api"

def check_api_endpoint(endpoint, description):
    """检查API端点并分析返回数据结构"""
    print(f"\n🔍 检查 {description}")
    print(f"   端点: {endpoint}")
    
    try:
        response = requests.get(f"{API_BASE}{endpoint}")
        
        if response.status_code != 200:
            print(f"   ❌ HTTP {response.status_code}: {response.text}")
            return None
            
        data = response.json()
        print(f"   ✅ 状态: 成功")
        
        # 分析数据结构
        if isinstance(data, dict):
            if 'success' in data and 'data' in data:
                print(f"   📦 格式: 标准格式 {{success, data, message}}")
                actual_data = data['data']
            else:
                print(f"   📦 格式: 直接数据对象")
                actual_data = data
        else:
            print(f"   📦 格式: 直接数据列表")
            actual_data = data
            
        # 输出数据示例
        if isinstance(actual_data, list):
            print(f"   📊 数据类型: 列表，共 {len(actual_data)} 项")
            if actual_data:
                print(f"   🔬 首项结构: {list(actual_data[0].keys()) if isinstance(actual_data[0], dict) else type(actual_data[0]).__name__}")
                if len(str(actual_data[0])) < 200:
                    print(f"   📝 首项内容: {actual_data[0]}")
        elif isinstance(actual_data, dict):
            print(f"   📊 数据类型: 对象")
            print(f"   🔬 对象结构: {list(actual_data.keys())}")
            if len(str(actual_data)) < 500:
                print(f"   📝 对象内容: {actual_data}")
        else:
            print(f"   📊 数据类型: {type(actual_data).__name__}")
            print(f"   📝 数据内容: {actual_data}")
            
        return actual_data
        
    except requests.RequestException as e:
        print(f"   ❌ 请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"   ❌ JSON解析失败: {e}")
        return None

def main():
    print("=" * 60)
    print("📋 前端页面与API对接状况诊断报告")
    print("=" * 60)
    
    # 1. 检查账号API
    accounts = check_api_endpoint("/accounts", "账号管理API")
    
    # 2. 检查视频素材分类API  
    material_categories = check_api_endpoint("/video-categories", "视频素材分类API")
    
    # 3. 检查视频素材API
    materials = check_api_endpoint("/video-materials", "视频素材API")
    
    # 4. 检查提示词API
    prompts = check_api_endpoint("/prompts", "提示词API")
    
    # 5. 检查封面模板API
    templates = check_api_endpoint("/cover-templates", "封面模板API")
    
    # 6. 检查背景音乐API
    music = check_api_endpoint("/background-music", "背景音乐API")
    
    # 7. 检查系统设置API
    settings = check_api_endpoint("/settings", "系统设置API")
    
    print("\n" + "=" * 60)
    print("🔍 前端数据联动问题诊断")
    print("=" * 60)
    
    # 分析素材分类显示问题
    print("\n1️⃣ 视频素材分类显示问题")
    if material_categories and isinstance(material_categories, list):
        print("   ✅ 素材分类API返回列表格式正确")
        if material_categories:
            first_category = material_categories[0]
            if isinstance(first_category, dict):
                print(f"   🔬 分类对象字段: {list(first_category.keys())}")
                if 'material_count' in first_category:
                    print("   ✅ 包含material_count字段")
                else:
                    print("   ⚠️  缺少material_count字段，前端显示素材个数会有问题")
                if 'name' in first_category:
                    print("   ✅ 包含name字段")
                else:
                    print("   ❌ 缺少name字段，前端无法显示分类名称")
        else:
            print("   ⚠️  分类列表为空")
    else:
        print("   ❌ 素材分类API返回格式不正确")
    
    # 分析提示词分组问题
    print("\n2️⃣ 提示词分组显示问题")
    if prompts and isinstance(prompts, list):
        print("   ✅ 提示词API返回列表格式正确")
        if prompts:
            categories = set()
            fields = set()
            for prompt in prompts:
                if isinstance(prompt, dict):
                    fields.update(prompt.keys())
                    if 'category' in prompt:
                        categories.add(prompt['category'])
            
            print(f"   🔬 提示词对象字段: {list(fields)}")
            print(f"   📂 提取到的分组: {list(categories)}")
            
            if 'category' in fields:
                print("   ✅ 包含category字段")
            else:
                print("   ❌ 缺少category字段，前端无法进行分组")
        else:
            print("   ⚠️  提示词列表为空")
    else:
        print("   ❌ 提示词API返回格式不正确")
    
    # 分析TTS音色问题
    print("\n3️⃣ TTS音色配置问题")
    if settings and isinstance(settings, dict):
        print("   ✅ 设置API返回对象格式正确")
        if 'tts' in settings:
            tts_config = settings['tts']
            print(f"   🔬 TTS配置: {tts_config}")
            if 'provider' in tts_config and 'voice' in tts_config:
                print(f"   ✅ TTS配置完整: provider={tts_config['provider']}, voice={tts_config['voice']}")
            else:
                print("   ⚠️  TTS配置不完整，前端音色选择可能有问题")
        else:
            print("   ❌ 设置中缺少tts配置")
    else:
        print("   ❌ 设置API返回格式不正确")
    
    # 分析背景音乐问题
    print("\n4️⃣ 背景音乐分类问题")
    if music and isinstance(music, list):
        print("   ✅ 背景音乐API返回列表格式正确")
        if music:
            categories = set()
            fields = set()
            for item in music:
                if isinstance(item, dict):
                    fields.update(item.keys())
                    if 'category' in item:
                        categories.add(item['category'])
            
            print(f"   🔬 音乐对象字段: {list(fields)}")
            print(f"   📂 提取到的分类: {list(categories)}")
            
            if 'category' in fields:
                print("   ✅ 包含category字段，可以提取分类")
            else:
                print("   ❌ 缺少category字段，前端无法进行分类")
        else:
            print("   ⚠️  背景音乐列表为空")
    else:
        print("   ❌ 背景音乐API返回格式不正确")
    
    # 分析封面模板问题
    print("\n5️⃣ 封面模板显示问题")
    if templates and isinstance(templates, list):
        print("   ✅ 封面模板API返回列表格式正确")
        if templates:
            first_template = templates[0]
            if isinstance(first_template, dict):
                print(f"   🔬 模板对象字段: {list(first_template.keys())}")
                if 'id' in first_template and 'name' in first_template:
                    print("   ✅ 包含id和name字段")
                else:
                    print("   ❌ 缺少id或name字段，前端显示会有问题")
        else:
            print("   ⚠️  模板列表为空")
    else:
        print("   ❌ 封面模板API返回格式不正确")
    
    # 分析账号配置问题
    print("\n6️⃣ 账号配置问题")
    if accounts and isinstance(accounts, list):
        print("   ✅ 账号API返回列表格式正确")
        if accounts:
            first_account = accounts[0]
            if isinstance(first_account, dict):
                print(f"   🔬 账号对象字段: {list(first_account.keys())}")
                if 'id' in first_account:
                    print("   ✅ 包含id字段")
                else:
                    print("   ❌ 缺少id字段，前端无法引用账号")
        else:
            print("   ⚠️  账号列表为空")
    else:
        print("   ❌ 账号API返回格式不正确")
    
    print("\n" + "=" * 60)
    print("📋 总结与建议")
    print("=" * 60)
    print("基于以上分析，需要重点关注的问题：")
    print("1. 确认所有API返回的数据格式与前端期望一致")
    print("2. 检查是否存在缺失的字段（如material_count）")
    print("3. 验证前端的数据处理逻辑是否正确")
    print("4. 确保apiService.ts中的数据适配逻辑正确")

if __name__ == "__main__":
    main()
