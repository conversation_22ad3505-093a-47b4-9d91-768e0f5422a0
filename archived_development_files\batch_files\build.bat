@echo off
REM 一键构建 EXE 文件
REM 使用方法: build.bat [protect_method]
REM protect_method: pyc (默认), encrypt, pyz

echo ============================================================
echo Reddit Story Video Generator - 一键构建工具
echo ============================================================
echo.

REM 获取保护方法参数
set "PROTECT_METHOD=%1"
if "%PROTECT_METHOD%"=="" set "PROTECT_METHOD=pyc"

echo 🔐 代码保护方法: %PROTECT_METHOD%
echo.

REM 检查 Python 环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Python，请安装 Python 3.8+ 并添加到 PATH
    pause
    exit /b 1
)

REM 切换到项目根目录
cd /d "%~dp0"

REM 运行构建脚本
echo 🚀 开始构建...
python build_config\exe_builder.py --protect %PROTECT_METHOD%

if errorlevel 1 (
    echo.
    echo ❌ 构建失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 🎉 构建成功！
echo 📁 输出文件位于: dist\RedditStoryVideoGenerator_Portable\
echo.
echo 按任意键打开输出目录...
pause >nul

explorer "dist\RedditStoryVideoGenerator_Portable"
