/**
 * 提示词管理API客户端
 */

import { DirectHttpClient } from './directHttpClient';

export interface ApiResponse<T = any> {
  success?: boolean;
  message?: string;
  data?: T;
  code?: string;
}

// 后端数据格式
export interface BackendPrompt {
  id: string;
  name: string;
  content: string;
  category: string;
  variables: string[];
  isBuiltIn: boolean;
  metadata: {
    description?: string;
    exampleOutput?: string;
    usageCount?: number;
  };
  createdAt: string;
  updatedAt: string;
}

// 前端数据格式
export interface FrontendPrompt {
  id: string;
  title: string;
  type: 'system' | 'story' | 'narration' | 'description';
  content: string;
  description?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  testCount: number;
}

// 数据格式转换函数
export function convertBackendToFrontend(backendPrompt: BackendPrompt): FrontendPrompt {
  // 将后端的 category 映射到前端的 type
  const typeMap: { [key: string]: 'system' | 'story' | 'narration' | 'description' } = {
    'rewrite': 'story',
    'title': 'story', 
    'description': 'description',
    'narration': 'narration',
    'system': 'system',
    'story': 'story'
  };

  // 从变量数组生成标签，或使用默认标签
  const tags = backendPrompt.variables.length > 0 
    ? backendPrompt.variables 
    : [backendPrompt.category];

  return {
    id: backendPrompt.id,
    title: backendPrompt.name,
    type: typeMap[backendPrompt.category] || 'system',
    content: backendPrompt.content,
    description: backendPrompt.metadata?.description,
    tags: tags,
    createdAt: backendPrompt.createdAt.split('T')[0], // 只保留日期部分
    updatedAt: backendPrompt.updatedAt.split('T')[0],
    testCount: backendPrompt.metadata?.usageCount || 0
  };
}

export function convertFrontendToBackend(frontendPrompt: Partial<FrontendPrompt>): any {
  // 将前端的 type 映射回后端的 category
  const categoryMap: { [key: string]: string } = {
    'system': 'system',
    'story': 'story',
    'narration': 'narration', 
    'description': 'description'
  };

  return {
    name: frontendPrompt.title,
    content: frontendPrompt.content,
    category: frontendPrompt.type ? categoryMap[frontendPrompt.type] : 'system',
    variables: frontendPrompt.tags || [],
    description: frontendPrompt.description,
    is_built_in: false
  };
}

// API 方法 - 使用 DirectHttpClient
export async function getPrompts(): Promise<FrontendPrompt[]> {
  try {
    const client = new DirectHttpClient('/api/prompts');
    const backendPrompts: BackendPrompt[] = await client.get('/');
    return backendPrompts.map(convertBackendToFrontend);
  } catch (error) {
    console.error('获取提示词失败:', error);
    return [];
  }
}

export async function getAllPrompts(): Promise<FrontendPrompt[]> {
  return getPrompts();
}

export async function createPrompt(promptData: Partial<FrontendPrompt>): Promise<FrontendPrompt | null> {
  try {
    const client = new DirectHttpClient('/api/prompts');
    const backendData = convertFrontendToBackend(promptData);
    const backendPrompt: BackendPrompt = await client.post('/', backendData);
    return convertBackendToFrontend(backendPrompt);
  } catch (error) {
    console.error('创建提示词失败:', error);
    return null;
  }
}

export async function updatePrompt(id: string, promptData: Partial<FrontendPrompt>): Promise<FrontendPrompt | null> {
  try {
    const client = new DirectHttpClient('/api/prompts');
    const backendData = convertFrontendToBackend(promptData);
    const backendPrompt: BackendPrompt = await client.put(`/${id}`, backendData);
    return convertBackendToFrontend(backendPrompt);
  } catch (error) {
    console.error('更新提示词失败:', error);
    return null;
  }
}

export async function deletePrompt(id: string): Promise<boolean> {
  try {
    const client = new DirectHttpClient('/api/prompts');
    await client.delete(`/${id}`);
    return true;
  } catch (error) {
    console.error('删除提示词失败:', error);
    return false;
  }
}

export async function usePrompt(id: string): Promise<boolean> {
  try {
    const client = new DirectHttpClient('/api/prompts');
    await client.post(`/${id}/use`);
    return true;
  } catch (error) {
    console.error('使用提示词失败:', error);
    return false;
  }
}
