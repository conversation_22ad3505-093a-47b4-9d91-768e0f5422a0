#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试封面截图服务的编码修复
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加后端路径到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from src.database.database import get_database_session
from src.database.models import Account
from src.services.cover_screenshot_service import cover_screenshot_service

async def test_encoding_fix():
    """测试编码修复"""
    print("🧪 开始测试封面截图服务的编码修复...")
    
    try:
        # 获取数据库会话
        async with get_database_session() as db:
            # 查找一个测试账号
            account = db.query(Account).first()
            
            if not account:
                print("❌ 未找到测试账号，请先创建账号")
                return False
            
            print(f"✅ 使用账号进行测试: {account.username}")
            
            # 使用第一个可用的封面模板
            template_id = "faeeface-f5a8-4ca3-a0bd-e38471b0a82f"  # 来自错误日志的模板ID
            title = "测试标题 - Test Title 测试编码修复"
            output_path = "test_cover_encoding.png"
            
            print(f"📝 模板ID: {template_id}")
            print(f"📝 标题: {title}")
            print(f"📝 输出路径: {output_path}")
            
            # 测试封面生成
            success = await cover_screenshot_service.generate_cover_screenshot(
                template_id=template_id,
                account=account,
                title=title,
                output_path=output_path,
                db=db,
                additional_variables={'timestamp': '2小时前', 'subreddit': 'r/stories'}
            )
            
            if success:
                print("✅ 封面生成测试成功！")
                if os.path.exists(output_path):
                    print(f"✅ 封面文件已生成: {output_path}")
                    # 清理测试文件
                    os.remove(output_path)
                    print("🧹 测试文件已清理")
                return True
            else:
                print("❌ 封面生成测试失败")
                return False
                
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("🔧 封面截图服务编码修复测试")
    print("=" * 60)
    
    success = await test_encoding_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 编码修复测试通过！")
        print("💡 Unicode编码错误已修复，可以正常生成封面")
    else:
        print("❌ 编码修复测试失败")
        print("💡 可能需要进一步检查编码相关问题")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
