'use client'

import { useEffect, useState } from 'react'
import { ExtensionProtection } from '../../components/ExtensionProtection'

export default function ExtensionTestPage() {
  const [mounted, setMounted] = useState(false)
  const [extensionAttributes, setExtensionAttributes] = useState<string[]>([])
  const [cleanupCount, setCleanupCount] = useState(0)

  useEffect(() => {
    setMounted(true)

    // 监控 body 属性变化
    const checkAttributes = () => {
      const body = document.body
      const attrs = []
      for (let i = 0; i < body.attributes.length; i++) {
        const attr = body.attributes[i]
        if (attr.name.includes('mpa-') || attr.name.includes('extension')) {
          attrs.push(`${attr.name}="${attr.value}"`)
        }
      }
      setExtensionAttributes(attrs)
    }

    // 模拟扩展添加属性
    const simulateExtension = () => {
      document.body.setAttribute('mpa-version', '11.3.7')
      document.body.setAttribute('mpa-extension-id', 'test-extension-id')
      setCleanupCount(prev => prev + 1)
    }

    checkAttributes()
    const interval = setInterval(checkAttributes, 500)

    return () => clearInterval(interval)
  }, [])

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading extension test...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <ExtensionProtection />
      <div className="min-h-screen bg-gray-50 py-8" suppressHydrationWarning>
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">
              扩展干扰测试页面
            </h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-800">扩展属性监控</h2>
                
                <div className={`p-4 rounded border-2 ${extensionAttributes.length === 0 ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}`}>
                  <p className={`font-medium ${extensionAttributes.length === 0 ? 'text-green-800' : 'text-yellow-800'}`}>
                    {extensionAttributes.length === 0 ? '✅ 无扩展属性检测到' : '⚠️ 检测到扩展属性'}
                  </p>
                  {extensionAttributes.length > 0 && (
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>检测到的属性:</p>
                      <ul className="list-disc list-inside">
                        {extensionAttributes.map((attr, index) => (
                          <li key={index} className="font-mono text-xs">{attr}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                
                <div className="p-4 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-blue-800 text-sm">
                    清理次数: <span className="font-bold">{cleanupCount}</span>
                  </p>
                </div>
              </div>
              
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-800">测试工具</h2>
                
                <button
                  onClick={() => {
                    document.body.setAttribute('mpa-version', '11.3.7')
                    document.body.setAttribute('mpa-extension-id', 'test-extension-id')
                    setCleanupCount(prev => prev + 1)
                  }}
                  className="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
                >
                  模拟扩展添加属性
                </button>
                
                <button
                  onClick={() => {
                    const body = document.body
                    body.removeAttribute('mpa-version')
                    body.removeAttribute('mpa-extension-id')
                  }}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                >
                  手动清理属性
                </button>
                
                <button
                  onClick={() => window.location.reload()}
                  className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                >
                  刷新页面
                </button>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800 mb-3">保护机制状态</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-800 text-sm font-medium">✅ ExtensionProtection</p>
                  <p className="text-green-600 text-xs">组件已激活</p>
                </div>
                
                <div className="p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-800 text-sm font-medium">✅ suppressHydrationWarning</p>
                  <p className="text-green-600 text-xs">已应用到关键元素</p>
                </div>
                
                <div className="p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-800 text-sm font-medium">✅ MutationObserver</p>
                  <p className="text-green-600 text-xs">DOM监控活跃</p>
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex space-x-4">
              <a 
                href="/" 
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                返回首页
              </a>
              <a 
                href="/test" 
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                基础测试页
              </a>
              <a 
                href="/diagnostics" 
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
              >
                系统诊断
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
