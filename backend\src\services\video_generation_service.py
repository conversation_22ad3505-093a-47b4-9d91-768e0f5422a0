"""
视频生成核心服务
"""

import os
import json
import asyncio
import uuid
import wave
import subprocess
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger
import traceback
from concurrent.futures import ThreadPoolExecutor

# 全局禁用SQLAlchemy的所有日志输出
import logging
logging.getLogger('sqlalchemy').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.engine').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.pool').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.dialects').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.orm').setLevel(logging.CRITICAL)

# 禁用所有SQL相关的日志记录器
for logger_name in logging.Logger.manager.loggerDict:
    if 'sqlalchemy' in logger_name.lower():
        logging.getLogger(logger_name).setLevel(logging.CRITICAL)
        logging.getLogger(logger_name).disabled = True

from PIL import Image, ImageDraw, ImageFont

from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy import and_, or_

from ..models import (
    VideoGenerationJob, VideoGenerationTask, TaskLog, TaskStatus,
    Account, VideoMaterial, BackgroundMusic, Prompt, CoverTemplate, Settings
)
from ..schemas.video_generation import (
    CreateVideoGenerationJobRequest, VideoGenerationJobConfig,
    AudioAnalysis, WordTimestamp
)
from .llm_service import LLMService
from .tts_service import TTSService

class AudioAnalysisService:
    """音频分析服务，使用Whisper进行精确时间戳分析"""
    
    _model = None
    _model_name = "base" # 使用基础模型，平衡性能和准确性

    @classmethod
    def _get_model(cls):
        """加载或返回已加载的Whisper模型"""
        if cls._model is None:
            try:
                import whisper
                logger.info(f"正在加载Whisper模型: {cls._model_name}...")
                cls._model = whisper.load_model(cls._model_name)
                logger.info("Whisper模型加载成功。")
            except ImportError:
                logger.error("Whisper库未安装。请运行 'pip install openai-whisper'.")
                raise
            except Exception as e:
                logger.error(f"加载Whisper模型失败: {e}")
                raise
        return cls._model

    @staticmethod
    async def analyze_audio(audio_file_path: str, story_text: str, speech_speed: float = 1.0) -> AudioAnalysis:
        """
        使用Whisper分析音频文件，提取精确的时间戳信息
        
        Args:
            audio_file_path: 音频文件路径
            story_text: 故事文本
            speech_speed: 语音倍速（用于调整封面显示时长）
        """
        try:
            model = AudioAnalysisService._get_model()
            logger.info(f"开始使用Whisper转录音频文件: {audio_file_path}")
            logger.info(f"语音倍速: {speech_speed}")
            
            # 异步执行转录，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=1) as executor:
                result = await loop.run_in_executor(
                    executor, 
                    lambda: model.transcribe(audio_file_path, word_timestamps=True, fp16=False)
                )
            
            logger.info("Whisper转录完成。")

            # 提取总时长和单词级别时间戳
            total_duration = result.get('segments', [{}])[-1].get('end', 0.0)
            word_timestamps = AudioAnalysisService._extract_word_timestamps(result)
            
            if not word_timestamps:
                logger.warning("Whisper未能提取任何单词时间戳。")
                raise ValueError("无法从音频中提取时间戳。")

            # 确定第一句话的时长（基于实际音频时间戳）
            raw_first_sentence_duration = AudioAnalysisService._find_first_sentence_duration(
                story_text, word_timestamps
            )
            
            # 注意：这里的时长已经是TTS按指定倍速生成的实际时长
            # 所以我们直接使用从音频分析得到的时长，无需额外调整
            first_sentence_duration = raw_first_sentence_duration
            
            logger.info(f"第一句话时长: {first_sentence_duration}秒 (已包含倍速影响)")
            
            return AudioAnalysis(
                total_duration=total_duration,
                first_sentence_duration=first_sentence_duration,
                word_timestamps=word_timestamps
            )
            
        except Exception as e:
            logger.error(f"使用Whisper进行音频分析失败: {str(e)}")
            # 在失败时提供一个合理的备用方案，考虑倍速影响
            fallback_first_duration = 3.0 / speech_speed  # 基础3秒除以倍速
            return AudioAnalysis(
                total_duration=30.0 / speech_speed,  # 假设一个默认时长，考虑倍速
                first_sentence_duration=fallback_first_duration,
                word_timestamps=[]
            )

    @staticmethod
    def _extract_word_timestamps(transcription_result: Dict[str, Any]) -> List[WordTimestamp]:
        """从Whisper的输出中提取并格式化单词时间戳"""
        timestamps = []
        for segment in transcription_result.get('segments', []):
            for word_info in segment.get('words', []):
                timestamps.append(WordTimestamp(
                    word=word_info['word'].strip(),
                    start=word_info['start'],
                    end=word_info['end']
                ))
        return timestamps

    @staticmethod
    def _find_first_sentence_duration(story_text: str, word_timestamps: List[WordTimestamp]) -> float:
        """根据原始文本和时间戳，精确计算第一句话的时长"""
        if not word_timestamps:
            return 3.0  # 默认值

        # 使用与_extract_first_sentence相同的句末标点符号（包括中英文）
        sentence_enders = ('.', '?', '!', '。', '？', '！')
        first_sentence_end_pos = -1
        found_ender = None
        
        for ender in sentence_enders:
            pos = story_text.find(ender)
            if pos != -1:
                if first_sentence_end_pos == -1 or pos < first_sentence_end_pos:
                    first_sentence_end_pos = pos
                    found_ender = ender
        
        if first_sentence_end_pos == -1:
            # 如果没有找到标点，使用前15个词作为第一句话
            return word_timestamps[min(14, len(word_timestamps)-1)].end if word_timestamps else 3.0

        # 提取第一句话文本（与_extract_first_sentence保持一致）
        first_sentence_text = story_text[:first_sentence_end_pos + 1].strip()
        logger.info(f"音频分析中的第一句话文本: '{first_sentence_text}' (结束标点: '{found_ender}')")
        
        # 计算第一句话包含多少个词
        # 简单通过空格分割来估算词数，可以更复杂以处理多重空格等情况
        num_words_in_first_sentence = len(first_sentence_text.split())

        if num_words_in_first_sentence == 0:
            return 3.0

        # 找到对应于第一句话最后一个词的时间戳
        if num_words_in_first_sentence <= len(word_timestamps):
            # 索引从0开始，所以减1
            last_word_index = num_words_in_first_sentence - 1
            return word_timestamps[last_word_index].end
        else:
            # 如果计算出的词数超过时间戳总数，返回最后一个词的结束时间
            return word_timestamps[-1].end


class SubtitleGenerator:
    """字幕生成器"""
    
    @staticmethod
    def generate_srt(word_timestamps: List[WordTimestamp], story_text: str,
                    first_sentence_duration: float, words_per_screen: int = 1,
                    include_all_words: bool = True) -> str:
        """
        生成SRT字幕文件内容

        Args:
            word_timestamps: 单词时间戳列表
            story_text: 故事文本
            first_sentence_duration: 第一句话时长（用于兼容性，当include_all_words=False时使用）
            words_per_screen: 每屏单词数
            include_all_words: 是否包含所有词语（包括第一句话），默认True
        """
        if not word_timestamps:
            logger.warning("没有词时间戳，无法生成字幕")
            return ""

        logger.info(f"开始生成SRT字幕 - 总词数: {len(word_timestamps)}, 包含所有词语: {include_all_words}")

        if include_all_words:
            # 包含所有词语，不跳过第一句话
            filtered_timestamps = word_timestamps
            logger.info(f"字幕包含所有 {len(filtered_timestamps)} 个词语")
        else:
            # 兼容旧逻辑：跳过第一句话的单词
            filtered_timestamps = [
                ts for ts in word_timestamps
                if ts.start >= first_sentence_duration
            ]

            skipped_count = len(word_timestamps) - len(filtered_timestamps)
            logger.info(f"跳过第一句话的单词数: {skipped_count}, 剩余字幕单词数: {len(filtered_timestamps)}")

            if skipped_count > 0:
                # 构建被跳过的第一句话内容（用于日志）
                first_sentence_words = [ts.word for ts in word_timestamps if ts.start < first_sentence_duration]
                skipped_text = ' '.join(first_sentence_words)
                logger.info(f"字幕跳过的第一句话内容（基于音频时长{first_sentence_duration}s）: '{skipped_text}'")

                # 提取文本方式的第一句话用于对比
                sentence_enders = ('.', '?', '!', '。', '？', '！')
                first_sentence_end_pos = -1
                for ender in sentence_enders:
                    pos = story_text.find(ender)
                    if pos != -1:
                        if first_sentence_end_pos == -1 or pos < first_sentence_end_pos:
                            first_sentence_end_pos = pos

                if first_sentence_end_pos != -1:
                    text_first_sentence = story_text[:first_sentence_end_pos + 1].strip()
                    logger.info(f"文本提取的第一句话内容（用于封面）: '{text_first_sentence}'")

                    if skipped_text.strip() != text_first_sentence.strip():
                        logger.warning(f"⚠️ 第一句话不匹配！音频跳过: '{skipped_text}' vs 封面文本: '{text_first_sentence}'")

        if not filtered_timestamps:
            logger.warning("过滤后没有剩余单词，无法生成字幕")
            return ""
        
        srt_content = []
        subtitle_index = 1
        
        # 分析句子结构，找出句子边界
        sentence_boundaries = []
        for i, ts in enumerate(filtered_timestamps):
            word = ts.word.strip()
            # 检查是否是句子结尾（包含句号、问号、感叹号等）
            if word.endswith(('.', '!', '?', '。', '！', '？')):
                sentence_boundaries.append(i)

        logger.info(f"检测到句子边界位置: {sentence_boundaries}")

        # 如果没有检测到句子边界，添加最后一个单词作为边界
        if not sentence_boundaries and filtered_timestamps:
            sentence_boundaries.append(len(filtered_timestamps) - 1)

        # 根据配置的每屏单词数和智能断句生成字幕
        i = 0
        while i < len(filtered_timestamps):
            # 确定当前字幕段的单词数
            remaining_words = len(filtered_timestamps) - i
            if remaining_words == 0:
                break

            # 找到下一个句子边界
            next_sentence_boundary = None
            for boundary in sentence_boundaries:
                if boundary >= i:
                    next_sentence_boundary = boundary
                    break

            # 计算当前断句的长度
            if next_sentence_boundary is not None:
                sentence_length = next_sentence_boundary - i + 1
            else:
                sentence_length = remaining_words

            # 根据配置的单词数和断句长度决定显示的单词数
            if words_per_screen <= sentence_length:
                # 如果配置的单词数小于等于当前断句长度，使用配置的单词数
                words_in_subtitle = min(words_per_screen, remaining_words)
            else:
                # 如果配置的单词数大于当前断句长度，只显示当前断句
                words_in_subtitle = min(sentence_length, remaining_words)

            start_time = filtered_timestamps[i].start
            end_time = filtered_timestamps[i + words_in_subtitle - 1].end

            # 提取单词文本，处理连续标点
            words = []
            for j in range(i, i + words_in_subtitle):
                word = filtered_timestamps[j].word.strip()
                words.append(word)

            # 检查是否需要添加后续的连续标点
            next_word_index = i + words_in_subtitle
            while next_word_index < len(filtered_timestamps):
                next_word = filtered_timestamps[next_word_index].word.strip()
                # 如果下一个"单词"只是标点符号，添加到当前字幕
                if len(next_word) <= 2 and all(c in '.,!?;:。，！？；：' for c in next_word):
                    words.append(next_word)
                    end_time = filtered_timestamps[next_word_index].end
                    next_word_index += 1
                else:
                    break

            subtitle_text = " ".join(words)

            # 格式化时间
            start_srt = SubtitleGenerator._format_srt_time(start_time)
            end_srt = SubtitleGenerator._format_srt_time(end_time)

            # 添加SRT段
            srt_content.append(f"{subtitle_index}")
            srt_content.append(f"{start_srt} --> {end_srt}")
            srt_content.append(subtitle_text)
            srt_content.append("")  # 空行

            logger.debug(f"字幕段 {subtitle_index}: '{subtitle_text}' ({words_in_subtitle}词)")

            subtitle_index += 1
            i = next_word_index if next_word_index > i + words_in_subtitle else i + words_in_subtitle
        
        return "\n".join(srt_content)
    
    @staticmethod
    def _format_srt_time(seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"


    


class VideoGenerationService:
    """视频生成主服务"""
    
    def __init__(self, session_maker: sessionmaker):
        self.session_maker = session_maker
        self.llm_service = LLMService(session_maker)
        self.tts_service = TTSService(session_maker)
        self.running_jobs: Dict[str, bool] = {}  # 作业运行状态追踪
        self._task_queue_running = False
        self._task_queue_task: Optional[asyncio.Task] = None
    
    async def create_job(self, request: CreateVideoGenerationJobRequest) -> VideoGenerationJob:
        """创建视频生成作业"""
        db = self.session_maker()
        try:
            # 计算总任务数
            total_tasks = sum(config.video_count for config in request.account_configs)
            
            # 创建作业
            job = VideoGenerationJob(
                name=request.name,
                description=request.description,
                config=request.config.dict(),
                account_configs=[config.dict() for config in request.account_configs],
                total_tasks=total_tasks,
                status=TaskStatus.PENDING
            )
            
            db.add(job)
            db.commit()
            
            # 创建具体任务
            await self._create_tasks_for_job(job, job.account_configs)
            
            logger.info(f"视频生成作业已创建: {job.id}, 总任务数: {total_tasks}")
            # Expunge the job from the session so it can be used in other sessions
            db.expunge(job)
            return job
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建作业失败: {str(e)}")
            raise
        finally:
            db.close()
    
    async def _create_tasks_for_job(
        self, job: VideoGenerationJob, 
        account_configs: List[Dict[str, Any]]
    ):
        """为作业创建具体任务"""
        db = self.session_maker()
        try:
            for account_config in account_configs:
                account_id = account_config['account_id']
                video_count = account_config['video_count']
                
                # 获取账号信息
                account = db.query(Account).filter(Account.id == account_id).first()
                if not account:
                    raise ValueError(f"账号不存在: {account_id}")
                
                # 为该账号创建指定数量的任务
                for i in range(video_count):
                    task = VideoGenerationTask(
                        job_id=job.id,
                        task_name=f"{account.name}_视频_{i+1}",
                        account_id=account_id,
                        status=TaskStatus.PENDING
                    )
                    db.add(task)
            
            db.commit()
        finally:
            db.close()

    async def create_batch_job(self, request) -> VideoGenerationJob:
        """创建批量视频生成作业（基于文案列表）"""
        from ..schemas.video_generation import CreateBatchVideoGenerationJobRequest

        db = self.session_maker()
        try:
            # 计算总任务数（等于文案数量）
            total_tasks = len(request.stories)

            # 转换配置格式以兼容现有系统
            config_dict = request.config.dict()

            # 创建账号配置列表（用于兼容现有系统）
            account_configs = []
            for account_id in request.account_ids:
                account_configs.append({
                    "account_id": account_id,
                    "video_count": 0  # 这里设为0，实际任务数由文案列表决定
                })

            # 创建作业
            job = VideoGenerationJob(
                name=request.name,
                description=request.description,
                config=config_dict,
                account_configs=account_configs,
                total_tasks=total_tasks,
                status=TaskStatus.PENDING
            )

            db.add(job)
            db.commit()

            # 创建基于文案列表的任务
            await self._create_batch_tasks_for_job(job, request.account_ids, request.stories, request.titles)

            logger.info(f"批量视频生成作业已创建: {job.id}, 总任务数: {total_tasks}")
            # Expunge the job from the session so it can be used in other sessions
            db.expunge(job)
            return job

        except Exception as e:
            db.rollback()
            logger.error(f"创建批量作业失败: {str(e)}")
            raise
        finally:
            db.close()

    async def _create_batch_tasks_for_job(
        self, job: VideoGenerationJob,
        account_ids: List[str],
        stories: List[str],
        titles: Optional[List[str]] = None
    ):
        """为批量作业创建基于文案列表的任务"""
        db = self.session_maker()
        try:
            # 验证所有账号存在
            accounts = {}
            for account_id in account_ids:
                account = db.query(Account).filter(Account.id == account_id).first()
                if not account:
                    raise ValueError(f"账号不存在: {account_id}")
                accounts[account_id] = account

            # 按顺序轮流分配文案给账号
            for i, story in enumerate(stories):
                # 轮流选择账号
                account_id = account_ids[i % len(account_ids)]
                account = accounts[account_id]

                # 获取对应的标题（如果有的话）
                custom_title = None
                if titles and i < len(titles) and titles[i].strip():
                    custom_title = titles[i].strip()

                # 创建任务，将文案存储在generated_story字段中
                # 批量任务使用新的分阶段状态
                task = VideoGenerationTask(
                    job_id=job.id,
                    task_name=f"{account.name}_文案_{i+1}",
                    account_id=account_id,
                    status=TaskStatus.AUDIO_PENDING,  # 批量任务从语音生成阶段开始
                    generated_story=story,  # 预设文案
                    custom_title=custom_title  # 自定义标题
                )
                db.add(task)

            db.commit()
        finally:
            db.close()

    async def start_job(self, job_id: str) -> bool:
        """将作业标记为待执行（由任务队列处理器处理）"""
        db = self.session_maker()
        try:
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
            if not job:
                return False
            
            # 只有PENDING状态的作业可以启动
            if job.status != TaskStatus.PENDING:
                logger.warning(f"作业 {job_id} 状态为 {job.status}，无法启动")
                return False
            
            # 注意：这里不直接启动，而是保持PENDING状态
            # 任务队列处理器会自动发现并处理PENDING状态的作业
            logger.info(f"作业 {job_id} 已标记为待执行，等待任务队列处理器处理")
            return True
            
        finally:
            db.close()
    
    # _execute_job 方法已被任务队列处理器取代

    async def _execute_audio_generation(self, task_id: str, job_config: Dict[str, Any]):
        """执行语音生成阶段"""
        db = self.session_maker()
        try:
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if not task:
                logger.error(f"任务 {task_id} 在语音生成时未找到。")
                return

            task.status = TaskStatus.RUNNING
            task.started_at = datetime.utcnow()
            task.current_step = "开始语音生成"
            db.commit()
            logger.info(f"任务 {task.id}: 开始语音生成阶段")

            # 1. 准备文案（批量任务已有预设文案）
            from .video_generation_helpers import VideoGenerationServiceHelpers
            helper = VideoGenerationServiceHelpers(self.session_maker)

            if task.generated_story and task.generated_story.strip():
                story = task.generated_story.strip()
                logger.info(f"任务 {task.id}: 使用预设文案. 文案长度: {len(story)}")
            else:
                # 生成新文案（普通模式）
                story = await helper._generate_story(task, job_config)
                task.generated_story = story
                logger.info(f"任务 {task.id}: 文案生成完成. 文案长度: {len(story)}")

            task.first_sentence = self._extract_first_sentence(story)
            task.current_step = "已生成文案"
            task.progress = 15
            db.commit()
            logger.info(f"任务 {task.id}: 第一句话已提取: '{task.first_sentence}'")

            # 2. 生成语音
            audio_path = await helper._generate_audio(task, job_config, story)
            task.audio_file_path = audio_path
            task.current_step = "语音生成完成"
            task.progress = 50  # 语音生成阶段完成50%
            task.status = TaskStatus.AUDIO_COMPLETED
            db.commit()
            logger.info(f"任务 {task.id}: 语音生成完成. 文件: {audio_path}")

        except Exception as e:
            logger.error(f"语音生成失败: {task_id}, {str(e)}")
            logger.error(traceback.format_exc())
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if task:
                task.status = TaskStatus.FAILED
                task.error_message = str(e)
                db.commit()
        finally:
            db.close()

    async def _execute_video_composition(self, task_id: str, job_config: Dict[str, Any]):
        """执行视频合成阶段"""
        db = self.session_maker()
        try:
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if not task:
                logger.error(f"任务 {task_id} 在视频合成时未找到。")
                return

            # 检查语音文件是否存在
            if not task.audio_file_path or not Path(task.audio_file_path).exists():
                raise ValueError("语音文件不存在，无法进行视频合成")

            task.status = TaskStatus.RUNNING
            task.current_step = "开始视频合成"
            db.commit()
            logger.info(f"任务 {task.id}: 开始视频合成阶段")

            from .video_generation_helpers import VideoGenerationServiceHelpers
            helper = VideoGenerationServiceHelpers(self.session_maker)

            # 3. 分析音频（使用已生成的语音文件）
            speech_speed = job_config.get('voice_settings', {}).get('speed', 1.0)
            audio_analysis = await AudioAnalysisService.analyze_audio(task.audio_file_path, task.generated_story, speech_speed)
            task.audio_analysis = audio_analysis.dict()
            task.current_step = "音频分析完成"
            task.progress = 60
            db.commit()
            logger.info(f"任务 {task.id}: 音频分析完成. 总时长: {audio_analysis.total_duration}s")

            # 4. 基于音频时长智能选择视频素材
            materials = await helper._select_materials(task, job_config, audio_analysis.total_duration)
            task.used_materials = [m.id for m in materials]
            task.current_step = "已选择素材"
            task.progress = 70
            db.commit()
            logger.info(f"任务 {task.id}: 素材选择完成. 选择了 {len(materials)} 个素材.")

            # 5. 生成字幕
            subtitle_config = job_config.get('subtitle_config', {})
            subtitle_path = await helper._generate_subtitles(task, audio_analysis, subtitle_config)
            task.subtitle_file_path = subtitle_path
            task.current_step = "已生成字幕"
            task.progress = 80
            db.commit()
            logger.info(f"任务 {task.id}: 字幕生成完成. 文件: {subtitle_path}")

            # 6. 生成封面
            cover_path = await helper._generate_cover(task, job_config)
            task.cover_image_path = cover_path
            task.current_step = "已生成封面"
            task.progress = 85
            db.commit()
            logger.info(f"任务 {task.id}: 封面生成完成. 文件: {cover_path}")

            # 7. 选择背景音乐
            background_music = await helper._select_background_music(task, job_config)
            task.used_music_id = background_music.id
            task.current_step = "已选择音乐"
            task.progress = 90
            db.commit()
            logger.info(f"任务 {task.id}: 背景音乐选择完成. 音乐ID: {background_music.id}")

            # 8. 合成最终视频
            video_path = await helper._compose_video(
                task, materials, background_music, audio_analysis,
                cover_path, subtitle_path, job_config
            )
            task.final_video_path = video_path
            task.current_step = "视频合成完成"
            task.progress = 100
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()

            db.commit()
            logger.info(f"任务 {task.id}: 视频合成完成. 文件: {video_path}")

        except Exception as e:
            logger.error(f"视频合成失败: {task_id}, {str(e)}")
            logger.error(traceback.format_exc())
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if task:
                task.status = TaskStatus.FAILED
                task.error_message = str(e)
                db.commit()
        finally:
            db.close()

    async def _execute_task(self, task_id: str, job_config: Dict[str, Any]):
        """执行单个视频生成任务"""
        db = self.session_maker()
        try:
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if not task:
                logger.error(f"任务 {task_id} 在执行时未找到。")
                return

            task.status = TaskStatus.RUNNING
            task.started_at = datetime.utcnow()
            task.current_step = "开始生成"
            db.commit()
            logger.info(f"任务 {task.id}: 开始执行")

            # 1. 生成或使用预设故事文案
            from .video_generation_helpers import VideoGenerationServiceHelpers
            helper = VideoGenerationServiceHelpers(self.session_maker)

            # 检查是否已有预设文案（批量生成模式）
            if task.generated_story and task.generated_story.strip():
                story = task.generated_story.strip()
                logger.info(f"任务 {task.id}: 使用预设文案. 文案长度: {len(story)}")
            else:
                # 生成新文案（普通模式）
                story = await helper._generate_story(task, job_config)
                task.generated_story = story
                logger.info(f"任务 {task.id}: 文案生成完成. 文案长度: {len(story)}")

            task.first_sentence = self._extract_first_sentence(story)  # 使用改进的提取函数
            task.current_step = "已生成文案"
            task.progress = 15
            db.commit()
            logger.info(f"任务 {task.id}: 第一句话已提取: '{task.first_sentence}'")

            # 2. 生成语音
            audio_path = await helper._generate_audio(task, job_config, story)
            task.audio_file_path = audio_path
            task.current_step = "已生成语音"
            task.progress = 30
            db.commit()
            logger.info(f"任务 {task.id}: 语音生成完成. 文件: {audio_path}")

            # 3. 分析音频（考虑语音倍速）
            speech_speed = job_config.get('voice_settings', {}).get('speed', 1.0)
            audio_analysis = await AudioAnalysisService.analyze_audio(audio_path, story, speech_speed)
            task.audio_analysis = audio_analysis.dict()
            logger.info(f"任务 {task.id}: 音频分析完成. 总时长: {audio_analysis.total_duration}s, 第一句时长: {audio_analysis.first_sentence_duration}s (倍速: {speech_speed})")

            # 4. 基于音频时长智能选择视频素材
            materials = await helper._select_materials(task, job_config, audio_analysis.total_duration)
            task.used_materials = [m.id for m in materials]
            task.current_step = "已选择素材"
            task.progress = 45
            db.commit()
            logger.info(f"任务 {task.id}: 素材选择完成. 选择了 {len(materials)} 个素材.")

            # 5. 生成字幕
            subtitle_config = job_config.get('subtitle_config', {})
            subtitle_path = await helper._generate_subtitles(task, audio_analysis, subtitle_config)
            task.subtitle_file_path = subtitle_path
            task.current_step = "已生成字幕"
            task.progress = 60
            db.commit()
            logger.info(f"任务 {task.id}: 字幕生成完成. 文件: {subtitle_path}")

            # 6. 生成封面
            cover_path = await helper._generate_cover(task, job_config)
            task.cover_image_path = cover_path
            task.current_step = "已生成封面"
            task.progress = 75
            db.commit()
            logger.info(f"任务 {task.id}: 封面生成完成. 文件: {cover_path}")

            # 7. 选择背景音乐
            background_music = await helper._select_background_music(task, job_config)
            task.used_music_id = background_music.id
            task.current_step = "已选择音乐"
            task.progress = 85
            db.commit()
            logger.info(f"任务 {task.id}: 背景音乐选择完成. 音乐ID: {background_music.id}")

            # 8. 合成最终视频
            video_path = await helper._compose_video(
                task, materials, background_music, audio_analysis, 
                cover_path, subtitle_path, job_config
            )
            task.final_video_path = video_path
            task.current_step = "视频合成完成"
            task.progress = 100
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            
            db.commit()
            logger.info(f"任务 {task.id}: 视频合成完成. 文件: {video_path}")
            
        except Exception as e:
            logger.error(f"任务执行失败: {task_id}, {str(e)}")
            logger.error(traceback.format_exc())
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if task:
                task.status = TaskStatus.FAILED
                task.error_message = str(e)
                db.commit()
        finally:
            db.close()
    
    def _update_job_progress(self, job_id: str):
        """更新作业进度"""
        db = self.session_maker()
        try:
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
            if not job:
                return

            tasks = db.query(VideoGenerationTask).filter(
                VideoGenerationTask.job_id == job.id
            ).all()

            completed = len([t for t in tasks if t.status == TaskStatus.COMPLETED])
            failed = len([t for t in tasks if t.status == TaskStatus.FAILED])

            # 计算语音生成阶段的进度
            audio_completed = len([t for t in tasks if t.status in [
                TaskStatus.AUDIO_COMPLETED, TaskStatus.VIDEO_PENDING, TaskStatus.COMPLETED
            ]])

            job.completed_tasks = completed
            job.failed_tasks = failed
            db.commit()

            # 记录阶段进度信息
            total_tasks = len(tasks)
            if total_tasks > 0:
                audio_progress = (audio_completed / total_tasks) * 100
                logger.debug(f"作业 {job_id}: 语音生成进度 {audio_progress:.1f}%, 完成任务 {completed}/{total_tasks}")

        finally:
            db.close()
    
    async def start_task_queue(self):
        """启动任务队列处理器"""
        if self._task_queue_running:
            logger.info("任务队列处理器已经在运行")
            return
        
        self._task_queue_running = True
        self._task_queue_task = asyncio.create_task(self._task_queue_worker())
        logger.info("任务队列处理器已启动")
    
    async def stop_task_queue(self):
        """停止任务队列处理器"""
        if not self._task_queue_running:
            return
        
        self._task_queue_running = False
        if self._task_queue_task:
            self._task_queue_task.cancel()
            try:
                await self._task_queue_task
            except asyncio.CancelledError:
                pass
        logger.info("任务队列处理器已停止")
    
    async def _task_queue_worker(self):
        """任务队列工作线程 - 持续从数据库获取并执行任务"""
        logger.info("任务队列工作线程开始运行, task_queue_running=", self._task_queue_running)
        
        while self._task_queue_running:
            try:
                # 1. 查找待执行的作业
                await self._process_pending_jobs()
                
                # 2. 查找待执行的任务
                await self._process_pending_tasks()
                
                # 3. 短暂休眠，避免过度消耗CPU
                await asyncio.sleep(2)  # 每2秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"任务队列处理器出错: {str(e)}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(5)  # 出错后等待5秒再继续
    
    async def _process_pending_jobs(self):
        """处理待启动的作业"""
        db = self.session_maker()
        try:
            # 查找状态为PENDING的作业
            pending_jobs = db.query(VideoGenerationJob).filter(
                VideoGenerationJob.status == TaskStatus.PENDING
            ).order_by(VideoGenerationJob.created_at).limit(5).all()  # 限制数量避免一次处理太多
            
            # logger.info(f"任务队列: 发现 {len(pending_jobs)} 个待处理作业(jobs)")

            for job in pending_jobs:
                if not self._task_queue_running:
                    break
                
                # 检查是否已经在处理中
                if job.id in self.running_jobs and self.running_jobs[job.id]:
                    continue
                
                # 启动作业
                job.status = TaskStatus.RUNNING
                job.started_at = datetime.utcnow()
                self.running_jobs[job.id] = True
                db.commit()
                
                logger.info(f"任务队列: 启动作业 {job.id} - {job.name}")
                
        finally:
            db.close()
    
    async def _process_pending_tasks(self):
        """处理待执行的任务 - 支持分阶段处理"""
        db = self.session_maker()
        try:
            # 优先级1: 处理语音生成阶段的任务
            audio_pending_task = db.query(VideoGenerationTask).join(VideoGenerationJob).filter(
                VideoGenerationJob.status == TaskStatus.RUNNING,
                VideoGenerationTask.status == TaskStatus.AUDIO_PENDING
            ).order_by(
                VideoGenerationJob.created_at,
                VideoGenerationTask.created_at
            ).first()

            if audio_pending_task and self._task_queue_running:
                job_id = audio_pending_task.job_id
                if self.running_jobs.get(job_id, False):
                    logger.info(f"任务队列: 开始执行语音生成 {audio_pending_task.id} - {audio_pending_task.task_name}")
                    await self._execute_audio_generation(audio_pending_task.id, audio_pending_task.job.config)
                    self._update_job_progress(job_id)
                    await self._check_audio_phase_completion(job_id)
                return

            # 优先级2: 处理视频合成阶段的任务
            video_pending_task = db.query(VideoGenerationTask).join(VideoGenerationJob).filter(
                VideoGenerationJob.status == TaskStatus.RUNNING,
                VideoGenerationTask.status == TaskStatus.VIDEO_PENDING
            ).order_by(
                VideoGenerationJob.created_at,
                VideoGenerationTask.created_at
            ).first()

            if video_pending_task and self._task_queue_running:
                job_id = video_pending_task.job_id
                if self.running_jobs.get(job_id, False):
                    logger.info(f"任务队列: 开始执行视频合成 {video_pending_task.id} - {video_pending_task.task_name}")
                    await self._execute_video_composition(video_pending_task.id, video_pending_task.job.config)
                    self._update_job_progress(job_id)
                    await self._check_job_completion(job_id)
                return

            # 优先级3: 处理传统的PENDING任务（兼容性）
            pending_task = db.query(VideoGenerationTask).join(VideoGenerationJob).filter(
                VideoGenerationJob.status == TaskStatus.RUNNING,
                VideoGenerationTask.status == TaskStatus.PENDING
            ).order_by(
                VideoGenerationJob.created_at,
                VideoGenerationTask.created_at
            ).first()

            if pending_task and self._task_queue_running:
                job_id = pending_task.job_id
                if self.running_jobs.get(job_id, False):
                    logger.info(f"任务队列: 开始执行传统任务 {pending_task.id} - {pending_task.task_name}")
                    await self._execute_task(pending_task.id, pending_task.job.config)
                    self._update_job_progress(job_id)
                    await self._check_job_completion(job_id)

        finally:
            db.close()

    async def _check_audio_phase_completion(self, job_id: str):
        """检查语音生成阶段是否完成，如果完成则启动视频合成阶段"""
        db = self.session_maker()
        try:
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
            if not job:
                return

            # 检查是否还有待语音生成的任务
            audio_pending_count = db.query(VideoGenerationTask).filter(
                VideoGenerationTask.job_id == job_id,
                VideoGenerationTask.status == TaskStatus.AUDIO_PENDING
            ).count()

            # 检查是否有正在进行语音生成的任务
            audio_running_count = db.query(VideoGenerationTask).filter(
                VideoGenerationTask.job_id == job_id,
                VideoGenerationTask.status == TaskStatus.RUNNING,
                VideoGenerationTask.current_step.like('%语音%')
            ).count()

            # 如果没有待语音生成和正在语音生成的任务，说明语音生成阶段完成
            if audio_pending_count == 0 and audio_running_count == 0:
                # 将所有AUDIO_COMPLETED状态的任务转为VIDEO_PENDING
                audio_completed_tasks = db.query(VideoGenerationTask).filter(
                    VideoGenerationTask.job_id == job_id,
                    VideoGenerationTask.status == TaskStatus.AUDIO_COMPLETED
                ).all()

                if audio_completed_tasks:
                    for task in audio_completed_tasks:
                        task.status = TaskStatus.VIDEO_PENDING
                        task.current_step = "等待视频合成"

                    db.commit()
                    logger.info(f"作业 {job_id}: 语音生成阶段完成，{len(audio_completed_tasks)} 个任务进入视频合成阶段")

        finally:
            db.close()

    async def _check_job_completion(self, job_id: str):
        """检查作业是否完成"""
        db = self.session_maker()
        try:
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
            if not job:
                return

            # 检查是否还有未完成的任务（包括所有阶段）
            incomplete_statuses = [
                TaskStatus.PENDING,
                TaskStatus.AUDIO_PENDING,
                TaskStatus.AUDIO_COMPLETED,
                TaskStatus.VIDEO_PENDING,
                TaskStatus.RUNNING
            ]

            incomplete_count = db.query(VideoGenerationTask).filter(
                VideoGenerationTask.job_id == job_id,
                VideoGenerationTask.status.in_(incomplete_statuses)
            ).count()

            # 如果没有未完成的任务，标记作业为完成
            if incomplete_count == 0 and self.running_jobs.get(job_id, False):
                job.status = TaskStatus.COMPLETED
                job.completed_at = datetime.utcnow()
                self.running_jobs[job_id] = False
                db.commit()

                logger.info(f"任务队列: 作业 {job_id} - {job.name} 执行完成")

        finally:
            db.close()

    # ...existing code...
    
    def pause_job(self, job_id: str) -> bool:
        """暂停作业"""
        db = self.session_maker()
        try:
            self.running_jobs[job_id] = False
            
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
            if job and job.status == TaskStatus.RUNNING:
                job.status = TaskStatus.PAUSED
                db.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"暂停作业失败: {str(e)}")
            return False
        finally:
            db.close()
    
    def resume_job(self, job_id: str) -> bool:
        """恢复作业"""
        db = self.session_maker()
        try:
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
            if job and job.status == TaskStatus.PAUSED:
                job.status = TaskStatus.RUNNING
                self.running_jobs[job_id] = True
                db.commit()
                
                logger.info(f"作业 {job_id} 已恢复，将由任务队列处理器继续执行")
                return True
            return False
        except Exception as e:
            logger.error(f"恢复作业失败: {str(e)}")
            return False
        finally:
            db.close()
    
    def cancel_job(self, job_id: str) -> bool:
        """取消作业"""
        db = self.session_maker()
        try:
            self.running_jobs[job_id] = False
            
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == job_id).first()
            if job:
                job.status = TaskStatus.CANCELLED
                db.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"取消作业失败: {str(e)}")
            return False
        finally:
            db.close()
    
    async def retry_task(self, task_id: str) -> bool:
        """重试任务"""
        db = self.session_maker()
        try:
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if not task:
                return False
            
            # 重置任务状态
            task.status = TaskStatus.PENDING
            task.progress = 0
            task.current_step = None
            task.error_message = None
            task.retry_count = (task.retry_count or 0) + 1
            db.commit()
            
            # 获取作业配置并重新执行任务
            job = db.query(VideoGenerationJob).filter(VideoGenerationJob.id == task.job_id).first()
            if job:
                await self._execute_task(task.id, job.config)
            
            return True
        except Exception as e:
            logger.error(f"重试任务失败: {str(e)}")
            return False
        finally:
            db.close()
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        db = self.session_maker()
        try:
            task = db.query(VideoGenerationTask).filter(VideoGenerationTask.id == task_id).first()
            if task:
                task.status = TaskStatus.CANCELLED
                db.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            return False
        finally:
            db.close()
    
    def _extract_first_sentence(self, story_text: str) -> str:
        """
        提取故事的第一句话
        支持多种句末标点：. ? ! 。 ？ ！
        """
        if not story_text:
            return ""
        
        logger.info(f"开始提取第一句话，原始文本: '{story_text[:100]}...'")
        
        # 定义句末标点符号（包括中英文）
        sentence_enders = ('.', '?', '!', '。', '？', '！')
        
        # 找到第一个句末标点的位置
        first_sentence_end_pos = -1
        found_ender = None
        
        for ender in sentence_enders:
            pos = story_text.find(ender)
            if pos != -1:
                if first_sentence_end_pos == -1 or pos < first_sentence_end_pos:
                    first_sentence_end_pos = pos
                    found_ender = ender
        
        if first_sentence_end_pos == -1:
            # 如果没有找到标点，返回前100个字符
            first_sentence = story_text[:100].strip()
            logger.warning(f"未找到句末标点，使用前100字符作为第一句话: '{first_sentence}'")
            return first_sentence
        
        # 提取第一句话（包含标点）
        first_sentence = story_text[:first_sentence_end_pos + 1].strip()
        logger.info(f"成功提取第一句话: '{first_sentence}' (结束标点: '{found_ender}', 位置: {first_sentence_end_pos})")
        
        return first_sentence
