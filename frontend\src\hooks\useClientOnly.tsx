'use client'

import { useEffect, useState } from 'react'

/**
 * useClientOnly Hook - 确保组件只在客户端渲染，避免hydration不匹配
 * @returns boolean - 是否为客户端环境
 */
export function useClientOnly(): boolean {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return isClient
}

/**
 * ClientOnly 组件 - 包装需要仅在客户端渲染的组件
 */
interface ClientOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const isClient = useClientOnly()

  if (!isClient) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * NoSSR 组件 - 动态导入的封装，禁用SSR
 */
export function NoSSR({ children }: { children: React.ReactNode }) {
  return (
    <ClientOnly
      fallback={
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      {children}
    </ClientOnly>
  )
}
