#!/usr/bin/env python
"""
快速修复资源模型的Base类引用问题
"""

with open('src/models/resources.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 修复Base类引用
content = content.replace('class Prompt(Base):', 'class Prompt(BaseModel):')
content = content.replace('class Account(Base):', 'class Account(BaseModel):')  
content = content.replace('class CoverTemplate(Base):', 'class CoverTemplate(BaseModel):')

with open('src/models/resources.py', 'w', encoding='utf-8') as f:
    f.write(content)

print('Base类引用修复完成')
