"""
环境检测和依赖安装工具
自动检测和安装构建所需的环境
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

class EnvironmentChecker:
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.project_root = Path(__file__).parent
        
    def check_all(self):
        """检查所有环境依赖"""
        print("🔍 环境依赖检测")
        print("=" * 50)
        
        results = {
            "python": self._check_python(),
            "pip": self._check_pip(),
            "nodejs": self._check_nodejs(),
            "npm": self._check_npm(),
            "venv": self._check_virtual_env(),
            "build_deps": self._check_build_dependencies()
        }
        
        print("\n📋 检测结果:")
        print("-" * 30)
        
        for name, (status, message) in results.items():
            icon = "✅" if status else "❌"
            print(f"{icon} {name.upper()}: {message}")
        
        # 统计
        passed = sum(1 for status, _ in results.values() if status)
        total = len(results)
        
        print(f"\n📊 通过: {passed}/{total}")
        
        if passed < total:
            print(f"\n🔧 需要解决 {total - passed} 个问题")
            self._provide_solutions(results)
        else:
            print("\n🎉 所有依赖都已满足！")
            
        return passed == total
    
    def _check_python(self):
        """检查 Python"""
        try:
            version = sys.version_info
            if version.major >= 3 and version.minor >= 8:
                return True, f"Python {version.major}.{version.minor}.{version.micro}"
            else:
                return False, f"版本过低: {version.major}.{version.minor} (需要 3.8+)"
        except:
            return False, "未找到 Python"
    
    def _check_pip(self):
        """检查 pip"""
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.split()[1]
                return True, f"pip {version}"
            else:
                return False, "pip 不可用"
        except:
            return False, "未找到 pip"
    
    def _check_nodejs(self):
        """检查 Node.js"""
        commands = ["node", "node.exe"] if self.is_windows else ["node"]
        
        for cmd in commands:
            try:
                result = subprocess.run([cmd, "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    return True, f"Node.js {version}"
            except:
                continue
        
        return False, "未找到 Node.js"
    
    def _check_npm(self):
        """检查 npm"""
        commands = ["npm", "npm.cmd"] if self.is_windows else ["npm"]
        
        for cmd in commands:
            try:
                result = subprocess.run([cmd, "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    return True, f"npm {version}"
            except:
                continue
        
        return False, "未找到 npm"
    
    def _check_virtual_env(self):
        """检查虚拟环境"""
        in_venv = (hasattr(sys, 'real_prefix') or 
                  (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))
        
        if in_venv:
            return True, "虚拟环境已激活"
        else:
            return False, "未使用虚拟环境 (推荐使用)"
    
    def _check_build_dependencies(self):
        """检查构建依赖"""
        try:
            # 检查基础依赖
            basic_deps = ['fastapi', 'uvicorn', 'pathlib']
            missing = []
            
            for dep in basic_deps:
                try:
                    __import__(dep)
                except ImportError:
                    missing.append(dep)
            
            if missing:
                return False, f"缺少依赖: {', '.join(missing)}"
            else:
                return True, "基础依赖已安装"
        except:
            return False, "无法检查依赖"
    
    def _provide_solutions(self, results):
        """提供解决方案"""
        print("\n🛠️  解决方案:")
        print("-" * 30)
        
        # Python 问题
        if not results["python"][0]:
            print("🐍 Python 问题:")
            print("   下载安装: https://www.python.org/downloads/")
            print("   确保添加到 PATH 环境变量")
            print()
        
        # Node.js 问题
        if not results["nodejs"][0] or not results["npm"][0]:
            print("🟢 Node.js/npm 问题:")
            print("   下载安装: https://nodejs.org/")
            print("   或者跳过前端构建: --skip-frontend")
            print()
        
        # 虚拟环境问题
        if not results["venv"][0]:
            print("📦 虚拟环境 (推荐):")
            print("   python -m venv venv")
            if self.is_windows:
                print("   venv\\Scripts\\activate")
            else:
                print("   source venv/bin/activate")
            print()
        
        # 依赖问题
        if not results["build_deps"][0]:
            print("📚 Python 依赖:")
            print("   pip install -r requirements.txt")
            print("   pip install -r requirements-build.txt")
            print()
    
    def auto_install_python_deps(self):
        """自动安装 Python 依赖"""
        print("📦 自动安装 Python 依赖...")
        
        requirements_files = [
            self.project_root / "backend" / "requirements.txt",
            self.project_root / "requirements-build.txt"
        ]
        
        for req_file in requirements_files:
            if req_file.exists():
                print(f"📄 安装: {req_file.name}")
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "install", "-r", str(req_file)
                    ], check=True)
                    print(f"✅ {req_file.name} 安装完成")
                except subprocess.CalledProcessError as e:
                    print(f"❌ {req_file.name} 安装失败: {e}")
                    return False
            else:
                print(f"⚠️  未找到: {req_file}")
        
        return True
    
    def interactive_setup(self):
        """交互式环境设置"""
        print("🔧 交互式环境设置")
        print("=" * 50)
        
        # 检查环境
        all_good = self.check_all()
        
        if all_good:
            print("\n🎉 环境检测通过，可以开始构建！")
            return True
        
        print(f"\n❓ 是否自动安装 Python 依赖? (y/n): ", end="")
        install_deps = input().strip().lower()
        
        if install_deps in ['y', 'yes', '是']:
            success = self.auto_install_python_deps()
            if success:
                print("\n✅ Python 依赖安装完成")
            else:
                print("\n❌ 部分依赖安装失败，请手动检查")
        
        # 重新检查
        print("\n🔄 重新检查环境...")
        return self.check_all()

def main():
    checker = EnvironmentChecker()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        checker.interactive_setup()
    else:
        checker.check_all()

if __name__ == "__main__":
    main()
