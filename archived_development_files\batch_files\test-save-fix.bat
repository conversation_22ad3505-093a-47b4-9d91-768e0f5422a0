@echo off
echo ===============================================
echo 封面模板保存功能错误修复测试
echo ===============================================

echo.
echo 错误分析:
echo - TypeError: Cannot read properties of undefined (reading 'toLowerCase')
echo - 发生在模板保存后返回列表页面时
echo - 原因: 回调数据格式不正确，导致模板名称为undefined
echo.

echo 修复内容:
echo 1. 增强 handleTemplateSave 回调的数据验证
echo 2. 修复 filteredTemplates 中的字符串安全处理
echo 3. 改进 SimpleCanvasEditor 的回调数据格式
echo 4. 增强 ErrorBoundary 的错误分析
echo.

echo 步骤 1: 初始化测试数据...
python init_template_data.py
if %ERRORLEVEL% NEQ 0 (
    echo 数据初始化失败！
    pause
    exit /b 1
)

echo.
echo 步骤 2: 启动后端服务...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\backend"
start "Backend Server" python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

echo 等待后端服务启动...
timeout /t 8

echo.
echo 步骤 3: 测试保存工作流程...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator"
python test_template_save_workflow.py

echo.
echo 步骤 4: 启动前端服务...
cd /d "d:\SHAOJIAHAO\IDE_WORKSPACES\CursorAICoding\RedditStoryVideoGenerator\frontend"
start "Frontend Server" npm run dev

echo.
echo 等待前端服务启动...
timeout /t 15

echo.
echo ===============================================
echo 测试指南：
echo.
echo 1. 访问封面模板页面:
echo    http://localhost:3000/covers
echo.
echo 2. 测试步骤:
echo    a) 点击"新建模板"创建一个新模板
echo    b) 在编辑器中添加一些元素
echo    c) 点击保存按钮
echo    d) 返回列表页面，检查是否有错误
echo.
echo 3. 验证修复:
echo    - 不应该出现 toLowerCase 错误
echo    - 模板列表应该正常显示
echo    - 模板名称应该正确更新
echo    - 控制台不应该有红色错误
echo.
echo 4. 如果仍有问题，请检查:
echo    - 浏览器控制台的详细错误信息
echo    - 后端API响应格式
echo    - 前端数据处理逻辑
echo ===============================================

pause
