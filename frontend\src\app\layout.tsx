import React from 'react'
import { ErrorBoundary } from '../components/ErrorBoundary'
import MainLayout from '@/components/layout/MainLayout'
import './globals.css'

export const metadata = {
  title: 'Reddit Story Video Generator',
  description: 'Generate engaging Reddit story videos automatically',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#3b82f6" />
        {/* 阻止一些扩展的干扰 */}
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      </head>
      <body className="antialiased" suppressHydrationWarning>
        <ErrorBoundary>
          <div id="root" suppressHydrationWarning>
            <MainLayout>
              {children}
            </MainLayout>
          </div>
        </ErrorBoundary>
        {/* 客户端脚本来处理扩展干扰 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if (typeof window !== 'undefined') {
                // 移除可能由扩展添加的属性
                const observer = new MutationObserver(() => {
                  const body = document.body;
                  if (body.hasAttribute('mpa-version')) {
                    body.removeAttribute('mpa-version');
                  }
                  if (body.hasAttribute('mpa-extension-id')) {
                    body.removeAttribute('mpa-extension-id');
                  }
                });
                observer.observe(document.body, { 
                  attributes: true, 
                  attributeFilter: ['mpa-version', 'mpa-extension-id'] 
                });
              }
            `,
          }}
        />
      </body>
    </html>
  )
}
