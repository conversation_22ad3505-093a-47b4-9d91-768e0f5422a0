import { create } from 'zustand';
import { 
  getAllTemplates, 
  getTemplateById, 
  createTemplate, 
  updateTemplate, 
  deleteTemplate as deleteTemplateApi,
  getTemplateStats,
  generateCover,
  getAvailableVariables,
  previewTemplate
} from '@/lib/api/coverTemplates';

export interface CoverElement {
  id: string;
  type: 'text' | 'image' | 'shape' | 'background';
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  opacity?: number;
  zIndex?: number;
  properties: Record<string, any>;
  variableBinding?: {
    enabled: boolean;
    variableName: string;
    propertyPath: string;
  };
}

export interface CoverTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  width: number;
  height: number;
  background: any;
  elements: CoverElement[];
  variables: any[];
  thumbnailPath?: string;
  thumbnailUrl?: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  elementCount: number;
  variableCount: number;
  hasVariables: boolean;
  canvasSize: string;
}

export interface TemplateStats {
  totalTemplates: number;
  publicTemplates: number;
  privateTemplates: number;
  categoryCounts: Record<string, number>;
  variableUsage: Record<string, number>;
}

export interface AvailableVariable {
  name: string;
  type: string;
  description: string;
  defaultValue?: any;
}

interface CoverTemplateStore {
  // 状态
  templates: CoverTemplate[];
  selectedTemplate: CoverTemplate | null;
  categories: string[];
  stats: TemplateStats | null;
  availableVariables: AvailableVariable[];
  loading: boolean;
  error: string | null;

  // 预览相关
  previewData: any;
  previewLoading: boolean;

  // 操作方法
  fetchTemplates: (params?: any) => Promise<void>;
  fetchTemplateById: (id: string) => Promise<CoverTemplate | null>;
  createTemplate: (data: any) => Promise<CoverTemplate | null>;
  updateTemplate: (id: string, data: any) => Promise<CoverTemplate | null>;
  deleteTemplate: (id: string) => Promise<void>;
  fetchStats: () => Promise<void>;
  fetchAvailableVariables: () => Promise<void>;
  generateCover: (templateId: string, data: any) => Promise<string | null>;
  previewTemplate: (templateId: string, data?: any) => Promise<string | null>;

  // 状态管理
  setSelectedTemplate: (template: CoverTemplate | null) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

export const useCoverTemplateStore = create<CoverTemplateStore>((set, get) => ({
  // 初始状态
  templates: [],
  selectedTemplate: null,
  categories: [],
  stats: null,
  availableVariables: [],
  loading: false,
  error: null,
  previewData: null,
  previewLoading: false,

  // 获取模板列表
  fetchTemplates: async (params = {}) => {
    set({ loading: true, error: null });
    try {
      const response = await getAllTemplates(params);
      if (response.success) {
        const rawTemplates = response.data.templates || [];
        // 转换数据格式以匹配前端接口
        const templates = rawTemplates.map((template: any) => ({
          id: template.id,
          name: template.name,
          description: template.metadata?.description || '',
          category: template.metadata?.category || '其他',
          width: template.metadata?.width || 1920,
          height: template.metadata?.height || 1080,
          background: {}, // 默认背景
          elements: [], // 默认元素
          variables: template.variables || [],
          thumbnailPath: template.previewPath,
          thumbnailUrl: template.previewPath,
          isPublic: template.isBuiltIn || false,
          createdAt: template.createdAt,
          updatedAt: template.updatedAt,
          elementCount: 0, // 需要从实际元素计算
          variableCount: template.variables?.length || 0,
          hasVariables: (template.variables?.length || 0) > 0
        }));
        
        const categories = Array.from(new Set(templates.map((t: CoverTemplate) => t.category))).filter(Boolean) as string[];
        set({ 
          templates,
          categories, // 不要添加'all'，由页面组件处理
          loading: false 
        });
      } else {
        throw new Error(response.message || '获取模板列表失败');
      }
    } catch (error) {
      console.error('获取模板列表失败:', error);
      set({ 
        error: error instanceof Error ? error.message : '获取模板列表失败',
        loading: false 
      });
    }
  },

  // 根据ID获取模板
  fetchTemplateById: async (id: string) => {
    set({ loading: true, error: null });
    try {
      const response = await getTemplateById(id);
      if (response.success) {
        const template = response.data;
        set({ selectedTemplate: template, loading: false });
        return template;
      } else {
        throw new Error(response.message || '获取模板详情失败');
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
      set({ 
        error: error instanceof Error ? error.message : '获取模板详情失败',
        loading: false 
      });
      return null;
    }
  },

  // 创建模板
  createTemplate: async (data: any) => {
    set({ loading: true, error: null });
    try {
      const response = await createTemplate(data);
      if (response.success) {
        const newTemplate = response.data;
        // 刷新整个模板列表以确保数据同步
        await get().fetchTemplates();
        set({ loading: false });
        return newTemplate;
      } else {
        throw new Error(response.message || '创建模板失败');
      }
    } catch (error) {
      console.error('创建模板失败:', error);
      set({ 
        error: error instanceof Error ? error.message : '创建模板失败',
        loading: false 
      });
      return null;
    }
  },

  // 更新模板
  updateTemplate: async (id: string, data: any) => {
    set({ loading: true, error: null });
    try {
      const response = await updateTemplate(id, data);
      if (response.success) {
        const updatedTemplate = response.data;
        set(state => ({
          templates: state.templates.map(t => 
            t.id === id ? updatedTemplate : t
          ),
          selectedTemplate: state.selectedTemplate?.id === id ? updatedTemplate : state.selectedTemplate,
          loading: false
        }));
        return updatedTemplate;
      } else {
        throw new Error(response.message || '更新模板失败');
      }
    } catch (error) {
      console.error('更新模板失败:', error);
      set({ 
        error: error instanceof Error ? error.message : '更新模板失败',
        loading: false 
      });
      return null;
    }
  },

  // 删除模板
  deleteTemplate: async (id: string) => {
    set({ loading: true, error: null });
    try {
      const response = await deleteTemplateApi(id);
      if (response.success) {
        set(state => ({
          templates: state.templates.filter(t => t.id !== id),
          selectedTemplate: state.selectedTemplate?.id === id ? null : state.selectedTemplate,
          loading: false
        }));
      } else {
        throw new Error(response.message || '删除模板失败');
      }
    } catch (error) {
      console.error('删除模板失败:', error);
      set({ 
        error: error instanceof Error ? error.message : '删除模板失败',
        loading: false 
      });
      throw error;
    }
  },

  // 获取统计信息
  fetchStats: async () => {
    try {
      const response = await getTemplateStats();
      if (response.success) {
        const rawStats = response.data;
        // 转换后端字段到前端接口格式
        const stats: TemplateStats = {
          totalTemplates: rawStats.total || 0,
          publicTemplates: rawStats.builtIn || 0,
          privateTemplates: rawStats.custom || 0,
          categoryCounts: rawStats.categories?.reduce((acc: Record<string, number>, cat: any) => {
            acc[cat.name] = cat.count;
            return acc;
          }, {}) || {},
          variableUsage: {} // 暂时为空，后续可以从其他API获取
        };
        set({ stats });
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  },

  // 获取可用变量
  fetchAvailableVariables: async () => {
    try {
      const response = await getAvailableVariables();
      if (response.success) {
        set({ availableVariables: response.data.variables || [] });
      }
    } catch (error) {
      console.error('获取可用变量失败:', error);
    }
  },

  // 生成封面
  generateCover: async (templateId: string, data: any) => {
    set({ loading: true, error: null });
    try {
      const response = await generateCover(templateId, data);
      if (response.success) {
        set({ loading: false });
        return response.data.coverUrl;
      } else {
        throw new Error(response.message || '生成封面失败');
      }
    } catch (error) {
      console.error('生成封面失败:', error);
      set({ 
        error: error instanceof Error ? error.message : '生成封面失败',
        loading: false 
      });
      return null;
    }
  },

  // 预览模板
  previewTemplate: async (templateId: string, data = {}) => {
    set({ previewLoading: true, error: null });
    try {
      const response = await previewTemplate(templateId, data);
      if (response.success) {
        set({ 
          previewData: response.data,
          previewLoading: false 
        });
        return response.data.previewUrl;
      } else {
        throw new Error(response.message || '预览生成失败');
      }
    } catch (error) {
      console.error('预览生成失败:', error);
      set({ 
        error: error instanceof Error ? error.message : '预览生成失败',
        previewLoading: false 
      });
      return null;
    }
  },

  // 状态管理方法
  setSelectedTemplate: (template: CoverTemplate | null) => {
    set({ selectedTemplate: template });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      templates: [],
      selectedTemplate: null,
      categories: [],
      stats: null,
      availableVariables: [],
      loading: false,
      error: null,
      previewData: null,
      previewLoading: false
    });
  }
}));
