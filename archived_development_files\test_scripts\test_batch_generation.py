#!/usr/bin/env python3
"""
批量视频生成功能测试脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.schemas.video_generation import CreateBatchVideoGenerationJobRequest, BatchVideoGenerationJobConfig
from src.services.video_generation_service import VideoGenerationService
from src.core.database import get_session_maker
from src.models.accounts import Account
from src.models.resources import VideoMaterial, BackgroundMusic
from src.models.cover_templates import CoverTemplate

async def test_batch_generation():
    """测试批量视频生成功能"""
    
    print("🚀 开始测试批量视频生成功能...")
    
    # 获取数据库会话
    session_maker = get_session_maker()
    db = session_maker()
    
    try:
        # 1. 检查必要的资源是否存在
        print("\n📋 检查系统资源...")
        
        # 检查账号
        accounts = db.query(Account).limit(2).all()
        if len(accounts) < 1:
            print("❌ 错误: 系统中没有可用的账号")
            return False
        print(f"✅ 找到 {len(accounts)} 个账号")
        
        # 检查视频素材
        materials = db.query(VideoMaterial).limit(5).all()
        if len(materials) < 1:
            print("❌ 错误: 系统中没有可用的视频素材")
            return False
        print(f"✅ 找到 {len(materials)} 个视频素材")
        
        # 检查背景音乐
        music = db.query(BackgroundMusic).limit(3).all()
        if len(music) < 1:
            print("❌ 错误: 系统中没有可用的背景音乐")
            return False
        print(f"✅ 找到 {len(music)} 个背景音乐")
        
        # 检查封面模板
        templates = db.query(CoverTemplate).limit(3).all()
        if len(templates) < 1:
            print("❌ 错误: 系统中没有可用的封面模板")
            return False
        print(f"✅ 找到 {len(templates)} 个封面模板")
        
        # 2. 创建测试数据
        print("\n📝 准备测试数据...")
        
        test_stories = [
            "这是第一个测试文案，用于验证批量生成功能是否正常工作。",
            "第二个测试文案，内容稍微长一些，包含更多的文字内容来测试语音生成。",
            "第三个测试文案，这是一个简短的测试。",
            "第四个测试文案，用于测试账号轮流分配功能。",
            "最后一个测试文案，验证整个批量生成流程。"
        ]
        
        account_ids = [account.id for account in accounts[:2]]  # 使用前两个账号
        
        print(f"✅ 准备了 {len(test_stories)} 条测试文案")
        print(f"✅ 选择了 {len(account_ids)} 个测试账号")
        
        # 3. 创建批量生成配置
        print("\n⚙️ 创建批量生成配置...")
        
        config = BatchVideoGenerationJobConfig(
            material_selection="random",
            video_material_group=materials[0].category if materials else None,
            voice_settings={
                "voice": "zh_female_zhixingnvsheng_mars_bigtts",
                "speed": 1.0
            },
            audio_settings={
                "speech_volume": 0.8,
                "background_music_volume": 0.3,
                "enable_background_music": True
            },
            music_selection="random",
            background_music_group=music[0].category if music else None,
            cover_template_id=templates[0].id,
            subtitle_config={
                "font_family": "Arial",
                "font_size": 24,
                "font_color": "#FFFFFF",
                "position": "bottom",
                "enabled": True
            },
            video_config={
                "resolution": "1080x1920",
                "fps": 30,
                "output_format": "mp4"
            }
        )
        
        # 4. 创建批量生成请求
        request = CreateBatchVideoGenerationJobRequest(
            name="批量生成测试作业",
            description="这是一个用于测试批量视频生成功能的测试作业",
            config=config,
            account_ids=account_ids,
            stories=test_stories
        )
        
        print("✅ 批量生成配置创建完成")
        
        # 5. 执行批量生成
        print("\n🎬 创建批量视频生成作业...")
        
        service = VideoGenerationService(session_maker)
        job = await service.create_batch_job(request)
        
        print(f"✅ 批量作业创建成功!")
        print(f"   作业ID: {job.id}")
        print(f"   作业名称: {job.name}")
        print(f"   总任务数: {job.total_tasks}")
        print(f"   状态: {job.status}")
        
        # 6. 验证任务创建
        print("\n🔍 验证任务创建...")
        
        from src.models.video_generation import VideoGenerationTask
        tasks = db.query(VideoGenerationTask).filter(VideoGenerationTask.job_id == job.id).all()
        
        print(f"✅ 创建了 {len(tasks)} 个任务")
        
        # 验证文案分配
        for i, task in enumerate(tasks):
            expected_account = account_ids[i % len(account_ids)]
            if task.account_id == expected_account:
                print(f"   ✅ 任务 {i+1}: 账号分配正确 ({task.account_id})")
            else:
                print(f"   ❌ 任务 {i+1}: 账号分配错误 (期望: {expected_account}, 实际: {task.account_id})")
            
            if task.generated_story and task.generated_story.strip():
                print(f"   ✅ 任务 {i+1}: 文案预设成功")
            else:
                print(f"   ❌ 任务 {i+1}: 文案预设失败")
        
        print("\n🎉 批量视频生成功能测试完成!")
        print(f"   可以在任务管理页面查看作业进度: /tasks")
        print(f"   作业ID: {job.id}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

async def main():
    """主函数"""
    print("=" * 60)
    print("批量视频生成功能测试")
    print("=" * 60)
    
    success = await test_batch_generation()
    
    if success:
        print("\n✅ 所有测试通过!")
        sys.exit(0)
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
