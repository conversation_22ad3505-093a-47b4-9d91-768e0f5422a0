# 视频生成功能需求文档

## 1. 功能概述

基于已有的资源管理模块（账号、素材、音乐、模板、提示词），实现个性化批量视频生成功能。

## 2. 输入条件配置

### 2.1 视频素材配置
- **选择范围**: 指定一个视频素材的分组
- **选择方式**: 
  - `随机` - 系统自动从分组中选择多样化的素材
  - `手动` - 用户手动选择具体的素材列表
- **使用规则**: 所选素材作为视频合成的画面输入

### 2.2 故事内容配置
- **提示词分组**: 指定一个提示词的分组
- **具体提示词**: 从该分组中选择一个具体的提示词
- **生成方式**: 通过大模型调用该提示词直接输出完整的故事文案

### 2.3 音频配置
- **音色选择**: 指定一个音色（从系统设置中支持的音色列表选择）
- **语速设置**: 配置语速倍率（0.5-2.0）
- **用途**: 用于故事文案自动配音，生成对应的MP3文件

### 2.4 背景音乐配置
- **音乐分组**: 指定一个背景音乐的分组
- **选择方式**:
  - `随机` - 批量任务时自动选择合适的背景音乐
  - `固定` - 指定某一首具体的背景音乐

### 2.5 账号和封面配置
- **账号选择**: 指定一个或多个账号
- **视频数量**: 为每个账号设定生成的视频数量
- **封面模板**: 指定一个封面模板
- **用途**: 每个任务生成一个视频封面浮层（静态图片），后续合成到视频中

### 2.6 字幕样式配置
- **字体**: 字幕字体设置
- **大小**: 字体大小（像素）
- **颜色**: 字体颜色（十六进制）
- **用途**: 在视频中根据时间戳生成对应的字幕文字

## 3. 任务生成规则

### 3.1 任务计算
- 根据所选的账号和每个账号的视频数，自动计算需要创建的总任务数
- 例如：账号A生成5个视频 + 账号B生成3个视频 = 总共8个任务

### 3.2 任务执行方式
- **执行模式**: 串行执行（一个任务完成后再执行下一个）
- **任务控制**: 支持暂停、取消、重试功能
- **文件命名**: `账号名称_第一句话.mp4`

## 4. 单个任务执行流程

### 4.1 素材选择
- 根据设定的规则，从视频素材分组中挑选素材
- **原则**: 单个视频内尽可能少重复素材
- **不够用处理**: 如果素材数量不足，简单重复使用

### 4.2 文案生成
- 调用指定的提示词，通过LLM生成一个完整的故事文案
- 提示词中的变量（如账号名称）会被自动替换

### 4.3 语音生成
- 将生成的文案调用TTS服务，生成文案对应的语音文件（MP3格式）
- 使用指定的音色和语速设置

### 4.4 音频分析和字幕生成
- **音频分析**: 由于TTS服务只返回整个MP3文件，需要自行实现音频分析来估算时间戳
- **第一句话时长**: 在音频分析时确定第一句话的实际音频时长
- **字幕规则**:
  - 按照空格或标点分割的英文单词（暂不支持中文）
  - 生成SRT字幕文件，每一段最多包含1-3个单词
  - 时间戳要与朗读时间精确匹配
  - **重要**: 文案的第一句话不生成字幕，因为该时段会用封面图覆盖画面

### 4.5 封面生成
- 根据封面模板、当前任务的账号头像和名称、以及文案的第一句话
- 替换模板中的变量，生成一张静态图片

## 5. 视频合成规则

### 5.1 画面合成
- **素材整合**: 根据文案的时长，将挑选的视频素材进行串联
  - 如果素材总时长超过文案时长：截断多余部分
  - 如果素材总时长不足：重复素材直到达到文案时长

### 5.2 音频合成
- **背景音乐处理**:
  - 如果音乐长度比文案时长长：自动截断
  - 如果音乐长度比文案时长短：重复播放直到达到文案时长
- **语音整合**: 将文案的语音整合到视频中

### 5.3 覆盖层处理
- **封面覆盖**: 在文案第一句话的时长部分，用生成的封面图在视频画面里居中浮动在视频素材上方
- **字幕显示**: 从文案第二句话开始，严格根据字幕文件呈现正确的字幕

### 5.4 视频输出规格
- **分辨率**: 1080×1920（竖屏），支持配置横屏更佳
- **帧率**: 30fps
- **格式**: MP4
- **字幕位置**: 视频画面的中心位置
- **字幕效果**: 暂无特殊效果要求

## 6. 技术实现要点

### 6.1 音频时间戳分析
- 由于TTS服务只返回完整MP3文件，需要自研音频分析功能
- 估算单词级别的时间戳信息
- 确定第一句话的准确时长

### 6.2 视频合成技术栈
- 使用FFmpeg进行视频处理和合成
- 支持视频格式转换、分辨率调整、时长控制
- 实现音频混合（语音+背景音乐）

### 6.3 素材选择策略
- 单视频内尽量避免重复素材
- 跨任务之间无重复要求
- 素材不足时的简单重复策略

### 6.4 任务管理
- 串行执行机制
- 任务状态跟踪（等待、运行、暂停、完成、失败、取消）
- 进度监控和错误处理
- 重试机制

## 7. 用户界面要求

### 7.1 配置界面
- 直观的分组选择器（视频素材、提示词、背景音乐）
- 账号多选和数量设定
- 音色、语速、字幕样式的可视化配置

### 7.2 监控界面
- 批量任务进度展示
- 单个任务状态和当前步骤
- 任务控制按钮（启动、暂停、取消、重试）

### 7.3 结果管理
- 生成的视频文件列表
- 下载和预览功能
- 任务日志和错误信息

## 8. 技术架构设计

### 8.1 后端架构
- **数据模型**: 
  - `VideoGenerationJob` - 视频生成作业（包含所有配置参数）
  - `VideoGenerationTask` - 单个视频生成任务
  - 支持任务状态管理、进度跟踪、错误记录
  
- **服务层**:
  - `VideoGenerationService` - 主要业务逻辑服务
  - `VideoGenerationHelpers` - 辅助方法（音频分析、字幕生成、视频合成）
  - `LLMService` - LLM调用服务（支持多种模型）
  - `TTSService` - 文本转语音服务

- **API层**:
  - `/api/video-generation/jobs` - 作业管理API
  - `/api/video-generation/tasks` - 任务管理API
  - 支持创建、查询、控制、删除等操作

### 8.2 前端架构
- **页面组件**:
  - `/generate` - 视频生成配置页面
  - `/tasks` - 任务管理监控页面
  
- **状态管理**:
  - 表单状态管理
  - 任务状态实时更新
  - 进度监控和错误处理

### 8.3 文件管理
- **工作目录**: `generated_videos/{job_id}/`
- **文件结构**:
  ```
  generated_videos/
  ├── {job_id}/
  │   ├── {task_id}_story.txt      # 生成的故事文案
  │   ├── {task_id}_audio.mp3      # TTS生成的音频
  │   ├── {task_id}_cover.png      # 生成的封面图
  │   ├── {task_id}_subtitles.srt  # 字幕文件
  │   └── {task_id}_final.mp4      # 最终视频
  ```

## 9. API接口设计

### 9.1 作业管理
```
POST /api/video-generation/jobs          # 创建视频生成作业
GET  /api/video-generation/jobs          # 获取作业列表
GET  /api/video-generation/jobs/{id}     # 获取作业详情
DELETE /api/video-generation/jobs/{id}   # 删除作业
```

### 9.2 任务管理
```
GET  /api/video-generation/tasks                    # 获取任务列表
GET  /api/video-generation/tasks/{id}               # 获取任务详情
POST /api/video-generation/tasks/{id}/pause         # 暂停任务
POST /api/video-generation/tasks/{id}/resume        # 恢复任务
POST /api/video-generation/tasks/{id}/cancel        # 取消任务
POST /api/video-generation/tasks/{id}/retry         # 重试任务
```

### 9.3 进度监控
```
GET  /api/video-generation/jobs/{id}/progress       # 获取作业进度
GET  /api/video-generation/tasks/{id}/logs          # 获取任务日志
```

## 10. 实现状态

### 10.1 已完成
- ✅ 需求文档整理和技术设计
- ✅ 后端数据模型和Schema设计
- ✅ 视频生成服务核心逻辑实现
- ✅ LLM和TTS服务集成
- ✅ API接口实现和路由注册
- ✅ 前端配置页面重构（支持所有输入项）
- ✅ 基础的任务管理页面框架

### 10.2 待完善
- 🔄 前端任务管理页面的完善（实时状态、进度展示、控制按钮）
- 🔄 音频分析和字幕生成的精确实现
- 🔄 视频合成流程的完整实现
- 🔄 封面生成的模板系统集成
- 🔄 错误处理和异常情况处理
- 🔄 文件路径管理和权限控制
- 🔄 端到端联调和测试

### 10.3 优化项
- 📋 任务日志详细记录
- 📋 结果文件的下载和预览功能
- 📋 批量操作优化
- 📋 UI/UX细节优化
- 📋 性能监控和优化

---

**文档版本**: v2.0  
**更新日期**: 2025-01-27  
**状态**: 核心功能基本实现，进入联调测试阶段
