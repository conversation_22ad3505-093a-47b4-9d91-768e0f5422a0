#!/usr/bin/env python3
"""
调试数据库路径问题
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.src.core.config import get_settings
import sqlite3

def debug_database_path():
    """调试数据库路径问题"""
    print("=== 调试数据库路径 ===")
    
    # 当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 配置中的数据库URL
    settings = get_settings()
    print(f"配置中的数据库URL: {settings.database_url}")
    
    # 解析SQLite数据库文件路径
    if settings.database_url.startswith("sqlite:///"):
        db_path = settings.database_url[10:]  # 移除 "sqlite:///"
        if db_path.startswith("./"):
            db_path = db_path[2:]  # 移除 "./"
        abs_db_path = os.path.abspath(db_path)
        print(f"数据库文件路径: {abs_db_path}")
        print(f"数据库文件是否存在: {os.path.exists(abs_db_path)}")
        
        if os.path.exists(abs_db_path):
            print(f"数据库文件大小: {os.path.getsize(abs_db_path)} bytes")
            
            # 检查表结构
            try:
                conn = sqlite3.connect(abs_db_path)
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='video_materials'")
                table_exists = cursor.fetchone()
                print(f"video_materials表是否存在: {table_exists is not None}")
                
                if table_exists:
                    cursor.execute("SELECT COUNT(*) FROM video_materials")
                    count = cursor.fetchone()[0]
                    print(f"video_materials表记录数: {count}")
                    
                    if count > 0:
                        cursor.execute("SELECT id, name FROM video_materials LIMIT 3")
                        rows = cursor.fetchall()
                        print("前3条记录:")
                        for row in rows:
                            print(f"  - {row}")
                
                conn.close()
            except Exception as e:
                print(f"数据库查询失败: {e}")
    
    # 检查backend目录下的数据库文件
    backend_db = "D:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend/reddit_story_generator.db"
    if os.path.exists(backend_db):
        print(f"\nbackend目录下的数据库文件: {os.path.abspath(backend_db)}")
        print(f"文件大小: {os.path.getsize(backend_db)} bytes")
        
        try:
            conn = sqlite3.connect(backend_db)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM video_materials")
            count = cursor.fetchone()[0]
            print(f"记录数: {count}")
            conn.close()
        except Exception as e:
            print(f"查询失败: {e}")

if __name__ == "__main__":
    debug_database_path()
