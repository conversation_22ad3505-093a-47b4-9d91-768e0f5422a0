#!/usr/bin/env python3
"""
快速截图服务验证工具
快速检查截图服务是否正常工作
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    # 检查Playwright
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright 已安装")
    except ImportError:
        print("❌ Playwright 未安装")
        print("   请运行: pip install playwright")
        print("   然后运行: playwright install chromium")
        return False
    
    # 检查数据库模块
    try:
        from src.core.database import get_db
        from src.services.cover_screenshot_service import cover_screenshot_service
        from src.models.accounts import Account
        from src.models.resources import CoverTemplate
        from sqlalchemy.orm import sessionmaker
        from sqlalchemy import create_engine
        print("✅ 数据库模块正常")
    except ImportError as e:
        print(f"❌ 数据库模块导入失败: {e}")
        return False
    
    return True

def setup_database():
    """设置数据库连接"""
    try:
        from sqlalchemy.orm import sessionmaker
        from sqlalchemy import create_engine
        
        DATABASE_URL = "sqlite:///./reddit_story_generator.db"
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        return SessionLocal()
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def create_simple_test_account():
    """创建简单测试账号"""
    class SimpleTestAccount:
        def __init__(self):
            self.id = 999
            self.name = "快速验证账号"
            self.platform = "reddit"
            # 不使用真实的头像文件，让服务使用默认头像
            self.avatar_file_path = None
    
    return SimpleTestAccount()

async def quick_screenshot_test():
    """快速截图测试"""
    print("\n🚀 开始快速截图测试...")
    
    try:
        from src.services.cover_screenshot_service import cover_screenshot_service
        from src.models.resources import CoverTemplate
        
        # 设置数据库
        db = setup_database()
        if not db:
            return False
        
        # 查找第一个可用模板
        try:
            template = db.query(CoverTemplate).first()
            if not template:
                print("❌ 没有找到封面模板")
                print("   请先导入模板或运行完整的测试脚本")
                return False
            
            print(f"📋 使用模板: {template.name} (ID: {template.id})")
        except Exception as e:
            print(f"❌ 查询模板失败: {e}")
            return False
        
        # 创建测试账号
        account = create_simple_test_account()
        print(f"👤 使用账号: {account.name}")
        
        # 设置输出路径
        output_dir = backend_dir / "quick_test_outputs"
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / f"quick_test_{int(time.time())}.png"
        
        # 执行截图
        test_title = "快速验证测试标题"
        print(f"📝 测试标题: {test_title}")
        print(f"📁 输出路径: {output_path}")
        
        print("\n⏱️ 开始生成截图...")
        start_time = time.time()
        
        success = await cover_screenshot_service.generate_cover_screenshot(
            template_id=str(template.id),
            account=account,
            title=test_title,
            output_path=str(output_path),
            db=db
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 检查结果
        if success and output_path.exists():
            file_size = output_path.stat().st_size
            print(f"✅ 截图生成成功!")
            print(f"   文件路径: {output_path}")
            print(f"   文件大小: {file_size} bytes")
            print(f"   耗时: {duration:.2f} 秒")
            
            if file_size < 1000:
                print("⚠️ 警告: 文件大小异常小，可能生成失败")
                return False
            
            return True
        else:
            print(f"❌ 截图生成失败 (耗时: {duration:.2f} 秒)")
            return False
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db' in locals() and db:
            try:
                db.close()
            except:
                pass

def create_minimal_html_test():
    """创建最小HTML测试"""
    print("\n🧪 创建最小HTML测试...")
    
    # 创建一个最简单的HTML用于测试
    simple_html = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最小测试</title>
    <style>
        #reddit-cover {
            width: 400px;
            height: 600px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        .info {
            font-size: 16px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="reddit-cover">
        <div class="title">快速验证测试</div>
        <div class="info">截图服务正常工作</div>
        <div class="info">测试时间: TIMESTAMP</div>
    </div>
</body>
</html>
    '''.replace('TIMESTAMP', time.strftime('%Y-%m-%d %H:%M:%S'))
    
    # 保存HTML文件
    output_dir = backend_dir / "quick_test_outputs"
    output_dir.mkdir(exist_ok=True)
    html_file = output_dir / "minimal_test.html"
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(simple_html)
    
    print(f"📄 最小HTML测试文件: {html_file}")
    
    # 尝试直接使用Playwright截图
    try:
        from playwright.sync_api import sync_playwright
        
        output_path = output_dir / f"minimal_test_{int(time.time())}.png"
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.set_viewport_size({"width": 1080, "height": 1920})
            
            # 使用file:// URL加载本地HTML
            page.goto(f"file://{html_file.absolute()}")
            page.wait_for_timeout(2000)
            
            # 截图reddit-cover元素
            cover_element = page.query_selector("#reddit-cover")
            if cover_element:
                cover_element.screenshot(path=str(output_path), type='png')
                browser.close()
                
                if output_path.exists():
                    file_size = output_path.stat().st_size
                    print(f"✅ 最小HTML测试成功!")
                    print(f"   文件路径: {output_path}")
                    print(f"   文件大小: {file_size} bytes")
                    return True
                else:
                    print("❌ 最小HTML测试失败: 文件未生成")
                    return False
            else:
                print("❌ 最小HTML测试失败: 找不到目标元素")
                browser.close()
                return False
    
    except Exception as e:
        print(f"❌ 最小HTML测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("⚡ 快速截图服务验证工具")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的依赖")
        return
    
    # 检查数据库
    db = setup_database()
    if not db:
        print("\n❌ 数据库连接失败")
        return
    
    try:
        from src.models.resources import CoverTemplate
        template_count = db.query(CoverTemplate).count()
        print(f"✅ 数据库连接正常，找到 {template_count} 个模板")
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return
    finally:
        db.close()
    
    print("\n选择验证模式:")
    print("1. 快速截图测试 (使用真实模板)")
    print("2. 最小HTML测试 (使用简单HTML)")
    print("3. 同时运行两个测试")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        success = await quick_screenshot_test()
        if success:
            print("\n🎉 快速截图测试通过！截图服务工作正常。")
        else:
            print("\n💥 快速截图测试失败！请检查配置或运行详细诊断。")
    
    elif choice == "2":
        success = create_minimal_html_test()
        if success:
            print("\n🎉 最小HTML测试通过！Playwright基础功能正常。")
        else:
            print("\n💥 最小HTML测试失败！请检查Playwright安装。")
    
    elif choice == "3":
        print("\n🔄 运行最小HTML测试...")
        html_success = create_minimal_html_test()
        
        print("\n🔄 运行快速截图测试...")
        screenshot_success = await quick_screenshot_test()
        
        print("\n" + "="*40)
        print("📊 综合测试结果:")
        print(f"  最小HTML测试: {'✅ 通过' if html_success else '❌ 失败'}")
        print(f"  快速截图测试: {'✅ 通过' if screenshot_success else '❌ 失败'}")
        
        if html_success and screenshot_success:
            print("\n🎉 所有测试通过！截图服务完全正常。")
        elif html_success:
            print("\n⚠️ Playwright基础功能正常，但模板截图失败。")
            print("   建议检查模板配置或运行详细诊断。")
        else:
            print("\n💥 基础功能失败！请检查Playwright安装和配置。")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    asyncio.run(main())
