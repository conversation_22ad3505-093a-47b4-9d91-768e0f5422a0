"""
LLM服务
"""

import asyncio
import aiohttp
import json
from typing import Optional
from sqlalchemy.orm import Session, sessionmaker
from loguru import logger

from ..services.settings_service import get_current_llm_config
from ..schemas.settings import LLMConfig


class LLMService:
    """LLM服务"""
    
    def __init__(self, session_maker: sessionmaker):
        self.session_maker = session_maker
    
    async def generate_text(self, prompt: str, variables: dict = None) -> str:
        """生成文本"""
        db = self.session_maker()
        try:
            # 获取LLM配置
            llm_config = get_current_llm_config(db)
            if not llm_config:
                raise ValueError("LLM配置未找到，请先在系统设置中配置LLM服务")
            
            if not llm_config.apiKey:
                raise ValueError("LLM API密钥未配置")
            
            # 替换变量
            if variables:
                for key, value in variables.items():
                    prompt = prompt.replace(f"{{{key}}}", str(value))
            
            # 根据提供商调用不同的API
            if llm_config.provider in ["openai", "azure", "yunwu", "local"]:
                return await self._call_openai_compatible_api(llm_config, prompt)
            elif llm_config.provider == "claude":
                return await self._call_claude_api(llm_config, prompt)
            else:
                raise ValueError(f"不��持的LLM提供商: {llm_config.provider}")
        finally:
            db.close()
    
    async def _call_openai_compatible_api(self, config: LLMConfig, prompt: str) -> str:
        """调用OpenAI兼容的API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.apiKey}"
        }
        
        # 构建消息
        messages = []
        if config.systemPrompt:
            messages.append({"role": "system", "content": config.systemPrompt})
        messages.append({"role": "user", "content": prompt})
        
        data = {
            "model": config.model,
            "messages": messages,
            "temperature": config.temperature,
            "max_tokens": config.maxTokens
        }
        
        # 确定API端点，并智能处理路径拼接
        base_url = config.endpoint.rstrip('/') if config.endpoint else ""
        api_path = "/v1/chat/completions"

        if config.provider == "azure":
            url = f"{base_url}/openai/deployments/{config.model}/chat/completions?api-version=2023-05-15"
            headers["api-key"] = config.apiKey
            del headers["Authorization"]
        elif base_url.endswith(api_path):
            # 如果端点已经包含了正确的路径，则直接使用
            url = base_url
        else:
            # 否则，安全地拼接
            url = f"{base_url}{api_path}"
        
        logger.debug(f"向LLM API ({url}) 发送请求, data: {json.dumps(data, indent=2)}")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                logger.debug(f"LLM API 响应状态: {response.status}")
                response_text = await response.text()
                logger.debug(f"LLM API 响应内容: {response_text}")

                if response.status != 200:
                    raise RuntimeError(f"LLM API调用失败: {response.status} - {response_text}")
                
                result = json.loads(response_text)
                
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    raise RuntimeError("LLM返回格式异常")
    
    async def _call_claude_api(self, config: LLMConfig, prompt: str) -> str:
        """调用Claude API"""
        headers = {
            "Content-Type": "application/json",
            "x-api-key": config.apiKey,
            "anthropic-version": "2023-06-01"
        }
        
        data = {
            "model": config.model,
            "max_tokens": config.maxTokens,
            "temperature": config.temperature,
            "messages": [
                {"role": "user", "content": prompt}
            ]
        }
        
        if config.systemPrompt:
            data["system"] = config.systemPrompt
        
        async with aiohttp.ClientSession() as session:
            # 规范化endpoint
            base_url = config.endpoint.rstrip('/') if config.endpoint else ""
            if base_url.endswith('/v1/messages'):
                base_url = base_url.replace('/v1/messages', '')
            elif base_url.endswith('/v1/chat/completions'): # 兼容OpenAI的路径，以防误设
                base_url = base_url.replace('/v1/chat/completions', '')

            async with session.post(f"{base_url}/v1/messages", headers=headers, json=data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise RuntimeError(f"Claude API调用失败: {response.status} - {error_text}")
                
                result = await response.json()
                
                if 'content' in result and len(result['content']) > 0:
                    return result['content'][0]['text']
                else:
                    raise RuntimeError("Claude返回格式异常")
