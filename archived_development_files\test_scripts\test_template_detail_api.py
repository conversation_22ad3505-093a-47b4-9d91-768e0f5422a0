#!/usr/bin/env python3
"""
测试模板详情API和数据加载
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/cover-templates"

def test_template_detail_api():
    print("=== 测试模板详情API和数据加载 ===\n")
    
    # 1. 先获取模板列表，找到一个现有的模板ID
    print("1. 获取模板列表...")
    try:
        list_response = requests.get(BASE_URL)
        if list_response.status_code == 200:
            list_data = list_response.json()
            print(f"模板列表响应: {json.dumps(list_data, indent=2, ensure_ascii=False)}")
            
            # 提取模板ID
            template_id = None
            if list_data.get('data', {}).get('templates'):
                templates = list_data['data']['templates']
                if templates:
                    template_id = templates[0]['id']
                    print(f"找到模板ID: {template_id}")
                else:
                    print("模板列表为空")
                    return
            else:
                print("无法从响应中提取模板列表")
                return
        else:
            print(f"获取模板列表失败: {list_response.status_code}")
            return
    except Exception as e:
        print(f"获取模板列表异常: {e}")
        return
    
    print("\n" + "="*50 + "\n")
    
    # 2. 获取单个模板详情
    print(f"2. 获取模板详情 (ID: {template_id})...")
    try:
        detail_response = requests.get(f"{BASE_URL}/{template_id}")
        print(f"详情API响应状态: {detail_response.status_code}")
        
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            print(f"详情API响应: {json.dumps(detail_data, indent=2, ensure_ascii=False)}")
            
            # 分析响应数据结构
            print("\n=== 数据结构分析 ===")
            print(f"响应类型: {type(detail_data)}")
            if isinstance(detail_data, dict):
                print(f"顶级字段: {list(detail_data.keys())}")
                
                # 检查data字段
                if 'data' in detail_data:
                    template_data = detail_data['data']
                    print(f"模板数据类型: {type(template_data)}")
                    print(f"模板数据字段: {list(template_data.keys()) if isinstance(template_data, dict) else 'N/A'}")
                    
                    # 检查关键字段
                    if isinstance(template_data, dict):
                        elements = template_data.get('elements', [])
                        background = template_data.get('background', {})
                        
                        print(f"\nElements字段:")
                        print(f"  类型: {type(elements)}")
                        print(f"  长度: {len(elements) if isinstance(elements, list) else 'N/A'}")
                        if isinstance(elements, list) and elements:
                            print(f"  第一个元素: {json.dumps(elements[0], indent=4, ensure_ascii=False)}")
                        
                        print(f"\nBackground字段:")
                        print(f"  类型: {type(background)}")
                        print(f"  内容: {json.dumps(background, indent=4, ensure_ascii=False)}")
                        
                        # 模拟前端数据提取
                        print(f"\n=== 模拟前端数据提取 ===")
                        extracted_template = detail_data['data'] if detail_data.get('data') else detail_data
                        extracted_elements = extracted_template.get('elements', [])
                        extracted_background = extracted_template.get('background', {})
                        
                        print(f"提取的elements: {extracted_elements}")
                        print(f"提取的background: {extracted_background}")
                        print(f"前端应该设置 {len(extracted_elements)} 个元素到画布上")
                        
        else:
            print(f"获取模板详情失败: {detail_response.status_code}")
            error_text = detail_response.text
            print(f"错误内容: {error_text}")
            
    except Exception as e:
        print(f"获取模板详情异常: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 3. 如果没有元素，创建一个包含元素的测试模板
    if template_id:
        print("3. 创建包含元素的测试模板...")
        test_template = {
            "name": "元素测试模板",
            "category": "测试",
            "description": "包含多种元素的测试模板",
            "variables": [],
            "elements": [
                {
                    "id": "test-text-1",
                    "type": "text",
                    "x": 50,
                    "y": 50,
                    "width": 200,
                    "height": 40,
                    "content": "标题文本",
                    "fontSize": 24,
                    "color": "#ffffff",
                    "textAlign": "center"
                },
                {
                    "id": "test-shape-1",
                    "type": "shape",
                    "x": 100,
                    "y": 120,
                    "width": 80,
                    "height": 80,
                    "shapeType": "circle",
                    "backgroundColor": "#ff6b6b",
                    "borderWidth": 2,
                    "borderColor": "#ffffff"
                },
                {
                    "id": "test-text-2",
                    "type": "text",
                    "x": 200,
                    "y": 220,
                    "width": 150,
                    "height": 30,
                    "content": "描述文本",
                    "fontSize": 16,
                    "color": "#cccccc",
                    "textAlign": "left"
                }
            ],
            "background": {
                "type": "gradient",
                "value": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
            },
            "is_built_in": False,
            "width": 1920,
            "height": 1080,
            "format": "png"
        }
        
        try:
            create_response = requests.post(BASE_URL, json=test_template)
            if create_response.status_code == 200:
                create_data = create_response.json()
                new_template_id = create_data.get('data', {}).get('id')
                print(f"创建成功，新模板ID: {new_template_id}")
                
                # 立即获取这个新模板来验证
                if new_template_id:
                    print(f"\n4. 验证新创建的模板 (ID: {new_template_id})...")
                    verify_response = requests.get(f"{BASE_URL}/{new_template_id}")
                    if verify_response.status_code == 200:
                        verify_data = verify_response.json()
                        template_data = verify_data.get('data', {})
                        elements = template_data.get('elements', [])
                        print(f"验证成功！新模板包含 {len(elements)} 个元素")
                        print(f"元素详情: {json.dumps(elements, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"验证失败: {verify_response.status_code}")
            else:
                print(f"创建测试模板失败: {create_response.status_code}")
        except Exception as e:
            print(f"创建测试模板异常: {e}")

if __name__ == "__main__":
    test_template_detail_api()
