#!/usr/bin/env python3
"""
简单的音频时长检查脚本
"""

import ffmpeg
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_audio_files():
    """检查音频文件"""
    audio_dir = Path("backend/uploads/audio")
    if not audio_dir.exists():
        logger.error(f"音频目录不存在: {audio_dir}")
        return
    
    logger.info("=== 检查音频文件 ===")
    for audio_file in audio_dir.glob("*.wav"):
        try:
            probe = ffmpeg.probe(str(audio_file))
            format_info = probe.get('format', {})
            duration = float(format_info.get('duration', 0))
            logger.info(f"音频文件: {audio_file.name}, 时长: {duration:.2f}s")
        except Exception as e:
            logger.error(f"检查音频文件失败 {audio_file.name}: {e}")

def check_video_materials():
    """检查视频素材"""
    materials_dir = Path("backend/uploads/video_materials")
    if not materials_dir.exists():
        logger.error(f"视频素材目录不存在: {materials_dir}")
        return
    
    logger.info("=== 检查视频素材 ===")
    materials = []
    total_duration = 0
    
    for video_file in materials_dir.glob("*.mp4"):
        if "thumb_" not in video_file.name:
            try:
                probe = ffmpeg.probe(str(video_file))
                video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
                if video_stream:
                    duration = float(video_stream.get('duration', 0))
                    materials.append({
                        'file': video_file.name,
                        'duration': duration
                    })
                    total_duration += duration
                    logger.info(f"视频素材: {video_file.name}, 时长: {duration:.2f}s")
            except Exception as e:
                logger.error(f"检查视频文件失败 {video_file.name}: {e}")
    
    logger.info(f"总共 {len(materials)} 个素材，总时长: {total_duration:.2f}s")
    
    # 模拟31秒音频的素材选择
    target_duration = 31.0
    logger.info(f"\n=== 模拟选择素材以匹配 {target_duration}s 音频 ===")
    
    selected_duration = 0
    selected_count = 0
    
    # 按时长从长到短排序
    sorted_materials = sorted(materials, key=lambda x: x['duration'], reverse=True)
    
    for material in sorted_materials:
        selected_duration += material['duration']
        selected_count += 1
        logger.info(f"选择: {material['file']}, 累计时长: {selected_duration:.2f}s")
        
        if selected_duration >= target_duration:
            logger.info(f"✅ 已选择足够素材，共 {selected_count} 个")
            break
    
    if selected_duration < target_duration:
        shortage = target_duration - selected_duration
        logger.warning(f"⚠️ 素材不足，缺少: {shortage:.2f}s")
        logger.warning("这就是导致23秒后黑屏的原因！")
        
        # 计算转场影响
        if selected_count > 1:
            transition_count = selected_count - 1
            transition_overlap = transition_count * 0.5  # 假设每个转场0.5秒
            actual_video_duration = selected_duration - transition_overlap
            logger.info(f"考虑转场效果后，实际视频时长: {actual_video_duration:.2f}s")
            
            if actual_video_duration < target_duration:
                final_shortage = target_duration - actual_video_duration
                logger.warning(f"⚠️ 转场后仍不足: {final_shortage:.2f}s")

def main():
    logger.info("🔍 开始简单音频时长检查...")
    
    check_audio_files()
    check_video_materials()
    
    logger.info("\n=== 结论 ===")
    logger.info("如果视频素材总时长不足以覆盖音频时长，")
    logger.info("就会出现23秒后黑屏的问题。")
    logger.info("解决方案：")
    logger.info("1. 添加更多视频素材")
    logger.info("2. 重复使用现有素材")
    logger.info("3. 修改素材选择算法")

if __name__ == "__main__":
    main()
