#!/usr/bin/env python3
"""
快速多段转场测试
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def quick_test():
    """快速测试多段转场"""
    
    logger.info("🎬 快速测试多段转场...")
    
    try:
        # 创建3个简单的测试视频
        colors = [
            ('red', '#FF0000', 2.0),
            ('green', '#00FF00', 2.0), 
            ('blue', '#0000FF', 2.0)
        ]
        
        test_videos = []
        durations = []
        
        for name, color, duration in colors:
            output_path = f"quick_{name}.mp4"
            logger.info(f"创建{duration}s {name}视频")
            
            (
                ffmpeg
                .input(f'color={color}:size=320x240:duration={duration}:rate=30', f='lavfi')
                .output(output_path, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
            
            test_videos.append(output_path)
            durations.append(duration)
        
        logger.info(f"视频文件: {test_videos}")
        logger.info(f"时长: {durations}")
        
        # 创建视频流
        streams = []
        for video_path in test_videos:
            stream = ffmpeg.input(video_path)
            streams.append(stream)
        
        logger.info("应用转场效果...")
        
        # 使用新的转场方法
        final_stream = VideoCompositionService._create_video_with_transitions(
            streams, 'fade', 0.5, durations
        )
        
        # 输出视频
        out = ffmpeg.output(
            final_stream,
            'quick_multi_test.mp4',
            vcodec='libx264',
            preset='ultrafast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        # 获取并打印命令
        cmd = ffmpeg.compile(out)
        logger.info("生成的FFmpeg命令:")
        logger.info(' '.join(cmd))
        
        # 执行命令
        logger.info("执行FFmpeg命令...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            if Path('quick_multi_test.mp4').exists():
                file_size = Path('quick_multi_test.mp4').stat().st_size
                logger.info("✅ 快速多段转场测试成功!")
                logger.info(f"文件大小: {file_size} bytes")
                logger.info("预期时长: 5.0s (2+2+2-2*0.5)")
                logger.info("预期效果: 红色(2s) → 绿色(2s) → 蓝色(2s) 用0.5s转场")
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ FFmpeg执行失败，返回码: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 清理测试文件
        for name in ['red', 'green', 'blue']:
            file_path = f"quick_{name}.mp4"
            if Path(file_path).exists():
                Path(file_path).unlink()

if __name__ == "__main__":
    logger.info("🚀 开始快速多段转场测试")
    
    success = quick_test()
    
    if success:
        logger.info("\n🎉 快速测试成功!")
        logger.info("多段转场基本功能正常。")
    else:
        logger.error("\n❌ 快速测试失败")
        sys.exit(1)
