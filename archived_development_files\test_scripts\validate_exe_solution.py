"""
验证静态前端 + 动态后端的 EXE 打包方案
测试 API 调用是否正确配置
"""

import asyncio
import aiohttp
import subprocess
import time
import sys
from pathlib import Path

class StaticFrontendValidator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.frontend_dist = self.project_root / "backend" / "frontend_dist"
        self.backend_process = None
        
    async def validate_solution(self):
        """验证完整解决方案"""
        print("🧪 验证静态前端 + 动态后端方案...")
        print("=" * 50)
        
        try:
            # 1. 检查前端构建产物
            self._check_frontend_build()
            
            # 2. 启动后端服务
            await self._start_backend()
            
            # 3. 模拟静态前端访问
            await self._test_static_frontend_api_calls()
            
            # 4. 测试 API 健康检查
            await self._test_api_health()
            
            print("=" * 50)
            print("✅ 验证完成！静态前端方案可行")
            
        except Exception as e:
            print("=" * 50) 
            print(f"❌ 验证失败: {e}")
            raise
        finally:
            self._cleanup()
    
    def _check_frontend_build(self):
        """检查前端构建产物"""
        print("📁 检查前端构建产物...")
        
        if not self.frontend_dist.exists():
            raise RuntimeError("前端构建目录不存在，请先运行前端构建")
        
        # 检查关键文件
        required_files = [
            "index.html",
            "_next/static",
            "api-config.json"  # 我们添加的 API 配置文件
        ]
        
        for file_path in required_files:
            full_path = self.frontend_dist / file_path
            if not full_path.exists():
                print(f"⚠️  缺少文件: {file_path}")
            else:
                print(f"✅ 找到文件: {file_path}")
        
        print("✅ 前端构建产物检查完成")
    
    async def _start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        backend_dir = self.project_root / "backend"
        
        # 启动后端（非阻塞）
        self.backend_process = subprocess.Popen([
            sys.executable, "main.py"
        ], cwd=backend_dir, 
           stdout=subprocess.PIPE, 
           stderr=subprocess.PIPE)
        
        # 等待后端启动
        print("⏳ 等待后端启动...")
        for i in range(30):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get("http://localhost:8000/api/health") as response:
                        if response.status == 200:
                            print("✅ 后端服务启动成功")
                            return
            except:
                pass
            
            await asyncio.sleep(1)
            print(f"   等待中... ({i+1}/30)")
        
        raise RuntimeError("后端启动超时")
    
    async def _test_static_frontend_api_calls(self):
        """测试静态前端的 API 调用"""
        print("🌐 测试前端 API 调用...")
        
        # 模拟前端会发起的 API 请求
        test_endpoints = [
            "/api/health",
            "/api/settings", 
            "/api/resources/validate",
            # 添加更多关键端点
        ]
        
        async with aiohttp.ClientSession() as session:
            for endpoint in test_endpoints:
                url = f"http://localhost:8000{endpoint}"
                
                try:
                    async with session.get(url) as response:
                        status = response.status
                        if status in [200, 401, 422]:  # 这些都是正常响应
                            print(f"✅ {endpoint} - HTTP {status}")
                        else:
                            print(f"⚠️  {endpoint} - HTTP {status}")
                except Exception as e:
                    print(f"❌ {endpoint} - 错误: {e}")
    
    async def _test_api_health(self):
        """测试 API 健康检查"""
        print("🏥 测试 API 健康检查...")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get("http://localhost:8000/api/health") as response:
                    health_data = await response.json()
                    print(f"✅ 健康检查响应: {health_data}")
                    
                    # 检查是否包含必要信息
                    if "status" in health_data:
                        print("✅ 健康检查格式正确")
                    else:
                        print("⚠️  健康检查响应格式可能需要改进")
                        
            except Exception as e:
                print(f"❌ 健康检查失败: {e}")
    
    def _cleanup(self):
        """清理资源"""
        if self.backend_process:
            print("🛑 停止后端服务...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
    
    def generate_deployment_report(self):
        """生成部署报告"""
        print("\n" + "=" * 60)
        print("📋 EXE 打包部署方案总结")
        print("=" * 60)
        
        report = """
🏗️  架构设计:
   ├── 前端: Next.js 静态导出 (Static HTML/JS/CSS)
   ├── 后端: FastAPI (内嵌在 EXE 中)
   ├── 数据库: SQLite (内嵌在 EXE 中)
   └── 通信: HTTP API (localhost:8000)

🔧 关键解决方案:
   1. 前端 API 地址硬编码为 localhost:8000
   2. 添加运行时 API 健康检查机制
   3. 前端使用静态导出，无需 Next.js 服务器
   4. 后端作为 HTTP 服务器内嵌在 EXE 中

✅ 优势:
   ✓ 单文件部署 (RedditStoryVideoGenerator.exe)
   ✓ 无需安装依赖环境
   ✓ 前端响应速度快 (静态文件)
   ✓ 后端功能完整保留
   ✓ 代码保护 (编译为字节码/加密)

⚠️  注意事项:
   • 需要确保端口 8000 未被占用
   • 首次启动可能需要较长时间
   • 杀毒软件可能误报，需要添加白名单
   • 静态前端无法使用 Next.js 的 SSR 功能

🚀 部署流程:
   1. 运行 build.bat 进行完整构建
   2. 生成 RedditStoryVideoGenerator.exe
   3. 用户直接运行 EXE 文件
   4. 自动启动后端服务
   5. 浏览器访问 http://localhost:8000

💡 这个方案完美解决了静态前端调用动态后端的问题！
"""
        
        print(report)

async def main():
    validator = StaticFrontendValidator("d:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator")
    
    try:
        await validator.validate_solution()
        validator.generate_deployment_report()
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        print("\n💡 建议:")
        print("   1. 确保已运行前端构建: npm run build:exe")
        print("   2. 确保后端依赖已安装: pip install -r requirements.txt")
        print("   3. 确保端口 8000 未被占用")

if __name__ == "__main__":
    asyncio.run(main())
