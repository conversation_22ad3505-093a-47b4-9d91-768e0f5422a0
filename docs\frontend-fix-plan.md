# 前端视频生成功能修复实施方案

## 当前状况评估

### ✅ 已确认可用的后端API
- `/api/accounts` - 账号管理
- `/api/video-materials` - 视频素材管理  
- `/api/background-music` - 背景音乐管理
- `/api/prompts` - 提示词管理
- `/api/cover-templates` - 封面模板管理
- `/api/settings` - 系统设置（包含音色配置）
- `/api/video-generation` - 新的视频生成API

### ❌ 需要修复的前端问题
1. **配置页面使用模拟数据**：`/frontend/src/app/generate/page.tsx`
2. **API调用路径错误**：前端调用`/api/generation/*`，实际应为`/api/video-generation/*`
3. **任务管理页面功能不完整**：`/frontend/src/app/tasks/page.tsx`
4. **状态管理需要更新**：`/frontend/src/store/generationStore.ts`

## 修复计划

### 第一阶段：API集成（优先级：高）

#### 1.1 更新前端API服务
创建新的API服务文件来对接实际的后端API：

```typescript
// /frontend/src/services/api.ts
// 统一的API调用服务，替换掉store中的硬编码API调用
```

#### 1.2 修复配置页面数据源
修改 `/frontend/src/app/generate/page.tsx`：
- 移除所有模拟数据
- 从实际API获取：账号列表、素材分组、提示词分组、音乐分组、封面模板
- 实现级联数据加载（先加载分组，再加载分组内的具体项目）

#### 1.3 更新API调用路径
修改所有API调用从 `/api/generation/*` 改为 `/api/video-generation/*`

### 第二阶段：业务逻辑修复（优先级：高）

#### 2.1 重构表单提交逻辑
当前前端是按单任务模式设计，需要改为批量作业模式：
- 提交时创建 `VideoGenerationJob`
- 支持多账号配置
- 每个账号可设置不同的视频数量

#### 2.2 实现任务监控
修改 `/frontend/src/app/tasks/page.tsx`：
- 显示作业列表（Job Level）
- 显示每个作业下的任务列表（Task Level）
- 实现任务控制（开始、暂停、取消、重试）
- 实时进度更新

### 第三阶段：功能完善（优先级：中）

#### 3.1 文件管理
- 实现视频预览
- 添加下载功能
- 结果文件管理

#### 3.2 用户体验优化
- 进度指示器
- 错误提示优化
- 加载状态管理

## 详细实施步骤

### Step 1: 创建API服务层
创建 `/frontend/src/services/apiService.ts` 统一管理所有API调用

### Step 2: 更新配置页面
1. 替换模拟数据为真实API调用
2. 实现数据级联加载
3. 更新表单验证逻辑
4. 修改提交逻辑适配新的作业模式

### Step 3: 修复任务管理页面
1. 重新设计状态管理结构
2. 实现作业和任务的层级显示
3. 添加控制按钮功能
4. 实现实时状态更新

### Step 4: 端到端测试
1. 配置页面功能测试
2. 任务执行流程测试  
3. 文件生成和下载测试

## 风险控制

### 高风险项及应对措施
1. **API数据结构不匹配**
   - 应对：先调用API查看实际数据结构，确保前端适配

2. **跨域和权限问题**
   - 应对：确认API权限配置，检查CORS设置

3. **文件路径和存储问题**
   - 应对：确认后端文件存储配置，测试文件上传下载

### 测试验证点
- [ ] 能够获取所有资源数据（账号、素材、音乐等）
- [ ] 能够成功提交视频生成作业
- [ ] 能够查看作业和任务状态
- [ ] 能够控制任务执行（暂停、取消等）
- [ ] 能够下载生成的视频文件

## 预期时间安排
- Step 1: API服务层 - 0.5天
- Step 2: 配置页面修复 - 1天
- Step 3: 任务管理页面 - 1天  
- Step 4: 测试和优化 - 0.5天
- **总计**: 3天

---

**下一步行动**: 开始Step 1，创建API服务层并验证后端API可用性
