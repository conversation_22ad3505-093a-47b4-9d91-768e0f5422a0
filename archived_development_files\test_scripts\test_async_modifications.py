#!/usr/bin/env python3
"""
测试异步修改是否生效
"""
import sys
import asyncio
from pathlib import Path

# 添加backend路径
backend_dir = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_dir))

async def test_async_modifications():
    """测试异步修改是否生效"""
    
    print("🔍 测试视频生成异步修改效果")
    print("=" * 60)
    
    try:
        # 测试1: FFmpeg异步函数导入
        print("\n1. 测试FFmpeg异步函数...")
        from src.services.video_generation_helpers import run_ffmpeg_async
        print("✅ FFmpeg异步函数导入成功")
        
        # 测试2: VideoCompositionService异步方法
        print("\n2. 测试VideoCompositionService异步方法...")
        from src.services.video_generation_helpers import VideoCompositionService
        
        # 检查compose_video是否是协程函数
        import inspect
        is_async = inspect.iscoroutinefunction(VideoCompositionService.compose_video)
        if is_async:
            print("✅ VideoCompositionService.compose_video 已异步化")
        else:
            print("❌ VideoCompositionService.compose_video 仍是同步函数")
            
        # 检查_create_intermediate_video是否是协程函数
        is_async_intermediate = inspect.iscoroutinefunction(VideoCompositionService._create_intermediate_video)
        if is_async_intermediate:
            print("✅ VideoCompositionService._create_intermediate_video 已异步化")
        else:
            print("❌ VideoCompositionService._create_intermediate_video 仍是同步函数")
        
        # 测试3: Whisper异步方法
        print("\n3. 测试Whisper异步方法...")
        from src.services.video_generation_service import AudioAnalysisService
        
        is_async_whisper = inspect.iscoroutinefunction(AudioAnalysisService.analyze_audio)
        if is_async_whisper:
            print("✅ AudioAnalysisService.analyze_audio 已异步化")
        else:
            print("❌ AudioAnalysisService.analyze_audio 仍是同步函数")
        
        # 测试4: Playwright异步方法
        print("\n4. 测试Playwright异步方法...")
        from src.services.cover_screenshot_service import CoverScreenshotService
        
        is_async_screenshot = inspect.iscoroutinefunction(CoverScreenshotService.generate_cover_screenshot)
        if is_async_screenshot:
            print("✅ CoverScreenshotService.generate_cover_screenshot 已异步化")
        else:
            print("❌ CoverScreenshotService.generate_cover_screenshot 仍是同步函数")
        
        print("\n" + "=" * 60)
        print("🎯 异步修改总结:")
        print(f"FFmpeg视频合成: {'✅ 已异步化' if is_async and is_async_intermediate else '❌ 未完全异步化'}")
        print(f"Whisper音频分析: {'✅ 已异步化' if is_async_whisper else '❌ 未异步化'}")
        print(f"Playwright截图: {'✅ 已异步化' if is_async_screenshot else '❌ 未异步化'}")
        
        if is_async and is_async_intermediate and is_async_whisper and is_async_screenshot:
            print("\n🎉 所有关键阻塞点都已成功异步化！")
            print("✅ HTTP服务器现在可以在视频生成期间保持响应")
            print("✅ 前端API请求pending问题应该已解决")
        else:
            print("\n⚠️ 部分异步化未完成，需要进一步检查")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_async_modifications())
