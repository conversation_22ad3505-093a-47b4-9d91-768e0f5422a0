"""
测试封面截图功能的演示脚本
验证网页截图服务是否能正确生成封面图
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)  # 切换到backend目录
sys.path.insert(0, str(backend_path))

# 加载环境变量
load_dotenv(dotenv_path=backend_path / '.env')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate
from backend.src.services.cover_screenshot_service import cover_screenshot_service
from loguru import logger

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_cover_screenshot():
    """测试封面截图功能"""
    db = SessionLocal()
    try:
        print("=== 封面截图功能测试 ===")
        
        # 1. 获取第一个账号
        account = db.query(Account).first()
        if not account:
            print("❌ 数据库中没有找到账号")
            return
        
        print(f"✅ 找到账号: {account.name}")
        
        # 2. 获取第一个模板
        template = db.query(CoverTemplate).first()
        if not template:
            print("❌ 数据库中没有找到封面模板")
            return
        
        print(f"✅ 找到封面模板: {template.name}")
        print(f"   模板ID: {template.id}")
        print(f"   模板变量: {template.variables}")
        
        # 3. 准备输出路径
        output_dir = Path("test_outputs")
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / f"test_cover_{template.id}.png"
        
        # 4. 生成封面截图
        print("🔄 开始生成封面截图...")
        
        title = "这是一个测试标题，用来验证封面截图功能是否正常工作"
        
        success = await cover_screenshot_service.generate_cover_screenshot(
            template_id=template.id,
            account=account,
            title=title,
            output_path=str(output_path),
            db=db
        )
        
        if success:
            print(f"✅ 封面截图生成成功!")
            print(f"   输出文件: {output_path}")
            print(f"   文件大小: {output_path.stat().st_size} bytes")
            
            # 检查文件是否存在
            if output_path.exists():
                print("✅ 输出文件确实存在")
            else:
                print("❌ 输出文件不存在")
        else:
            print("❌ 封面截图生成失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        await cover_screenshot_service.cleanup()
        db.close()

async def test_template_rendering():
    """测试模板渲染功能"""
    db = SessionLocal()
    try:
        print("\n=== 模板渲染功能测试 ===")
        
        # 导入模板服务
        from backend.src.services.template_import_service import template_import_service
        
        # 获取第一个模板
        template = db.query(CoverTemplate).first()
        if not template:
            print("❌ 数据库中没有找到封面模板")
            return
        
        print(f"✅ 找到模板: {template.name}")
        
        # 准备测试变量
        variables = {
            'avatar': 'https://via.placeholder.com/50',
            'account_name': 'test_account',
            'title': '这是一个测试标题',
            'description': '这是一个测试描述'
        }
        
        # 渲染模板
        rendered_html = template_import_service.render_template(
            template_id=template.id,
            variables=variables,
            db=db
        )
        
        print("✅ 模板渲染成功")
        print(f"   渲染结果长度: {len(rendered_html)} 字符")
        
        # 保存渲染结果到文件
        output_html = Path("test_outputs") / f"rendered_{template.id}.html"
        with open(output_html, 'w', encoding='utf-8') as f:
            f.write(rendered_html)
        
        print(f"   渲染结果已保存到: {output_html}")
        
        # 检查是否包含reddit-cover元素
        if 'id="reddit-cover"' in rendered_html:
            print("✅ 发现 reddit-cover 元素")
        else:
            print("❌ 未发现 reddit-cover 元素")
        
    except Exception as e:
        print(f"❌ 模板渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

async def main():
    """主测试函数"""
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    
    try:
        # 测试模板渲染
        await test_template_rendering()
        
        # 测试封面截图
        await test_cover_screenshot()
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
