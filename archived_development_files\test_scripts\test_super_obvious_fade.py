#!/usr/bin/env python3
"""
超明显的fade测试 - 长时间、慢速度，绝对能看清楚
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_super_obvious_fade():
    """超明显的fade测试"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始超明显fade测试...")
    logger.info("设置：封面显示15秒，fade动画5秒，绝对能看清楚！")
    
    # 超明显的测试配置
    test_configs = [
        {
            'name': '超明显淡入-5秒动画',
            'animation': 'fade_in',
            'duration': 15.0,  # 封面显示15秒
            'animation_duration': 5.0,  # 淡入动画5秒
            'output': 'super_fade_in.mp4'
        },
        {
            'name': '超明显淡出-5秒动画', 
            'animation': 'fade_out',
            'duration': 15.0,  # 封面显示15秒
            'animation_duration': 5.0,  # 淡出动画5秒
            'output': 'super_fade_out.mp4'
        },
        {
            'name': '超明显淡入淡出-各5秒动画',
            'animation': 'fade_in_out',
            'duration': 20.0,  # 封面显示20秒
            'animation_duration': 5.0,  # 淡入淡出各5秒
            'output': 'super_fade_in_out.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数 - 更大的封面，更明显
        cover_width = int(width * 0.9)  # 90%宽度，更大更明显
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px (90%视频宽度), 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"封面显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"super_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    # 应用封面叠加效果
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频 - 生成更长的视频
                    total_duration = config['duration'] + 5  # 多5秒缓冲
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=total_duration
                    ).overwrite_output()
                    
                    logger.info(f"开始生成{total_duration}秒的测试视频...")
                    
                    # 显示FFmpeg命令
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
                    
                    # 执行
                    ffmpeg.run(out, quiet=True)
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功! 文件大小: {file_size} bytes")
                        success_count += 1
                        
                        # 详细说明预期效果
                        if config['animation'] == 'fade_in':
                            logger.info(f"   预期效果: 0-5秒封面从透明慢慢变不透明，5-15秒保持不透明")
                        elif config['animation'] == 'fade_out':
                            logger.info(f"   预期效果: 0-10秒封面保持不透明，10-15秒慢慢变透明")
                        elif config['animation'] == 'fade_in_out':
                            logger.info(f"   预期效果: 0-5秒淡入，5-15秒保持，15-20秒淡出")
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"\n=== 超明显fade测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count > 0:
            logger.info("\n📋 超明显测试文件:")
            logger.info("- super_fade_in.mp4 (15秒封面，5秒淡入动画)")
            logger.info("- super_fade_out.mp4 (15秒封面，5秒淡出动画)")
            logger.info("- super_fade_in_out.mp4 (20秒封面，各5秒淡入淡出)")
            
            logger.info("\n🎬 如何验证:")
            logger.info("1. super_fade_in.mp4: 前5秒应该看到封面慢慢出现")
            logger.info("2. super_fade_out.mp4: 10-15秒应该看到封面慢慢消失")
            logger.info("3. super_fade_in_out.mp4: 0-5秒淡入，15-20秒淡出")
            
            logger.info("\n⚠️ 如果这些超长时间的动画都看不到效果，")
            logger.info("那么问题确实在于fade滤镜本身或者FFmpeg配置！")
            
            return True
        else:
            logger.error("❌ 所有测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_direct_ffmpeg_super_fade():
    """直接用FFmpeg命令测试超明显fade"""
    
    logger.info("\n🔧 直接FFmpeg命令测试超明显fade...")
    
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    if not Path(video_path).exists() or not Path(cover_path).exists():
        logger.error("测试文件不存在")
        return False
    
    import subprocess
    
    # 测试1: 超明显淡入 - 10秒动画
    logger.info("测试1: 超明显淡入 - 10秒动画")
    cmd1 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-loop', '1', '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=800:-1,fade=in:0:300[faded];[0:v][faded]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2',
        '-t', '15',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'direct_super_fade_in.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd1)}")
    result1 = subprocess.run(cmd1, capture_output=True, text=True)
    
    if result1.returncode == 0:
        logger.info("✅ direct_super_fade_in.mp4 生成成功")
        logger.info("   预期: 前10秒封面慢慢从透明变不透明")
    else:
        logger.error(f"❌ 失败: {result1.stderr}")
    
    # 测试2: 超明显淡出 - 10秒动画
    logger.info("测试2: 超明显淡出 - 10秒动画")
    cmd2 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-loop', '1', '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=800:-1,fade=out:150:300[faded];[0:v][faded]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2',
        '-t', '15',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'direct_super_fade_out.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd2)}")
    result2 = subprocess.run(cmd2, capture_output=True, text=True)
    
    if result2.returncode == 0:
        logger.info("✅ direct_super_fade_out.mp4 生成成功")
        logger.info("   预期: 5-15秒封面慢慢从不透明变透明")
    else:
        logger.error(f"❌ 失败: {result2.stderr}")
    
    return True

if __name__ == "__main__":
    logger.info("🚀 开始超明显fade测试")
    logger.info("这次设置超长时间、超慢速度，绝对能看清楚有没有效果！")
    
    # 测试1: 使用我们的方法
    success1 = test_super_obvious_fade()
    
    # 测试2: 直接FFmpeg命令
    success2 = test_direct_ffmpeg_super_fade()
    
    logger.info("\n🎯 测试完成！")
    logger.info("如果这些超长时间的测试文件都没有fade效果，")
    logger.info("那么问题确实在于fade滤镜本身或者系统配置！")
    
    logger.info("\n📁 生成的测试文件:")
    logger.info("- super_fade_in.mp4 (我们的方法，5秒淡入)")
    logger.info("- super_fade_out.mp4 (我们的方法，5秒淡出)")
    logger.info("- super_fade_in_out.mp4 (我们的方法，各5秒)")
    logger.info("- direct_super_fade_in.mp4 (直接FFmpeg，10秒淡入)")
    logger.info("- direct_super_fade_out.mp4 (直接FFmpeg，10秒淡出)")
