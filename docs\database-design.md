# 数据库模型设计文档

## 🎯 概述

基于前端Zustand状态管理结构，设计对应的后端数据库模型，确保数据结构的一致性和完整性。

## 📊 表结构设计

### 1. 设置表 (settings)

```sql
CREATE TABLE settings (
    id VARCHAR(36) PRIMARY KEY COMMENT '设置ID',
    user_id VARCHAR(36) DEFAULT NULL COMMENT '用户ID（可选，支持多用户）',
    
    -- TTS配置
    tts_provider VARCHAR(50) NOT NULL DEFAULT 'openai' COMMENT 'TTS提供商',
    tts_api_key TEXT COMMENT 'TTS API密钥',
    tts_endpoint VARCHAR(500) COMMENT 'TTS API端点',
    tts_voice VARCHAR(100) NOT NULL DEFAULT 'alloy' COMMENT 'TTS语音',
    tts_model VARCHAR(100) COMMENT 'TTS模型',
    tts_speed DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT 'TTS语速',
    tts_pitch DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT 'TTS音调',
    tts_volume DECIMAL(3,2) NOT NULL DEFAULT 1.0 COMMENT 'TTS音量',
    
    -- LLM配置
    llm_provider VARCHAR(50) NOT NULL DEFAULT 'openai' COMMENT 'LLM提供商',
    llm_api_key TEXT COMMENT 'LLM API密钥',
    llm_endpoint VARCHAR(500) COMMENT 'LLM API端点',
    llm_model VARCHAR(100) NOT NULL DEFAULT 'gpt-3.5-turbo' COMMENT 'LLM模型',
    llm_temperature DECIMAL(3,2) NOT NULL DEFAULT 0.7 COMMENT 'LLM温度',
    llm_max_tokens INT NOT NULL DEFAULT 2000 COMMENT 'LLM最大Token数',
    llm_system_prompt TEXT COMMENT 'LLM系统提示词',
    
    -- 通用设置
    theme VARCHAR(20) NOT NULL DEFAULT 'light' COMMENT '主题',
    language VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言',
    auto_save BOOLEAN NOT NULL DEFAULT TRUE COMMENT '自动保存',
    show_tips BOOLEAN NOT NULL DEFAULT TRUE COMMENT '显示提示',
    output_directory VARCHAR(500) COMMENT '输出目录',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设置表';
```

### 2. 资源文件表 (resource_files)

```sql
CREATE TABLE resource_files (
    id VARCHAR(36) PRIMARY KEY COMMENT '文件ID',
    name VARCHAR(255) NOT NULL COMMENT '文件名称',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_url VARCHAR(500) COMMENT '文件访问URL',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(100) NOT NULL COMMENT '文件MIME类型',
    file_extension VARCHAR(20) NOT NULL COMMENT '文件扩展名',
    
    -- 媒体信息
    duration DECIMAL(10,2) COMMENT '时长（秒）',
    width INT COMMENT '宽度（像素）',
    height INT COMMENT '高度（像素）',
    bitrate VARCHAR(50) COMMENT '比特率',
    frame_rate DECIMAL(5,2) COMMENT '帧率',
    
    -- 缩略图
    thumbnail_path VARCHAR(500) COMMENT '缩略图路径',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    
    -- 元数据
    metadata JSON COMMENT '其他元数据',
    
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    
    INDEX idx_file_type (file_type),
    INDEX idx_uploaded_at (uploaded_at),
    INDEX idx_file_size (file_size)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源文件表';
```

### 3. 背景音乐表 (background_music)

```sql
CREATE TABLE background_music (
    id VARCHAR(36) PRIMARY KEY COMMENT '音乐ID',
    name VARCHAR(255) NOT NULL COMMENT '音乐名称',
    file_id VARCHAR(36) NOT NULL COMMENT '关联文件ID',
    category VARCHAR(100) NOT NULL DEFAULT '其他' COMMENT '音乐分类',
    tags JSON COMMENT '标签列表',
    description TEXT COMMENT '描述',
    is_built_in BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否内置',
    
    -- 音频特性
    bpm INT COMMENT '节拍（每分钟节拍数）',
    key_signature VARCHAR(10) COMMENT '调性',
    mood VARCHAR(50) COMMENT '情绪标签',
    
    -- 统计信息
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (file_id) REFERENCES resource_files(id) ON DELETE CASCADE,
    INDEX idx_category (category),
    INDEX idx_is_built_in (is_built_in),
    INDEX idx_usage_count (usage_count),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='背景音乐表';
```

### 4. 视频素材表 (video_materials)

```sql
CREATE TABLE video_materials (
    id VARCHAR(36) PRIMARY KEY COMMENT '视频ID',
    name VARCHAR(255) NOT NULL COMMENT '视频名称',
    file_id VARCHAR(36) NOT NULL COMMENT '关联文件ID',
    type ENUM('background', 'overlay', 'transition', 'effect') NOT NULL DEFAULT 'background' COMMENT '视频类型',
    category VARCHAR(100) NOT NULL DEFAULT '其他' COMMENT '视频分类',
    tags JSON COMMENT '标签列表',
    description TEXT COMMENT '描述',
    is_built_in BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否内置',
    
    -- 视频特性
    resolution VARCHAR(20) COMMENT '分辨率',
    aspect_ratio VARCHAR(20) COMMENT '宽高比',
    color_profile VARCHAR(50) COMMENT '色彩配置',
    has_audio BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否包含音频',
    
    -- 使用场景
    suitable_for JSON COMMENT '适用场景列表',
    mood VARCHAR(50) COMMENT '情绪标签',
    
    -- 统计信息
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (file_id) REFERENCES resource_files(id) ON DELETE CASCADE,
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_is_built_in (is_built_in),
    INDEX idx_resolution (resolution),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频素材表';
```

### 5. 提示词表 (prompts)

```sql
CREATE TABLE prompts (
    id VARCHAR(36) PRIMARY KEY COMMENT '提示词ID',
    name VARCHAR(255) NOT NULL COMMENT '提示词名称',
    content TEXT NOT NULL COMMENT '提示词内容',
    category VARCHAR(100) NOT NULL DEFAULT '通用' COMMENT '提示词分类',
    description TEXT COMMENT '描述',
    variables JSON COMMENT '变量列表',
    tags JSON COMMENT '标签列表',
    is_built_in BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否内置',
    
    -- 使用统计
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    success_rate DECIMAL(5,2) COMMENT '成功率',
    
    -- 评估信息
    avg_response_time DECIMAL(8,2) COMMENT '平均响应时间（秒）',
    avg_output_length INT COMMENT '平均输出长度',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category (category),
    INDEX idx_is_built_in (is_built_in),
    INDEX idx_usage_count (usage_count),
    INDEX idx_success_rate (success_rate),
    FULLTEXT idx_content (content, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提示词表';
```

### 6. 账号表 (accounts)

```sql
CREATE TABLE accounts (
    id VARCHAR(36) PRIMARY KEY COMMENT '账号ID',
    name VARCHAR(255) NOT NULL COMMENT '账号名称',
    type ENUM('reddit', 'youtube', 'tiktok', 'twitter', 'instagram', 'other') NOT NULL COMMENT '账号类型',
    username VARCHAR(255) NOT NULL COMMENT '用户名',
    email VARCHAR(255) COMMENT '邮箱',
    password_encrypted TEXT COMMENT '加密密码',
    description TEXT COMMENT '描述',
    status ENUM('unused', 'used', 'disabled') NOT NULL DEFAULT 'unused' COMMENT '账号状态',
    tags JSON COMMENT '标签列表',
    
    -- 账号信息
    avatar_url VARCHAR(500) COMMENT '头像URL',
    profile_url VARCHAR(500) COMMENT '个人主页URL',
    verified BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否认证',
    
    -- 使用统计
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    
    -- 账号状态
    karma INT COMMENT 'Karma值（Reddit）',
    followers_count INT COMMENT '粉丝数',
    account_age_days INT COMMENT '账号年龄（天数）',
    
    -- 风险评估
    risk_level ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'low' COMMENT '风险等级',
    last_check_at TIMESTAMP NULL COMMENT '最后检查时间',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_username (username),
    INDEX idx_usage_count (usage_count),
    INDEX idx_risk_level (risk_level),
    UNIQUE KEY uk_type_username (type, username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账号表';
```

### 7. 封面模板表 (cover_templates)

```sql
CREATE TABLE cover_templates (
    id VARCHAR(36) PRIMARY KEY COMMENT '模板ID',
    name VARCHAR(255) NOT NULL COMMENT '模板名称',
    category VARCHAR(100) NOT NULL DEFAULT '通用' COMMENT '模板分类',
    description TEXT COMMENT '描述',
    tags JSON COMMENT '标签列表',
    is_built_in BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否内置',
    
    -- 模板文件
    template_path VARCHAR(500) COMMENT '模板文件路径',
    preview_path VARCHAR(500) COMMENT '预览图路径',
    preview_url VARCHAR(500) COMMENT '预览图URL',
    
    -- 模板配置
    variables JSON COMMENT '可配置变量',
    default_config JSON COMMENT '默认配置',
    canvas_size JSON COMMENT '画布尺寸配置',
    
    -- 模板结构
    elements JSON COMMENT '元素配置',
    layers JSON COMMENT '图层配置',
    animations JSON COMMENT '动画配置',
    
    -- 使用统计
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    rating DECIMAL(3,2) COMMENT '评分',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category (category),
    INDEX idx_is_built_in (is_built_in),
    INDEX idx_usage_count (usage_count),
    INDEX idx_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='封面模板表';
```

### 8. 生成任务表 (generation_tasks)

```sql
CREATE TABLE generation_tasks (
    id VARCHAR(36) PRIMARY KEY COMMENT '任务ID',
    
    -- 任务基本信息
    name VARCHAR(255) COMMENT '任务名称',
    type ENUM('single', 'batch') NOT NULL DEFAULT 'single' COMMENT '任务类型',
    priority ENUM('low', 'normal', 'high', 'urgent') NOT NULL DEFAULT 'normal' COMMENT '任务优先级',
    
    -- 任务状态
    status ENUM('pending', 'queued', 'processing', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '任务状态',
    progress INT NOT NULL DEFAULT 0 COMMENT '进度百分比',
    current_step VARCHAR(255) COMMENT '当前步骤',
    
    -- 时间信息
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    estimated_end_time TIMESTAMP NULL COMMENT '预计结束时间',
    total_duration INT COMMENT '总耗时（秒）',
    
    -- 配置信息
    config JSON NOT NULL COMMENT '生成配置',
    
    -- 结果信息
    output_path VARCHAR(500) COMMENT '输出文件路径',
    output_url VARCHAR(500) COMMENT '输出文件URL',
    output_size BIGINT COMMENT '输出文件大小',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    
    -- 错误信息
    error_message TEXT COMMENT '错误信息',
    error_details JSON COMMENT '错误详情',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    max_retries INT NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    
    -- 资源使用
    cpu_time DECIMAL(10,2) COMMENT 'CPU使用时间',
    memory_peak BIGINT COMMENT '内存峰值使用',
    gpu_time DECIMAL(10,2) COMMENT 'GPU使用时间',
    
    -- 批量任务信息
    parent_task_id VARCHAR(36) COMMENT '父任务ID',
    batch_index INT COMMENT '批量任务索引',
    batch_total INT COMMENT '批量任务总数',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_start_time (start_time),
    INDEX idx_parent_task_id (parent_task_id),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (parent_task_id) REFERENCES generation_tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生成任务表';
```

### 9. 任务日志表 (task_logs)

```sql
CREATE TABLE task_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    
    level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL DEFAULT 'info' COMMENT '日志级别',
    step VARCHAR(255) NOT NULL COMMENT '执行步骤',
    message TEXT NOT NULL COMMENT '日志消息',
    details JSON COMMENT '详细信息',
    
    -- 性能信息
    execution_time DECIMAL(8,4) COMMENT '执行时间（秒）',
    memory_usage BIGINT COMMENT '内存使用（字节）',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_level (level),
    INDEX idx_step (step),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (task_id) REFERENCES generation_tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务日志表';
```

### 10. 系统配置表 (system_config)

```sql
CREATE TABLE system_config (
    id VARCHAR(36) PRIMARY KEY COMMENT '配置ID',
    key_name VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键名',
    value TEXT COMMENT '配置值',
    value_type ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string' COMMENT '值类型',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_editable BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否可编辑',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category (category),
    INDEX idx_key_name (key_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

## 🔗 关系图

```
settings (用户设置)

resource_files (文件基础信息)
    ↑
    ├── background_music (背景音乐)
    ├── video_materials (视频素材)
    └── cover_templates (封面模板)

prompts (提示词)

accounts (账号)

generation_tasks (生成任务)
    ├── task_logs (任务日志)
    └── 自关联 (parent_task_id)

system_config (系统配置)
```

## 📝 索引策略

### 主要查询索引
1. **资源查询**：按类型、分类、内置状态
2. **任务查询**：按状态、优先级、时间
3. **使用统计**：按使用次数、最后使用时间
4. **全文搜索**：提示词内容、描述

### 复合索引
```sql
-- 资源文件复合索引
CREATE INDEX idx_resource_type_category ON background_music(category, is_built_in, created_at);
CREATE INDEX idx_video_type_category ON video_materials(type, category, is_built_in);

-- 任务状态复合索引
CREATE INDEX idx_task_status_priority ON generation_tasks(status, priority, created_at);
CREATE INDEX idx_task_batch ON generation_tasks(parent_task_id, batch_index);

-- 使用统计复合索引
CREATE INDEX idx_usage_stats ON background_music(usage_count DESC, last_used_at DESC);
```

## 🔄 数据迁移

### 初始数据种子

```sql
-- 插入默认系统配置
INSERT INTO system_config (id, key_name, value, value_type, category, description) VALUES
('cfg-001', 'max_file_size', '104857600', 'number', 'upload', '最大文件上传大小（字节）'),
('cfg-002', 'supported_audio_formats', '["mp3","wav","flac","aac"]', 'json', 'upload', '支持的音频格式'),
('cfg-003', 'supported_video_formats', '["mp4","avi","mov","mkv"]', 'json', 'upload', '支持的视频格式'),
('cfg-004', 'max_concurrent_tasks', '3', 'number', 'generation', '最大并发任务数'),
('cfg-005', 'temp_file_cleanup_hours', '24', 'number', 'system', '临时文件清理时间（小时）');

-- 插入内置音乐分类
INSERT INTO background_music (id, name, file_id, category, is_built_in, created_at) VALUES
('music-built-001', '舒缓钢琴', 'file-built-001', '舒缓', TRUE, NOW()),
('music-built-002', '动感电子', 'file-built-002', '动感', TRUE, NOW()),
('music-built-003', '古典弦乐', 'file-built-003', '古典', TRUE, NOW());

-- 插入内置提示词
INSERT INTO prompts (id, name, content, category, is_built_in, variables, created_at) VALUES
('prompt-built-001', 'Reddit恐怖故事改写', 
'请将以下Reddit恐怖故事改写为更适合视频内容的版本...', 
'恐怖', TRUE, '["story_content", "target_length"]', NOW()),
('prompt-built-002', 'Reddit感人故事改写', 
'请将以下Reddit感人故事改写为更适合视频内容的版本...', 
'感人', TRUE, '["story_content", "target_length"]', NOW());
```

## 🔍 查询优化

### 常用查询SQL

```sql
-- 获取资源列表（分页、过滤、搜索）
SELECT m.*, f.file_url, f.file_size, f.duration 
FROM background_music m
JOIN resource_files f ON m.file_id = f.id
WHERE m.category = ? 
AND m.is_built_in = ?
AND (m.name LIKE ? OR m.description LIKE ?)
ORDER BY m.usage_count DESC, m.created_at DESC
LIMIT ? OFFSET ?;

-- 获取任务统计
SELECT 
    status,
    COUNT(*) as count,
    AVG(total_duration) as avg_duration
FROM generation_tasks
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY status;

-- 获取使用频率最高的资源
SELECT 
    'music' as type, name, usage_count, last_used_at
FROM background_music
WHERE usage_count > 0
UNION ALL
SELECT 
    'video' as type, name, usage_count, last_used_at
FROM video_materials
WHERE usage_count > 0
ORDER BY usage_count DESC
LIMIT 10;
```

这个数据库设计完全对应前端的Zustand状态管理结构，确保数据的一致性和完整性，同时考虑了性能优化和扩展性。
