#!/usr/bin/env python3
"""
启动后端服务器进行前后端联调
"""

import sys
import os
from pathlib import Path
import uvicorn

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

def start_backend_server():
    """启动后端API服务器"""
    print("🚀 Starting Reddit Story Video Generator Backend API...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Interactive API: http://localhost:8000/redoc")
    print("\n⚡ Starting server with auto-reload enabled...")
    
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n✋ Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")

if __name__ == "__main__":
    start_backend_server()
