#!/usr/bin/env python3
"""
前端数据联动综合测试
验证所有配置项的API联动和数据流
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def run_command(cmd, cwd=None, timeout=10):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)

def check_backend():
    """检查后端服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/api/accounts", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_api_endpoints():
    """测试所有前端需要的API端点"""
    endpoints = [
        ("/accounts", "账号列表"),
        ("/video-categories", "视频素材分类"),
        ("/video-materials", "视频素材列表"), 
        ("/prompts", "提示词列表"),
        ("/cover-templates", "封面模板列表"),
        ("/settings", "系统设置")
    ]
    
    results = {}
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"http://localhost:8000/api{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                results[endpoint] = {
                    "success": True,
                    "data": data,
                    "count": len(data.get("data", [])) if isinstance(data.get("data"), list) else "N/A"
                }
            else:
                results[endpoint] = {
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                }
        except Exception as e:
            results[endpoint] = {
                "success": False,
                "error": str(e)
            }
    
    return results

def test_data_linkage():
    """测试数据联动逻辑"""
    print("🔗 测试数据联动逻辑")
    print("-" * 40)
    
    # 1. 测试视频素材分类 -> 素材列表联动
    try:
        categories_resp = requests.get("http://localhost:8000/api/video-categories")
        if categories_resp.status_code == 200:
            categories = categories_resp.json().get("data", [])
            if categories:
                first_category = categories[0]["name"]
                materials_resp = requests.get(f"http://localhost:8000/api/video-materials?category={first_category}")
                if materials_resp.status_code == 200:
                    materials = materials_resp.json().get("data", [])
                    print(f"✅ 素材联动: {first_category} -> {len(materials)} 个素材")
                else:
                    print(f"❌ 素材联动失败: {materials_resp.status_code}")
            else:
                print("⚠️  没有素材分类数据")
        else:
            print(f"❌ 获取素材分类失败: {categories_resp.status_code}")
    except Exception as e:
        print(f"❌ 素材联动测试异常: {e}")
    
    # 2. 测试提示词分类提取和筛选
    try:
        prompts_resp = requests.get("http://localhost:8000/api/prompts")
        if prompts_resp.status_code == 200:
            prompts = prompts_resp.json().get("data", [])
            if prompts:
                # 提取分类
                categories = list(set(p["category"] for p in prompts))
                print(f"✅ 提示词分类提取: {len(categories)} 个分类")
                
                # 测试分类筛选
                if categories:
                    first_category = categories[0]
                    filtered = [p for p in prompts if p["category"] == first_category]
                    print(f"✅ 提示词筛选: {first_category} -> {len(filtered)} 个提示词")
            else:
                print("⚠️  没有提示词数据")
        else:
            print(f"❌ 获取提示词失败: {prompts_resp.status_code}")
    except Exception as e:
        print(f"❌ 提示词联动测试异常: {e}")
    
    # 3. 测试设置数据适配
    try:
        settings_resp = requests.get("http://localhost:8000/api/settings")
        if settings_resp.status_code == 200:
            settings = settings_resp.json().get("data", {})
            if "tts" in settings:
                print(f"✅ TTS设置获取成功: {settings['tts'].get('voice', 'N/A')}")
                # 前端应该适配为音色列表
                print("✅ 前端TTS音色适配: 已在apiService中实现")
            else:
                print("⚠️  没有TTS设置数据")
        else:
            print(f"❌ 获取设置失败: {settings_resp.status_code}")
    except Exception as e:
        print(f"❌ 设置联动测试异常: {e}")

def main():
    """主测试流程"""
    print("🚀 前端数据联动综合测试")
    print("=" * 50)
    
    # 1. 检查后端服务
    print("1️⃣ 检查后端服务...")
    if check_backend():
        print("✅ 后端服务运行正常")
    else:
        print("❌ 后端服务未运行，请先启动后端")
        print("   运行: cd backend && python main.py")
        return
    
    # 2. 测试API端点
    print("\n2️⃣ 测试API端点...")
    results = test_api_endpoints()
    
    success_count = 0
    for endpoint, result in results.items():
        if result["success"]:
            count = result.get("count", "N/A")
            print(f"✅ {endpoint}: {count} 项数据")
            success_count += 1
        else:
            print(f"❌ {endpoint}: {result['error']}")
    
    print(f"\nAPI测试结果: {success_count}/{len(results)} 成功")
    
    # 3. 测试数据联动
    print("\n3️⃣ 测试数据联动...")
    test_data_linkage()
    
    # 4. 检查前端TypeScript错误
    print("\n4️⃣ 检查前端代码...")
    frontend_dir = Path(__file__).parent / "frontend"
    
    if frontend_dir.exists():
        # 检查TypeScript编译
        success, stdout, stderr = run_command("npm run build", cwd=frontend_dir, timeout=60)
        if success:
            print("✅ 前端TypeScript编译成功")
        else:
            print("❌ 前端TypeScript编译失败")
            if stderr:
                print(f"   错误: {stderr[:500]}...")
    else:
        print("⚠️  找不到frontend目录")
    
    # 5. 生成测试报告
    print("\n" + "=" * 50)
    print("📊 测试报告")
    print("=" * 50)
    
    if success_count == len(results):
        print("🎉 所有API端点测试通过！")
        print("✅ 前端数据联动应该可以正常工作")
        print("\n📝 下一步建议:")
        print("   1. 启动前端开发服务器: cd frontend && npm run dev")
        print("   2. 打开浏览器访问: http://localhost:3000/generate")
        print("   3. 测试所有下拉框和数据联动功能")
        print("   4. 或者打开frontend_api_test.html进行快速测试")
    else:
        print("⚠️  有API端点测试失败，需要修复后端问题")
        print("📝 检查项目:")
        print("   1. 确保数据库已初始化并有测试数据")
        print("   2. 检查后端API路由和数据格式")
        print("   3. 查看后端日志了解具体错误")

if __name__ == "__main__":
    main()
