#!/usr/bin/env python3
"""
简单测试复制功能的脚本
"""

import json

# 模拟前端发送的模板数据
test_template_data = {
    "name": "测试复制模板",
    "category": "现代",
    "description": "这是一个测试复制功能的模板",
    "variables": [
        {
            "name": "title",
            "type": "text",
            "label": "标题",
            "defaultValue": "示例标题"
        }
    ],
    "elements": [
        {
            "id": "text1",
            "type": "text",
            "x": 100,
            "y": 100,
            "width": 300,
            "height": 50,
            "content": "主标题",
            "style": {
                "fontSize": 24,
                "fontWeight": "bold",
                "color": "#333333"
            }
        }
    ],
    "background": {
        "type": "gradient",
        "value": {
            "direction": "to bottom right",
            "colors": ["#FF6B6B", "#4ECDC4"]
        }
    },
    "is_built_in": False,
    "width": 1920,
    "height": 1080,
    "format": "png"
}

print("测试模板数据:")
print(json.dumps(test_template_data, indent=2, ensure_ascii=False))

print(f"\n元素数量: {len(test_template_data.get('elements', []))}")
print(f"变量数量: {len(test_template_data.get('variables', []))}")
print(f"背景类型: {test_template_data.get('background', {}).get('type')}")

# 模拟复制过程中的数据处理
copy_data = {
    **test_template_data,
    "name": f"{test_template_data['name']} - 副本",
    "description": f"复制自 {test_template_data['name']}",
    "is_built_in": False
}

# 删除不需要的字段
fields_to_remove = [
    'id', 'createdAt', 'updatedAt', 'usageCount', 
    'thumbnailPath', 'thumbnailUrl', 'elementCount', 
    'variableCount', 'hasVariables', 'canvasSize', 'isPublic'
]

for field in fields_to_remove:
    copy_data.pop(field, None)

print("\n复制后的模板数据:")
print(json.dumps(copy_data, indent=2, ensure_ascii=False))

print(f"\n复制后元素数量: {len(copy_data.get('elements', []))}")
print(f"复制后变量数量: {len(copy_data.get('variables', []))}")
print(f"复制后背景类型: {copy_data.get('background', {}).get('type')}")

print("\n✅ 数据结构检查完成，可以用于测试API调用")
