"""
封面模板相关的Pydantic模式
用于API请求和响应的数据验证
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

class VariableSchema(BaseModel):
    """变量模式"""
    name: str = Field(..., description="变量名")
    type: str = Field(..., description="变量类型 (text/image)")
    label: str = Field(..., description="显示标签")
    defaultValue: Optional[str] = Field(None, description="默认值")
    required: bool = Field(False, description="是否必需")
    description: Optional[str] = Field("", description="描述")

class VariableBindingSchema(BaseModel):
    """变量绑定模式"""
    variable: str = Field(..., description="绑定的变量名")
    property: str = Field(..., description="绑定的属性名")

class ElementPropertiesSchema(BaseModel):
    """元素属性模式（基础）"""
    # 通用属性
    fill: Optional[str] = Field(None, description="填充颜色")
    stroke: Optional[str] = Field(None, description="边框颜色")
    strokeWidth: Optional[float] = Field(None, description="边框宽度")
    
    # 文本属性
    text: Optional[str] = Field(None, description="文本内容")
    fontSize: Optional[float] = Field(None, description="字体大小")
    fontFamily: Optional[str] = Field(None, description="字体族")
    fontWeight: Optional[str] = Field(None, description="字体粗细")
    textAlign: Optional[str] = Field(None, description="文本对齐")
    
    # 图片属性
    src: Optional[str] = Field(None, description="图片源")
    borderRadius: Optional[float] = Field(None, description="圆角")
    
    class Config:
        extra = "allow"  # 允许额外属性

class ElementSchema(BaseModel):
    """元素模式"""
    id: str = Field(..., description="元素ID")
    type: str = Field(..., description="元素类型")
    name: str = Field(..., description="元素名称")
    
    # 位置和尺寸
    x: float = Field(0, description="X坐标")
    y: float = Field(0, description="Y坐标")
    width: float = Field(100, description="宽度")
    height: float = Field(100, description="高度")
    
    # 变换属性
    rotation: float = Field(0, description="旋转角度")
    opacity: float = Field(1.0, ge=0, le=1, description="透明度")
    zIndex: int = Field(0, description="层级")
    
    # 样式属性
    properties: ElementPropertiesSchema = Field(default_factory=ElementPropertiesSchema, description="元素属性")
    
    # 变量绑定
    variableBinding: Optional[VariableBindingSchema] = Field(None, description="变量绑定")
    
    @validator('type')
    def validate_element_type(cls, v):
        allowed_types = ['text', 'image', 'shape', 'background']
        if v not in allowed_types:
            raise ValueError(f'元素类型必须是: {", ".join(allowed_types)}')
        return v

class BackgroundSchema(BaseModel):
    """背景模式"""
    type: str = Field("color", description="背景类型 (color/gradient/image)")
    value: str = Field("#ffffff", description="背景值")
    
    # 渐变属性
    gradient: Optional[Dict[str, Any]] = Field(None, description="渐变配置")
    
    # 图片属性
    image: Optional[Dict[str, Any]] = Field(None, description="背景图片配置")

class CoverTemplateCreateRequest(BaseModel):
    """创建模板请求"""
    name: str = Field(..., min_length=1, max_length=255, description="模板名称")
    description: Optional[str] = Field("", description="模板描述")
    category: str = Field("general", description="模板分类")
    width: int = Field(1920, ge=100, le=10000, description="画布宽度")
    height: int = Field(1080, ge=100, le=10000, description="画布高度")
    background: BackgroundSchema = Field(default_factory=BackgroundSchema, description="背景配置")
    elements: List[ElementSchema] = Field(default_factory=list, description="元素列表")
    variables: List[VariableSchema] = Field(default_factory=list, description="变量列表")
    isPublic: bool = Field(True, description="是否公开")

class CoverTemplateUpdateRequest(BaseModel):
    """更新模板请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")
    category: Optional[str] = Field(None, description="模板分类")
    width: Optional[int] = Field(None, ge=100, le=10000, description="画布宽度")
    height: Optional[int] = Field(None, ge=100, le=10000, description="画布高度")
    background: Optional[BackgroundSchema] = Field(None, description="背景配置")
    elements: Optional[List[ElementSchema]] = Field(None, description="元素列表")
    variables: Optional[List[VariableSchema]] = Field(None, description="变量列表")
    isPublic: Optional[bool] = Field(None, description="是否公开")

class CoverTemplateResponse(BaseModel):
    """模板响应"""
    id: str
    name: str
    description: Optional[str]
    category: str
    width: int
    height: int
    background: Dict[str, Any]
    elements: List[Dict[str, Any]]
    variables: List[Dict[str, Any]]
    thumbnailPath: Optional[str]
    thumbnailUrl: Optional[str]
    isPublic: bool
    elementCount: int
    variableCount: int
    hasVariables: bool
    canvasSize: str
    createdAt: Optional[str]
    updatedAt: Optional[str]

class TemplateListQuery(BaseModel):
    """模板列表查询"""
    category: Optional[str] = Field(None, description="分类筛选")
    search: Optional[str] = Field(None, description="搜索关键词")
    hasVariables: Optional[bool] = Field(None, description="是否有变量")
    isPublic: Optional[bool] = Field(None, description="是否公开")
    page: int = Field(1, ge=1, description="页码")
    pageSize: int = Field(20, ge=1, le=100, description="每页数量")

class PreviewDataSchema(BaseModel):
    """预览数据模式"""
    avatar: Optional[str] = Field(None, description="头像URL")
    nickname: Optional[str] = Field(None, description="昵称")
    title: Optional[str] = Field(None, description="标题")

class GenerateCoverRequest(BaseModel):
    """生成封面请求"""
    templateId: str = Field(..., description="模板ID")
    data: PreviewDataSchema = Field(..., description="变量数据")
    accountId: Optional[str] = Field(None, description="账号ID（自动获取数据）")
    format: str = Field("png", description="输出格式")
    quality: int = Field(90, ge=1, le=100, description="输出质量")

class GenerateCoverResponse(BaseModel):
    """生成封面响应"""
    success: bool
    coverUrl: Optional[str] = None
    coverPath: Optional[str] = None
    message: Optional[str] = None

class AvailableVariablesResponse(BaseModel):
    """可用变量响应"""
    variables: List[VariableSchema]

class TemplateStatsResponse(BaseModel):
    """模板统计响应"""
    totalTemplates: int
    publicTemplates: int
    privateTemplates: int
    categoryCounts: Dict[str, int]
    variableUsage: Dict[str, int]
