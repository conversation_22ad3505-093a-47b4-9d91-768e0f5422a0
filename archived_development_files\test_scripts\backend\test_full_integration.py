#!/usr/bin/env python3
"""
前后端联调自动化验证脚本
运行完整的端到端测试，验证API功能和数据一致性
"""

import asyncio
import aiohttp
import json
import time
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
import subprocess
import signal
import os

class IntegrationTestRunner:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.api_base = f"{self.backend_url}/api/v1"
        self.backend_process = None
        self.frontend_process = None
        
    async def wait_for_server(self, url: str, timeout: int = 30) -> bool:
        """等待服务器启动"""
        print(f"⏳ Waiting for server at {url}...")
        
        async with aiohttp.ClientSession() as session:
            for i in range(timeout):
                try:
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=2)) as response:
                        if response.status == 200:
                            print(f"✅ Server at {url} is ready!")
                            return True
                except:
                    pass
                await asyncio.sleep(1)
        
        print(f"❌ Server at {url} failed to start within {timeout} seconds")
        return False
    
    def start_backend(self) -> bool:
        """启动后端服务"""
        print("🚀 Starting backend server...")
        backend_dir = Path(__file__).parent
        
        try:
            self.backend_process = subprocess.Popen(
                [sys.executable, "start_server.py"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            return True
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_frontend(self) -> bool:
        """启动前端服务"""
        print("🎨 Starting frontend server...")
        frontend_dir = Path(__file__).parent.parent / "frontend"
        
        try:
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            return True
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
    
    def stop_servers(self):
        """停止服务器"""
        print("🛑 Stopping servers...")
        
        if self.backend_process:
            try:
                if os.name == 'nt':
                    subprocess.run(["taskkill", "/F", "/T", "/PID", str(self.backend_process.pid)], 
                                 capture_output=True)
                else:
                    self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
            except:
                pass
        
        if self.frontend_process:
            try:
                if os.name == 'nt':
                    subprocess.run(["taskkill", "/F", "/T", "/PID", str(self.frontend_process.pid)], 
                                 capture_output=True)
                else:
                    self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
            except:
                pass
    
    async def test_api_endpoints(self) -> Dict[str, Any]:
        """测试所有API端点"""
        results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "details": []
        }
        
        async with aiohttp.ClientSession() as session:
            
            # 1. 健康检查
            await self._test_endpoint(session, "GET", "/health", results, "Health Check")
            
            # 2. 设置API
            await self._test_endpoint(session, "GET", "/api/v1/settings", results, "Get Settings")
            
            # 3. 背景音乐API
            await self._test_endpoint(session, "GET", "/api/v1/background-music", results, "List Background Music")
            await self._test_endpoint(session, "GET", "/api/v1/background-music/categories", results, "Get Music Categories")
            
            # 4. 视频素材API
            await self._test_endpoint(session, "GET", "/api/v1/video-materials", results, "List Video Materials")
            await self._test_endpoint(session, "GET", "/api/v1/video-materials/categories", results, "Get Video Categories")
            
            # 5. 提示词API
            await self._test_endpoint(session, "GET", "/api/v1/prompts", results, "List Prompts")
            await self._test_endpoint(session, "GET", "/api/v1/prompts/categories", results, "Get Prompt Categories")
            
            # 6. 账号API
            await self._test_endpoint(session, "GET", "/api/v1/accounts", results, "List Accounts")
            await self._test_endpoint(session, "GET", "/api/v1/accounts/platforms", results, "Get Account Platforms")
            
            # 7. 封面模板API
            await self._test_endpoint(session, "GET", "/api/v1/cover-templates", results, "List Cover Templates")
            await self._test_endpoint(session, "GET", "/api/v1/cover-templates/categories", results, "Get Template Categories")
        
        return results
    
    async def _test_endpoint(self, session: aiohttp.ClientSession, method: str, endpoint: str, 
                           results: Dict[str, Any], description: str):
        """测试单个API端点"""
        url = f"{self.backend_url}{endpoint}"
        results["total_tests"] += 1
        
        try:
            async with session.request(method, url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    results["passed"] += 1
                    results["details"].append({
                        "test": description,
                        "status": "✅ PASS",
                        "method": method,
                        "endpoint": endpoint,
                        "response_keys": list(data.keys()) if isinstance(data, dict) else "non-dict"
                    })
                    print(f"✅ {description}")
                else:
                    results["failed"] += 1
                    results["details"].append({
                        "test": description,
                        "status": f"❌ FAIL ({response.status})",
                        "method": method,
                        "endpoint": endpoint,
                        "error": f"HTTP {response.status}"
                    })
                    print(f"❌ {description} - HTTP {response.status}")
        except Exception as e:
            results["failed"] += 1
            results["details"].append({
                "test": description,
                "status": f"❌ ERROR",
                "method": method,
                "endpoint": endpoint,
                "error": str(e)
            })
            print(f"❌ {description} - {e}")
    
    async def test_frontend_accessibility(self) -> bool:
        """测试前端可访问性"""
        print("🎯 Testing frontend accessibility...")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(self.frontend_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        print("✅ Frontend is accessible")
                        return True
                    else:
                        print(f"❌ Frontend returned HTTP {response.status}")
                        return False
            except Exception as e:
                print(f"❌ Frontend accessibility test failed: {e}")
                return False
    
    async def run_integration_tests(self) -> bool:
        """运行完整的集成测试"""
        print("🧪 Starting Integration Tests")
        print("=" * 50)
        
        # 启动服务器
        if not self.start_backend():
            return False
        
        if not self.start_frontend():
            self.stop_servers()
            return False
        
        try:
            # 等待服务器启动
            backend_ready = await self.wait_for_server(f"{self.backend_url}/health", 45)
            if not backend_ready:
                return False
            
            frontend_ready = await self.wait_for_server(self.frontend_url, 45)
            if not frontend_ready:
                return False
            
            # 运行API测试
            print("\n📡 Testing API Endpoints")
            print("-" * 30)
            api_results = await self.test_api_endpoints()
            
            # 测试前端
            print("\n🎯 Testing Frontend")
            print("-" * 20)
            frontend_ok = await self.test_frontend_accessibility()
            
            # 输出结果
            print("\n📊 Test Results Summary")
            print("=" * 30)
            print(f"API Tests: {api_results['passed']}/{api_results['total_tests']} passed")
            print(f"Frontend: {'✅ OK' if frontend_ok else '❌ FAIL'}")
            
            # 详细结果
            if api_results['failed'] > 0:
                print("\n❌ Failed API Tests:")
                for detail in api_results['details']:
                    if 'ERROR' in detail['status'] or 'FAIL' in detail['status']:
                        print(f"  - {detail['test']}: {detail['status']}")
                        if 'error' in detail:
                            print(f"    Error: {detail['error']}")
            
            success = api_results['failed'] == 0 and frontend_ok
            
            if success:
                print("\n🎉 All integration tests passed!")
                print("\n📋 Manual Testing Checklist:")
                print("  1. ✅ Backend API is running and healthy")
                print("  2. ✅ Frontend is accessible")
                print("  3. ✅ All API endpoints respond correctly")
                print("\n🌐 Access URLs:")
                print(f"  Frontend: {self.frontend_url}")
                print(f"  Backend API: {self.backend_url}")
                print(f"  API Docs: {self.backend_url}/docs")
                
                print("\n🔍 Next Steps for Manual Testing:")
                print("  - Open the frontend and test each page")
                print("  - Verify data loading and saving")
                print("  - Check browser console for errors")
                print("  - Test form submissions and validations")
                
            else:
                print("\n❌ Some integration tests failed!")
                print("Please review the errors above and fix them before proceeding.")
            
            return success
            
        finally:
            # 清理
            print("\n🧹 Cleaning up...")
            # 给用户选择是否保持服务器运行
            try:
                if input("\n🤔 Keep servers running for manual testing? (y/N): ").lower() != 'y':
                    self.stop_servers()
                else:
                    print(f"\n✨ Servers are still running!")
                    print(f"Frontend: {self.frontend_url}")
                    print(f"Backend: {self.backend_url}")
                    print("Press Ctrl+C to stop servers when done.")
                    
                    # 等待用户中断
                    try:
                        while True:
                            await asyncio.sleep(1)
                    except KeyboardInterrupt:
                        print("\n🛑 Stopping servers...")
                        self.stop_servers()
            except KeyboardInterrupt:
                print("\n🛑 Stopping servers...")
                self.stop_servers()

async def main():
    """主函数"""
    runner = IntegrationTestRunner()
    
    try:
        success = await runner.run_integration_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        runner.stop_servers()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        runner.stop_servers()
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 Reddit Story Video Generator - Integration Test Runner")
    print("=" * 60)
    asyncio.run(main())
