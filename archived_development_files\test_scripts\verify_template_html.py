#!/usr/bin/env python3
"""
验证模板导入后的图片路径更新
"""

import os
import sys
from pathlib import Path

# 添加backend到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from src.services.template_import_service import TemplateImportService
from src.core.database import get_db_session
from src.models.resources import CoverTemplate

def verify_template_html():
    """验证模板HTML内容"""
    print("=== 验证模板HTML内容 ===")
    
    service = TemplateImportService()
    db = get_db_session()
    
    try:
        # 获取最新的社交媒体模板
        template = db.query(CoverTemplate).filter(CoverTemplate.name == "社交媒体帖子模板").first()
        
        if not template:
            print("❌ 未找到社交媒体帖子模板")
            return False
        
        print(f"✅ 找到模板: {template.name} (ID: {template.id})")
        
        # 读取处理后的HTML文件
        template_file = service.templates_dir / f"{template.id}.html"
        
        if not template_file.exists():
            print(f"❌ 模板文件不存在: {template_file}")
            return False
        
        with open(template_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"📄 模板文件: {template_file}")
        
        # 查看图片路径
        image_paths = service.extract_image_paths_from_html(html_content)
        print(f"🖼️  当前HTML中的图片路径: {image_paths}")
        
        # 检查是否包含更新后的路径
        if any(path.startswith(f"{template.id}_images/") for path in image_paths):
            print("✅ 图片路径已正确更新为模板专用路径")
        else:
            print("⚠️  图片路径可能未正确更新")
            
        # 显示部分HTML内容（图片相关部分）
        print("\n📋 HTML中的图片标签示例:")
        import re
        img_tags = re.findall(r'<img[^>]+>', html_content, re.IGNORECASE)
        for i, tag in enumerate(img_tags[:5]):  # 只显示前5个
            print(f"   {i+1}. {tag}")
        
        if len(img_tags) > 5:
            print(f"   ... 还有 {len(img_tags) - 5} 个图片标签")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

def main():
    """主函数"""
    success = verify_template_html()
    
    if success:
        print("\n🎉 模板验证成功！")
    else:
        print("\n💥 模板验证失败！")
        
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
