# 批量封面生成脚本使用说明

## 脚本功能
这个脚本可以根据指定的账号名称、文案目录和封面模板，自动为目录中的每个txt文案文件生成对应的封面图片。

## 文件位置
- `generate_covers_from_scripts.py` - 完整功能版本
- `simple_cover_generator.py` - 简化版本，兼容性更好

## 使用方法

### 1. 查看可用账号
```bash
cd archived_development_files\test_scripts
python simple_cover_generator.py --list-accounts
```

### 2. 查看可用模板
```bash
python simple_cover_generator.py --list-templates
```

### 3. 批量生成封面
```bash
python simple_cover_generator.py -a "账号名称" -d "文案目录路径" -t "模板名称"

[示例]
python simple_cover_generator.py -a "Reddit Nightcup" -d "C:/Users/<USER>/Desktop/test" -t "0703007"

```

## 参数说明
- `-a, --account`: 账号名称（必需）
- `-d, --directory`: 存放视频文案(txt)的目录路径（必需）
- `-t, --template`: 封面模板名称（必需）
- `--list-accounts`: 列出所有可用账号
- `--list-templates`: 列出所有可用模板

## 使用示例

### 查看可用资源
```bash
# 查看可用账号
python simple_cover_generator.py --list-accounts

# 查看可用模板
python simple_cover_generator.py --list-templates
```

### 生成封面
```bash
# 为指定账号和模板生成封面
python simple_cover_generator.py -a "主账户" -d "D:\Scripts\MyScripts" -t "简约风格"
```

## 工作流程
1. 脚本会扫描指定目录中的所有`.txt`文件
2. 读取每个文件的第一句话作为封面标题
3. 使用指定的账号信息（包括账号名称和头像）
4. 调用指定的封面模板生成封面图片
5. 将生成的封面保存为同名的`.png`文件在同一目录中

## 文件命名规则
- 输入文件：`story1.txt`
- 输出文件：`story1.png`

## 注意事项
1. **运行环境**: 确保在项目根目录或test_scripts目录中运行脚本
2. **backend服务**: 确保后端服务正在运行
3. **数据库连接**: 确保数据库可以正常连接
4. **账号和模板**: 使用`--list-accounts`和`--list-templates`查看可用资源
5. **文件覆盖**: 脚本会跳过已存在的PNG文件，不会覆盖
6. **文件编码**: 确保txt文件使用UTF-8编码

## 输出格式
脚本会显示详细的执行进度和统计信息：
- 处理的文件数量
- 成功生成的封面数量
- 失败的文件数量
- 总体成功率

## 错误处理
- 如果账号不存在，会提示并列出可用账号
- 如果模板不存在，会提示并列出可用模板
- 如果文件读取失败，会跳过该文件并继续处理
- 如果封面生成失败，会记录错误并继续处理下一个文件

## 示例输出
```
=== 批量生成封面 ===
账号名称: 主账户
文案目录: D:\Scripts\MyScripts
封面模板: 简约风格
==================================================
✅ 使用账号: 主账户
✅ 使用模板: 简约风格
✅ 处理文件数: 5
--------------------------------------------------
[1/5] 处理文件: story1.txt
  标题: 这是一个关于冒险的故事...
✅ 封面生成成功: story1.png (187463 bytes)

[2/5] 处理文件: story2.txt
  标题: 在一个遥远的星球上...
✅ 封面生成成功: story2.png (192847 bytes)

==================================================
📊 生成统计:
  成功: 5
  失败: 0
  总计: 5
  成功率: 100.0%
```

## 故障排除
1. **导入错误**: 确保脚本在正确的目录中运行
2. **数据库连接失败**: 检查backend服务是否正在运行
3. **找不到账号/模板**: 使用列表命令查看可用资源
4. **文件权限问题**: 确保对目录有读写权限
5. **编码问题**: 确保txt文件使用UTF-8编码
