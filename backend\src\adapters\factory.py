"""
Adapter factory for creating database, cache, and AI service adapters
based on configuration settings
"""

import yaml
from pathlib import Path
from typing import Dict, Any
from loguru import logger

from .base import DatabaseAdapter, CacheAdapter, TTSAdapter, LLMAdapter
from .database.sqlite import SqliteAdapter
from .cache.memory import MemoryCacheAdapter


class AdapterFactory:
    """Factory class for creating adapters based on configuration"""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.config = self._load_config()
        logger.info(f"Adapter factory initialized for environment: {environment}")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from config.yaml"""
        config_path = Path(__file__).parent.parent.parent.parent / "shared" / "config" / "config.yaml"
        
        if not config_path.exists():
            logger.warning(f"Config file not found at {config_path}, using defaults")
            return self._get_default_config()
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return config.get("profiles", {}).get(self.environment, self._get_default_config())
        except Exception as e:
            logger.error(f"Error loading config: {e}, using defaults")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for development environment"""
        return {
            "database": {
                "type": "sqlite",
                "config": {
                    "db_path": "./data/dev.db"
                }
            },
            "cache": {
                "type": "memory",
                "config": {}
            },
            "tts": {
                "type": "openai",
                "config": {
                    "api_key": ""
                }
            },
            "llm": {
                "type": "openai",
                "config": {
                    "api_key": "",
                    "model": "gpt-3.5-turbo"
                }
            }
        }
    
    def create_database_adapter(self) -> DatabaseAdapter:
        """Create database adapter based on configuration"""
        db_config = self.config.get("database", {})
        db_type = db_config.get("type", "sqlite")
        config = db_config.get("config", {})
        
        if db_type == "sqlite":
            db_path = config.get("db_path", "./data/app.db")
            return SqliteAdapter(db_path)
        else:
            raise ValueError(f"Unsupported database type: {db_type}")
    
    def create_cache_adapter(self) -> CacheAdapter:
        """Create cache adapter based on configuration"""
        cache_config = self.config.get("cache", {})
        cache_type = cache_config.get("type", "memory")
        config = cache_config.get("config", {})
        
        if cache_type == "memory":
            return MemoryCacheAdapter()
        else:
            raise ValueError(f"Unsupported cache type: {cache_type}")
    
    def create_tts_adapter(self) -> TTSAdapter:
        """Create TTS adapter based on configuration"""
        # TODO: Implement TTS adapters
        raise NotImplementedError("TTS adapters not yet implemented")
    
    def create_llm_adapter(self) -> LLMAdapter:
        """Create LLM adapter based on configuration"""
        # TODO: Implement LLM adapters
        raise NotImplementedError("LLM adapters not yet implemented")
