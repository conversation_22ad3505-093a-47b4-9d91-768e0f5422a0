@echo off
REM Test Backend Only - Reddit Story Video Generator

echo 🔧 Testing Backend Only...

REM 检查 Python
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.11+ first.
    pause
    exit /b 1
)

REM 设置环境变量
set ENVIRONMENT=development

echo 📦 Setting up backend...

cd backend
 
REM 创建虚拟环境 (如果不存在)
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

REM 安装依赖
echo Installing Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully!

REM 启动后端 (先尝试简化版本)
echo.
echo 🚀 Starting backend server (simplified version)...
echo.
echo If you see "Application startup complete", the backend is working!
echo Press Ctrl+C to stop the server
echo.
echo 📍 API endpoints:
echo   • Health: http://localhost:8000/api/health
echo   • Test:   http://localhost:8000/api/test
echo   • Docs:   http://localhost:8000/docs
echo.

python simple_main.py
if %errorlevel% neq 0 (
    echo.
    echo ⚠️ Simplified version failed, trying full version...
    echo.
    python main.py
)

cd ..
pause
