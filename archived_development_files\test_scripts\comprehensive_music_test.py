#!/usr/bin/env python3
"""
全面的背景音乐管理功能测试
测试上传、分类管理、持久化等核心功能
"""

import sys
import os
import time
import requests
import json
from pathlib import Path

API_BASE = "http://localhost:8000/api"

def wait_for_backend(max_tries=30):
    """等待后端服务启动"""
    print("等待后端服务启动...")
    for i in range(max_tries):
        try:
            response = requests.get(f"{API_BASE}/health", timeout=2)
            if response.status_code == 200:
                print("✅ 后端服务已启动")
                return True
        except:
            pass
        print(f"尝试连接后端... ({i+1}/{max_tries})")
        time.sleep(1)
    
    print("❌ 无法连接到后端服务")
    return False

def test_category_persistence():
    """测试分类持久化问题"""
    print("\n🔄 测试分类持久化...")
    
    # 1. 获取初始分类列表
    print("1. 获取初始分类列表")
    response = requests.get(f"{API_BASE}/background-music/categories/list")
    if response.status_code == 200:
        initial_categories = response.json()["data"]
        print(f"   初始分类: {initial_categories}")
    else:
        print(f"   ❌ 获取分类失败: {response.text}")
        return False
    
    # 2. 添加新分类
    test_category = f"test_category_{int(time.time())}"
    print(f"2. 添加新分类: {test_category}")
    form_data = {"category": test_category}
    response = requests.post(f"{API_BASE}/background-music/categories", data=form_data)
    if response.status_code == 200:
        print(f"   ✅ 分类添加成功")
    else:
        print(f"   ❌ 分类添加失败: {response.text}")
        return False
    
    # 3. 验证分类是否存在
    print("3. 验证新分类是否存在")
    response = requests.get(f"{API_BASE}/background-music/categories/list")
    if response.status_code == 200:
        updated_categories = response.json()["data"]
        if test_category in updated_categories:
            print(f"   ✅ 新分类存在于列表中: {updated_categories}")
        else:
            print(f"   ❌ 新分类不存在于列表中: {updated_categories}")
            return False
    else:
        print(f"   ❌ 获取更新后的分类失败: {response.text}")
        return False
    
    # 4. 删除测试分类
    print(f"4. 删除测试分类: {test_category}")
    response = requests.delete(f"{API_BASE}/background-music/categories/{test_category}")
    if response.status_code == 200:
        print(f"   ✅ 分类删除成功")
    else:
        print(f"   ❌ 分类删除失败: {response.text}")
    
    return True

def test_music_upload_flow():
    """测试音乐上传流程"""
    print("\n🎵 测试音乐上传流程...")
    
    # 创建测试音频文件 (模拟)
    print("注意：此测试需要真实的音频文件才能完全验证上传功能")
    
    # 测试获取音乐列表
    print("1. 获取音乐列表")
    response = requests.get(f"{API_BASE}/background-music")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ 获取音乐列表成功，共 {data['data']['total']} 首音乐")
        return True
    else:
        print(f"   ❌ 获取音乐列表失败: {response.text}")
        return False

def test_api_endpoints():
    """测试所有API端点"""
    print("\n🔧 测试API端点...")
    
    endpoints = [
        ("GET", "/background-music", "获取音乐列表"),
        ("GET", "/background-music/categories/list", "获取分类列表"),
        ("GET", "/health", "健康检查"),
    ]
    
    success_count = 0
    for method, endpoint, description in endpoints:
        try:
            response = None
            if method == "GET":
                response = requests.get(f"{API_BASE}{endpoint}")
            
            if response and response.status_code == 200:
                print(f"   ✅ {description}: 成功")
                success_count += 1
            else:
                status_code = response.status_code if response else "N/A"
                print(f"   ❌ {description}: 失败 ({status_code})")
        except Exception as e:
            print(f"   ❌ {description}: 异常 ({str(e)})")
    
    print(f"\nAPI测试结果: {success_count}/{len(endpoints)} 个端点正常")
    return success_count == len(endpoints)

def main():
    print("🎵 背景音乐管理功能全面测试")
    print("=" * 50)
    
    # 检查后端是否运行
    if not wait_for_backend():
        print("\n❌ 测试失败：无法连接到后端服务")
        print("请确保后端服务正在运行在 http://localhost:8000")
        return False
    
    # 运行测试
    tests = [
        ("API端点测试", test_api_endpoints),
        ("分类持久化测试", test_category_persistence),
        ("音乐上传流程测试", test_music_upload_flow),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(tests)} 个测试通过")
    
    if success_count == len(tests):
        print("\n🎉 所有测试通过！背景音乐管理功能工作正常。")
    else:
        print("\n⚠️  部分测试失败，请检查相关功能。")
    
    return success_count == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
