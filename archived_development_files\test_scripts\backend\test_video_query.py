#!/usr/bin/env python3
"""
直接测试VideoMaterial数据查询
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.database import SessionLocal
from src.models.resources import VideoMaterial

def test_video_materials():
    """测试视频素材查询"""
    try:
        # 获取数据库会话
        db = SessionLocal()
        
        # 查询所有视频素材 - 不过滤
        print("查询所有记录（无过滤）...")
        all_materials = db.query(VideoMaterial).all()
        print(f"无过滤查询结果: {len(all_materials)} 个")
        
        # 查询未删除的视频素材
        print("\n查询未删除的记录...")
        active_materials = db.query(VideoMaterial).filter(VideoMaterial.is_deleted == False).all()
        print(f"未删除查询结果: {len(active_materials)} 个")
        
        # 直接SQL查询
        print("\n直接SQL查询...")
        from sqlalchemy import text
        result = db.execute(text("SELECT COUNT(*) FROM video_materials"))
        count = result.scalar()
        print(f"SQL查询总数: {count}")
        
        result = db.execute(text("SELECT COUNT(*) FROM video_materials WHERE is_deleted = 0"))
        active_count = result.scalar()
        print(f"SQL查询未删除数: {active_count}")
        
        # 查看前几条记录的详细信息
        result = db.execute(text("SELECT id, name, is_deleted FROM video_materials LIMIT 3"))
        records = result.fetchall()
        print(f"\n前3条记录详情:")
        for record in records:
            print(f"  ID: {record[0]}, Name: {record[1]}, is_deleted: {record[2]}")
        
        db.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_video_materials()
