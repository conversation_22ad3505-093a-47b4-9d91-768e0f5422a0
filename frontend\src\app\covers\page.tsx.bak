'use client';

import { useState, useEffect } from 'react';
import { 
  PlusIcon, 
  MagnifyingGlassIcon,
  ArrowLeftIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';
import SimpleCanvasEditor from '@/components/SimpleCanvasEditor';

// 模板类型
interface Template {
  id: string;
  name: string;
  category: string;
  isActive: boolean;
  usageCount: number;
  createdAt: string;
}

// 分类常量
const CATEGORIES = ['全部', '经典', '现代', '简约', '科技'];

export default function CoversPage() {
  // 页面状态
  const [currentView, setCurrentView] = useState<'list' | 'editor'>('list');
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [templates, setTemplates] = useState<Template[]>([
    {
      id: '1',
      name: '经典蓝色模板',
      category: '经典',
      isActive: true,
      usageCount: 15,
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      name: '现代渐变模板',
      category: '现代',
      isActive: true,
      usageCount: 8,
      createdAt: '2024-01-14'
    },
    {
      id: '3',
      name: '简约白色模板',
      category: '简约',
      isActive: false,
      usageCount: 3,
      createdAt: '2024-01-13'
    }
  ]);

  // 创建模板
  const handleCreateTemplate = (templateData: { name: string; category: string; description: string }) => {
    const newTemplate: Template = {
      id: Date.now().toString(),
      name: templateData.name,
      category: templateData.category,
      isActive: false,
      usageCount: 0,
      createdAt: new Date().toISOString().split('T')[0]
    };
    
    setTemplates(prev => [...prev, newTemplate]);
    setShowCreateModal(false);
    setCurrentView('editor');
  };

  // 筛选模板
  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === '全部' || template.category === selectedCategory;
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // 统计数据
  const stats = {
    total: templates.length,
    active: templates.filter(t => t.isActive).length,
    draft: templates.filter(t => !t.isActive).length,
    totalUsage: templates.reduce((sum, t) => sum + t.usageCount, 0)
  };

  if (currentView === 'editor') {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* 头部导航 */}
        <div className="bg-white border-b">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setCurrentView('list')}
                  className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  <ArrowLeftIcon className="w-4 h-4" />
                  返回列表
                </button>
                <h1 className="text-xl font-semibold text-gray-900">模板编辑器</h1>
              </div>
            </div>
          </div>
        </div>

        {/* 编辑器内容 */}
        <div className="p-6 h-[calc(100vh-80px)]">
          <SimpleCanvasEditor />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b">
        <div className="px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">封面模板管理</h1>
              <p className="mt-2 text-gray-600">创建和管理视频封面模板，支持自定义文本、颜色、位置等属性</p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <PlusIcon className="w-4 h-4" />
              新建模板
            </button>
          </div>
        </div>
      </div>

      <div className="px-6 py-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-blue-600 mb-1">{stats.total}</div>
            <div className="text-sm text-gray-600">总模板数</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-green-600 mb-1">{stats.active}</div>
            <div className="text-sm text-gray-600">已启用</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-orange-600 mb-1">{stats.draft}</div>
            <div className="text-sm text-gray-600">草稿</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="text-2xl font-bold text-purple-600 mb-1">{stats.totalUsage}</div>
            <div className="text-sm text-gray-600">使用次数</div>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="p-6 border-b">
            <div className="flex flex-col md:flex-row gap-4">
              {/* 分类筛选 */}
              <div className="flex flex-wrap gap-2">
                {CATEGORIES.map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>

              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索模板..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 模板列表 */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    模板名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    分类
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    使用次数
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTemplates.map(template => (
                  <tr key={template.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-8 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded mr-3"></div>
                        <div className="text-sm font-medium text-gray-900">{template.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        {template.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${
                        template.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        <span className={`w-1.5 h-1.5 rounded-full ${
                          template.isActive ? 'bg-green-500' : 'bg-yellow-500'
                        }`}></span>
                        {template.isActive ? '已启用' : '草稿'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {template.usageCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {template.createdAt}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <EyeIcon className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => setCurrentView('editor')}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button className="text-blue-600 hover:text-blue-900">
                          <DocumentDuplicateIcon className="w-4 h-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 创建模板模态框 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">新建封面模板</h3>
            </div>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget);
              handleCreateTemplate({
                name: formData.get('name') as string,
                category: formData.get('category') as string,
                description: formData.get('description') as string
              });
            }}>
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
                  <input
                    type="text"
                    name="name"
                    required
                    placeholder="请输入模板名称"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">模板分类</label>
                  <select
                    name="category"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="经典">经典风格</option>
                    <option value="现代">现代风格</option>
                    <option value="简约">简约风格</option>
                    <option value="科技">科技风格</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">模板描述</label>
                  <textarea
                    name="description"
                    rows={3}
                    placeholder="请输入模板描述"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  创建模板
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
import {
  PlusIcon,
  MagnifyingGlassIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  CameraIcon,
  CloudArrowUpIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  RectangleStackIcon,
  PhotoIcon,
  Square3Stack3DIcon,
  CursorArrowRaysIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  CheckCircleIcon,
  ClockIcon,
  LinkIcon,
  XMarkIcon,
  WifiIcon
} from '@heroicons/react/24/outline';

// 分类和工具常量
const CATEGORIES = ['全部', '经典', '现代', '简约', '科技'];
const TOOLS = [
  { id: 'select', name: '选择', icon: CursorArrowRaysIcon },
  { id: 'text', name: '文本', icon: RectangleStackIcon },
  { id: 'shape', name: '形状', icon: Square3Stack3DIcon },
  { id: 'image', name: '图片', icon: PhotoIcon }
] as const;

// 渐变预设
const GRADIENT_PRESETS = {
  'blue-purple': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  'sunset': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  'ocean': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  'forest': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  'fire': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
};

// 画布元素组件
const CanvasElement = ({ element, isSelected, onSelect, onUpdate }: {
  element: CoverElement;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<CoverElement>) => void;
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, elemX: 0, elemY: 0 });
  const [resizeStart, setResizeStart] = useState({ 
    x: 0, y: 0, width: 0, height: 0, handle: '' 
  });

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect();
    
    setIsDragging(true);
    setDragStart({
      x: e.clientX,
      y: e.clientY,
      elemX: element.x,
      elemY: element.y
    });
  };

  const handleResizeMouseDown = (e: React.MouseEvent, handle: string) => {
    e.stopPropagation();
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: element.width,
      height: element.height,
      handle
    });
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const deltaX = e.clientX - dragStart.x;
        const deltaY = e.clientY - dragStart.y;
        const newX = Math.max(0, Math.min(270, dragStart.elemX + deltaX));
        const newY = Math.max(0, Math.min(150, dragStart.elemY + deltaY));
        
        onUpdate({ x: newX, y: newY });
      } else if (isResizing) {
        const deltaX = e.clientX - resizeStart.x;
        const deltaY = e.clientY - resizeStart.y;
        
        let newWidth = resizeStart.width;
        let newHeight = resizeStart.height;
        let newX = element.x;
        let newY = element.y;

        switch (resizeStart.handle) {
          case 'se': // 右下
            newWidth = Math.max(20, resizeStart.width + deltaX);
            newHeight = Math.max(20, resizeStart.height + deltaY);
            break;
          case 'sw': // 左下
            newWidth = Math.max(20, resizeStart.width - deltaX);
            newHeight = Math.max(20, resizeStart.height + deltaY);
            newX = element.x - (newWidth - resizeStart.width);
            break;
          case 'ne': // 右上
            newWidth = Math.max(20, resizeStart.width + deltaX);
            newHeight = Math.max(20, resizeStart.height - deltaY);
            newY = element.y - (newHeight - resizeStart.height);
            break;
          case 'nw': // 左上
            newWidth = Math.max(20, resizeStart.width - deltaX);
            newHeight = Math.max(20, resizeStart.height - deltaY);
            newX = element.x - (newWidth - resizeStart.width);
            newY = element.y - (newHeight - resizeStart.height);
            break;
        }
        
        onUpdate({ 
          width: newWidth, 
          height: newHeight,
          x: newX,
          y: newY
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
    };

    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, dragStart, resizeStart, onUpdate, element.x, element.y]);

  const elementStyle = {
    position: 'absolute' as const,
    left: `${element.x}px`,
    top: `${element.y}px`,
    width: `${element.width}px`,
    height: element.type === 'text' ? 'auto' : `${element.height}px`,
    cursor: isDragging ? 'grabbing' : 'move',
    border: isSelected ? '2px dashed #2563eb' : '2px dashed transparent',
    transition: isSelected ? 'none' : 'all 0.2s',
    userSelect: 'none' as const,
    opacity: element.opacity || 1,
    transform: `rotate(${element.rotation || 0}deg)`,
    zIndex: element.zIndex || 0,
    ...getElementTypeStyles(element)
  };

  // 显示变量绑定指示器
  const showVariableIndicator = element.variableBinding?.enabled;

  return (
    <div
      style={elementStyle}
      onMouseDown={handleMouseDown}
      onClick={(e) => {
        e.stopPropagation();
        onSelect();
      }}
      title={showVariableIndicator ? `变量绑定: ${element.variableBinding?.variableName}` : element.name}
    >
      {/* 变量绑定指示器 */}
      {showVariableIndicator && (
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <WifiIcon className="w-2 h-2 text-white" />
        </div>
      )}
      
      {/* 缩放手柄 */}
      {isSelected && element.type !== 'text' && (
        <>
          {/* 四个角的缩放手柄 */}
          <div
            className="absolute -top-1 -left-1 w-2 h-2 bg-blue-600 border border-white rounded-sm cursor-nw-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, 'nw')}
          />
          <div
            className="absolute -top-1 -right-1 w-2 h-2 bg-blue-600 border border-white rounded-sm cursor-ne-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, 'ne')}
          />
          <div
            className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-600 border border-white rounded-sm cursor-sw-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, 'sw')}
          />
          <div
            className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-600 border border-white rounded-sm cursor-se-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, 'se')}
          />
        </>
      )}
      
      {element.type === 'text' && (element.properties?.content || element.name || '新文本')}
      {element.type === 'image' && !element.properties?.imageUrl && '图片'}
    </div>
  );
};

// 获取元素类型样式
const getElementTypeStyles = (element: CoverElement) => {
  const styles: any = {};

  if (element.type === 'text') {
    styles.color = element.properties?.color || '#000000';
    styles.fontSize = `${element.properties?.fontSize || 16}px`;
    styles.textAlign = element.properties?.textAlign || 'left';
    styles.fontWeight = element.properties?.fontWeight || 'normal';
    styles.fontFamily = element.properties?.fontFamily || 'Arial';
    styles.lineHeight = element.properties?.lineHeight || 1.2;
    styles.wordWrap = element.properties?.autoWrap ? 'break-word' : 'normal';
    styles.whiteSpace = element.properties?.autoWrap ? 'normal' : 'nowrap';
    styles.overflow = 'hidden';
    styles.textShadow = '1px 1px 2px rgba(0, 0, 0, 0.3)';
    styles.padding = '2px 4px';
  } else if (element.type === 'shape') {
    styles.backgroundColor = element.properties?.backgroundColor || '#cccccc';
    
    // 边框样式
    if (element.properties?.borderWidth && element.properties.borderWidth > 0) {
      const borderStyle = element.properties?.borderStyle || 'solid';
      styles.border = `${element.properties.borderWidth}px ${borderStyle} ${element.properties?.borderColor || '#000000'}`;
    }
    
    // 圆角
    if (element.properties?.borderRadius) {
      styles.borderRadius = `${element.properties.borderRadius}px`;
    }
    
    // 形状类型
    const shapeType = element.properties?.shapeType || 'rectangle';
    if (shapeType === 'circle') {
      styles.borderRadius = '50%';
    } else if (shapeType === 'ellipse') {
      styles.borderRadius = '50%';
    } else if (shapeType === 'triangle') {
      styles.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';
      styles.backgroundColor = element.properties?.backgroundColor || '#cccccc';
    } else if (shapeType === 'star') {
      styles.clipPath = 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)';
    } else if (shapeType === 'square') {
      // 保持正方形比例
      const size = Math.min(element.width, element.height);
      styles.width = `${size}px`;
      styles.height = `${size}px`;
    }
  } else if (element.type === 'image') {
    const imageUrl = element.properties?.imageUrl;
    styles.backgroundColor = imageUrl ? 'transparent' : '#f3f4f6';
    styles.border = imageUrl ? 'none' : '2px dashed #9ca3af';
    styles.display = 'flex';
    styles.alignItems = 'center';
    styles.justifyContent = 'center';
    styles.fontSize = '12px';
    styles.color = '#6b7280';
    
    if (imageUrl) {
      styles.backgroundImage = `url(${imageUrl})`;
      styles.backgroundSize = element.properties?.objectFit || 'cover';
      styles.backgroundPosition = 'center';
      styles.backgroundRepeat = 'no-repeat';
    }
    
    // 图片形状
    const imageType = element.properties?.imageType || 'rectangle';
    if (imageType === 'circle') {
      styles.borderRadius = '50%';
    } else if (imageType === 'square') {
      const size = Math.min(element.width, element.height);
      styles.width = `${size}px`;
      styles.height = `${size}px`;
      styles.borderRadius = '4px';
    } else {
      styles.borderRadius = '4px';
    }
  }

  return styles;
};

// 创建模板模态框
const CreateTemplateModal = ({ isOpen, onClose, onCreate }: {
  isOpen: boolean;
  onClose: () => void;
  onCreate: (templateData: any) => Promise<CoverTemplate | null>;
}) => {
  const [formData, setFormData] = useState({
    name: '',
    category: '经典',
    width: 1920,
    height: 1080,
    description: '',
    background: {
      type: 'gradient' as 'solid' | 'gradient' | 'image',
      value: GRADIENT_PRESETS['blue-purple']
    },
    isPublic: false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      alert('请输入模板名称');
      return;
    }

    await onCreate({
      name: formData.name,
      category: formData.category,
      width: formData.width,
      height: formData.height,
      description: formData.description,
      background: formData.background,
      isPublic: formData.isPublic,
      elements: [],
      variables: []
    });

    onClose();
    setFormData({
      name: '',
      category: '经典',
      width: 1920,
      height: 1080,
      description: '',
      background: {
        type: 'gradient' as 'solid' | 'gradient' | 'image',
        value: GRADIENT_PRESETS['blue-purple']
      },
      isPublic: false
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">新建封面模板</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模板名称
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入模板名称"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模板分类
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {CATEGORIES.slice(1).map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              画面宽度
            </label>
            <input
              type="number"
              value={formData.width}
              onChange={(e) => setFormData({ ...formData, width: parseInt(e.target.value) || 1920 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min="100"
              max="4096"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              画面高度
            </label>
            <input
              type="number"
              value={formData.height}
              onChange={(e) => setFormData({ ...formData, height: parseInt(e.target.value) || 1080 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min="100"
              max="4096"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              背景类型
            </label>
            <select
              value={formData.background.type}
              onChange={(e) => setFormData({ 
                ...formData, 
                background: { 
                  ...formData.background, 
                  type: e.target.value as 'solid' | 'gradient' | 'image' 
                } 
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="solid">纯色背景</option>
              <option value="gradient">渐变背景</option>
            </select>
          </div>

          {formData.background.type === 'solid' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                背景颜色
              </label>
              <input
                type="color"
                value={formData.background.value}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  background: { type: 'solid', value: e.target.value } 
                })}
                className="w-full h-10 border border-gray-300 rounded-md"
              />
            </div>
          )}

          {formData.background.type === 'gradient' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                渐变样式
              </label>
              <select
                value={Object.keys(GRADIENT_PRESETS).find(key => 
                  GRADIENT_PRESETS[key as keyof typeof GRADIENT_PRESETS] === formData.background.value
                ) || 'blue-purple'}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  background: { 
                    type: 'gradient', 
                    value: GRADIENT_PRESETS[e.target.value as keyof typeof GRADIENT_PRESETS] 
                  } 
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="blue-purple">蓝紫渐变</option>
                <option value="sunset">日落渐变</option>
                <option value="ocean">海洋渐变</option>
                <option value="forest">森林渐变</option>
                <option value="fire">火焰渐变</option>
              </select>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模板描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入模板描述"
            />
          </div>

          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={formData.isPublic}
                onChange={(e) => setFormData({ ...formData, isPublic: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">公开模板（其他用户可见）</span>
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              创建模板
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default function CoverTemplatesPage() {
  const {
    templates,
    selectedTemplate,
    categories,
    stats,
    availableVariables,
    loading,
    error,
    fetchTemplates,
    fetchTemplateById,
    fetchStats,
    fetchAvailableVariables,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setSelectedTemplate,
    setError,
    clearError
  } = useCoverTemplateStore();

  // 本地状态
  const [selectedElement, setSelectedElement] = useState<CoverElement | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [currentTool, setCurrentTool] = useState<'select' | 'text' | 'shape' | 'image'>('select');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showVariableModal, setShowVariableModal] = useState(false);
  const [canvasBackground, setCanvasBackground] = useState({
    type: 'gradient',
    value: GRADIENT_PRESETS['blue-purple']
  });
  const [elementCounter, setElementCounter] = useState(0);
  
  // 撤销重做功能
  const [historyStack, setHistoryStack] = useState<CoverTemplate[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string>('');
  
  const canvasRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 初始化数据
  useEffect(() => {
    fetchTemplates();
    fetchStats();
    fetchAvailableVariables();
  }, []);

  // 历史记录管理
  const saveToHistory = (template: CoverTemplate) => {
    if (!template) return;
    
    const newHistory = historyStack.slice(0, historyIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(template)));
    
    // 限制历史记录数量
    if (newHistory.length > 50) {
      newHistory.shift();
    } else {
      setHistoryIndex(historyIndex + 1);
    }
    
    setHistoryStack(newHistory);
  };

  // 撤销
  const handleUndo = () => {
    if (historyIndex > 0 && selectedTemplate) {
      const prevTemplate = historyStack[historyIndex - 1];
      setSelectedTemplate(prevTemplate);
      setHistoryIndex(historyIndex - 1);
      setSelectedElement(null);
    }
  };

  // 重做
  const handleRedo = () => {
    if (historyIndex < historyStack.length - 1 && selectedTemplate) {
      const nextTemplate = historyStack[historyIndex + 1];
      setSelectedTemplate(nextTemplate);
      setHistoryIndex(historyIndex + 1);
      setSelectedElement(null);
    }
  };

  // 监听键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        handleUndo();
      } else if ((e.ctrlKey && e.shiftKey && e.key === 'Z') || (e.ctrlKey && e.key === 'y')) {
        e.preventDefault();
        handleRedo();
      } else if (e.key === 'Delete' && selectedElement) {
        e.preventDefault();
        deleteElement(selectedElement.id);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedElement, historyIndex, historyStack]);

  // 层级调整功能
  const moveElementLayer = async (elementId: string, direction: 'up' | 'down' | 'top' | 'bottom') => {
    if (!selectedTemplate || !selectedTemplate.elements) return;

    const elements = [...selectedTemplate.elements];
    const elementIndex = elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    const element = elements[elementIndex];
    
    switch (direction) {
      case 'up':
        if (elementIndex < elements.length - 1) {
          elements[elementIndex] = elements[elementIndex + 1];
          elements[elementIndex + 1] = element;
        }
        break;
      case 'down':
        if (elementIndex > 0) {
          elements[elementIndex] = elements[elementIndex - 1];
          elements[elementIndex - 1] = element;
        }
        break;
      case 'top':
        elements.splice(elementIndex, 1);
        elements.push(element);
        break;
      case 'bottom':
        elements.splice(elementIndex, 1);
        elements.unshift(element);
        break;
    }

    // 重新设置 zIndex
    elements.forEach((el, index) => {
      el.zIndex = index;
    });

    const updatedTemplate = {
      ...selectedTemplate,
      elements
    };

    saveToHistory(selectedTemplate);
    await updateTemplate(selectedTemplate.id, updatedTemplate);
  };

  // 元素对齐功能
  const alignElements = async (alignment: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => {
    if (!selectedTemplate || !selectedElement) return;

    const canvasWidth = 320;
    const canvasHeight = 180;
    let newX = selectedElement.x;
    let newY = selectedElement.y;

    switch (alignment) {
      case 'left':
        newX = 0;
        break;
      case 'center':
        newX = (canvasWidth - selectedElement.width) / 2;
        break;
      case 'right':
        newX = canvasWidth - selectedElement.width;
        break;
      case 'top':
        newY = 0;
        break;
      case 'middle':
        newY = (canvasHeight - selectedElement.height) / 2;
        break;
      case 'bottom':
        newY = canvasHeight - selectedElement.height;
        break;
    }

    saveToHistory(selectedTemplate);
    await updateElement(selectedElement.id, { x: newX, y: newY });
  };

  // 过滤模板
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = !searchTerm || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description && template.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // 选择模板
  // 选择模板
  const selectTemplate = async (template: CoverTemplate) => {
    try {
      // 从后端获取完整的模板数据（包括元素）
      const fullTemplate = await fetchTemplateById(template.id);
      if (fullTemplate) {
        setSelectedTemplate(fullTemplate);
        setSelectedElement(null);
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
      // 回退到使用列表中的模板数据
      setSelectedTemplate(template);
      setSelectedElement(null);
    }
  };

  // 选择元素
  const selectElement = (element: CoverElement) => {
    setSelectedElement(element);
  };

  // 添加元素
  const addElement = async (elementData: Partial<CoverElement>) => {
    if (!selectedTemplate) return;

    // 保存到历史记录
    saveToHistory(selectedTemplate);

    const newElement: CoverElement = {
      id: `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: `${elementData.type}_${elementCounter + 1}`,
      zIndex: (selectedTemplate.elements || []).length,
      opacity: 1,
      rotation: 0,
      properties: {},
      variableBinding: {
        enabled: false,
        variableName: '',
        propertyPath: ''
      },
      ...elementData
    } as CoverElement;

    const updatedTemplate = {
      ...selectedTemplate,
      elements: [...(selectedTemplate.elements || []), newElement]
    };

    await updateTemplate(selectedTemplate.id, updatedTemplate);
    setElementCounter(elementCounter + 1);
  };

  // 更新元素
  const updateElement = async (elementId: string, updates: Partial<CoverElement>) => {
    if (!selectedTemplate) return;

    // 保存到历史记录（仅对重要更改）
    if (updates.x !== undefined || updates.y !== undefined || 
        updates.width !== undefined || updates.height !== undefined ||
        updates.properties !== undefined) {
      saveToHistory(selectedTemplate);
    }

    const updatedElements = (selectedTemplate.elements || []).map(element =>
      element.id === elementId ? { ...element, ...updates } : element
    );

    const updatedTemplate = {
      ...selectedTemplate,
      elements: updatedElements
    };

    await updateTemplate(selectedTemplate.id, updatedTemplate);
  };

  // 删除元素
  const deleteElement = async (elementId: string) => {
    if (!selectedTemplate) return;

    // 保存到历史记录
    saveToHistory(selectedTemplate);

    const updatedElements = (selectedTemplate.elements || []).filter(element => element.id !== elementId);
    
    const updatedTemplate = {
      ...selectedTemplate,
      elements: updatedElements
    };

    await updateTemplate(selectedTemplate.id, updatedTemplate);
    
    if (selectedElement?.id === elementId) {
      setSelectedElement(null);
    }
  };

  // 复制模板
  const duplicateTemplate = async (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;

    const newTemplate = {
      ...template,
      name: `${template.name} (副本)`,
      id: undefined // 让后端生成新ID
    };

    await createTemplate(newTemplate);
    // 刷新统计数据
    await fetchStats();
  };

  // 创建模板的包装函数，用于刷新统计数据
  const handleCreateTemplate = async (templateData: any) => {
    const result = await createTemplate(templateData);
    // 刷新统计数据
    await fetchStats();
    return result;
  };

  // 处理画布点击
  const handleCanvasClick = (e: React.MouseEvent) => {
    if (!selectedTemplate) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left - 50;
    const y = e.clientY - rect.top - 10;

    if (currentTool === 'text') {
      const newElement: Partial<CoverElement> = {
        type: 'text',
        x: Math.max(0, x),
        y: Math.max(0, y),
        width: 200,
        height: 20,
        properties: {
          content: `新文本 ${elementCounter + 1}`,
          fontSize: 16,
          color: '#ffffff',
          textAlign: 'left'
        }
      };
      addElement(newElement);
      setElementCounter(elementCounter + 1);
    } else if (currentTool === 'shape') {
      const newElement: Partial<CoverElement> = {
        type: 'shape',
        x: Math.max(0, x),
        y: Math.max(0, y),
        width: 50,
        height: 50,
        properties: {
          backgroundColor: '#ffffff',
          shapeType: 'rectangle'
        }
      };
      addElement(newElement);
      setElementCounter(elementCounter + 1);
    } else if (currentTool === 'image') {
      const newElement: Partial<CoverElement> = {
        type: 'image',
        x: Math.max(0, x),
        y: Math.max(0, y),
        width: 80,
        height: 60,
        properties: {
          imageType: 'rectangle'
        }
      };
      addElement(newElement);
      setElementCounter(elementCounter + 1);
    }
  };

  // 处理键盘删除
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' && selectedElement) {
        deleteElement(selectedElement.id);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedElement, deleteElement]);

  // 保存模板功能
  const handleSaveTemplate = async () => {
    if (!selectedTemplate) {
      alert('请先选择一个模板');
      return;
    }

    try {
      // 准备要保存的数据，包含完整的elements和background
      const saveData = {
        name: selectedTemplate.name,
        description: selectedTemplate.description || '',
        category: selectedTemplate.category,
        width: selectedTemplate.width,
        height: selectedTemplate.height,
        background: canvasBackground,
        elements: selectedTemplate.elements || [],
        isPublic: selectedTemplate.isPublic,
        metadata: {
          elementCount: selectedTemplate.elements?.length || 0,
          hasVariables: (selectedTemplate.elements || []).some(el => el.variableBinding?.enabled),
          canvasSize: `${selectedTemplate.width}x${selectedTemplate.height}`
        }
      };

      await updateTemplate(selectedTemplate.id, saveData);
      alert('模板保存成功！');
    } catch (error) {
      console.error('保存模板失败:', error);
      alert('保存模板失败，请稍后重试');
    }
  };

  // 导出画布为PNG图片（使用Canvas API的替代方案）
  const handleExportCanvas = async () => {
    if (!canvasRef.current || !selectedTemplate) {
      alert('请先选择一个模板');
      return;
    }

    try {
      // 创建一个临时的canvas元素
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('无法创建画布上下文');
      }

      // 设置画布尺寸
      canvas.width = selectedTemplate.width;
      canvas.height = selectedTemplate.height;

      // 绘制背景
      if (canvasBackground.type === 'solid') {
        ctx.fillStyle = canvasBackground.value;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      } else if (canvasBackground.type === 'gradient') {
        // 简单渐变处理
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      // 绘制元素
      selectedTemplate.elements?.forEach(element => {
        ctx.save();
        
        // 设置透明度
        ctx.globalAlpha = element.opacity || 1;
        
        // 应用变换
        if (element.rotation) {
          const centerX = element.x + element.width / 2;
          const centerY = element.y + element.height / 2;
          ctx.translate(centerX, centerY);
          ctx.rotate((element.rotation * Math.PI) / 180);
          ctx.translate(-centerX, -centerY);
        }

        // 根据元素类型绘制
        if (element.type === 'text') {
          ctx.fillStyle = element.properties?.color || '#000000';
          ctx.font = `${element.properties?.fontSize || 16}px ${element.properties?.fontFamily || 'Arial'}`;
          ctx.fillText(
            element.properties?.content || '文本',
            element.x,
            element.y + (element.properties?.fontSize || 16)
          );
        } else if (element.type === 'shape') {
          ctx.fillStyle = element.properties?.backgroundColor || '#cccccc';
          if (element.properties?.shapeType === 'circle') {
            ctx.beginPath();
            ctx.arc(
              element.x + element.width / 2,
              element.y + element.height / 2,
              Math.min(element.width, element.height) / 2,
              0,
              2 * Math.PI
            );
            ctx.fill();
          } else {
            ctx.fillRect(element.x, element.y, element.width, element.height);
          }
          
          // 绘制边框
          if (element.properties?.borderWidth > 0) {
            ctx.strokeStyle = element.properties?.borderColor || '#000000';
            ctx.lineWidth = element.properties?.borderWidth;
            ctx.strokeRect(element.x, element.y, element.width, element.height);
          }
        }
        
        ctx.restore();
      });

      // 创建下载链接
      const link = document.createElement('a');
      link.download = `${selectedTemplate.name}_cover.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
    } catch (error) {
      console.error('导出图片失败:', error);
      alert('导出图片失败，请稍后重试');
    }
  };

  // 测试生成图片功能
  const handleTestGenerate = async () => {
    if (!selectedTemplate) {
      alert('请先选择一个模板');
      return;
    }

    try {
      // 模拟变量数据用于测试
      const testData = {
        title: '测试标题',
        description: '这是一个测试描述',
        author: '测试作者',
        category: '测试分类'
      };

      // 如果有绑定变量的元素，使用测试数据更新
      const updatedElements = selectedTemplate.elements?.map(element => {
        if (element.variableBinding?.enabled && element.variableBinding.variableName) {
          const variableValue = testData[element.variableBinding.variableName as keyof typeof testData];
          if (variableValue && element.variableBinding.propertyPath) {
            const elementCopy = JSON.parse(JSON.stringify(element));
            // 简单的属性路径设置
            if (element.variableBinding.propertyPath === 'properties.content') {
              elementCopy.properties.content = variableValue;
            }
            return elementCopy;
          }
        }
        return element;
      }) || [];

      // 临时更新元素用于预览
      if (selectedTemplate.elements) {
        setSelectedTemplate({
          ...selectedTemplate,
          elements: updatedElements
        });
        
        // 等待DOM更新后导出
        setTimeout(handleExportCanvas, 100);
      }
    } catch (error) {
      console.error('测试生成失败:', error);
      alert('测试生成失败，请稍后重试');
    }
  };

  // 导入模板
  const handleImportTemplate = () => {
    fileInputRef.current?.click();
  };

  const handleFileImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const templateData = JSON.parse(event.target?.result as string);
          // 调用创建模板API导入
          createTemplate(templateData);
        } catch (error) {
          alert('导入失败，请检查文件格式');
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className="text-xl font-semibold text-gray-900">封面模板管理</h1>
          <button
            onClick={() => window.history.back()}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            ← 返回
          </button>
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-6">
        {/* 页面标题 */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">封面模板设计器</h2>
          <p className="text-gray-600">创建和管理视频封面模板，支持自定义文本、颜色、位置等属性</p>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{stats?.totalTemplates || 0}</div>
            <div className="text-xs text-gray-500 uppercase tracking-wide">总模板数</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{stats?.publicTemplates || 0}</div>
            <div className="text-xs text-gray-500 uppercase tracking-wide">公开模板</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{stats?.privateTemplates || 0}</div>
            <div className="text-xs text-gray-500 uppercase tracking-wide">私有模板</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{templates.filter(t => t.hasVariables).length}</div>
            <div className="text-xs text-gray-500 uppercase tracking-wide">变量模板</div>
          </div>
        </div>

        {/* 主要内容区 */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 mb-8">
          {/* 模板编辑器 */}
          <div className="xl:col-span-3 bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-lg font-medium text-gray-900">模板编辑器</h3>
            </div>

            <div className="flex flex-col min-h-[600px]">
              {/* 工具栏 */}
              <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div className="flex gap-2">
                  {TOOLS.map(tool => {
                    const Icon = tool.icon;
                    return (
                      <button
                        key={tool.id}
                        onClick={() => setCurrentTool(tool.id)}
                        className={`flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                          currentTool === tool.id
                            ? 'bg-blue-600 text-white'
                            : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <Icon className="w-4 h-4" />
                        {tool.name}
                      </button>
                    );
                  })}
                </div>
                <div className="flex gap-2">
                  <button 
                    onClick={handleUndo}
                    disabled={historyIndex <= 0}
                    className={`flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      historyIndex <= 0 
                        ? 'text-gray-400 bg-gray-100 cursor-not-allowed' 
                        : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                    }`}
                    title="撤销 (Ctrl+Z)"
                  >
                    <ArrowUturnLeftIcon className="w-4 h-4" />
                    撤销
                  </button>
                  <button 
                    onClick={handleRedo}
                    disabled={historyIndex >= historyStack.length - 1}
                    className={`flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      historyIndex >= historyStack.length - 1 
                        ? 'text-gray-400 bg-gray-100 cursor-not-allowed' 
                        : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                    }`}
                    title="重做 (Ctrl+Y)"
                  >
                    <ArrowUturnRightIcon className="w-4 h-4" />
                    重做
                  </button>
                  {selectedElement && (
                    <>
                      <div className="h-6 border-l border-gray-300 mx-2"></div>
                      {/* 对齐工具 */}
                      <button 
                        onClick={() => alignElements('left')}
                        className="flex items-center gap-1 px-2 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="左对齐"
                      >
                        ⊞⊣
                      </button>
                      <button 
                        onClick={() => alignElements('center')}
                        className="flex items-center gap-1 px-2 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="水平居中"
                      >
                        ⊞⊡
                      </button>
                      <button 
                        onClick={() => alignElements('right')}
                        className="flex items-center gap-1 px-2 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="右对齐"
                      >
                        ⊞⊢
                      </button>
                      <button 
                        onClick={() => alignElements('top')}
                        className="flex items-center gap-1 px-2 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="顶部对齐"
                      >
                        ⊤
                      </button>
                      <button 
                        onClick={() => alignElements('middle')}
                        className="flex items-center gap-1 px-2 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="垂直居中"
                      >
                        ⊟
                      </button>
                      <button 
                        onClick={() => alignElements('bottom')}
                        className="flex items-center gap-1 px-2 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="底部对齐"
                      >
                        ⊥
                      </button>
                      <div className="h-6 border-l border-gray-300 mx-2"></div>
                      {/* 层级工具 */}
                      <button 
                        onClick={() => moveElementLayer(selectedElement.id, 'top')}
                        className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="置于顶层"
                      >
                        ↑↑
                      </button>
                      <button 
                        onClick={() => moveElementLayer(selectedElement.id, 'up')}
                        className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="上移一层"
                      >
                        ↑
                      </button>
                      <button 
                        onClick={() => moveElementLayer(selectedElement.id, 'down')}
                        className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="下移一层"
                      >
                        ↓
                      </button>
                      <button 
                        onClick={() => moveElementLayer(selectedElement.id, 'bottom')}
                        className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        title="置于底层"
                      >
                        ↓↓
                      </button>
                      <button 
                        onClick={() => deleteElement(selectedElement.id)}
                        className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
                        title="删除元素 (Delete)"
                      >
                        <TrashIcon className="w-4 h-4" />
                        删除
                      </button>
                    </>
                  )}
                  <div className="h-6 border-l border-gray-300 mx-2"></div>
                  <button 
                    onClick={handleTestGenerate}
                    className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <CameraIcon className="w-4 h-4" />
                    测试生成
                  </button>
                  <button 
                    onClick={handleExportCanvas}
                    className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4" />
                    导出PNG
                  </button>
                  <button 
                    onClick={handleSaveTemplate}
                    className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    <CloudArrowUpIcon className="w-4 h-4" />
                    保存
                  </button>
                </div>
              </div>

              {/* 画布容器 */}
              <div className="flex-1 flex items-center justify-center bg-gray-100 p-6">
                {selectedTemplate ? (
                  <div
                    ref={canvasRef}
                    className={`relative w-80 h-45 rounded-lg overflow-hidden shadow-lg ${
                      currentTool !== 'select' ? 'cursor-crosshair' : 'cursor-default'
                    }`}
                    style={{ 
                      background: selectedTemplate.background?.value || canvasBackground.value,
                      width: '320px',
                      height: '180px'
                    }}
                    onClick={handleCanvasClick}
                  >
                    {selectedTemplate.elements && selectedTemplate.elements.map(element => (
                      <CanvasElement
                        key={element.id}
                        element={element}
                        isSelected={selectedElement?.id === element.id}
                        onSelect={() => selectElement(element)}
                        onUpdate={(updates) => updateElement(element.id, updates)}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <div className="w-80 h-45 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                      <div>
                        <p className="mb-2">请选择一个模板开始编辑</p>
                        <button
                          onClick={() => setShowCreateModal(true)}
                          className="text-blue-600 hover:text-blue-700 font-medium"
                        >
                          或者创建新模板
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧面板 */}
          <div className="flex flex-col gap-6">
            {/* 属性面板 */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h3 className="text-lg font-medium text-gray-900">属性设置</h3>
              </div>
              <div className="p-4">
                {selectedElement ? (
                  <>
                    {/* 变量绑定状态 */}
                    <div className="mb-4 p-3 bg-gray-50 rounded-md">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-gray-700">变量绑定</h4>
                          {selectedElement.variableBinding?.enabled ? (
                            <p className="text-xs text-green-600 mt-1">
                              <WifiIcon className="w-3 h-3 inline mr-1" />
                              已绑定到 {selectedElement.variableBinding.variableName}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500 mt-1">未绑定变量</p>
                          )}
                        </div>
                        <button
                          onClick={() => setShowVariableModal(true)}
                          className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                          <LinkIcon className="w-3 h-3" />
                          {selectedElement.variableBinding?.enabled ? '编辑' : '绑定'}
                        </button>
                      </div>
                    </div>
                    
                    <ElementPropertiesPanel
                      element={selectedElement}
                      onUpdate={(updates) => updateElement(selectedElement.id, updates)}
                    />
                  </>
                ) : (
                  <BackgroundPropertiesPanel
                    background={canvasBackground}
                    onUpdate={setCanvasBackground}
                  />
                )}
              </div>
            </div>

            {/* 模板库 */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h3 className="text-lg font-medium text-gray-900">模板库</h3>
              </div>
              
              {/* 分类过滤 */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex flex-wrap gap-2">
                  {['all', ...categories].map((category, index) => (
                    <button
                      key={`category-${category}-${index}`}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                        selectedCategory === category
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {category === 'all' ? '全部' : category}
                    </button>
                  ))}
                </div>
              </div>

              {/* 模板网格 */}
              <div className="p-4 grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
                {filteredTemplates.slice(0, 4).map(template => (
                  <div
                    key={template.id}
                    className="border border-gray-200 rounded-md overflow-hidden cursor-pointer hover:border-blue-500 hover:shadow-sm transition-all"
                    onClick={() => selectTemplate(template)}
                  >
                    <div
                      className="w-full h-20 flex items-center justify-center text-white text-xs font-bold"
                      style={{
                        background: template.background?.value || '#667eea'
                      }}
                    >
                      {template.name}
                    </div>
                    <div className="p-2">
                      <div className="font-medium text-sm text-gray-900 mb-1">{template.name}</div>
                      <div className="text-xs text-gray-500">
                        {template.elementCount}个元素
                        {template.hasVariables && ' · 含变量'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 模板列表 */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">我的模板</h3>
            <div className="flex gap-3">
              <div className="relative">
                <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索模板..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept=".json"
                onChange={handleFileImport}
                className="hidden"
              />
              <button
                onClick={handleImportTemplate}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <ArrowUpTrayIcon className="w-4 h-4" />
                导入模板
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                <PlusIcon className="w-4 h-4" />
                新建模板
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    预览
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    模板名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    元素数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTemplates.map(template => (
                  <tr key={template.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div
                        className="w-15 h-8 rounded border"
                        style={{
                          background: template.background?.value || '#667eea'
                        }}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{template.name}</div>
                        <div className="text-sm text-gray-500">
                          {template.width}×{template.height} · {template.category}
                          {template.hasVariables && ' · 含变量'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {template.createdAt}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                        template.isPublic
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {template.isPublic ? (
                          <CheckCircleIcon className="w-3 h-3" />
                        ) : (
                          <ClockIcon className="w-3 h-3" />
                        )}
                        {template.isPublic ? '公开' : '私有'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {template.elementCount}个元素
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button
                          onClick={() => selectTemplate(template)}
                          className="text-blue-600 hover:text-blue-700 flex items-center gap-1 px-2 py-1 text-xs bg-blue-50 rounded"
                        >
                          <PencilIcon className="w-3 h-3" />
                          编辑
                        </button>
                        <button
                          onClick={() => duplicateTemplate(template.id)}
                          className="text-gray-600 hover:text-gray-700 flex items-center gap-1 px-2 py-1 text-xs bg-gray-50 rounded"
                        >
                          <DocumentDuplicateIcon className="w-3 h-3" />
                          复制
                        </button>
                        <button
                          onClick={() => {
                            if (confirm('确定要删除这个模板吗？')) {
                              deleteTemplate(template.id);
                            }
                          }}
                          className="text-red-600 hover:text-red-700 flex items-center gap-1 px-2 py-1 text-xs bg-red-50 rounded"
                        >
                          <TrashIcon className="w-3 h-3" />
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 创建模板模态框 */}
      <CreateTemplateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreate={handleCreateTemplate}
      />
      
      {/* 变量绑定模态框 */}
      <VariableBindingModal
        isOpen={showVariableModal}
        onClose={() => setShowVariableModal(false)}
        element={selectedElement}
        availableVariables={availableVariables}
        onUpdate={updateElement}
      />
    </div>
  );
}

// 元素属性面板组件
const ElementPropertiesPanel = ({ element, onUpdate }: {
  element: CoverElement;
  onUpdate: (updates: Partial<CoverElement>) => void;
}) => {
  const updateProperty = (propertyPath: string, value: any) => {
    const properties = { ...element.properties };
    
    // 简单的路径设置，支持点号分隔的路径
    const pathParts = propertyPath.split('.');
    let current = properties;
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!current[pathParts[i]]) {
        current[pathParts[i]] = {};
      }
      current = current[pathParts[i]];
    }
    current[pathParts[pathParts.length - 1]] = value;
    
    onUpdate({ properties });
  };

  if (element.type === 'text') {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">文本内容</label>
          <textarea
            value={element.properties?.content || ''}
            onChange={(e) => updateProperty('content', e.target.value)}
            rows={3}
            placeholder="请输入文本内容"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              字体大小: {element.properties?.fontSize || 16}px
            </label>
            <input
              type="range"
              min="12"
              max="72"
              value={element.properties?.fontSize || 16}
              onChange={(e) => updateProperty('fontSize', parseInt(e.target.value))}
              className="w-full"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">字体粗细</label>
            <select
              value={element.properties?.fontWeight || 'normal'}
              onChange={(e) => updateProperty('fontWeight', e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="normal">普通</option>
              <option value="bold">粗体</option>
              <option value="lighter">细体</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">字体家族</label>
          <select
            value={element.properties?.fontFamily || 'Arial'}
            onChange={(e) => updateProperty('fontFamily', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="Arial">Arial</option>
            <option value="Microsoft YaHei">微软雅黑</option>
            <option value="PingFang SC">苹方</option>
            <option value="Helvetica">Helvetica</option>
            <option value="Georgia">Georgia</option>
            <option value="Times New Roman">Times New Roman</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">文字颜色</label>
          <input
            type="color"
            value={element.properties?.color || '#000000'}
            onChange={(e) => updateProperty('color', e.target.value)}
            className="w-full h-10 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">文本对齐</label>
          <div className="grid grid-cols-3 gap-1">
            {['left', 'center', 'right'].map(align => (
              <button
                key={align}
                onClick={() => updateProperty('textAlign', align)}
                className={`px-3 py-2 text-xs border rounded ${
                  element.properties?.textAlign === align
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {align === 'left' ? '左对齐' : align === 'center' ? '居中' : '右对齐'}
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">文本宽度</label>
          <input
            type="number"
            min="50"
            max="280"
            value={element.width || 200}
            onChange={(e) => onUpdate({ width: parseInt(e.target.value) })}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={element.properties?.autoWrap || false}
              onChange={(e) => updateProperty('autoWrap', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700">自动换行</span>
          </label>
        </div>

        <PositionControls element={element} onUpdate={onUpdate} />
      </div>
    );
  }

  if (element.type === 'shape') {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">形状类型</label>
          <select
            value={element.properties?.shapeType || 'rectangle'}
            onChange={(e) => updateProperty('shapeType', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="rectangle">矩形</option>
            <option value="square">正方形</option>
            <option value="circle">圆形</option>
            <option value="ellipse">椭圆</option>
            <option value="triangle">三角形</option>
            <option value="star">星形</option>
          </select>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">宽度</label>
            <input
              type="number"
              min="10"
              max="400"
              value={element.width || 100}
              onChange={(e) => onUpdate({ width: parseInt(e.target.value) })}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">高度</label>
            <input
              type="number"
              min="10"
              max="400"
              value={element.height || 100}
              onChange={(e) => onUpdate({ height: parseInt(e.target.value) })}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">填充颜色</label>
          <input
            type="color"
            value={element.properties?.backgroundColor || '#cccccc'}
            onChange={(e) => updateProperty('backgroundColor', e.target.value)}
            className="w-full h-10 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">边框颜色</label>
          <input
            type="color"
            value={element.properties?.borderColor || '#000000'}
            onChange={(e) => updateProperty('borderColor', e.target.value)}
            className="w-full h-10 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            边框宽度: {element.properties?.borderWidth || 0}px
          </label>
          <input
            type="range"
            min="0"
            max="10"
            value={element.properties?.borderWidth || 0}
            onChange={(e) => updateProperty('borderWidth', parseInt(e.target.value))}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">边框样式</label>
          <select
            value={element.properties?.borderStyle || 'solid'}
            onChange={(e) => updateProperty('borderStyle', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="solid">实线</option>
            <option value="dashed">虚线</option>
            <option value="dotted">点线</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            圆角半径: {element.properties?.borderRadius || 0}px
          </label>
          <input
            type="range"
            min="0"
            max="50"
            value={element.properties?.borderRadius || 0}
            onChange={(e) => updateProperty('borderRadius', parseInt(e.target.value))}
            className="w-full"
          />
        </div>

        <PositionControls element={element} onUpdate={onUpdate} />
      </div>
    );
  }

  if (element.type === 'image') {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">图片形状</label>
          <select
            value={element.properties?.imageType || 'rectangle'}
            onChange={(e) => updateProperty('imageType', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="rectangle">矩形</option>
            <option value="square">正方形</option>
            <option value="circle">圆形</option>
          </select>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">宽度</label>
            <input
              type="number"
              min="20"
              max="400"
              value={element.width || 120}
              onChange={(e) => onUpdate({ width: parseInt(e.target.value) })}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">高度</label>
            <input
              type="number"
              min="20"
              max="400"
              value={element.height || 90}
              onChange={(e) => onUpdate({ height: parseInt(e.target.value) })}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">图片设置</label>
          <div className="space-y-2">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (event) => {
                    updateProperty('imageUrl', event.target?.result as string);
                  };
                  reader.readAsDataURL(file);
                }
              }}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            
            <input
              type="url"
              placeholder="或输入图片URL"
              value={element.properties?.imageUrl || ''}
              onChange={(e) => updateProperty('imageUrl', e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">图片适应方式</label>
          <select
            value={element.properties?.objectFit || 'cover'}
            onChange={(e) => updateProperty('objectFit', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="cover">覆盖</option>
            <option value="contain">包含</option>
            <option value="fill">拉伸</option>
            <option value="scale-down">缩小</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">替代文本</label>
          <input
            type="text"
            placeholder="图片描述（用于无障碍访问）"
            value={element.properties?.alt || ''}
            onChange={(e) => updateProperty('alt', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <PositionControls element={element} onUpdate={onUpdate} />
      </div>
    );
  }

  return null;
};

// 位置控制组件
const PositionControls = ({ element, onUpdate }: {
  element: CoverElement;
  onUpdate: (updates: Partial<CoverElement>) => void;
}) => (
  <div>
    <label className="block text-sm font-medium text-gray-700 mb-1">位置调整</label>
    <div className="grid grid-cols-2 gap-2">
      <input
        type="number"
        placeholder="X坐标"
        value={element.x || 0}
        onChange={(e) => onUpdate({ x: parseInt(e.target.value) || 0 })}
        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <input
        type="number"
        placeholder="Y坐标"
        value={element.y || 0}
        onChange={(e) => onUpdate({ y: parseInt(e.target.value) || 0 })}
        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div className="grid grid-cols-2 gap-2 mt-2">
      <div>
        <label className="block text-xs font-medium text-gray-600 mb-1">透明度</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={element.opacity || 1}
          onChange={(e) => onUpdate({ opacity: parseFloat(e.target.value) })}
          className="w-full"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-600 mb-1">旋转角度</label>
        <input
          type="range"
          min="0"
          max="360"
          value={element.rotation || 0}
          onChange={(e) => onUpdate({ rotation: parseInt(e.target.value) })}
          className="w-full"
        />
      </div>
    </div>
  </div>
);

// 背景属性面板组件
const BackgroundPropertiesPanel = ({ background, onUpdate }: {
  background: any;
  onUpdate: (background: any) => void;
}) => (
  <div className="space-y-4">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">背景类型</label>
      <select
        value={background.type}
        onChange={(e) => onUpdate({ ...background, type: e.target.value })}
        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="solid">纯色背景</option>
        <option value="gradient">渐变背景</option>
        <option value="image">图片背景</option>
      </select>
    </div>

    {background.type === 'solid' && (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">背景颜色</label>
        <input
          type="color"
          value={background.value}
          onChange={(e) => onUpdate({ type: 'solid', value: e.target.value })}
          className="w-full h-10 border border-gray-300 rounded-md"
        />
      </div>
    )}

    {background.type === 'gradient' && (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">渐变样式</label>
        <select
          value={Object.keys(GRADIENT_PRESETS).find(key => GRADIENT_PRESETS[key as keyof typeof GRADIENT_PRESETS] === background.value) || 'blue-purple'}
          onChange={(e) => onUpdate({ type: 'gradient', value: GRADIENT_PRESETS[e.target.value as keyof typeof GRADIENT_PRESETS] })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="blue-purple">蓝紫渐变</option>
          <option value="sunset">日落渐变</option>
          <option value="ocean">海洋渐变</option>
          <option value="forest">森林渐变</option>
          <option value="fire">火焰渐变</option>
        </select>
      </div>
    )}

    {background.type === 'image' && (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">背景图片</label>
        <input
          type="file"
          accept="image/*"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              const reader = new FileReader();
              reader.onload = (event) => {
                onUpdate({ type: 'image', value: `url(${event.target?.result})` });
              };
              reader.readAsDataURL(file);
            }
          }}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    )}

    <button
      className="w-full mt-4 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
      onClick={() => {
        console.log('应用背景更改');
      }}
    >
      应用更改
    </button>
  </div>
);

// 变量绑定模态框
const VariableBindingModal = ({ isOpen, onClose, element, availableVariables, onUpdate }: {
  isOpen: boolean;
  onClose: () => void;
  element: CoverElement | null;
  availableVariables: any[];
  onUpdate: (elementId: string, updates: Partial<CoverElement>) => void;
}) => {
  const [bindingData, setBindingData] = useState({
    enabled: false,
    variableName: '',
    propertyPath: ''
  });

  useEffect(() => {
    if (element?.variableBinding) {
      setBindingData(element.variableBinding);
    } else {
      setBindingData({
        enabled: false,
        variableName: '',
        propertyPath: ''
      });
    }
  }, [element]);

  const handleSubmit = () => {
    if (!element) return;
    
    onUpdate(element.id, {
      variableBinding: bindingData
    });
    
    onClose();
  };

  const getAvailableProperties = () => {
    if (!element) return [];
    
    switch (element.type) {
      case 'text':
        return [
          { path: 'properties.content', label: '文本内容' },
          { path: 'properties.color', label: '文字颜色' },
          { path: 'properties.fontSize', label: '字体大小' }
        ];
      case 'image':
        return [
          { path: 'properties.imageUrl', label: '图片地址' },
          { path: 'properties.alt', label: '图片描述' }
        ];
      case 'shape':
        return [
          { path: 'properties.backgroundColor', label: '背景颜色' },
          { path: 'properties.borderColor', label: '边框颜色' }
        ];
      default:
        return [];
    }
  };

  if (!isOpen || !element) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">
            变量绑定 - {element.name}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          {/* 启用变量绑定 */}
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={bindingData.enabled}
                onChange={(e) => setBindingData({ ...bindingData, enabled: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">启用变量绑定</span>
            </label>
            <p className="text-xs text-gray-500 mt-1">
              启用后，该元素的属性将根据绑定的变量动态更新
            </p>
          </div>

          {bindingData.enabled && (
            <>
              {/* 选择变量 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  绑定变量
                </label>
                <select
                  value={bindingData.variableName}
                  onChange={(e) => setBindingData({ ...bindingData, variableName: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择变量</option>
                  {availableVariables.map(variable => (
                    <option key={variable.name} value={variable.name}>
                      {variable.name} - {variable.description}
                    </option>
                  ))}
                </select>
              </div>

              {/* 选择属性 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  绑定属性
                </label>
                <select
                  value={bindingData.propertyPath}
                  onChange={(e) => setBindingData({ ...bindingData, propertyPath: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择属性</option>
                  {getAvailableProperties().map(property => (
                    <option key={property.path} value={property.path}>
                      {property.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* 预览效果 */}
              {bindingData.variableName && bindingData.propertyPath && (
                <div className="bg-blue-50 p-3 rounded-md">
                  <h4 className="text-sm font-medium text-blue-900 mb-1">绑定预览</h4>
                  <p className="text-xs text-blue-700">
                    变量 <code className="bg-blue-100 px-1 rounded">{bindingData.variableName}</code> 
                    的值将应用到元素的 <code className="bg-blue-100 px-1 rounded">{bindingData.propertyPath}</code> 属性
                  </p>
                </div>
              )}
            </>
          )}
        </div>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            保存绑定
          </button>
        </div>
      </div>
    </div>
  );
};
