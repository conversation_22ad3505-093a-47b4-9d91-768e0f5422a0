"""
Reddit Story Video Generator Backend API
Main FastAPI application entry point
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse, Response
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import uvicorn
from loguru import logger

# 导入应用初始化服务
from src.services.app_init_service import app_init_service

# --- Loguru Configuration ---
# 确保在应用启动时配置好日志记录器
# 移除了默认的处理器，并添加了一个新的，级别为DEBUG
logger.remove()
logger.add(sys.stderr, level="DEBUG")

# 配置标准logging模块的日志也发送到loguru
import logging

# ===== 全局禁用 SQLAlchemy 日志输出 =====
# 这是最强力的禁用方式，在应用启动时就设置
logging.getLogger('sqlalchemy').disabled = True
logging.getLogger('sqlalchemy.engine').disabled = True
logging.getLogger('sqlalchemy.pool').disabled = True
logging.getLogger('sqlalchemy.dialects').disabled = True
logging.getLogger('sqlalchemy.orm').disabled = True

# 遍历所有已存在的logger并禁用SQLAlchemy相关的
for name in list(logging.Logger.manager.loggerDict.keys()):
    if 'sqlalchemy' in name.lower():
        logging.getLogger(name).disabled = True
        logging.getLogger(name).setLevel(logging.CRITICAL)

# 设置根级别的sqlalchemy日志为CRITICAL
logging.getLogger('sqlalchemy').setLevel(logging.CRITICAL)
# ===== SQLAlchemy 日志禁用结束 =====
class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists.
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message.
        frame, depth = sys._getframe(6), 6
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

# 设置所有标准logging的日志级别为INFO，并使用InterceptHandler
logging.basicConfig(handlers=[InterceptHandler()], level=logging.INFO, force=True)
# --------------------------

from src.core.config import get_settings
from src.api.routes import api_router
from src.core.database import init_db


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    
    # Startup
    logger.info("Starting Reddit Story Video Generator Backend...")
    settings = get_settings()
    
    # 确保必要的目录存在
    try:
        settings.ensure_directories()
        logger.info("Directory structure verified")
    except Exception as e:
        logger.error(f"Directory setup failed: {e}")
    
    # Initialize database
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
    
    # 启动任务队列处理器
    try:
        from src.core.database import get_session_maker
        from src.services.video_generation_service import VideoGenerationService
        from src.core.services import set_video_generation_service
        
        session_maker = get_session_maker()
        task_service = VideoGenerationService(session_maker)
        await task_service.start_task_queue()
        
        # 设置全局服务实例
        set_video_generation_service(task_service)
        
        # 保存到app.state以便在关闭时停止
        app.state.task_service = task_service
        
        logger.info("任务队列处理器启动成功")
    except Exception as e:
        logger.error(f"任务队列处理器启动失败: {e}")
    
    # 执行应用初始化
    try:
        app_init_service.startup()
        logger.info("应用初始化成功")
    except Exception as e:
        logger.error(f"应用初始化失败: {e}")
    
    logger.info(f"Backend started successfully in {settings.environment} mode")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Reddit Story Video Generator Backend...")
    
    # 停止任务队列处理器
    try:
        if hasattr(app.state, 'task_service'):
            await app.state.task_service.stop_task_queue()
            logger.info("任务队列处理器已停止")
    except Exception as e:
        logger.error(f"停止任务队列处理器时出错: {e}")
    
    logger.info("Backend shutdown complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down backend...")


# Create FastAPI application
app = FastAPI(
    title="Reddit Story Video Generator API",
    description="Backend API for generating Reddit story videos with AI",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Get settings for configuration
settings = get_settings()

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    logger.error(f"Global exception handler caught: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc),
            "path": str(request.url.path)
        }
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "reddit-story-generator-backend",
        "version": "0.1.0"
    }


# Include API routes
app.include_router(api_router, prefix="/api")

# 添加静态文件服务 - 用于提供模板图片资源
templates_dir = backend_dir / "templates"
if templates_dir.exists():
    app.mount("/templates", StaticFiles(directory=str(templates_dir)), name="templates")
    logger.info(f"静态文件服务已启用: /templates -> {templates_dir}")
else:
    logger.warning(f"模板目录不存在: {templates_dir}")

# 添加前端静态文件服务 - 用于EXE部署
frontend_static_dir = settings.resolved_frontend_static_dir
if frontend_static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_static_dir)), name="frontend")
    logger.info(f"前端静态文件服务已启用: /static -> {frontend_static_dir}")
    
    # 提供 index.html 作为根路径（SPA支持）
    @app.get("/", include_in_schema=False)
    async def serve_frontend():
        index_file = frontend_static_dir / "index.html"
        if index_file.exists():
            with open(index_file, 'r', encoding='utf-8') as f:
                return Response(content=f.read(), media_type="text/html")
        return {"message": "Reddit Story Video Generator API", "docs": "/docs"}
    
    # 处理 SPA 路由回退
    @app.get("/{path:path}", include_in_schema=False)
    async def serve_spa_routes(path: str):
        # 如果是API路径，不处理
        if path.startswith("api/") or path.startswith("docs") or path.startswith("redoc"):
            return JSONResponse({"error": "Not found"}, status_code=404)
        
        # 尝试提供静态文件
        file_path = frontend_static_dir / path
        if file_path.exists() and file_path.is_file():
            # 根据文件扩展名确定 MIME 类型
            if path.endswith('.js'):
                media_type = "application/javascript"
            elif path.endswith('.css'):
                media_type = "text/css"
            elif path.endswith('.html'):
                media_type = "text/html"
            else:
                media_type = "application/octet-stream"
            
            with open(file_path, 'rb') as f:
                return Response(content=f.read(), media_type=media_type)
        
        # 回退到 index.html（SPA路由）
        index_file = frontend_static_dir / "index.html"
        if index_file.exists():
            with open(index_file, 'r', encoding='utf-8') as f:
                return Response(content=f.read(), media_type="text/html")
        
        return JSONResponse({"error": "Not found"}, status_code=404)
else:
    logger.warning(f"前端静态文件目录不存在: {frontend_static_dir}")
    
    # 如果没有前端文件，提供简单的API信息页面
    @app.get("/", include_in_schema=False)
    async def api_info():
        return {
            "message": "Reddit Story Video Generator API", 
            "docs": "/docs",
            "redoc": "/redoc",
            "version": "0.1.0"
        }


if __name__ == "__main__":
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Reddit Story Video Generator API Server')
    parser.add_argument('--host', default=None, help='Host to bind to')
    parser.add_argument('--port', type=int, default=None, help='Port to bind to')
    parser.add_argument('--log-level', default=None, help='Log level')
    args = parser.parse_args()
    
    # Development server
    settings = get_settings()
    
    # 检查是否在打包的exe环境中
    is_frozen = getattr(sys, 'frozen', False)
    
    # 在exe环境中禁用reload功能
    enable_reload = (settings.environment == "development" and not is_frozen)
    
    # 使用命令行参数覆盖配置
    host = args.host if args.host else settings.api_host
    port = args.port if args.port else settings.api_port
    log_level = args.log_level if args.log_level else settings.log_level
    
    print(f"启动服务器: http://{host}:{port}")
    print(f"环境: {'EXE' if is_frozen else '开发'}")
    print(f"重载: {'禁用' if not enable_reload else '启用'}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=enable_reload,
        log_level=log_level.lower()
    )
