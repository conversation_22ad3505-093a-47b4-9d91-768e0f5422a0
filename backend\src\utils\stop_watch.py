#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Python计时器工具
作者: GitHub Copilot
功能:
- 创建计时器实例并记录起始时间
- 支持多次stop记录时间戳
- 打印各阶段时间差（毫秒为单位）
"""

import time
from typing import List, Tuple, Optional
from datetime import datetime


class StopWatch:
    """计时器类"""
    
    def __init__(self, name: str):
        """
        初始化计时器
        
        Args:
            name: 计时器名称
        """
        self.name = name
        self.start_time = time.time()
        self.timestamps: List[Tuple[float, str]] = []
        print(f"⏱️  计时器 '{self.name}' 已启动")
    
    def stop(self, node_name: str = "") -> float:
        """
        记录当前时间戳
        
        Args:
            node_name: 节点名称，默认为空
            
        Returns:
            float: 当前时间戳
        """
        current_time = time.time()
        
        # 如果没有提供节点名称，自动生成
        if not node_name:
            node_name = f"节点{len(self.timestamps) + 1}"
        
        self.timestamps.append((current_time, node_name))
        print(f"📍 记录时间点: {node_name}")
        
        return current_time
    
    def print(self) -> None:
        """打印时间差序列（毫秒为单位）"""
        if not self.timestamps:
            print(f"⚠️  计时器 '{self.name}' 暂无记录")
            return
        
        print(f"\n📊 计时器 '{self.name}' 结果:")
        print("=" * 50)
        
        # 第一个时间差：第一个stop时间 - 启动时间
        first_timestamp, first_name = self.timestamps[0]
        first_duration = (first_timestamp - self.start_time) * 1000  # 转换为毫秒
        
        result_parts = [f"【{first_name}】{first_duration:.2f}ms"]
        
        # 后续时间差：当前stop时间 - 上一个stop时间
        for i in range(1, len(self.timestamps)):
            current_timestamp, current_name = self.timestamps[i]
            previous_timestamp, _ = self.timestamps[i - 1]
            
            duration = (current_timestamp - previous_timestamp) * 1000  # 转换为毫秒
            result_parts.append(f"【{current_name}】{duration:.2f}ms")
        
        # 打印结果
        result_str = " / ".join(result_parts)
        print(result_str)
        
        # 打印总计时间
        total_time = (self.timestamps[-1][0] - self.start_time) * 1000
        print(f"\n⏱️  总计时间: {total_time:.2f}ms")
        print("=" * 50)
    
    def get_total_time(self) -> float:
        """
        获取总计时间（毫秒）
        
        Returns:
            float: 总计时间，如果没有记录则返回从启动到现在的时间
        """
        if not self.timestamps:
            return (time.time() - self.start_time) * 1000
        
        return (self.timestamps[-1][0] - self.start_time) * 1000
    
    def get_stage_times(self) -> List[Tuple[str, float]]:
        """
        获取各阶段时间列表
        
        Returns:
            List[Tuple[str, float]]: 包含(节点名称, 时间差ms)的列表
        """
        if not self.timestamps:
            return []
        
        stage_times = []
        
        # 第一个阶段
        first_timestamp, first_name = self.timestamps[0]
        first_duration = (first_timestamp - self.start_time) * 1000
        stage_times.append((first_name, first_duration))
        
        # 后续阶段
        for i in range(1, len(self.timestamps)):
            current_timestamp, current_name = self.timestamps[i]
            previous_timestamp, _ = self.timestamps[i - 1]
            duration = (current_timestamp - previous_timestamp) * 1000
            stage_times.append((current_name, duration))
        
        return stage_times
    
    def reset(self) -> None:
        """重置计时器"""
        self.start_time = time.time()
        self.timestamps.clear()
        print(f"🔄 计时器 '{self.name}' 已重置")


def demo():
    """演示功能"""
    print("🚀 StopWatch 计时器演示\n")
    
    # 创建计时器实例
    timer = StopWatch("测试计时")
    
    # 模拟一些操作
    print("\n💤 模拟操作1 (0.5秒)...")
    time.sleep(0.5)
    timer.stop("计时阶段1")
    
    print("\n💤 模拟操作2 (0.3秒)...")
    time.sleep(0.3)
    timer.stop("计时阶段2")
    
    print("\n💤 模拟操作3 (0.2秒)...")
    time.sleep(0.2)
    timer.stop("计时阶段3")
    
    # 打印结果
    timer.print()
    
    print("\n" + "="*60)
    print("📝 使用示例:")
    print("""
# 创建计时器
timer = StopWatch("我的任务")

# 记录时间点
timer.stop("准备阶段")
timer.stop("执行阶段")
timer.stop("完成阶段")

# 打印结果
timer.print()

# 获取各阶段时间
stage_times = timer.get_stage_times()
for name, duration in stage_times:
    print(f"{name}: {duration:.2f}ms")
""")


if __name__ == "__main__":
    demo()
