"""
简单的API测试脚本
"""

import requests
import json

def test_backend_api():
    """测试后端API是否可用"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试后端API可用性")
    print("=" * 40)
    
    # 1. 测试健康检查
    print("1. 测试后端健康检查...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {data}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   ❌ 后端健康检查失败: {e}")
        return False
    
    # 2. 测试API健康检查
    print("\n2. 测试API健康检查...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {data}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   ❌ API健康检查失败: {e}")
        return False
    
    # 3. 测试封面模板列表接口
    print("\n3. 测试封面模板列表接口...")
    try:
        response = requests.get(f"{base_url}/api/cover-templates", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   模板数量: {len(data.get('data', []))}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   ❌ 封面模板列表接口失败: {e}")
        return False
    
    # 4. 测试创建封面模板接口
    print("\n4. 测试创建封面模板接口...")
    template_data = {
        "name": "测试模板API",
        "category": "测试",
        "description": "API测试模板",
        "elements": [],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/cover-templates",
            json=template_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 创建成功: {data}")
            template_id = data.get('data', {}).get('id')
            
            # 测试删除刚创建的模板
            if template_id:
                print(f"\n5. 清理测试模板 (ID: {template_id})...")
                try:
                    delete_response = requests.delete(f"{base_url}/api/cover-templates/{template_id}")
                    print(f"   删除状态码: {delete_response.status_code}")
                except Exception as e:
                    print(f"   删除失败: {e}")
            
            return True
        else:
            print(f"   ❌ 创建失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 创建请求失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试后端API")
    print("请确保后端服务正在运行: python backend/main.py")
    print()
    
    success = test_backend_api()
    
    if success:
        print("\n✅ 后端API测试全部通过!")
        print("💡 如果前端仍然失败，请检查浏览器开发者工具的网络请求")
    else:
        print("\n❌ 后端API测试失败!")
        print("🔧 请检查:")
        print("   1. 后端服务是否启动: python backend/main.py")
        print("   2. 端口8000是否被占用")
        print("   3. 数据库连接是否正常")
