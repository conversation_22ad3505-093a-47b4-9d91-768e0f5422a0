"""
视频素材管理API
"""

from fastapi import APIRouter, Depends, HTTPException, Query, File, UploadFile, Form, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import uuid4
import os
import shutil
from pathlib import Path
import mimetypes
import subprocess
import json
import cv2

from ..core.database import get_db
from ..core.responses import success_response, error_response
from ..models.resources import VideoMaterial
from ..schemas.resources import (
    VideoMaterialCreate,
    VideoMaterialUpdate,
    VideoMaterialResponse,
    VideoMaterialQuery,
    BulkVideoMaterialResponse
)

router = APIRouter(prefix="/video-materials", tags=["video-materials"])

# 上传目录配置
UPLOAD_DIR = Path("uploads/video_materials")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 支持的文件类型 - 只支持视频格式
SUPPORTED_VIDEO_TYPES = {
    '.mp4': 'video/mp4',
    '.mov': 'video/quicktime',
    '.avi': 'video/x-msvideo',
    '.webm': 'video/webm',
    '.mkv': 'video/x-matroska'
}

# 所有支持的文件类型（只有视频）
ALL_SUPPORTED_TYPES = SUPPORTED_VIDEO_TYPES

def get_safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    # 移除路径分隔符和其他危险字符
    safe_filename = "".join(c for c in filename if c.isalnum() or c in "._-")
    # 限制长度
    if len(safe_filename) > 255:
        name, ext = os.path.splitext(safe_filename)
        safe_filename = name[:255-len(ext)] + ext
    return safe_filename

def get_video_info(file_path: str) -> dict:
    """使用ffprobe获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams',
            str(file_path)
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            raise Exception(f"ffprobe failed: {result.stderr}")
        
        data = json.loads(result.stdout)
        
        # 查找视频流
        video_stream = None
        for stream in data.get('streams', []):
            if stream.get('codec_type') == 'video':
                video_stream = stream
                break
        
        if not video_stream:
            raise Exception("No video stream found")
        
        format_info = data.get('format', {})
        
        return {
            'duration': float(format_info.get('duration', 0)),
            'width': int(video_stream.get('width', 0)),
            'height': int(video_stream.get('height', 0)),
            'frame_rate': eval(video_stream.get('avg_frame_rate', '0/1')),
            'bitrate': int(format_info.get('bit_rate', 0)),
            'format': format_info.get('format_name', '').split(',')[0]
        }
    except Exception as e:
        # 如果ffprobe失败，尝试使用OpenCV
        try:
            cap = cv2.VideoCapture(str(file_path))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            cap.release()
            
            return {
                'duration': duration,
                'width': width,
                'height': height,
                'frame_rate': fps,
                'bitrate': 0,
                'format': os.path.splitext(file_path)[1][1:]
            }
        except Exception as cv_error:
            raise Exception(f"Failed to get video info: {str(e)}, OpenCV error: {str(cv_error)}")

def generate_thumbnail(file_path: str, thumbnail_path: str) -> bool:
    """生成视频缩略图"""
    try:
        # 使用ffmpeg生成视频缩略图
        cmd = [
            'ffmpeg', '-i', str(file_path), '-ss', '00:00:01.000', '-vframes', '1',
            '-f', 'image2', '-y', str(thumbnail_path)
        ]
        result = subprocess.run(cmd, capture_output=True, timeout=30)
        return result.returncode == 0
    except Exception:
        return False

@router.get("/", response_model=List[VideoMaterialResponse])
async def get_video_materials(
    query: VideoMaterialQuery = Depends(),
    db: Session = Depends(get_db)
):
    """获取视频素材列表"""
    try:
        db_query = db.query(VideoMaterial).filter(VideoMaterial.is_deleted == False)
        
        # 分类过滤
        if query.category:
            db_query = db_query.filter(VideoMaterial.category == query.category)
        
        # 搜索过滤
        if query.search:
            db_query = db_query.filter(
                VideoMaterial.name.contains(query.search)
            )
        
        # 分页
        if query.skip is not None:
            db_query = db_query.offset(query.skip)
        if query.limit is not None:
            db_query = db_query.limit(query.limit)
        
        video_materials = db_query.all()
        
        # 转换为前端格式
        result = []
        for material in video_materials:
            frontend_data = material.to_frontend_format()
            result.append(VideoMaterialResponse(**frontend_data))
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取视频素材列表失败: {str(e)}"
        )

@router.post("/", response_model=VideoMaterialResponse)
async def create_video_material(
    material_data: VideoMaterialCreate,
    db: Session = Depends(get_db)
):
    """创建视频素材"""
    try:        # 创建新视频素材
        db_material = VideoMaterial(
            id=str(uuid4()),
            name=material_data.name,
            file_path=material_data.file_path,
            duration=material_data.duration,
            category=material_data.category,
            resolution=material_data.resolution,
            tags=material_data.tags,
            is_built_in=material_data.is_built_in,
            file_size=material_data.file_size,
            format=material_data.format,
            frame_rate=material_data.frame_rate,
            bitrate=material_data.bitrate,
            thumbnail_path=material_data.thumbnail_path
        )
        
        db.add(db_material)
        db.commit()
        db.refresh(db_material)
        
        # 转换为前端格式
        frontend_data = db_material.to_frontend_format()
        return VideoMaterialResponse(**frontend_data)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建视频素材失败: {str(e)}"
        )

@router.get("/{material_id}", response_model=VideoMaterialResponse)
async def get_video_material(
    material_id: str,
    db: Session = Depends(get_db)
):
    """获取单个视频素材"""
    try:
        db_material = db.query(VideoMaterial).filter(VideoMaterial.id == material_id).first()
        if not db_material:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频素材未找到"
            )
        
        # 转换为前端格式
        frontend_data = db_material.to_frontend_format()
        return VideoMaterialResponse(**frontend_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取视频素材失败: {str(e)}"
        )

@router.put("/{material_id}", response_model=VideoMaterialResponse)
async def update_video_material(
    material_id: str,
    material_data: VideoMaterialUpdate,
    db: Session = Depends(get_db)
):
    """更新视频素材"""
    try:
        db_material = db.query(VideoMaterial).filter(VideoMaterial.id == material_id).first()
        if not db_material:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频素材未找到"
            )
        
        # 更新字段
        update_data = material_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == "filePath":
                setattr(db_material, "file_path", value)
            elif field == "isBuiltIn":
                setattr(db_material, "is_built_in", value)
            elif field == "metadata" and value:
                # 更新元数据字段
                if "fileSize" in value:
                    setattr(db_material, "file_size", value["fileSize"])
                if "format" in value:
                    setattr(db_material, "format", value["format"])
                if "frameRate" in value:
                    setattr(db_material, "frame_rate", value["frameRate"])
                if "bitrate" in value:
                    setattr(db_material, "bitrate", value["bitrate"])
                if "thumbnailPath" in value:
                    setattr(db_material, "thumbnail_path", value["thumbnailPath"])
            else:
                setattr(db_material, field, value)
        
        db.commit()
        db.refresh(db_material)
        
        # 转换为前端格式
        frontend_data = db_material.to_frontend_format()
        return VideoMaterialResponse(**frontend_data)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新视频素材失败: {str(e)}"
        )

@router.delete("/{material_id}")
async def delete_video_material(
    material_id: str,
    db: Session = Depends(get_db)
):
    """删除视频素材"""
    try:
        db_material = db.query(VideoMaterial).filter(VideoMaterial.id == material_id).first()
        if not db_material:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频素材未找到"
            )
        
        db.delete(db_material)
        db.commit()
        
        return success_response("视频素材删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除视频素材失败: {str(e)}"
        )

@router.post("/bulk", response_model=BulkVideoMaterialResponse)
async def bulk_create_video_materials(
    materials_data: List[VideoMaterialCreate],
    db: Session = Depends(get_db)
):
    """批量创建视频素材"""
    try:
        created_materials = []
        failed_materials = []
        
        for material_data in materials_data:
            try:
                db_material = VideoMaterial(
                    id=str(uuid4()),
                    name=material_data.name,
                    file_path=material_data.file_path,
                    duration=material_data.duration,
                    category=material_data.category,
                    resolution=material_data.resolution,
                    tags=material_data.tags,
                    is_built_in=material_data.is_built_in,
                    file_size=material_data.file_size,
                    format=material_data.format,
                    frame_rate=material_data.frame_rate,
                    bitrate=material_data.bitrate,
                    thumbnail_path=material_data.thumbnail_path
                )
                
                db.add(db_material)
                db.flush()  # 获取生成的ID但不提交
                
                frontend_data = db_material.to_frontend_format()
                created_materials.append(VideoMaterialResponse(**frontend_data))
                
            except Exception as e:
                failed_materials.append({
                    "name": material_data.name,
                    "error": str(e)
                })
        
        db.commit()
        
        return BulkVideoMaterialResponse(
            success=created_materials,
            failed=failed_materials,
            total=len(materials_data),
            success_count=len(created_materials),
            failed_count=len(failed_materials)
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建视频素材失败: {str(e)}"
        )

@router.delete("/bulk")
async def bulk_delete_video_materials(
    material_ids: List[str] = Query(..., description="要删除的视频素材ID列表"),
    db: Session = Depends(get_db)
):
    """批量删除视频素材"""
    try:
        deleted_count = db.query(VideoMaterial).filter(
            VideoMaterial.id.in_(material_ids)
        ).delete(synchronize_session=False)
        
        db.commit()
        
        return success_response(f"成功删除{deleted_count}个视频素材")
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除视频素材失败: {str(e)}"
        )

@router.post("/upload")
async def upload_video_file(
    file: UploadFile = File(...),
    category: str = Form("general"),
    tags: str = Form(""),
    db: Session = Depends(get_db)
):
    """上传视频/图片文件"""
    print(f"🟠 后端接收上传请求: 文件='{file.filename}', 分类='{category}', 标签='{tags}'")
    
    # 检查category是否为空
    if not category or category.strip() == "":
        print(f"🔴 警告: category参数为空，使用默认值")
        category = "general"
    else:
        category = category.strip()
    
    file_path = None
    thumbnail_path = None
    
    try:
        # 验证文件类型
        file_ext = os.path.splitext(file.filename or "")[1].lower()
        if file_ext not in ALL_SUPPORTED_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型: {file_ext}"
            )
        
        # 生成安全的文件名
        safe_filename = get_safe_filename(file.filename or "unknown")
        unique_filename = f"{uuid4().hex}_{safe_filename}"
        file_path = UPLOAD_DIR / unique_filename
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        file_size = os.path.getsize(file_path)
        
        # 获取视频文件信息
        file_info = get_video_info(str(file_path))
        
        # 生成缩略图
        thumbnail_filename = f"thumb_{unique_filename}.jpg"
        thumbnail_path = UPLOAD_DIR / thumbnail_filename
        thumbnail_generated = generate_thumbnail(str(file_path), str(thumbnail_path))
        
        # 解析标签
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
        
        print(f"🟠 准备创建数据库记录，分类: '{category}'")
        
        # 创建数据库记录
        db_material = VideoMaterial(
            id=str(uuid4()),
            name=safe_filename,
            file_path=str(file_path),
            duration=file_info['duration'],
            category=category,
            resolution=f"{file_info['width']}x{file_info['height']}",
            tags=tag_list,
            is_built_in=False,
            file_size=file_size,
            format=file_info['format'],
            frame_rate=file_info['frame_rate'],
            bitrate=file_info['bitrate'],
            thumbnail_path=str(thumbnail_path) if thumbnail_generated else None
        )
        
        print(f"🟠 数据库记录创建成功: ID={db_material.id}, 分类='{db_material.category}'")
        
        db.add(db_material)
        db.commit()
        db.refresh(db_material)
        
        print(f"🟠 数据库保存完成，最终分类: '{db_material.category}'")
        
        # 返回上传结果
        return success_response({
            "id": db_material.id,
            "filePath": str(file_path),
            "thumbnailPath": str(thumbnail_path) if thumbnail_generated else None,
            "duration": file_info['duration'],
            "resolution": f"{file_info['width']}x{file_info['height']}",
            "fileSize": file_size,
            "format": file_info['format'],
            "frameRate": file_info['frame_rate'],
            "bitrate": file_info['bitrate'],
            "width": file_info['width'],
            "height": file_info['height']
        }, "文件上传成功")
        
    except HTTPException:
        # 清理文件
        if file_path and file_path.exists():
            file_path.unlink()
        if thumbnail_path and thumbnail_path.exists():
            thumbnail_path.unlink()
        raise
    except Exception as e:
        # 清理文件
        if file_path and file_path.exists():
            file_path.unlink()
        if thumbnail_path and thumbnail_path.exists():
            thumbnail_path.unlink()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )

@router.post("/upload/bulk")
async def bulk_upload_video_files(
    files: List[UploadFile] = File(...),
    category: str = Form("general"),
    db: Session = Depends(get_db)
):
    """批量上传视频/图片文件"""
    print(f"🟠 后端接收批量上传请求: {len(files)}个文件, 分类='{category}'")
    
    # 检查category是否为空
    if not category or category.strip() == "":
        print(f"🔴 警告: 批量上传category参数为空，使用默认值")
        category = "general"
    else:
        category = category.strip()
    
    try:
        success_materials = []
        failed_uploads = []
        
        for file in files:
            file_path = None
            thumbnail_path = None
            try:
                # 验证文件类型
                file_ext = os.path.splitext(file.filename or "")[1].lower()
                if file_ext not in ALL_SUPPORTED_TYPES:
                    failed_uploads.append({
                        "name": file.filename or "unknown",
                        "error": f"不支持的文件类型: {file_ext}"
                    })
                    continue
                
                # 生成安全的文件名
                safe_filename = get_safe_filename(file.filename or "unknown")
                unique_filename = f"{uuid4().hex}_{safe_filename}"
                file_path = UPLOAD_DIR / unique_filename
                
                # 保存文件
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)
                
                file_size = os.path.getsize(file_path)
                
                # 获取视频文件信息
                file_info = get_video_info(str(file_path))
                
                # 生成缩略图
                thumbnail_filename = f"thumb_{unique_filename}.jpg"
                thumbnail_path = UPLOAD_DIR / thumbnail_filename
                thumbnail_generated = generate_thumbnail(str(file_path), str(thumbnail_path))
                
                # 创建数据库记录
                db_material = VideoMaterial(
                    id=str(uuid4()),
                    name=safe_filename,
                    file_path=str(file_path),
                    duration=file_info['duration'],
                    category=category,
                    resolution=f"{file_info['width']}x{file_info['height']}",
                    tags=[],
                    is_built_in=False,
                    file_size=file_size,
                    format=file_info['format'],
                    frame_rate=file_info['frame_rate'],
                    bitrate=file_info['bitrate'],
                    thumbnail_path=str(thumbnail_path) if thumbnail_generated else None
                )
                
                print(f"🟠 批量上传 - 处理文件: {safe_filename}, 分类: {category}")
                
                db.add(db_material)
                db.flush()
                
                # 转换为前端格式
                frontend_data = db_material.to_frontend_format()
                success_materials.append(VideoMaterialResponse(**frontend_data))
                
            except Exception as e:
                failed_uploads.append({
                    "name": file.filename or "unknown",
                    "error": str(e)
                })
                
                # 清理文件
                if file_path and file_path.exists():
                    file_path.unlink()
                if thumbnail_path and thumbnail_path.exists():
                    thumbnail_path.unlink()
        
        db.commit()
        
        return BulkVideoMaterialResponse(
            success=success_materials,
            failed=failed_uploads,
            total=len(files),
            success_count=len(success_materials),
            failed_count=len(failed_uploads)
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量上传失败: {str(e)}"
        )


@router.get("/file/{material_id}")
async def serve_video_file(
    material_id: str,
    db: Session = Depends(get_db)
):
    """提供视频文件的HTTP访问"""
    from fastapi.responses import FileResponse
    import mimetypes
    
    # 查找素材
    material = db.query(VideoMaterial).filter(VideoMaterial.id == material_id).first()
    if not material:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="素材不存在"
        )
    
    # 检查文件是否存在
    file_path = Path(str(material.file_path))  # 确保转换为字符串
    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )
    
    # 获取MIME类型
    mime_type, _ = mimetypes.guess_type(str(file_path))
    if not mime_type:
        mime_type = "video/mp4"  # 默认视频类型
    
    return FileResponse(
        path=str(file_path),
        media_type=mime_type,
        filename=str(material.name)  # 确保转换为字符串
    )

@router.get("/thumbnail/{material_id}")
async def serve_thumbnail_file(
    material_id: str,
    db: Session = Depends(get_db)
):
    """提供缩略图文件的HTTP访问"""
    from fastapi.responses import FileResponse
    import mimetypes
    
    # 查找素材
    material = db.query(VideoMaterial).filter(VideoMaterial.id == material_id).first()
    if not material:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="素材不存在"
        )
    
    # 检查缩略图是否存在
    if not getattr(material, 'thumbnail_path', None):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="缩略图不存在"
        )
    
    thumbnail_path = Path(str(getattr(material, 'thumbnail_path', '')))
    if not thumbnail_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="缩略图文件不存在"
        )
    
    # 获取MIME类型
    mime_type, _ = mimetypes.guess_type(str(thumbnail_path))
    if not mime_type:
        mime_type = "image/jpeg"  # 默认图片类型
    
    return FileResponse(
        path=str(thumbnail_path),
        media_type=mime_type,
        filename=f"thumb_{material.name}.jpg"
    )
