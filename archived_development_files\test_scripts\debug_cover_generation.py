"""
调试封面截图问题
模拟实际视频生成流程中的调用方式
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from src.core.database import get_session_maker
from src.services.cover_screenshot_service import cover_screenshot_service
from src.models.accounts import Account
from src.models.resources import CoverTemplate
from src.models.video_generation import VideoGenerationTask, VideoGenerationJob
from loguru import logger

async def debug_cover_generation():
    """调试封面生成问题"""
    print("🔍 开始调试封面生成问题...")
    
    session_maker = get_session_maker()
    db = session_maker()
    
    try:
        # 1. 查找最近失败的任务
        failed_task = db.query(VideoGenerationTask).filter(
            VideoGenerationTask.status == 'failed',
            VideoGenerationTask.current_step == '已生成字幕'
        ).order_by(VideoGenerationTask.updated_at.desc()).first()
        
        if not failed_task:
            print("❌ 没有找到在封面生成步骤失败的任务")
            return
        
        print(f"📋 找到失败任务: {failed_task.id} - {failed_task.task_name}")
        print(f"   失败步骤: {failed_task.current_step}")
        print(f"   第一句话: {failed_task.first_sentence}")
        
        # 2. 获取作业配置
        job = db.query(VideoGenerationJob).filter(
            VideoGenerationJob.id == failed_task.job_id
        ).first()
        
        if not job:
            print("❌ 找不到相关作业")
            return
        
        job_config = job.config
        template_id = job_config.get('cover_template_id')
        
        if not template_id:
            print("❌ 作业配置中没有封面模板ID")
            return
        
        print(f"📝 使用模板ID: {template_id}")
        
        # 3. 获取账号信息
        account = db.query(Account).filter(Account.id == failed_task.account_id).first()
        if not account:
            print("❌ 找不到相关账号")
            return
        
        print(f"👤 使用账号: {account.name}")
        print(f"   头像路径: {account.avatar_file_path}")
        
        # 4. 验证模板存在性
        template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
        if not template:
            print(f"❌ 模板不存在: {template_id}")
            return
        
        print(f"📄 模板信息: {template.name}")
        
        # 5. 准备输出路径
        output_dir = Path(__file__).parent / "debug_outputs"
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / f"debug_cover_{failed_task.id}.png"
        
        print(f"📁 输出路径: {output_path}")
        
        # 6. 尝试生成封面 - 使用实际的调用方式
        print("\n🔄 开始生成封面（使用实际调用方式）...")
        
        try:
            # 方式1：使用generate_cover_for_video_task（实际使用的方法）
            success1 = await cover_screenshot_service.generate_cover_for_video_task(
                task=failed_task,
                template_id=template_id,
                account=account,
                title=failed_task.first_sentence,
                output_path=str(output_path.with_name(f"debug_cover_method1_{failed_task.id}.png")),
                db=db
            )
            
            print(f"方式1 (generate_cover_for_video_task): {'成功' if success1 else '失败'}")
            
        except Exception as e:
            print(f"方式1失败: {e}")
            import traceback
            traceback.print_exc()
            success1 = False
        
        try:
            # 方式2：使用generate_cover_screenshot（直接方法）
            success2 = await cover_screenshot_service.generate_cover_screenshot(
                template_id=template_id,
                account=account,
                title=failed_task.first_sentence,
                output_path=str(output_path.with_name(f"debug_cover_method2_{failed_task.id}.png")),
                db=db,
                additional_variables={
                    'timestamp': '2小时前',
                    'subreddit': 'r/stories',
                }
            )
            
            print(f"方式2 (generate_cover_screenshot): {'成功' if success2 else '失败'}")
            
        except Exception as e:
            print(f"方式2失败: {e}")
            import traceback
            traceback.print_exc()
            success2 = False
        
        # 7. 检查头像文件
        print(f"\n🖼️  头像文件检查:")
        if account.avatar_file_path:
            backend_root = Path(__file__).parent
            avatar_path = backend_root / "backend" / account.avatar_file_path
            print(f"   路径: {avatar_path}")
            print(f"   存在: {avatar_path.exists()}")
            if avatar_path.exists():
                print(f"   大小: {avatar_path.stat().st_size} bytes")
        else:
            print("   无头像文件路径")
        
        # 8. 检查模板文件
        print(f"\n📄 模板文件检查:")
        template_dir = backend_root / "backend" / "templates"
        template_file = template_dir / f"{template_id}.html"
        print(f"   模板文件: {template_file}")
        print(f"   存在: {template_file.exists()}")
        
        # 检查模板图片目录
        template_images_dir = template_dir / f"{template_id}_images"
        print(f"   图片目录: {template_images_dir}")
        print(f"   存在: {template_images_dir.exists()}")
        if template_images_dir.exists():
            image_files = list(template_images_dir.rglob("*"))
            print(f"   图片文件数量: {len(image_files)}")
            for img_file in image_files[:5]:  # 只显示前5个
                print(f"     - {img_file.name} ({img_file.stat().st_size} bytes)")
        
        # 9. 总结
        print(f"\n📊 调试总结:")
        print(f"   任务ID: {failed_task.id}")
        print(f"   模板ID: {template_id}")
        print(f"   账号: {account.name}")
        print(f"   方式1结果: {'成功' if success1 else '失败'}")
        print(f"   方式2结果: {'成功' if success2 else '失败'}")
        
        if success1 or success2:
            print("✅ 封面生成可以正常工作")
        else:
            print("❌ 封面生成存在问题，需要进一步调试")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_cover_generation())
