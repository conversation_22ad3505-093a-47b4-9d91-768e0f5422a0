"""
账户管理API
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import uuid4
from datetime import datetime

from ..core.database import get_db
from ..core.responses import success_response, error_response
from ..models.resources import Account
from ..schemas.resources import (
    AccountCreate,
    AccountUpdate,
    AccountResponse,
    AccountQuery,
    BulkAccountResponse
)

router = APIRouter(prefix="/accounts", tags=["accounts"])

@router.get("/", response_model=List[AccountResponse])
async def get_accounts(
    query: AccountQuery = Depends(),
    db: Session = Depends(get_db)
):
    """获取账户列表"""
    try:
        db_query = db.query(Account)
        
        # 平台过滤
        if query.platform:
            db_query = db_query.filter(Account.platform == query.platform)
        
        # 激活状态过滤
        if query.is_active is not None:
            db_query = db_query.filter(Account.is_active == query.is_active)
        
        # 搜索过滤
        if query.search:
            db_query = db_query.filter(
                Account.name.contains(query.search)
            )
        
        # 分页
        if query.skip is not None:
            db_query = db_query.offset(query.skip)
        if query.limit is not None:
            db_query = db_query.limit(query.limit)
        
        accounts = db_query.all()
        
        # 转换为前端格式
        result = []
        for account in accounts:
            frontend_data = account.to_frontend_format()
            result.append(AccountResponse(**frontend_data))
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账户列表失败: {str(e)}"
        )

@router.post("/", response_model=AccountResponse)
async def create_account(
    account_data: AccountCreate,
    db: Session = Depends(get_db)
):
    """创建账户"""
    try:
        # 创建新账户
        db_account = Account(
            id=str(uuid4()),
            name=account_data.name,
            platform=account_data.platform,
            is_active=account_data.is_active,
            config=account_data.config,
            credentials=account_data.credentials
        )
        
        db.add(db_account)
        db.commit()
        db.refresh(db_account)
        
        # 转换为前端格式
        frontend_data = db_account.to_frontend_format()
        return AccountResponse(**frontend_data)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建账户失败: {str(e)}"
        )

@router.get("/{account_id}", response_model=AccountResponse)
async def get_account(
    account_id: str,
    db: Session = Depends(get_db)
):
    """获取单个账户"""
    try:
        db_account = db.query(Account).filter(Account.id == account_id).first()
        if not db_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账户未找到"
            )
        
        # 转换为前端格式
        frontend_data = db_account.to_frontend_format()
        return AccountResponse(**frontend_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账户失败: {str(e)}"
        )

@router.put("/{account_id}", response_model=AccountResponse)
async def update_account(
    account_id: str,
    account_data: AccountUpdate,
    db: Session = Depends(get_db)
):
    """更新账户"""
    try:
        db_account = db.query(Account).filter(Account.id == account_id).first()
        if not db_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账户未找到"
            )
        
        # 更新字段
        update_data = account_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == "isActive":
                setattr(db_account, "is_active", value)
            else:
                setattr(db_account, field, value)
        
        db.commit()
        db.refresh(db_account)
        
        # 转换为前端格式
        frontend_data = db_account.to_frontend_format()
        return AccountResponse(**frontend_data)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新账户失败: {str(e)}"
        )

@router.delete("/{account_id}")
async def delete_account(
    account_id: str,
    db: Session = Depends(get_db)
):
    """删除账户"""
    try:
        db_account = db.query(Account).filter(Account.id == account_id).first()
        if not db_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账户未找到"
            )
        
        db.delete(db_account)
        db.commit()
        
        return success_response(None, "账户删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除账户失败: {str(e)}"
        )

@router.post("/bulk", response_model=BulkAccountResponse)
async def bulk_create_accounts(
    accounts_data: List[AccountCreate],
    db: Session = Depends(get_db)
):
    """批量创建账户"""
    try:
        created_accounts = []
        failed_accounts = []
        
        for account_data in accounts_data:
            try:
                db_account = Account(
                    id=str(uuid4()),
                    name=account_data.name,
                    platform=account_data.platform,
                    is_active=account_data.is_active,
                    config=account_data.config,
                    credentials=account_data.credentials
                )
                
                db.add(db_account)
                db.flush()  # 获取生成的ID但不提交
                
                frontend_data = db_account.to_frontend_format()
                created_accounts.append(AccountResponse(**frontend_data))
                
            except Exception as e:
                failed_accounts.append({
                    "name": account_data.name,
                    "error": str(e)
                })
        
        db.commit()
        
        return BulkAccountResponse(
            success=created_accounts,
            failed=failed_accounts,
            total=len(accounts_data),
            success_count=len(created_accounts),
            failed_count=len(failed_accounts)
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建账户失败: {str(e)}"
        )

@router.delete("/bulk")
async def bulk_delete_accounts(
    account_ids: List[str] = Query(..., description="要删除的账户ID列表"),
    db: Session = Depends(get_db)
):
    """批量删除账户"""
    try:
        deleted_count = db.query(Account).filter(
            Account.id.in_(account_ids)
        ).delete(synchronize_session=False)
        
        db.commit()
        
        return success_response(None, f"成功删除{deleted_count}个账户")
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除账户失败: {str(e)}"
        )

@router.get("/platforms/list")
async def get_account_platforms(db: Session = Depends(get_db)):
    """获取账户平台列表"""
    try:
        platforms = db.query(Account.platform).distinct().all()
        platform_list = [platform[0] for platform in platforms if platform[0]]
        
        return success_response({"platforms": platform_list}, "获取平台列表成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取平台列表失败: {str(e)}"
        )

@router.post("/{account_id}/activate")
async def activate_account(
    account_id: str,
    db: Session = Depends(get_db)
):
    """激活账户"""
    try:
        db_account = db.query(Account).filter(Account.id == account_id).first()
        if not db_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账户未找到"
            )
        
        db_account.is_active = True  # type: ignore
        db.commit()
        
        return success_response(None, "账户已激活")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"激活账户失败: {str(e)}"
        )

@router.post("/{account_id}/deactivate")
async def deactivate_account(
    account_id: str,
    db: Session = Depends(get_db)
):
    """停用账户"""
    try:
        db_account = db.query(Account).filter(Account.id == account_id).first()
        if not db_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账户未找到"
            )
        
        db_account.is_active = False  # type: ignore
        db.commit()
        
        return success_response(None, "账户已停用")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停用账户失败: {str(e)}"
        )

@router.post("/{account_id}/use")
async def use_account(
    account_id: str,
    db: Session = Depends(get_db)
):
    """使用账户（更新使用时间和计数）"""
    try:
        db_account = db.query(Account).filter(Account.id == account_id).first()
        if not db_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账户未找到"
            )
          # 更新使用时间和计数
        db_account.last_used_at = datetime.utcnow()  # type: ignore
        db_account.video_count += 1  # type: ignore
        db.commit()
        
        return success_response(None, "账户使用记录已更新")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新账户使用记录失败: {str(e)}"
        )
