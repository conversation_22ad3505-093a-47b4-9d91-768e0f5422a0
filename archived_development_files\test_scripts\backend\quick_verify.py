#!/usr/bin/env python
"""
后端快速验证脚本
"""
import sys
import os
from pathlib import Path

# 设置Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def main():
    print("🚀 Reddit Story Video Generator - 后端快速验证\n")
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1: 配置模块
    total_tests += 1
    try:
        from src.core.config import get_settings
        settings = get_settings()
        print(f"✅ 1. 配置模块: environment={settings.environment}")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 1. 配置模块: {e}")
    
    # 测试2: 数据库模块
    total_tests += 1
    try:
        from src.core.database import get_db, init_db
        print("✅ 2. 数据库模块: 导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 2. 数据库模块: {e}")
    
    # 测试3: 响应格式
    total_tests += 1
    try:
        from src.core.responses import ApiResponse
        print("✅ 3. 响应格式: 导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 3. 响应格式: {e}")
    
    # 测试4: 设置模型
    total_tests += 1
    try:
        from src.models.settings import Settings
        settings = Settings()
        data = settings.to_frontend_format()
        print(f"✅ 4. 设置模型: TTS={data['tts']['provider']}, LLM={data['llm']['provider']}")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 4. 设置模型: {e}")
    
    # 测试5: Schema验证
    total_tests += 1
    try:
        from src.schemas.settings import SettingsResponse, TTSConfig, LLMConfig
        print("✅ 5. Schema验证: 导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 5. Schema验证: {e}")
    
    # 测试6: API路由
    total_tests += 1
    try:
        from src.api.settings import router
        from src.api.routes import api_router
        print("✅ 6. API路由: 导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 6. API路由: {e}")
    
    # 测试7: FastAPI应用
    total_tests += 1
    try:
        from main import app
        print(f"✅ 7. FastAPI应用: 创建成功，路由数={len(app.routes)}")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 7. FastAPI应用: {e}")
    
    # 总结
    print("\n" + "="*60)
    print(f"📊 验证结果: {tests_passed}/{total_tests} 项测试通过")
    
    if tests_passed == total_tests:
        print("🎉 所有验证通过! 后端准备就绪!")
        print("\n📝 启动服务:")
        print("运行: uvicorn main:app --reload --host 0.0.0.0 --port 8000")
        print("访问: http://localhost:8000/docs")
        print("测试: http://localhost:8000/api/v1/settings")
        return True
    else:
        print("⚠️ 部分验证失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
