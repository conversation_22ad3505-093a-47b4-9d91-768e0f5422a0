#!/usr/bin/env python3
"""
调试音频时长分析 - 检查是否音频分析有问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.models.video_generation import VideoGenerationTask
from src.database import get_db
from sqlalchemy.orm import Session

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_recent_task_audio_duration():
    """调试最近任务的音频时长"""
    
    logger.info("🔍 调试最近任务的音频时长分析")
    
    try:
        # 获取数据库连接
        db: Session = next(get_db())
        
        # 获取最近的任务
        recent_tasks = db.query(VideoGenerationTask).order_by(
            VideoGenerationTask.created_at.desc()
        ).limit(5).all()
        
        if not recent_tasks:
            logger.error("❌ 没有找到任何任务")
            return False
        
        logger.info(f"找到 {len(recent_tasks)} 个最近的任务")
        
        for i, task in enumerate(recent_tasks):
            logger.info(f"\n📋 任务 {i+1}: {task.id}")
            logger.info(f"状态: {task.status}")
            logger.info(f"创建时间: {task.created_at}")
            logger.info(f"当前步骤: {task.current_step}")
            
            # 检查音频分析结果
            if task.audio_analysis:
                audio_analysis = task.audio_analysis
                logger.info(f"音频分析结果:")
                logger.info(f"  总时长: {audio_analysis.get('total_duration', 'N/A')}s")
                logger.info(f"  第一句时长: {audio_analysis.get('first_sentence_duration', 'N/A')}s")
                logger.info(f"  时间戳数量: {len(audio_analysis.get('word_timestamps', []))}")
                
                # 检查是否有12秒左右的时长
                total_duration = audio_analysis.get('total_duration', 0)
                if 11 <= total_duration <= 13:
                    logger.warning(f"⚠️ 发现可疑的音频时长: {total_duration}s (接近12秒)")
                elif total_duration > 15:
                    logger.info(f"✅ 音频时长正常: {total_duration}s (超过15秒)")
                else:
                    logger.info(f"📊 音频时长: {total_duration}s")
            else:
                logger.warning("⚠️ 没有音频分析结果")
            
            # 检查音频文件是否存在
            if task.audio_file_path:
                audio_path = Path(task.audio_file_path)
                if audio_path.exists():
                    logger.info(f"音频文件存在: {audio_path}")
                    
                    # 使用ffprobe检查实际音频时长
                    try:
                        import subprocess
                        cmd = [
                            'ffprobe', '-v', 'quiet', '-print_format', 'json',
                            '-show_format', str(audio_path)
                        ]
                        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                        
                        if result.returncode == 0:
                            import json
                            probe_data = json.loads(result.stdout)
                            actual_duration = float(probe_data['format']['duration'])
                            logger.info(f"实际音频文件时长: {actual_duration}s")
                            
                            # 比较分析结果和实际时长
                            if task.audio_analysis:
                                analyzed_duration = task.audio_analysis.get('total_duration', 0)
                                diff = abs(actual_duration - analyzed_duration)
                                if diff > 0.5:
                                    logger.warning(f"⚠️ 音频分析时长与实际时长差异较大: {diff}s")
                                else:
                                    logger.info(f"✅ 音频分析时长准确，差异: {diff}s")
                        else:
                            logger.error(f"ffprobe失败: {result.stderr}")
                    except Exception as e:
                        logger.error(f"检查音频文件时长失败: {e}")
                else:
                    logger.warning(f"音频文件不存在: {audio_path}")
            else:
                logger.warning("⚠️ 没有音频文件路径")
            
            # 检查生成的故事文本长度
            if task.generated_story:
                story_length = len(task.generated_story)
                word_count = len(task.generated_story.split())
                logger.info(f"故事文本长度: {story_length} 字符, {word_count} 词")
                
                if word_count < 50:
                    logger.warning(f"⚠️ 故事文本较短，可能导致音频时长不足")
                elif word_count > 200:
                    logger.info(f"✅ 故事文本充足")
            else:
                logger.warning("⚠️ 没有生成的故事文本")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    logger.info("🚀 开始调试音频时长分析")
    
    success = debug_recent_task_audio_duration()
    
    if success:
        logger.info("\n🎉 音频时长调试完成!")
    else:
        logger.error("\n❌ 音频时长调试失败")
        sys.exit(1)
