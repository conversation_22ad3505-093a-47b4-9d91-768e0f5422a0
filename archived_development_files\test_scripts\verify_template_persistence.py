#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证模板持久化功能的代码修改是否正确
"""

import os
import re

def check_canvas_editor_modifications():
    """检查SimpleCanvasEditor的修改"""
    
    print("🔍 检查SimpleCanvasEditor组件修改...")
    
    canvas_file = "frontend/src/components/SimpleCanvasEditor.tsx"
    
    if not os.path.exists(canvas_file):
        print(f"❌ 文件不存在: {canvas_file}")
        return False
    
    with open(canvas_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = {
        "Props接口定义": [
            r'interface SimpleCanvasEditorProps',
            r'templateId\?:.*string',
            r'templateName\?:.*string',
            r'onSave\?:.*TemplateData'
        ],
        "加载功能": [
            r'loadTemplate.*async',
            r'/api/cover-templates/',
            r'loadFromLocalStorage',
            r'useEffect.*templateId'
        ],
        "保存功能": [
            r'handleSave.*async',
            r'setIsSaving\(true\)',
            r'fetch.*PUT.*POST',
            r'Content-Type.*application/json'
        ],
        "状态管理": [
            r'isLoading.*useState',
            r'isSaving.*useState',
            r'保存中\.\.\.'
        ]
    }
    
    all_passed = True
    
    for feature_name, patterns in checks.items():
        print(f"\n📋 检查 {feature_name}:")
        feature_passed = True
        
        for i, pattern in enumerate(patterns, 1):
            if re.search(pattern, content, re.DOTALL):
                print(f"   ✅ 检查点 {i}: 通过")
            else:
                print(f"   ❌ 检查点 {i}: 失败 - {pattern}")
                feature_passed = False
        
        if not feature_passed:
            all_passed = False
    
    return all_passed

def check_page_modifications():
    """检查页面组件的修改"""
    
    print("\n🔍 检查页面组件修改...")
    
    page_file = "frontend/src/app/covers/page.tsx"
    
    if not os.path.exists(page_file):
        print(f"❌ 文件不存在: {page_file}")
        return False
    
    with open(page_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = {
        "状态管理": [
            r'currentTemplate.*useState',
            r'setCurrentTemplate'
        ],
        "编辑器集成": [
            r'templateId=\{currentTemplate\?\.id\}',
            r'templateName=\{currentTemplate\?\.name',
            r'onSave=\{handleTemplateSave\}'
        ],
        "事件处理": [
            r'handleEditTemplate',
            r'handleTemplateSave',
            r'setCurrentTemplate\(template\)'
        ]
    }
    
    all_passed = True
    
    for feature_name, patterns in checks.items():
        print(f"\n📋 检查 {feature_name}:")
        feature_passed = True
        
        for i, pattern in enumerate(patterns, 1):
            if re.search(pattern, content, re.DOTALL):
                print(f"   ✅ 检查点 {i}: 通过")
            else:
                print(f"   ❌ 检查点 {i}: 失败 - {pattern}")
                feature_passed = False
        
        if not feature_passed:
            all_passed = False
    
    return all_passed

def check_backend_modifications():
    """检查后端API修改"""
    
    print("\n🔍 检查后端API修改...")
    
    api_file = "backend/src/api/cover_template.py"
    schema_file = "backend/src/schemas/resources.py"
    model_file = "backend/src/models/resources.py"
    
    files_to_check = [
        (api_file, "API文件"),
        (schema_file, "Schema文件"),
        (model_file, "模型文件")
    ]
    
    all_passed = True
    
    for file_path, file_name in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ {file_name}不存在: {file_path}")
            all_passed = False
            continue
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if file_name == "API文件":
            patterns = [
                r'elements.*JSON',
                r'background.*JSON',
                r'@router\.get.*template_id',
                r'@router\.put.*template_id'
            ]
        elif file_name == "Schema文件":
            patterns = [
                r'elements.*List\[Dict',
                r'background.*Dict',
                r'CoverTemplateUpdate'
            ]
        else:  # 模型文件
            patterns = [
                r'elements.*Column\(JSON',
                r'background.*Column\(JSON',
                r'to_frontend_format'
            ]
        
        print(f"\n📋 检查 {file_name}:")
        file_passed = True
        
        for i, pattern in enumerate(patterns, 1):
            if re.search(pattern, content, re.DOTALL):
                print(f"   ✅ 检查点 {i}: 通过")
            else:
                print(f"   ❌ 检查点 {i}: 失败 - {pattern}")
                file_passed = False
        
        if not file_passed:
            all_passed = False
    
    return all_passed

def main():
    """主函数"""
    print("🧪 验证模板持久化功能代码修改")
    print("=" * 60)
    
    # 检查前端修改
    canvas_ok = check_canvas_editor_modifications()
    page_ok = check_page_modifications()
    
    # 检查后端修改
    backend_ok = check_backend_modifications()
    
    print("\n" + "=" * 60)
    print("📊 检查结果汇总:")
    print(f"   SimpleCanvasEditor组件: {'✅ 通过' if canvas_ok else '❌ 失败'}")
    print(f"   页面组件: {'✅ 通过' if page_ok else '❌ 失败'}")
    print(f"   后端API: {'✅ 通过' if backend_ok else '❌ 失败'}")
    
    if canvas_ok and page_ok and backend_ok:
        print("\n🎉 所有修改都已正确实施！")
        print("\n✨ 功能改进总结:")
        print("   1. ✅ 模板保存到数据库而非localStorage")
        print("   2. ✅ 编辑器支持templateId和templateName参数")
        print("   3. ✅ 进入编辑器时自动加载模板数据")
        print("   4. ✅ 保存时显示加载状态和进度")
        print("   5. ✅ 支持elements和background的完整持久化")
        print("   6. ✅ 前后端数据格式统一")
        print("   7. ✅ 错误处理和兜底方案（localStorage）")
        
        print("\n📝 使用方法:")
        print("   1. 启动后端: cd backend && python main.py")
        print("   2. 启动前端: cd frontend && npm run dev")
        print("   3. 访问: http://localhost:3000/covers")
        print("   4. 创建/编辑模板，所有更改将自动保存到数据库")
        print("   5. 重新进入编辑器时，所有数据会自动加载")
    else:
        print("\n⚠️  发现一些问题，请检查上述失败的检查点")

if __name__ == "__main__":
    main()
