#!/usr/bin/env python3
"""
测试后端API是否正常工作
"""

import requests
import json

def test_cover_template_api():
    """测试封面模板API"""
    base_url = "http://localhost:8000"
    
    print("测试封面模板API...")
    
    # 1. 获取模板列表
    try:
        print("\n1. 获取模板列表...")
        response = requests.get(f"{base_url}/api/cover-templates")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success') and data.get('data'):
                templates = data['data']
                print(f"找到 {len(templates)} 个模板")
                
                # 2. 测试获取第一个模板的详情
                if templates:
                    template_id = templates[0]['id']
                    print(f"\n2. 获取模板详情 (ID: {template_id})...")
                    
                    response2 = requests.get(f"{base_url}/api/cover-templates/{template_id}")
                    print(f"状态码: {response2.status_code}")
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        print(f"模板详情: {json.dumps(data2, indent=2, ensure_ascii=False)}")
                        return True
                    else:
                        print(f"获取模板详情失败: {response2.text}")
                        return False
                else:
                    print("没有找到模板")
                    return False
            else:
                print("API响应格式错误")
                return False
        else:
            print(f"获取模板列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"API测试失败: {e}")
        return False

if __name__ == '__main__':
    test_cover_template_api()
