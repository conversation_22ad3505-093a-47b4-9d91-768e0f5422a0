"""
F5-TTS音色管理数据库模型
"""

from sqlalchemy import Column, String, Text, Boolean, Integer, DECIMAL, TIMESTAMP, func
from . import BaseModel
import uuid
from datetime import datetime


class F5TTSVoice(BaseModel):
    """F5-TTS音色模型"""
    __tablename__ = "f5_tts_voices"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False, comment="音色名称")
    description = Column(Text, comment="音色描述")
    language = Column(String(10), nullable=False, default="zh-CN", comment="语言")
    gender = Column(String(10), nullable=False, comment="性别")
    
    # F5-TTS特有字段
    ref_audio_path = Column(String(500), nullable=False, comment="参考音频文件路径")
    ref_text = Column(Text, nullable=False, comment="参考音频对应文本")
    
    # 配置参数
    remove_silence = Column(Boolean, default=False, comment="是否移除静音")
    cross_fade_duration = Column(DECIMAL(3,2), default=0.15, comment="交叉淡化时长")
    nfe_value = Column(Integer, default=32, comment="NFE参数")
    randomize_seed = Column(Boolean, default=True, comment="是否随机种子")
    
    # 状态管理
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_built_in = Column(Boolean, default=False, comment="是否内置")
    
    # 统计信息
    usage_count = Column(Integer, default=0, comment="使用次数")
    last_used_at = Column(TIMESTAMP, nullable=True, comment="最后使用时间")
    
    # 时间戳
    created_at = Column(TIMESTAMP, default=func.now(), comment="创建时间")
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def to_frontend_format(self):
        """转换为前端期望的格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "language": self.language,
            "gender": self.gender,
            "provider": "f5-tts",
            "ref_audio_path": self.ref_audio_path,
            "ref_text": self.ref_text,
            "remove_silence": self.remove_silence,
            "cross_fade_duration": float(self.cross_fade_duration) if self.cross_fade_duration else 0.15,
            "nfe_value": self.nfe_value,
            "randomize_seed": self.randomize_seed,
            "is_active": self.is_active,
            "is_built_in": self.is_built_in,
            "usage_count": self.usage_count,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count = (self.usage_count or 0) + 1
        self.last_used_at = func.now()
