-- F5-TTS数据库迁移SQL脚本

-- 1. 添加f5_tts_endpoint字段到settings表
ALTER TABLE settings ADD COLUMN f5_tts_endpoint VARCHAR(500);

-- 2. 创建f5_tts_voices表
CREATE TABLE IF NOT EXISTS f5_tts_voices (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    language VARCHAR(10) NOT NULL DEFAULT 'zh-CN',
    gender VARCHAR(10) NOT NULL,
    ref_audio_path VARCHAR(500) NOT NULL,
    ref_text TEXT NOT NULL,
    remove_silence BOOLEAN DEFAULT 0,
    cross_fade_duration DECIMAL(3,2) DEFAULT 0.15,
    nfe_value INTEGER DEFAULT 32,
    randomize_seed BOOLEAN DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    is_built_in BOOLEAN DEFAULT 0,
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 验证迁移结果
.schema settings
.schema f5_tts_voices
