"""
完整测试：验证圆角封面在正常视频生成流程中的效果
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

# 加载环境变量
load_dotenv(dotenv_path=backend_path / '.env')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate, VideoMaterial, BackgroundMusic, Prompt, VideoGenerationTask, VideoGenerationJob, TaskStatus
from backend.src.services.video_generation_service import VideoGenerationService

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_full_video_generation_with_rounded_cover():
    """测试完整的视频生成流程（包含圆角封面）"""
    db = SessionLocal()
    try:
        print("=== 完整视频生成流程测试（圆角封面） ===")
        
        # 1. 检查必要的资源
        account = db.query(Account).first()
        if not account:
            print("❌ 数据库中没有找到账号")
            return
        
        video_materials = db.query(VideoMaterial).limit(3).all()
        if len(video_materials) < 2:
            print("❌ 数据库中视频素材不足")
            return
        
        prompt = db.query(Prompt).first()
        if not prompt:
            print("❌ 数据库中没有找到提示词")
            return
        
        music = db.query(BackgroundMusic).first()
        if not music:
            print("❌ 数据库中没有找到背景音乐")
            return
        
        template = db.query(CoverTemplate).first()
        if not template:
            print("❌ 数据库中没有找到封面模板")
            return
        
        print(f"✅ 资源检查完成")
        print(f"   账号: {account.name}")
        print(f"   视频素材: {len(video_materials)}个")
        print(f"   背景音乐: {music.name}")
        print(f"   封面模板: {template.name}")
        print(f"   提示词: {prompt.name}")
        
        # 2. 创建测试任务
        task = VideoGenerationTask(
            account_id=account.id,
            job_id="rounded-corner-full-test",
            task_index=1,
            reddit_url="https://test.reddit.com/r/test/comments/test",
            status=TaskStatus.PENDING,
            generated_story="这是一个完整的测试故事，用来验证圆角封面功能在正常视频生成流程中的效果。我们希望看到封面图片具有漂亮的圆角效果。",
            first_sentence="这是一个完整的测试故事，用来验证圆角封面功能。"
        )
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 创建测试任务: {task.id}")
        
        # 3. 准备作业配置
        job_config = {
            "video_material_group": "shorts",
            "material_selection": "random",
            "prompt_id": prompt.id,
            "voice_settings": {"voice": "zh_male_beijingxiaoye_emo_v2_mars_bigtts", "speed": 1.0},
            "background_music_group": music.category or "default",
            "music_selection": "random",
            "cover_template_id": template.id,
            "subtitle_settings": {"font": "Arial", "size": 24, "color": "#ffffff"},
            "video_settings": {"resolution": "1080x1920", "fps": 30, "format": "mp4"}
        }
        
        # 4. 创建视频生成服务
        video_service = VideoGenerationService(SessionLocal)
        
        print("🔄 开始完整视频生成流程...")
        
        # 5. 执行完整的视频生成流程
        result = await video_service._execute_task(str(task.id), job_config)
        
        if result and "video_path" in result:
            video_path = result["video_path"]
            full_video_path = backend_path / video_path
            
            if full_video_path.exists():
                file_size = full_video_path.stat().st_size / (1024 * 1024)  # MB
                print(f"✅ 完整视频生成成功!")
                print(f"   输出路径: {video_path}")
                print(f"   文件大小: {file_size:.2f} MB")
                
                # 验证圆角效果参数
                print(f"🎬 圆角封面效果验证:")
                print(f"   📐 封面宽度: 864px (视频宽度的80%)")
                print(f"   📐 圆角半径: 34px (封面宽度的4%)")
                print(f"   ⏱️ 封面显示时长: 根据第一句话语音时长自动计算")
                print(f"   🎯 居中显示: 封面在视频画面中自动居中")
                
                print("✅ 圆角封面功能已集成到完整视频生成流程中！")
                print("📹 请播放生成的视频文件，验证以下效果:")
                print("   1. 封面图片应该具有圆角效果（不是直角）")
                print("   2. 封面大小合适（80%宽度）且居中显示")
                print("   3. 封面在第一句话期间显示，然后淡出")
                print("   4. 其他视频合成功能（字幕、背景音乐等）正常工作")
                
                return True
            else:
                print("❌ 视频文件生成失败")
                return False
        else:
            print("❌ 视频生成流程失败")
            print(f"   结果: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_full_video_generation_with_rounded_cover())
