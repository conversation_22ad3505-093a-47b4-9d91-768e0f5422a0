"""
测试视频生成中封面失败时的处理
验证Playwright失败时任务直接终止，不回退到PIL
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 设置路径和工作目录
backend_path = Path(__file__).parent / 'backend'
os.chdir(backend_path)
sys.path.insert(0, str(backend_path))

# 加载环境变量
load_dotenv(dotenv_path=backend_path / '.env')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.src.models import Account, CoverTemplate
from backend.src.services.video_generation_helpers import VideoGenerationServiceHelpers

# 数据库连接
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def test_video_cover_failure_handling():
    """测试视频生成中封面失败的处理"""
    db = SessionLocal()
    try:
        print("=== 测试视频生成中封面失败处理 ===")
        
        # 获取测试资源
        account = db.query(Account).first()
        template = db.query(CoverTemplate).first()
        
        if not account or not template:
            print("❌ 缺少必要的测试资源")
            return
        
        print(f"✅ 使用账号: {account.name}")
        print(f"✅ 使用模板: {template.name}")
        
        # 构建测试任务数据
        class MockTask:
            def __init__(self, account_id):
                self.id = "failure-test-task"
                self.account_id = account_id
                self.generated_story = "这是一个测试任务失败处理的故事。"
                self.first_sentence = "这是一个测试任务失败处理的故事。"
        
        task = MockTask(account.id)
        
        # 构建作业配置 - 使用不存在的模板ID来故意引发失败
        job_config = {
            "cover_template_id": "non-existent-template-id",  # 故意使用不存在的模板
            "subtitle_settings": {"font": "Arial", "size": 24, "color": "#ffffff"}
        }
        
        print(f"🔄 开始测试封面生成失败处理...")
        print(f"   使用不存在的模板ID: {job_config['cover_template_id']}")
        
        # 创建服务实例
        helpers = VideoGenerationServiceHelpers(SessionLocal)
        
        # 调用封面生成函数 - 应该失败
        try:
            cover_path = await helpers._generate_cover(
                task=task,
                job_config=job_config
            )
            print(f"❌ 预期失败但成功了: {cover_path}")
            
        except Exception as e:
            print(f"✅ 任务正确失败: {e}")
            print("✅ 没有回退到PIL生成方式")
            
        # 现在测试正常的封面生成
        print(f"\n🔄 测试正常的封面生成...")
        job_config["cover_template_id"] = template.id
        
        try:
            cover_path = await helpers._generate_cover(
                task=task,
                job_config=job_config
            )
            print(f"✅ 正常封面生成成功: {cover_path}")
            
            # 检查文件是否存在
            full_path = backend_path / cover_path
            if full_path.exists():
                file_size = full_path.stat().st_size
                print(f"   文件大小: {file_size} 字节")
            else:
                print("❌ 文件未生成")
                
        except Exception as e:
            print(f"❌ 正常封面生成失败: {e}")
            
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_video_cover_failure_handling())
