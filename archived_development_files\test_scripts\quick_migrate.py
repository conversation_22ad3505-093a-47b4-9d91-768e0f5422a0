import sqlite3

# 连接数据库
conn = sqlite3.connect('reddit_story_generator.db')
cursor = conn.cursor()

try:
    # 添加f5_tts_endpoint字段
    cursor.execute("ALTER TABLE settings ADD COLUMN f5_tts_endpoint VARCHAR(500)")
    print("✅ 添加f5_tts_endpoint字段成功")
except sqlite3.OperationalError as e:
    if "duplicate column name" in str(e):
        print("✅ f5_tts_endpoint字段已存在")
    else:
        print(f"❌ 添加字段失败: {e}")

try:
    # 创建f5_tts_voices表
    cursor.execute("""
        CREATE TABLE f5_tts_voices (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            language VARCHAR(10) NOT NULL DEFAULT 'zh-CN',
            gender VARCHAR(10) NOT NULL,
            ref_audio_path VARCHAR(500) NOT NULL,
            ref_text TEXT NOT NULL,
            remove_silence BOOLEAN DEFAULT 0,
            cross_fade_duration DECIMAL(3,2) DEFAULT 0.15,
            nfe_value INTEGER DEFAULT 32,
            randomize_seed BOOLEAN DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            is_built_in BOOLEAN DEFAULT 0,
            usage_count INTEGER DEFAULT 0,
            last_used_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    print("✅ 创建f5_tts_voices表成功")
except sqlite3.OperationalError as e:
    if "already exists" in str(e):
        print("✅ f5_tts_voices表已存在")
    else:
        print(f"❌ 创建表失败: {e}")

# 提交更改
conn.commit()
conn.close()

print("🎉 数据库迁移完成！")
