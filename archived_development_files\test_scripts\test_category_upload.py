#!/usr/bin/env python3
"""
测试视频上传时分类参数是否正确传递和存储
"""

import requests
import os
from pathlib import Path

# 配置
API_BASE = "http://localhost:8000"
UPLOAD_URL = f"{API_BASE}/api/video-materials/upload"

# 创建一个测试文件（小的视频文件模拟）
def create_test_file():
    """创建一个小的测试文件"""
    test_file = Path("test_video.mp4")
    if not test_file.exists():
        # 创建一个很小的假视频文件用于测试
        with open(test_file, "wb") as f:
            f.write(b"fake video content for testing")
    return test_file

def test_category_upload():
    """测试分类上传"""
    test_file = create_test_file()
    
    try:
        # 测试不同分类的上传
        test_categories = ["general", "action", "comedy", "drama"]
        
        for category in test_categories:
            print(f"\n测试分类: {category}")
            
            with open(test_file, "rb") as f:
                files = {"file": (test_file.name, f, "video/mp4")}
                data = {
                    "category": category,
                    "tags": "test,upload"
                }
                
                response = requests.post(UPLOAD_URL, files=files, data=data)
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✓ 上传成功")
                    print(f"  响应: {result}")
                else:
                    print(f"✗ 上传失败: {response.status_code}")
                    print(f"  错误: {response.text}")
                    
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()

def test_query_by_category():
    """测试按分类查询"""
    query_url = f"{API_BASE}/api/video-materials"
    
    test_categories = ["general", "action", "comedy", "drama"]
    
    for category in test_categories:
        print(f"\n查询分类: {category}")
        
        response = requests.get(query_url, params={"category": category})
        
        if response.status_code == 200:
            materials = response.json()
            print(f"✓ 查询成功，找到 {len(materials)} 个素材")
            for material in materials:
                print(f"  - {material.get('name')} (分类: {material.get('category')})")
        else:
            print(f"✗ 查询失败: {response.status_code}")
            print(f"  错误: {response.text}")

if __name__ == "__main__":
    print("开始测试分类上传功能...")
    
    # 首先测试服务器是否在运行
    try:
        response = requests.get(f"{API_BASE}/docs")
        print("✓ 后端服务器正在运行")
    except:
        print("✗ 后端服务器未运行，请先启动后端服务")
        exit(1)
    
    # 测试上传
    test_category_upload()
    
    # 测试查询
    test_query_by_category()
    
    print("\n测试完成！")
