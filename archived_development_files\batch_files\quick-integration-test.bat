@echo off
:: Reddit故事视频生成器 - 快速前后端联调脚本
echo ===============================================
echo   Reddit Story Video Generator 
echo   Quick Integration Test
echo ===============================================
echo.

:: 设置工作目录
cd /d "%~dp0"

:: 检查Python环境
echo 🔍 Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to PATH
    pause
    exit /b 1
)

echo ✅ Python environment check passed
echo.

:: 运行快速集成测试
echo 🧪 Running Quick Integration Test
echo --------------------------------
cd backend

echo 📋 This test will check:
echo   - Server connectivity
echo   - API endpoint responses  
echo   - Frontend accessibility
echo   - Basic integration health
echo.

python quick_integration_test_fixed.py
set TEST_RESULT=%errorlevel%

echo.
if %TEST_RESULT% equ 0 (
    echo ===============================================
    echo   🎉 Quick Integration Test PASSED!
    echo ===============================================
    echo.
    echo ✅ Your frontend and backend are working together correctly!
    echo.
    echo 📋 Recommended Next Steps:
    echo   1. Open the frontend and test each page manually
    echo   2. Try creating, editing, and deleting items  
    echo   3. Check browser console for any errors
    echo   4. Verify data persistence across page reloads
    echo.
    echo 🌐 Quick Access:
    echo   Frontend: http://localhost:3000
    echo   Backend API: http://localhost:8000
    echo   API Documentation: http://localhost:8000/docs
    echo.
    echo 🎯 For detailed manual testing, run:
    echo   python manual_test_guide.py
    echo.
) else (
    echo ===============================================
    echo   ❌ Quick Integration Test FAILED!
    echo ===============================================
    echo.
    echo 🔧 Common Issues and Solutions:
    echo.
    echo 1. Backend Server Issues:
    echo    - Check if Python dependencies are installed
    echo    - Try: pip install -r requirements.txt
    echo    - Check for port conflicts ^(8000^)
    echo.
    echo 2. Frontend Server Issues:
    echo    - Check if Node.js is installed
    echo    - Try: cd ../frontend ^&^& npm install
    echo    - Check for port conflicts ^(3000^)
    echo.
    echo 3. Database Issues:
    echo    - Check if SQLite database file exists
    echo    - Try: python -c "from src.core.database import init_db; init_db()"
    echo.
    echo 4. API Route Issues:
    echo    - Check backend console for errors
    echo    - Verify all models are imported correctly
    echo.
    echo 🔄 To debug further:
    echo   1. Start backend: python start_server.py
    echo   2. Check console output for detailed errors
    echo   3. Open API docs: http://localhost:8000/docs
    echo   4. Test individual endpoints manually
    echo.
)

echo Press any key to continue...
pause >nul
