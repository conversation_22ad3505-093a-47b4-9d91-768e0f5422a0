'use client'

import React, { useEffect, useState } from 'react'
import { ClientOnly } from '../../hooks/useClientOnly'

export default function DiagnosticsPage() {
  const [diagnostics, setDiagnostics] = useState({
    isClient: false,
    isHydrated: false,
    userAgent: '',
    timestamp: '',
    location: '',
    nextVersion: '',
    reactVersion: '',
  })

  useEffect(() => {
    setDiagnostics({
      isClient: true,
      isHydrated: true,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      location: window.location.href,
      nextVersion: process.env.__NEXT_VERSION__ || 'Unknown',
      reactVersion: React.version || 'Unknown',
    })
  }, [])

  return (
    <ClientOnly
      fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载诊断信息...</p>
          </div>
        </div>
      }
    >
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">
              前端诊断信息
            </h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-800">渲染状态</h2>
                
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${diagnostics.isClient ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm">客户端渲染: {diagnostics.isClient ? '✅ 正常' : '❌ 失败'}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${diagnostics.isHydrated ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm">Hydration: {diagnostics.isHydrated ? '✅ 成功' : '❌ 失败'}</span>
                </div>
              </div>
              
              <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-800">环境信息</h2>
                
                <div className="text-sm space-y-2">
                  <div>
                    <span className="font-medium">Next.js 版本:</span> {diagnostics.nextVersion}
                  </div>
                  <div>
                    <span className="font-medium">React 版本:</span> {diagnostics.reactVersion}
                  </div>
                  <div>
                    <span className="font-medium">时间戳:</span> {diagnostics.timestamp}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800 mb-3">浏览器信息</h2>
              <div className="bg-gray-50 p-3 rounded text-xs text-gray-600 font-mono">
                {diagnostics.userAgent || '加载中...'}
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800 mb-3">URL 信息</h2>
              <div className="bg-gray-50 p-3 rounded text-xs text-gray-600 font-mono">
                {diagnostics.location || '加载中...'}
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800 mb-3">操作</h2>
              <div className="flex space-x-4">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  刷新页面
                </button>
                <button
                  onClick={() => window.history.back()}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                >
                  返回上页
                </button>
                <a
                  href="/"
                  className="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                >
                  回到首页
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ClientOnly>
  )
}
